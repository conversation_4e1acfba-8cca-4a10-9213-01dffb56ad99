<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f6fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
        .btn-info { background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #17a2b8;
        }
        
        .websocket-status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎛️ 控制台功能测试</h1>
        
        <div class="websocket-status disconnected" id="wsStatus">
            ❌ WebSocket未连接
        </div>
        
        <div class="test-section">
            <h3>📱 模拟设备控制</h3>
            <p>测试各种设备控制命令的模态对话框</p>
            
            <button class="btn btn-primary" onclick="testVideoControls()">🎥 视频参数设置</button>
            <button class="btn btn-success" onclick="testGameControls()">🎮 游戏启动设置</button>
            <button class="btn btn-info" onclick="testScreenshot()">📸 视频流截屏</button>
            <button class="btn btn-warning" onclick="testUpgrade()">📦 应用升级</button>
            
            <div class="result" id="commandResult" style="display: none;">
                <strong>命令结果：</strong>
                <div id="commandOutput"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📨 消息测试</h3>
            <p>测试各种消息提示和WebSocket通信</p>
            
            <button class="btn btn-success" onclick="testToast('success')">✅ 成功消息</button>
            <button class="btn btn-danger" onclick="testToast('error')">❌ 错误消息</button>
            <button class="btn btn-warning" onclick="testToast('warning')">⚠️ 警告消息</button>
            <button class="btn btn-info" onclick="testToast('info')">ℹ️ 信息消息</button>
            
            <button class="btn" onclick="connectWebSocket()">🔗 连接WebSocket</button>
            <button class="btn" onclick="disconnectWebSocket()">🔌 断开WebSocket</button>
        </div>
        
        <div class="test-section">
            <h3>📋 消息日志</h3>
            <div class="log" id="messageLog">
                <div class="log-entry log-info">[INFO] 测试页面已加载</div>
            </div>
            <button class="btn" onclick="clearLog()">🧹 清空日志</button>
        </div>
    </div>

    <script>
        let websocket = null;
        let testDeviceId = 'test-device-001';
        
        // 模拟admin.js的功能
        const admin = {
            showModal: function(title, content, confirmCallback) {
                const result = confirm(`${title}\n\n${content.replace(/<[^>]*>/g, '')}\n\n点击确定继续，取消退出`);
                if (result && confirmCallback) {
                    confirmCallback();
                }
            },
            
            showToast: function(type, title, message) {
                addLog(`[${type.toUpperCase()}] ${title}: ${message}`, type);
            },
            
            sendQuickCommand: function(deviceId, command, params = {}) {
                addLog(`[COMMAND] 发送命令: ${command} -> ${deviceId}`, 'info');
                addLog(`[PARAMS] ${JSON.stringify(params, null, 2)}`, 'info');
                
                // 模拟命令发送
                setTimeout(() => {
                    this.showToast('success', '命令发送', `命令 ${command} 已发送到设备 ${deviceId}`);
                }, 500);
            }
        };
        
        function testVideoControls() {
            const content = `
                分辨率: 720p (1280x720)
                码率: 3000 kbps
                帧率: 60 fps
            `;
            
            admin.showModal('🎥 视频参数设置', content, () => {
                admin.sendQuickCommand(testDeviceId, 'change_resolution', {
                    resolution: '720p',
                    bitrate: 3000,
                    framerate: 60
                });
            });
        }
        
        function testGameControls() {
            const content = `
                启用自动启动游戏: 是
                游戏包名: com.example.testgame
            `;
            
            admin.showModal('🎮 游戏启动设置', content, () => {
                admin.sendQuickCommand(testDeviceId, 'set_auto_start_game', {
                    enabled: true,
                    package_name: 'com.example.testgame'
                });
            });
        }
        
        function testScreenshot() {
            const content = `
                此功能将从当前视频流中抽取一帧画面作为截屏
                截屏备注: 测试截屏
            `;
            
            admin.showModal('📸 视频流截屏', content, () => {
                admin.sendQuickCommand(testDeviceId, 'take_screenshot', {
                    request_id: `test_${Date.now()}`,
                    note: '测试截屏',
                    source: 'video_stream'
                });
            });
        }
        
        function testUpgrade() {
            const content = `
                APK下载地址: https://example.com/app.apk
                版本号: 2.0.0
                升级类型: 强制升级
            `;
            
            admin.showModal('📦 应用升级', content, () => {
                admin.sendQuickCommand(testDeviceId, 'upgrade', {
                    apk_url: 'https://example.com/app.apk',
                    version: '2.0.0',
                    force: true
                });
            });
        }
        
        function testToast(type) {
            const messages = {
                success: '操作成功完成！',
                error: '操作失败，请重试',
                warning: '请注意相关风险',
                info: '这是一条信息提示'
            };
            
            admin.showToast(type, `${type.toUpperCase()}测试`, messages[type]);
        }
        
        function connectWebSocket() {
            if (websocket) {
                addLog('[WEBSOCKET] 已存在连接', 'warning');
                return;
            }
            
            try {
                const wsUrl = 'ws://localhost:8765';
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = () => {
                    addLog('[WEBSOCKET] 连接成功', 'success');
                    updateWSStatus(true);
                    
                    // 注册为测试管理控制台
                    const registerMsg = {
                        type: 'register',
                        id: 'test-admin-console',
                        role: 'admin',
                        name: '测试管理控制台'
                    };
                    websocket.send(JSON.stringify(registerMsg));
                };
                
                websocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        addLog(`[WEBSOCKET] 收到消息: ${data.type}`, 'info');
                        addLog(`[DATA] ${JSON.stringify(data, null, 2)}`, 'info');
                        
                        if (data.type === 'command_response') {
                            admin.showToast(data.success ? 'success' : 'error', 
                                '命令响应', `${data.command}: ${data.message}`);
                        } else if (data.type === 'screenshot_result') {
                            admin.showToast(data.success ? 'success' : 'error',
                                '截屏结果', data.success ? '截屏成功' : data.message);
                        }
                    } catch (e) {
                        addLog(`[WEBSOCKET] 解析消息失败: ${e.message}`, 'error');
                    }
                };
                
                websocket.onclose = () => {
                    addLog('[WEBSOCKET] 连接已断开', 'warning');
                    updateWSStatus(false);
                    websocket = null;
                };
                
                websocket.onerror = (error) => {
                    addLog('[WEBSOCKET] 连接错误', 'error');
                };
                
            } catch (e) {
                addLog(`[WEBSOCKET] 连接失败: ${e.message}`, 'error');
            }
        }
        
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                addLog('[WEBSOCKET] 主动断开连接', 'info');
            } else {
                addLog('[WEBSOCKET] 没有活动连接', 'warning');
            }
        }
        
        function updateWSStatus(connected) {
            const status = document.getElementById('wsStatus');
            if (connected) {
                status.className = 'websocket-status connected';
                status.textContent = '✅ WebSocket已连接';
            } else {
                status.className = 'websocket-status disconnected';
                status.textContent = '❌ WebSocket未连接';
            }
        }
        
        function addLog(message, type = 'info') {
            const log = document.getElementById('messageLog');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('messageLog').innerHTML = 
                '<div class="log-entry log-info">[INFO] 日志已清空</div>';
        }
        
        // 页面加载完成后自动连接
        window.addEventListener('load', () => {
            addLog('[SYSTEM] 测试页面加载完成', 'success');
        });
    </script>
</body>
</html>
