@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM WebRTC 信令服务器启动脚本 (Windows版本)
REM 使用方法: start_signaling_server.bat [dev|prod|background]

REM 默认配置
set DEFAULT_WS_HOST=0.0.0.0
set DEFAULT_WS_PORT=28765
set DEFAULT_HTTP_HOST=0.0.0.0
set DEFAULT_HTTP_PORT=28080
set DEFAULT_WEB_DIR=/www/wwwroot/sj/web
set DEFAULT_LOG_DIR=logs

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set SERVER_SCRIPT=%SCRIPT_DIR%enhanced_signaling_server.py

REM 检查 Python 脚本是否存在
if not exist "%SERVER_SCRIPT%" (
    echo ❌ 错误: 找不到服务器脚本 %SERVER_SCRIPT%
    pause
    exit /b 1
)

REM 创建日志目录
if not exist "%DEFAULT_LOG_DIR%" mkdir "%DEFAULT_LOG_DIR%"

REM 获取运行模式
set MODE=%1
if "%MODE%"=="" set MODE=dev

if /i "%MODE%"=="dev" goto :dev
if /i "%MODE%"=="development" goto :dev
if /i "%MODE%"=="prod" goto :prod
if /i "%MODE%"=="production" goto :prod
if /i "%MODE%"=="background" goto :background
if /i "%MODE%"=="bg" goto :background
if /i "%MODE%"=="stop" goto :stop
if /i "%MODE%"=="status" goto :status
if /i "%MODE%"=="logs" goto :logs
if /i "%MODE%"=="help" goto :help
if /i "%MODE%"=="-h" goto :help
if /i "%MODE%"=="--help" goto :help
goto :unknown

:dev
echo 🚀 启动开发模式...
python "%SERVER_SCRIPT%" --ws-host %DEFAULT_WS_HOST% --ws-port %DEFAULT_WS_PORT% --http-host %DEFAULT_HTTP_HOST% --http-port %DEFAULT_HTTP_PORT% --web-dir "%DEFAULT_WEB_DIR%" --log-dir "%DEFAULT_LOG_DIR%" --log-level DEBUG
goto :end

:prod
echo 🏭 启动生产模式...
python "%SERVER_SCRIPT%" --ws-host %DEFAULT_WS_HOST% --ws-port %DEFAULT_WS_PORT% --http-host %DEFAULT_HTTP_HOST% --http-port %DEFAULT_HTTP_PORT% --web-dir "%DEFAULT_WEB_DIR%" --log-dir "%DEFAULT_LOG_DIR%" --log-level INFO
goto :end

:background
echo 🌙 启动后台模式...
start /b python "%SERVER_SCRIPT%" --ws-host %DEFAULT_WS_HOST% --ws-port %DEFAULT_WS_PORT% --http-host %DEFAULT_HTTP_HOST% --http-port %DEFAULT_HTTP_PORT% --web-dir "%DEFAULT_WEB_DIR%" --log-dir "%DEFAULT_LOG_DIR%" --log-level INFO --no-console
echo ✅ 服务器已在后台启动
echo 📝 日志文件: %DEFAULT_LOG_DIR%\signaling_server_*.log
echo 🔍 查看日志: type %DEFAULT_LOG_DIR%\signaling_server_*.log
echo ⏹️  停止服务: taskkill /f /im python.exe
goto :end

:stop
echo ⏹️  停止信令服务器...
taskkill /f /im python.exe /fi "WINDOWTITLE eq enhanced_signaling_server*" 2>nul
if %errorlevel%==0 (
    echo ✅ 服务器已停止
) else (
    echo ⚠️  没有找到运行中的服务器进程
)
goto :end

:status
echo 🔍 检查服务器状态...
tasklist /fi "IMAGENAME eq python.exe" | find "python.exe" >nul
if %errorlevel%==0 (
    echo ✅ Python进程正在运行
    tasklist /fi "IMAGENAME eq python.exe"
) else (
    echo ❌ 没有找到Python进程
)
goto :end

:logs
echo 📝 查看最新日志...
if exist "%DEFAULT_LOG_DIR%" (
    for /f "delims=" %%i in ('dir /b /o-d "%DEFAULT_LOG_DIR%\signaling_server_*.log" 2^>nul') do (
        set LATEST_LOG=%DEFAULT_LOG_DIR%\%%i
        goto :show_log
    )
    echo ❌ 没有找到日志文件
    goto :end
    :show_log
    echo 📄 日志文件: !LATEST_LOG!
    type "!LATEST_LOG!"
) else (
    echo ❌ 日志目录不存在: %DEFAULT_LOG_DIR%
)
goto :end

:help
echo WebRTC 信令服务器启动脚本 (Windows版本)
echo.
echo 使用方法:
echo   %~nx0 [模式]
echo.
echo 模式:
echo   dev, development  - 开发模式 (DEBUG日志级别)
echo   prod, production  - 生产模式 (INFO日志级别)
echo   background, bg    - 后台模式 (后台运行，仅文件日志)
echo   stop              - 停止服务器
echo   status            - 查看服务器状态
echo   logs              - 查看最新日志
echo   help              - 显示此帮助信息
echo.
echo 配置:
echo   WebSocket端口: %DEFAULT_WS_PORT%
echo   HTTP端口: %DEFAULT_HTTP_PORT%
echo   Web目录: %DEFAULT_WEB_DIR%
echo   日志目录: %DEFAULT_LOG_DIR%
echo.
echo 示例:
echo   %~nx0 dev              # 开发模式
echo   %~nx0 prod             # 生产模式
echo   %~nx0 background       # 后台运行
echo   %~nx0 stop             # 停止服务
echo   %~nx0 logs             # 查看日志
goto :end

:unknown
echo ❌ 未知模式: %MODE%
echo 💡 使用 '%~nx0 help' 查看帮助信息
goto :end

:end
if not "%MODE%"=="background" pause
