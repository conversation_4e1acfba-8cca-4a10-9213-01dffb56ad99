<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .demo-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .ip-group {
            margin-bottom: 20px;
            background: white;
            border-radius: 12px;
            padding: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .ip-group-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f1f3f4;
        }
        .ip-group-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }
        .ip-group-count {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }
        .device-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }
        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }
        .device-info-value {
            color: #495057;
            font-weight: 600;
        }
        .game-name {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }
        .dropdown-demo {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }
        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }
        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }
        .log-demo {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success {
            background: rgba(72, 187, 120, 0.2);
        }
        .log-error {
            background: rgba(245, 101, 101, 0.2);
        }
        .log-info {
            background: rgba(66, 153, 225, 0.2);
        }
        .config-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .config-item {
            margin-bottom: 15px;
        }
        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        .config-item select,
        .config-item textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 管理控制台功能增强演示</h1>
        
        <!-- 发送端状态监控演示 -->
        <div class="section">
            <h2>📊 发送端状态监控 - 按IP分类显示</h2>
            
            <div class="ip-group">
                <div class="ip-group-header">
                    <h3 class="ip-group-title">📍 *************</h3>
                    <span class="ip-group-count">2 设备</span>
                </div>
                <div class="device-grid">
                    <div class="device-card">
                        <h3>🎮 gamev-8cd7c032</h3>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value"><span class="game-name">ocean3</span></span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">分辨率:</span>
                            <span class="device-info-value">1920x1080</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">观看者:</span>
                            <span class="device-info-value">3</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">最近上线:</span>
                            <span class="device-info-value">当前在线</span>
                        </div>
                    </div>
                    
                    <div class="device-card">
                        <h3>🎮 gamev-9de8f143</h3>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-offline">● 离线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value"><span class="game-name">mygame</span></span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">分辨率:</span>
                            <span class="device-info-value">1280x720</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">观看者:</span>
                            <span class="device-info-value">0</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">最近上线:</span>
                            <span class="device-info-value">15分钟前</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="ip-group">
                <div class="ip-group-header">
                    <h3 class="ip-group-title">📍 *************</h3>
                    <span class="ip-group-count">1 设备</span>
                </div>
                <div class="device-grid">
                    <div class="device-card">
                        <h3>🎮 gamev-abc12345</h3>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value">无</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">分辨率:</span>
                            <span class="device-info-value">1920x1080</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">观看者:</span>
                            <span class="device-info-value">1</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">最近上线:</span>
                            <span class="device-info-value">当前在线</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3栏功能菜单演示 -->
        <div class="section">
            <h2>⚙️ 功能菜单 - 3栏布局</h2>
            <div class="dropdown-demo">
                <div class="dropdown-section">
                    <h4>服务控制</h4>
                    <button class="dropdown-btn">🚀 启动服务</button>
                    <button class="dropdown-btn">⏹️ 停止服务</button>
                    <button class="dropdown-btn">🔄 重启服务</button>
                    
                    <h4>视频控制</h4>
                    <button class="dropdown-btn">🎥 视频参数设置</button>
                    <button class="dropdown-btn">📸 视频流截屏</button>
                </div>

                <div class="dropdown-section">
                    <h4>游戏控制</h4>
                    <button class="dropdown-btn">🎮 游戏设置</button>
                    
                    <h4>日志管理</h4>
                    <button class="dropdown-btn">📝 开启日志显示</button>
                    <button class="dropdown-btn">🚫 关闭日志显示</button>
                    <button class="dropdown-btn">📥 下载日志(FTP)</button>
                </div>

                <div class="dropdown-section">
                    <h4>网络配置</h4>
                    <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                    <button class="dropdown-btn">📡 发送网络配置</button>
                    
                    <h4>系统控制</h4>
                    <button class="dropdown-btn">🔄 重启设备</button>
                    <button class="dropdown-btn">📦 升级应用</button>
                </div>
            </div>
        </div>

        <!-- STUN/TURN配置演示 -->
        <div class="section">
            <h2>🌐 STUN/TURN服务器配置</h2>
            <div class="config-demo">
                <div class="config-item">
                    <label>常用STUN服务器配置</label>
                    <select>
                        <option value="">选择预设配置</option>
                        <option value="google">Google STUN服务器</option>
                        <option value="mozilla">Mozilla STUN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>
                
                <div class="config-item">
                    <label>STUN服务器 (每行一个)</label>
                    <textarea rows="3">stun:stun.l.google.com:19302
stun:stun1.l.google.com:19302
stun:stun2.l.google.com:19302</textarea>
                </div>

                <div class="config-item">
                    <label>常用TURN服务器配置</label>
                    <select>
                        <option value="">选择预设配置</option>
                        <option value="numb">Numb TURN服务器</option>
                        <option value="xirsys">Xirsys TURN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>

                <div class="config-item">
                    <label>TURN服务器配置 (JSON格式)</label>
                    <textarea rows="4">[{
  "urls": "turn:numb.viagenie.ca",
  "username": "<EMAIL>",
  "credential": "muazkh"
}]</textarea>
                </div>

                <button class="btn">保存配置</button>
                <button class="btn">保存并广播</button>
            </div>
        </div>

        <!-- 操作日志演示 -->
        <div class="section">
            <h2>📝 操作日志 - 发送端执行消息</h2>
            <div class="log-demo">
                <div class="log-entry log-info">[14:32:15] [系统] 管理控制台已加载</div>
                <div class="log-entry log-success">[14:32:20] [命令响应] gamev-8cd7c032: start_service - 服务启动成功</div>
                <div class="log-entry log-info">[14:32:25] [设备状态] gamev-8cd7c032: [upgrade] download_started (30%) - 开始下载APK</div>
                <div class="log-entry log-info">[14:32:30] [设备状态] gamev-8cd7c032: [upgrade] ftp_connecting (35%) - 连接FTP服务器...</div>
                <div class="log-entry log-success">[14:32:35] [设备状态] gamev-8cd7c032: [upgrade] ftp_connected (40%) - FTP连接成功，开始下载...</div>
                <div class="log-entry log-info">[14:32:45] [设备状态] gamev-8cd7c032: [upgrade] downloading (60%) - APK下载中...</div>
                <div class="log-entry log-success">[14:33:00] [设备状态] gamev-8cd7c032: [upgrade] download_completed (70%) - FTP下载完成</div>
                <div class="log-entry log-success">[14:33:05] [设备状态] gamev-8cd7c032: [upgrade] download_verified (80%) - APK验证通过，准备安装</div>
                <div class="log-entry log-success">[14:33:15] [截屏结果] gamev-9de8f143: 截屏操作完成</div>
                <div class="log-entry log-error">[14:33:20] [命令响应] gamev-abc12345: reboot_device - 重启失败：权限不足</div>
                <div class="log-entry log-info">[14:33:25] [配置] STUN/TURN配置已更新并广播到3个设备</div>
            </div>
        </div>

        <!-- FTP升级演示 -->
        <div class="section">
            <h2>📦 FTP升级支持</h2>
            <div class="demo-card">
                <h3>支持的下载协议</h3>
                <ul>
                    <li><strong>HTTP/HTTPS</strong>: http://example.com/app.apk</li>
                    <li><strong>FTP</strong>: ftp://username:<EMAIL>:21/path/app.apk</li>
                </ul>
                
                <h3>FTP下载流程</h3>
                <ol>
                    <li>自动解析FTP URL参数（主机、端口、用户名、密码、路径）</li>
                    <li>建立FTP连接并登录</li>
                    <li>设置二进制传输模式</li>
                    <li>下载文件到本地存储</li>
                    <li>验证APK文件完整性</li>
                    <li>执行安装流程</li>
                </ol>
                
                <h3>示例FTP地址</h3>
                <code>ftp://39.96.165.173/webrtc_sender_builds/webrtc_sender.apk</code>
            </div>
        </div>
    </div>
</body>
</html>
