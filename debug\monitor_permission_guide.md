# 监控权限控制功能说明

## 功能概述

实现了基于密码的监控权限控制，不同密码只能查看来自不同服务器域名的设备，并在监控页面显示房间名信息。

## 主要功能

### 1. 权限控制
- **管理员密码**: `tb###` - 可以查看所有设备
- **域名密码**: 不同密码只能查看特定服务器域名的设备
- **权限隔离**: 确保不同用户只能看到授权范围内的设备

### 2. 房间信息显示
- 在监控页面显示设备的房间名称
- 显示设备所属的服务器域名
- 按权限过滤显示内容

## 数据库字段

### fa_sender_device_info 表新增字段
```sql
monitor_password VARCHAR(100) DEFAULT '' COMMENT '监控密码，用于权限控制'
```

## 权限验证逻辑

### 密码类型
1. **管理员密码** (`tb###`)
   - 返回 `['*']` 表示可访问所有域名
   - 可以查看所有设备

2. **域名密码** (自定义)
   - 查询数据库中匹配该密码的设备
   - 返回这些设备所属的服务器域名列表
   - 只能查看对应域名的设备

### 验证流程
```
用户输入密码 → 验证密码 → 获取允许访问的域名列表 → 过滤设备列表 → 显示结果
```

## 配置示例

### 设置监控密码
```sql
-- 为测试服务器的设备设置密码
UPDATE fa_sender_device_info
SET monitor_password = 'test6743'
WHERE room_server_domain = 'http://testva2.91jdcd.com';

-- 为银梦科技服务器的设备设置密码
UPDATE fa_sender_device_info
SET monitor_password = 'yinmeng3623'
WHERE room_server_domain = 'http://bsth5.yinmengkj.cn';

-- 为游戏服务器的设备设置密码
UPDATE fa_sender_device_info
SET monitor_password = 'game8754'
WHERE room_server_domain = 'http://yx.yhdyc.com';
```

## 使用方法

### 1. 管理员访问
- 密码: `tb###`
- 权限: 查看所有设备
- 用途: 系统管理员全局监控

### 2. 域名管理员访问
- 密码: `test6743` (测试服务器)
- 权限: 只能查看 `http://testva2.91jdcd.com` 的设备
- 用途: 特定服务器的设备监控

### 3. 监控页面显示
- 设备基本信息（CPU ID、发送端ID、游戏等）
- **新增**: 房间名称和服务器域名
- 在线状态和运行时间
- 设备硬件信息

## 界面变化

### 设备卡片新增内容
```html
🏠 房间: 水浒传_DF14 | 🌐 服务器: http://testva2.91jdcd.com
```

### 权限提示
- 认证成功后显示允许访问的域名
- 只显示有权限查看的设备
- 无权限设备自动过滤

## 安全特性

1. **密码验证**: 每次连接都需要验证密码
2. **权限隔离**: 不同密码看到不同的设备列表
3. **实时过滤**: 设备更新时自动按权限过滤
4. **连接管理**: 无效密码自动断开连接

## 测试方法

### 1. 设置测试数据
```bash
# 方法1：分步执行（推荐）
mysql -u root -p fa_game < debug/add_monitor_password_field.sql
mysql -u root -p fa_game < debug/update_monitor_passwords.sql

# 方法2：一次执行（可能有语法警告）
mysql -u root -p fa_game < debug/set_monitor_passwords_safe.sql
```

### 2. 测试不同密码
- 访问: `http://your-server:28080/boot_monitor.html`
- 输入密码: `tb###` (查看所有设备)
- 输入密码: `test6743` (只看测试服务器设备)
- 输入密码: `yinmeng3623` (只看银梦科技服务器设备)
- 输入密码: `game8754` (只看游戏服务器设备)

### 3. 验证权限
- 确认不同密码看到不同的设备列表
- 确认房间名正确显示
- 确认服务器域名正确显示

## 版本信息

- **当前版本**: 1.0.7
- **更新内容**: 
  - 新增监控权限控制
  - 新增房间名显示
  - 新增基于密码的设备过滤
- **更新时间**: 2025-08-28

## 注意事项

1. **密码安全**: 建议使用复杂密码
2. **权限分配**: 合理分配不同用户的访问权限
3. **数据同步**: 确保房间信息已正确更新到设备表
4. **性能考虑**: 大量设备时权限过滤可能影响性能

## 故障排除

### 常见问题
1. **密码错误**: 检查数据库中的 `monitor_password` 字段
2. **无设备显示**: 检查 `room_server_domain` 字段是否正确
3. **房间名不显示**: 确认房间信息已更新到数据库
4. **权限异常**: 检查密码验证逻辑和数据库连接
