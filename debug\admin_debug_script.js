// Admin.html 下拉菜单调试脚本
// 在浏览器控制台中运行此脚本来诊断问题

console.log('🔧 开始下拉菜单调试...');

// 1. 检查页面中的下拉菜单元素
function checkDropdownElements() {
    console.log('\n=== 检查下拉菜单元素 ===');
    
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    const toggles = document.querySelectorAll('.dropdown-toggle');
    
    console.log(`找到 ${dropdowns.length} 个下拉菜单`);
    console.log(`找到 ${toggles.length} 个下拉按钮`);
    
    dropdowns.forEach((dropdown, index) => {
        console.log(`菜单 ${index + 1}:`, {
            id: dropdown.id,
            display: getComputedStyle(dropdown).display,
            position: getComputedStyle(dropdown).position,
            zIndex: getComputedStyle(dropdown).zIndex,
            width: dropdown.offsetWidth,
            height: dropdown.offsetHeight
        });
    });
    
    return { dropdowns, toggles };
}

// 2. 检查CSS样式冲突
function checkCSSConflicts() {
    console.log('\n=== 检查CSS样式冲突 ===');
    
    const dropdown = document.querySelector('.dropdown-menu');
    if (!dropdown) {
        console.error('未找到下拉菜单元素');
        return;
    }
    
    const computedStyle = getComputedStyle(dropdown);
    const relevantStyles = {
        position: computedStyle.position,
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        transform: computedStyle.transform,
        transition: computedStyle.transition,
        zIndex: computedStyle.zIndex,
        left: computedStyle.left,
        top: computedStyle.top,
        width: computedStyle.width,
        height: computedStyle.height,
        minWidth: computedStyle.minWidth,
        maxWidth: computedStyle.maxWidth
    };
    
    console.log('下拉菜单计算样式:', relevantStyles);
    
    // 检查是否有transform或transition干扰
    if (relevantStyles.transform !== 'none') {
        console.warn('⚠️ 发现transform样式:', relevantStyles.transform);
    }
    if (relevantStyles.transition !== 'none') {
        console.warn('⚠️ 发现transition样式:', relevantStyles.transition);
    }
}

// 3. 测试位置计算
function testPositionCalculation() {
    console.log('\n=== 测试位置计算 ===');
    
    const dropdown = document.querySelector('.dropdown-menu');
    const toggle = document.querySelector('.dropdown-toggle');
    
    if (!dropdown || !toggle) {
        console.error('未找到必要的元素');
        return;
    }
    
    console.log('开始位置计算测试...');
    
    // 模拟显示过程
    dropdown.style.visibility = 'hidden';
    dropdown.style.opacity = '0';
    dropdown.style.display = 'block';
    
    // 强制重新渲染
    dropdown.offsetHeight;
    
    const buttonRect = toggle.getBoundingClientRect();
    const dropdownRect = dropdown.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    
    console.log('尺寸信息:', {
        button: {
            left: buttonRect.left,
            top: buttonRect.top,
            bottom: buttonRect.bottom,
            right: buttonRect.right,
            width: buttonRect.width,
            height: buttonRect.height
        },
        dropdown: {
            width: dropdownRect.width,
            height: dropdownRect.height
        },
        window: {
            width: windowWidth,
            height: windowHeight
        }
    });
    
    // 计算位置
    let left = buttonRect.left;
    let top = buttonRect.bottom + 10;
    
    console.log('初始位置:', { left, top });
    
    // 边界检查
    if (left + dropdownRect.width > windowWidth - 20) {
        left = buttonRect.right - dropdownRect.width;
        console.log('调整右边界，新left:', left);
    }
    if (left < 20) {
        left = 20;
        console.log('调整左边界，新left:', left);
    }
    if (top + dropdownRect.height > windowHeight - 20) {
        top = buttonRect.top - dropdownRect.height - 10;
        console.log('调整下边界，新top:', top);
    }
    if (top < 20) {
        top = 20;
        console.log('调整上边界，新top:', top);
    }
    
    console.log('最终位置:', { left, top });
    
    // 应用位置
    dropdown.style.left = `${left}px`;
    dropdown.style.top = `${top}px`;
    dropdown.style.visibility = 'visible';
    dropdown.style.opacity = '1';
    
    console.log('位置已应用，菜单应该可见');
    
    // 恢复隐藏状态
    setTimeout(() => {
        dropdown.style.display = 'none';
        dropdown.style.removeProperty('left');
        dropdown.style.removeProperty('top');
        dropdown.style.removeProperty('visibility');
        dropdown.style.removeProperty('opacity');
        console.log('测试完成，已恢复隐藏状态');
    }, 3000);
}

// 4. 检查JavaScript错误
function checkJavaScriptErrors() {
    console.log('\n=== 检查JavaScript状态 ===');
    
    // 检查admin对象是否存在
    if (typeof admin !== 'undefined') {
        console.log('✅ admin对象存在');
        
        // 检查关键方法
        const methods = ['toggleDropdown', 'adjustDropdownPosition', 'executeToggleDropdown'];
        methods.forEach(method => {
            if (typeof admin[method] === 'function') {
                console.log(`✅ admin.${method} 方法存在`);
            } else {
                console.error(`❌ admin.${method} 方法不存在`);
            }
        });
    } else {
        console.error('❌ admin对象不存在');
    }
    
    // 检查事件监听器
    const toggles = document.querySelectorAll('.dropdown-toggle');
    toggles.forEach((toggle, index) => {
        const onclick = toggle.getAttribute('onclick');
        console.log(`按钮 ${index + 1} onclick:`, onclick);
    });
}

// 5. 修复函数
function applyQuickFix() {
    console.log('\n=== 应用快速修复 ===');
    
    // 移除可能干扰的CSS
    const style = document.createElement('style');
    style.textContent = `
        .dropdown-menu {
            position: fixed !important;
            display: none !important;
            z-index: 999999 !important;
            transform: none !important;
            transition: none !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .dropdown-menu.show {
            display: block !important;
        }
        
        .dropdown-toggle.active {
            transform: none !important;
        }
    `;
    document.head.appendChild(style);
    
    console.log('✅ 快速修复CSS已应用');
    
    // 重写位置计算函数
    if (typeof admin !== 'undefined') {
        admin.adjustDropdownPosition = function(dropdown, button) {
            if (dropdown.hasAttribute('data-positioned')) {
                return;
            }
            
            console.log('🔧 使用修复版位置计算');
            
            dropdown.style.visibility = 'hidden';
            dropdown.style.opacity = '0';
            dropdown.style.display = 'block';
            dropdown.offsetHeight;
            
            const buttonRect = button.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            let left = buttonRect.left;
            let top = buttonRect.bottom + 10;
            
            if (left + dropdownRect.width > windowWidth - 20) {
                left = buttonRect.right - dropdownRect.width;
            }
            if (left < 20) {
                left = 20;
            }
            if (top + dropdownRect.height > windowHeight - 20) {
                top = buttonRect.top - dropdownRect.height - 10;
            }
            if (top < 20) {
                top = 20;
            }
            
            dropdown.style.left = `${left}px`;
            dropdown.style.top = `${top}px`;
            dropdown.style.visibility = 'visible';
            dropdown.style.opacity = '1';
            dropdown.setAttribute('data-positioned', 'true');
            
            console.log('✅ 位置计算完成:', { left, top });
        };
        
        console.log('✅ 位置计算函数已修复');
    }
}

// 6. 运行完整诊断
function runFullDiagnosis() {
    console.log('🚀 开始完整诊断...');
    
    checkDropdownElements();
    checkCSSConflicts();
    checkJavaScriptErrors();
    
    console.log('\n=== 诊断完成 ===');
    console.log('如果发现问题，可以运行 applyQuickFix() 来应用快速修复');
    console.log('然后运行 testPositionCalculation() 来测试修复效果');
}

// 导出函数到全局
window.debugDropdown = {
    checkElements: checkDropdownElements,
    checkCSS: checkCSSConflicts,
    testPosition: testPositionCalculation,
    checkJS: checkJavaScriptErrors,
    quickFix: applyQuickFix,
    fullDiagnosis: runFullDiagnosis
};

console.log('🎯 调试脚本已加载！');
console.log('运行 debugDropdown.fullDiagnosis() 开始诊断');
console.log('运行 debugDropdown.quickFix() 应用快速修复');
console.log('运行 debugDropdown.testPosition() 测试位置计算');

// 自动运行诊断
runFullDiagnosis();
