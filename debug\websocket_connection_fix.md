# WebSocket 连接问题修复

## 问题描述

### 1. WebSocket连接失败
```
WebSocket connection to 'ws://************:28080/' failed
WebSocket连接已关闭: 1006
```

### 2. 用户体验问题
- 密码框输入后没有隐藏
- 下次登录需要重新输入密码

## 问题分析

### 1. 端口配置错误

**问题**：WebSocket尝试连接HTTP端口28080
```javascript
const wsUrl = `${protocol}//${window.location.host}`;  // 使用HTTP端口
```

**原因**：
- HTTP服务器运行在端口28080
- WebSocket服务器运行在端口28765
- 两者是不同的服务

### 2. 认证流程问题

**问题**：认证成功后界面处理不正确
- 登录界面没有在正确时机隐藏
- 保存的密码没有自动登录

## 修复方案

### 1. 修复WebSocket端口

**修复前**：
```javascript
const wsUrl = `${protocol}//${window.location.host}`;  // 错误：使用HTTP端口
```

**修复后**：
```javascript
const hostname = window.location.hostname;
const wsPort = '28765'; // WebSocket专用端口
const wsUrl = `${protocol}//${hostname}:${wsPort}`;
```

### 2. 修复自动登录逻辑

**修复前**：
```javascript
if (savedPassword) {
    password = savedPassword;
    document.getElementById('passwordInput').value = savedPassword;
    authenticate(); // 仍然显示登录界面
}
```

**修复后**：
```javascript
if (savedPassword) {
    password = savedPassword;
    document.getElementById('passwordInput').value = savedPassword;
    // 直接隐藏登录界面并连接
    document.getElementById('loginOverlay').style.display = 'none';
    connectWebSocket();
}
```

### 3. 改进认证结果处理

**修复前**：
```javascript
case 'auth_result':
    if (!data.success) {
        // 只处理失败情况
    }
    break;
```

**修复后**：
```javascript
case 'auth_result':
    if (data.success) {
        console.log('认证成功');
        // 隐藏登录界面
        document.getElementById('loginOverlay').style.display = 'none';
        updateConnectionStatus('connected', '已连接');
    } else {
        console.log('认证失败:', data.message);
        showLoginError(data.message || '认证失败');
        document.getElementById('loginOverlay').style.display = 'flex';
        localStorage.removeItem('monitor_password');
        if (websocket) {
            websocket.close();
        }
    }
    break;
```

### 4. 改进错误处理

**新增错误码1006处理**：
```javascript
} else if (event.code === 1006) {
    // 连接失败，可能是网络问题或服务器未启动
    console.log('连接失败，可能是网络问题或服务器未启动');
    updateConnectionStatus('disconnected', '连接失败，请检查网络');
    scheduleReconnect();
}
```

## 服务器端口说明

### 端口分配

| 服务 | 端口 | 协议 | 用途 |
|------|------|------|------|
| HTTP服务器 | 28080 | HTTP/HTTPS | 静态文件、API接口 |
| WebSocket服务器 | 28765 | WebSocket | 实时通信、信令 |

### 启动命令

```bash
# 启动服务器（同时启动HTTP和WebSocket服务）
python3 enhanced_signaling_server.py --ws-port 28765 --http-port 28080
```

### 访问方式

```
# 访问监控页面
http://your-server:28080/boot_monitor.html

# WebSocket连接
ws://your-server:28765/
```

## 用户体验改进

### 1. 自动登录流程

```
页面加载 → 检查localStorage → 有保存密码？
    ↓                              ↓
直接连接WebSocket ← ← ← ← ← ← ← ← ← YES
    ↓
发送认证请求 → 认证成功 → 隐藏登录界面 → 显示设备列表
```

### 2. 首次登录流程

```
页面加载 → 检查localStorage → 无保存密码
    ↓
显示登录界面 → 用户输入密码 → 点击登录 → 保存密码
    ↓
连接WebSocket → 发送认证请求 → 认证成功 → 隐藏登录界面
```

### 3. 认证失败处理

```
认证失败 → 显示错误信息 → 清除保存密码 → 显示登录界面
```

## 连接状态管理

### 状态指示器

| 状态 | 颜色 | 说明 |
|------|------|------|
| 未连接 | 🔴 红色 | 初始状态或连接断开 |
| 正在连接 | 🟡 黄色 | WebSocket连接中 |
| 已连接 | 🟢 绿色 | 认证成功，正常工作 |

### 错误码说明

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 1006 | 连接异常关闭 | 自动重连 |
| 1008 | 认证失败 | 显示登录界面 |
| 其他 | 网络或服务器问题 | 自动重连 |

## 测试方法

### 1. 连接测试

```bash
# 测试WebSocket连接
wscat -c ws://your-server:28765

# 发送认证消息
{"type":"subscribe_boot_monitor","password":"tb###"}
```

### 2. 功能测试

1. **首次访问**：
   - 显示登录界面
   - 输入密码后隐藏界面
   - 显示设备列表

2. **再次访问**：
   - 直接显示设备列表
   - 无需输入密码

3. **错误密码**：
   - 显示错误信息
   - 清除保存的密码
   - 要求重新输入

### 3. 网络测试

1. **服务器未启动**：
   - 显示连接失败
   - 自动重连

2. **网络中断**：
   - 检测到断开
   - 自动重连

## 故障排除

### 1. 连接失败

**检查项目**：
- 服务器是否启动
- 端口28765是否开放
- 防火墙设置
- 网络连接

**解决方法**：
```bash
# 检查端口
netstat -an | grep 28765

# 检查防火墙
sudo ufw status

# 测试连接
telnet your-server 28765
```

### 2. 认证失败

**检查项目**：
- 密码是否正确（默认：tb###）
- 服务器端密码配置
- 浏览器控制台错误

**解决方法**：
```python
# 检查服务器端密码配置
BOOT_MONITOR_PASSWORD = "tb###"
```

### 3. 界面问题

**检查项目**：
- 浏览器控制台错误
- localStorage存储
- CSS样式加载

**解决方法**：
```javascript
// 清除保存的密码
localStorage.removeItem('monitor_password');

// 强制刷新页面
location.reload();
```

## 版本更新

当前版本：v1.26 → v1.27

主要修复：
- 修复WebSocket端口配置错误
- 改进自动登录逻辑
- 完善认证结果处理
- 增强错误处理和用户体验
- 添加详细的连接状态管理
