<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin下拉菜单修复版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible; /* 允许下拉菜单超出容器边界 */
            min-height: 100vh; /* 使用最小高度而不是固定高度 */
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 15px 15px 0 0;
        }

        .main-content {
            padding: 30px;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .device-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
        }

        .device-info {
            margin-bottom: 15px;
        }

        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }

        .device-info-value {
            color: #495057;
            font-weight: 600;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .device-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .control-main-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            transition: all 0.2s;
        }

        .control-dropdown {
            position: relative;
            z-index: 1000;
        }

        .dropdown-toggle {
            position: relative;
            z-index: 100000;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .dropdown-toggle:hover:not(.active) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .dropdown-toggle.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transform: none !important;
        }

        .dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw;
            z-index: 999999;
            display: none;
            padding: 15px;
            /* 移除可能导致冲突的transform和transition */
        }

        .dropdown-menu.show {
            display: block !important;
        }

        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }

        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }

        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000000;
            max-width: 300px;
            font-family: monospace;
        }

        @media (max-width: 768px) {
            .dropdown-menu {
                min-width: 250px;
                right: auto;
            }
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debugPanel">
        <strong>🔧 Admin修复版测试</strong><br>
        <div id="debugInfo">等待操作...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 Admin下拉菜单修复版</h1>
            <p>基于简化版的成功逻辑修复</p>
        </div>

        <div class="main-content">
            <h2>📊 模拟设备列表</h2>
            
            <div class="devices-grid">
                <!-- 设备1 -->
                <div class="device-card">
                    <div class="device-info">
                        <div class="device-info-item">
                            <span class="device-info-label">设备ID:</span>
                            <span class="device-info-value">gamev-fixed001</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value">ocean3</span>
                        </div>
                    </div>
                    
                    <div class="device-controls">
                        <button class="control-main-btn">
                            🔄 重启服务
                        </button>
                        <div class="control-dropdown">
                            <button class="dropdown-toggle" onclick="toggleDropdown('fixed001')">
                                ⚙️
                            </button>
                            <div class="dropdown-menu" id="dropdown-fixed001">
                                <div class="dropdown-columns">
                                    <div class="dropdown-section">
                                        <h4>服务控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('启动服务')">🚀 启动服务</button>
                                        <button class="dropdown-btn" onclick="testAction('停止服务')">⏹️ 停止服务</button>
                                        <button class="dropdown-btn" onclick="testAction('重启服务')">🔄 重启服务</button>
                                        <h4>视频控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('视频参数设置')">🎥 视频参数设置</button>
                                        <button class="dropdown-btn" onclick="testAction('视频流截屏')">📸 视频流截屏</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>游戏控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('游戏设置')">🎮 游戏设置</button>
                                        <h4>日志管理</h4>
                                        <button class="dropdown-btn" onclick="testAction('开启日志显示')">📝 开启日志显示</button>
                                        <button class="dropdown-btn" onclick="testAction('关闭日志显示')">🚫 关闭日志显示</button>
                                        <button class="dropdown-btn" onclick="testAction('下载日志')">📥 下载日志(FTP)</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>网络配置</h4>
                                        <button class="dropdown-btn" onclick="testAction('STUN/TURN配置')">🌐 STUN/TURN配置</button>
                                        <button class="dropdown-btn" onclick="testAction('发送网络配置')">📡 发送网络配置</button>
                                        <h4>系统控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('重启设备')">🔄 重启设备</button>
                                        <button class="dropdown-btn" onclick="testAction('升级应用')">📦 升级应用</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备2 -->
                <div class="device-card">
                    <div class="device-info">
                        <div class="device-info-item">
                            <span class="device-info-label">设备ID:</span>
                            <span class="device-info-value">gamev-fixed002</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value">mygame</span>
                        </div>
                    </div>
                    
                    <div class="device-controls">
                        <button class="control-main-btn">
                            🔄 重启服务
                        </button>
                        <div class="control-dropdown">
                            <button class="dropdown-toggle" onclick="toggleDropdown('fixed002')">
                                ⚙️
                            </button>
                            <div class="dropdown-menu" id="dropdown-fixed002">
                                <div class="dropdown-columns">
                                    <div class="dropdown-section">
                                        <h4>服务控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('启动服务')">🚀 启动服务</button>
                                        <button class="dropdown-btn" onclick="testAction('停止服务')">⏹️ 停止服务</button>
                                        <button class="dropdown-btn" onclick="testAction('重启服务')">🔄 重启服务</button>
                                        <h4>视频控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('视频参数设置')">🎥 视频参数设置</button>
                                        <button class="dropdown-btn" onclick="testAction('视频流截屏')">📸 视频流截屏</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>游戏控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('游戏设置')">🎮 游戏设置</button>
                                        <h4>日志管理</h4>
                                        <button class="dropdown-btn" onclick="testAction('开启日志显示')">📝 开启日志显示</button>
                                        <button class="dropdown-btn" onclick="testAction('关闭日志显示')">🚫 关闭日志显示</button>
                                        <button class="dropdown-btn" onclick="testAction('下载日志')">📥 下载日志(FTP)</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>网络配置</h4>
                                        <button class="dropdown-btn" onclick="testAction('STUN/TURN配置')">🌐 STUN/TURN配置</button>
                                        <button class="dropdown-btn" onclick="testAction('发送网络配置')">📡 发送网络配置</button>
                                        <h4>系统控制</h4>
                                        <button class="dropdown-btn" onclick="testAction('重启设备')">🔄 重启设备</button>
                                        <button class="dropdown-btn" onclick="testAction('升级应用')">📦 升级应用</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let dropdownTimeout = null;
        let positionCalculations = 0;
        
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>最新操作:</strong> ${message}<br>
                <strong>位置计算:</strong> ${positionCalculations}<br>
                <small>时间: ${new Date().toLocaleTimeString()}</small>
            `;
        }

        function toggleDropdown(deviceId) {
            updateDebugInfo(`点击设备: ${deviceId}`);
            
            // 防抖机制
            if (dropdownTimeout) {
                clearTimeout(dropdownTimeout);
            }
            
            dropdownTimeout = setTimeout(() => {
                executeToggleDropdown(deviceId);
            }, 50);
        }
        
        function executeToggleDropdown(deviceId) {
            console.log('执行切换:', deviceId);
            
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${deviceId}`) {
                    menu.classList.remove('show');
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                    menu.style.removeProperty('opacity');
                    menu.style.removeProperty('display');
                    menu.removeAttribute('data-positioned');
                }
            });
            document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                if (!btn.onclick.toString().includes(deviceId)) {
                    btn.classList.remove('active');
                }
            });

            const dropdown = document.getElementById(`dropdown-${deviceId}`);
            const button = document.querySelector(`[onclick="toggleDropdown('${deviceId}')"]`);
            const isShowing = dropdown.classList.contains('show');

            if (isShowing) {
                // 隐藏
                dropdown.classList.remove('show');
                button.classList.remove('active');
                dropdown.removeAttribute('data-positioned');
                
                dropdown.style.removeProperty('left');
                dropdown.style.removeProperty('top');
                dropdown.style.removeProperty('visibility');
                dropdown.style.removeProperty('opacity');
                dropdown.style.removeProperty('display');
                updateDebugInfo(`隐藏菜单: ${deviceId}`);
            } else {
                // 显示 - 使用简化版的成功逻辑
                dropdown.removeAttribute('data-positioned');
                adjustDropdownPosition(dropdown, button);
                dropdown.classList.add('show');
                button.classList.add('active');
                updateDebugInfo(`显示菜单: ${deviceId}`);
            }
        }

        function adjustDropdownPosition(dropdown, button) {
            if (dropdown.hasAttribute('data-positioned')) {
                console.log('已定位，跳过重复计算');
                return;
            }
            
            positionCalculations++;
            console.log('开始位置计算 - 使用简化版逻辑');
            
            // 使用简化版的成功方法
            dropdown.style.visibility = 'hidden';
            dropdown.style.opacity = '0';
            dropdown.style.display = 'block';
            
            // 强制重新渲染
            dropdown.offsetHeight;
            
            const buttonRect = button.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            const menuWidth = dropdownRect.width;
            const menuHeight = dropdownRect.height;

            // 默认位置：按钮下方左对齐
            let left = buttonRect.left;
            let top = buttonRect.bottom + 10;
            
            console.log('初始位置:', { left, top });

            // 边界检查
            if (left + menuWidth > windowWidth - 20) {
                left = buttonRect.right - menuWidth;
                console.log('调整右边界，新left:', left);
            }
            if (left < 20) {
                left = 20;
                console.log('调整左边界，新left:', left);
            }
            if (top + menuHeight > windowHeight - 20) {
                top = buttonRect.top - menuHeight - 10;
                console.log('调整下边界，新top:', top);
            }
            if (top < 20) {
                top = 20;
                console.log('调整上边界，新top:', top);
            }

            console.log('最终位置:', { left, top });

            // 设置位置
            dropdown.style.left = `${left}px`;
            dropdown.style.top = `${top}px`;
            dropdown.style.visibility = 'visible';
            dropdown.style.opacity = '1';
            dropdown.setAttribute('data-positioned', 'true');
            
            updateDebugInfo(`位置计算完成: (${Math.round(left)}, ${Math.round(top)})`);
        }
        
        function testAction(action) {
            updateDebugInfo(`执行功能: ${action}`);
            alert(`执行功能: ${action}`);
        }

        // 点击外部关闭
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                    menu.style.removeProperty('opacity');
                    menu.style.removeProperty('display');
                    menu.removeAttribute('data-positioned');
                });
                document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                    btn.classList.remove('active');
                });
                updateDebugInfo('点击外部，关闭所有菜单');
            }
        });

        updateDebugInfo('页面加载完成 - 基于简化版逻辑');
    </script>
</body>
</html>
