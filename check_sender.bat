@echo off
chcp 65001 > nul
REM 设置控制台代码页为UTF-8
powershell -command "$OutputEncoding = [Console]::OutputEncoding = [Text.Encoding]::UTF8"
echo ===================================
echo    线上线下服务台状态检查工具
echo ===================================
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请安装Python 3.7或更高版本
    goto :end
)

REM 检查依赖
echo 正在检查依赖...
python -c "import websockets" > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖...
    pip install websockets
)

REM 获取发送端ID
set /p SENDER_ID="请输入要检查的发送端ID (例如 sender-30c75756): "
if "%SENDER_ID%"=="" (
    echo 错误: 发送端ID不能为空
    goto :end
)

REM 启动检查工具
echo.
echo 正在检查发送端状态...
echo.
echo 检查信息:
echo - 信令服务器: wss://sling.91jdcd.com/ws/
echo - 目标发送端: %SENDER_ID%
echo.

python check_sender.py --sender "%SENDER_ID%" --signaling wss://sling.91jdcd.com/ws/

:end
pause
