package com.example.webrtcsender.utils

import android.content.Context
import kotlinx.coroutines.*
import okhttp3.*
import org.apache.commons.net.ftp.FTP
import org.apache.commons.net.ftp.FTPClient
import java.io.File
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 上传结果数据类
 */
data class UploadResult(
    val success: Boolean,
    val filename: String = "",
    val message: String = ""
)

/**
 * 日志管理器
 * 负责日志文件管理、上传等功能
 */
object LogManager {
    private const val TAG = "LogManager"
    private const val MAX_LOG_SIZE = 5 * 1024 * 1024 // 5MB
    
    /**
     * 获取最近的日志文件（约5MB）
     */
    fun getRecentLogFiles(context: Context): List<File> {
        val logDir = context.getExternalFilesDir("logs") ?: return emptyList()
        
        if (!logDir.exists()) {
            return emptyList()
        }
        
        // 获取所有日志文件，按修改时间排序
        val logFiles = logDir.listFiles { file ->
            file.isFile && file.name.endsWith(".log")
        }?.sortedByDescending { it.lastModified() } ?: return emptyList()
        
        val selectedFiles = mutableListOf<File>()
        var totalSize = 0L
        
        // 选择最新的日志文件，直到达到5MB限制
        for (file in logFiles) {
            if (totalSize + file.length() <= MAX_LOG_SIZE) {
                selectedFiles.add(file)
                totalSize += file.length()
            } else {
                break
            }
        }
        
        return selectedFiles
    }
    
    /**
     * 上传日志文件到FTP服务器
     */
    suspend fun uploadLogToFtp(context: Context, deviceId: String): UploadResult = withContext(Dispatchers.IO) {
        try {
            val logFiles = getRecentLogFiles(context)
            if (logFiles.isEmpty()) {
                Logger.w(TAG, "没有找到日志文件")
                return@withContext UploadResult(false, "", "没有找到日志文件")
            }
            
            // 创建合并的日志文件
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val mergedLogFile = File(context.cacheDir, "${deviceId}_${timestamp}.log")
            
            // 合并日志文件
            mergedLogFile.outputStream().use { output ->
                for (logFile in logFiles) {
                    output.write("=== ${logFile.name} ===\n".toByteArray())
                    logFile.inputStream().use { input ->
                        input.copyTo(output)
                    }
                    output.write("\n\n".toByteArray())
                }
            }
            
            Logger.i(TAG, "合并日志文件完成: ${mergedLogFile.absolutePath}, 大小: ${mergedLogFile.length()} bytes")
            
            // 上传到FTP
            val ftpClient = FTPClient()
            try {
                ftpClient.connect(Constants.FTP_HOST, Constants.FTP_PORT)
                val loginSuccess = ftpClient.login(Constants.FTP_USERNAME, Constants.FTP_PASSWORD)
                
                if (!loginSuccess) {
                    Logger.e(TAG, "FTP登录失败")
                    return@withContext UploadResult(false, "", "FTP登录失败")
                }
                
                ftpClient.enterLocalPassiveMode()
                ftpClient.setFileType(FTP.BINARY_FILE_TYPE)
                
                // 上传文件
                FileInputStream(mergedLogFile).use { inputStream ->
                    val uploadSuccess = ftpClient.storeFile(mergedLogFile.name, inputStream)
                    if (uploadSuccess) {
                        Logger.i(TAG, "日志文件上传成功: ${mergedLogFile.name}")
                        return@withContext UploadResult(true, mergedLogFile.name, "上传成功")
                    } else {
                        Logger.e(TAG, "日志文件上传失败")
                        return@withContext UploadResult(false, "", "文件上传失败")
                    }
                }
            } finally {
                try {
                    ftpClient.logout()
                    ftpClient.disconnect()
                } catch (e: Exception) {
                    Logger.w(TAG, "关闭FTP连接失败: ${e.message}")
                }
                
                // 清理临时文件
                mergedLogFile.delete()
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "上传日志到FTP失败", e)
            return@withContext UploadResult(false, "", "上传异常: ${e.message}")
        }
    }
    
    /**
     * 上传日志文件到HTTP接口
     */
    suspend fun uploadLogToHttp(context: Context, deviceId: String): UploadResult = withContext(Dispatchers.IO) {
        try {
            val logFiles = getRecentLogFiles(context)
            if (logFiles.isEmpty()) {
                Logger.w(TAG, "没有找到日志文件")
                return@withContext UploadResult(false, "", "没有找到日志文件")
            }
            
            // 创建合并的日志文件
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val mergedLogFile = File(context.cacheDir, "${deviceId}_${timestamp}.log")
            
            // 合并日志文件
            mergedLogFile.outputStream().use { output ->
                for (logFile in logFiles) {
                    output.write("=== ${logFile.name} ===\n".toByteArray())
                    logFile.inputStream().use { input ->
                        input.copyTo(output)
                    }
                    output.write("\n\n".toByteArray())
                }
            }
            
            Logger.i(TAG, "合并日志文件完成: ${mergedLogFile.absolutePath}, 大小: ${mergedLogFile.length()} bytes")
            
            // 创建HTTP请求
            val client = OkHttpClient()
            val requestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(
                    "file",
                    mergedLogFile.name,
                    RequestBody.create(MediaType.parse("application/octet-stream"), mergedLogFile)
                )
                .build()
            
            val request = Request.Builder()
                .url(Constants.UPLOAD_LOG_URL)
                .post(requestBody)
                .build()
            
            val response = client.newCall(request).execute()
            val success = response.isSuccessful

            // 清理临时文件
            mergedLogFile.delete()

            if (success) {
                Logger.i(TAG, "日志文件上传成功: ${mergedLogFile.name}")
                return@withContext UploadResult(true, mergedLogFile.name, "上传成功")
            } else {
                Logger.e(TAG, "日志文件上传失败: ${response.code()}")
                return@withContext UploadResult(false, "", "HTTP上传失败: ${response.code()}")
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "上传日志到HTTP失败", e)
            return@withContext UploadResult(false, "", "上传异常: ${e.message}")
        }
    }
    
    /**
     * 清理旧的日志文件
     */
    fun cleanOldLogFiles(context: Context, maxFiles: Int = 10) {
        try {
            val logDir = context.getExternalFilesDir("logs") ?: return
            
            if (!logDir.exists()) {
                return
            }
            
            val logFiles = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log")
            }?.sortedByDescending { it.lastModified() } ?: return
            
            // 保留最新的maxFiles个文件，删除其余的
            if (logFiles.size > maxFiles) {
                val filesToDelete = logFiles.drop(maxFiles)
                for (file in filesToDelete) {
                    if (file.delete()) {
                        Logger.d(TAG, "删除旧日志文件: ${file.name}")
                    }
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "清理旧日志文件失败", e)
        }
    }
}
