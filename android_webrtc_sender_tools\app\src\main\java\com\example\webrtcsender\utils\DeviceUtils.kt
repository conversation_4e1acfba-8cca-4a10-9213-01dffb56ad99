package com.example.webrtcsender.utils

import android.content.Context
import android.os.Build
import android.provider.Settings
import java.security.MessageDigest

object DeviceUtils {
    
    /**
     * 获取设备的唯一标识符（基于CPU信息和设备信息）
     */
    fun getDeviceId(context: Context): String {
        return try {
            // 获取设备的硬件信息
            val deviceInfo = StringBuilder()
            
            // 添加CPU信息
            deviceInfo.append(Build.HARDWARE)
            deviceInfo.append(Build.BOARD)
            deviceInfo.append(Build.BOOTLOADER)
            deviceInfo.append(Build.DEVICE)
            deviceInfo.append(Build.DISPLAY)
            deviceInfo.append(Build.FINGERPRINT)
            deviceInfo.append(Build.HOST)
            deviceInfo.append(Build.ID)
            deviceInfo.append(Build.MANUFACTURER)
            deviceInfo.append(Build.MODEL)
            deviceInfo.append(Build.PRODUCT)
            deviceInfo.append(Build.SERIAL)
            
            // 尝试获取Android ID作为补充
            try {
                val androidId = Settings.Secure.getString(
                    context.contentResolver,
                    Settings.Secure.ANDROID_ID
                )
                if (!androidId.isNullOrEmpty()) {
                    deviceInfo.append(androidId)
                }
            } catch (e: Exception) {
                Logger.w("DeviceUtils", "无法获取Android ID: ${e.message}")
            }
            
            // 计算MD5哈希
            val md5 = MessageDigest.getInstance("MD5")
            val hashBytes = md5.digest(deviceInfo.toString().toByteArray())
            
            // 转换为十六进制字符串并取后8位
            val hexString = hashBytes.joinToString("") { "%02x".format(it) }
            hexString.takeLast(8)
            
        } catch (e: Exception) {
            Logger.e("DeviceUtils", "生成设备ID失败", e)
            // 如果失败，使用时间戳作为后备方案
            System.currentTimeMillis().toString().takeLast(8)
        }
    }
    
    /**
     * 生成发送端ID
     */
    fun generateSenderId(context: Context): String {
        val deviceId = getDeviceId(context)
        return "gamev-$deviceId"
    }
}
