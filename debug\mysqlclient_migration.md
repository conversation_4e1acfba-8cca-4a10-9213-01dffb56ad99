# MySQL客户端迁移到mysqlclient

## 🎯 迁移概述

将信令服务器的MySQL连接器从`mysql-connector-python`迁移到`mysqlclient`，以提供更好的性能和兼容性。

## 🔧 主要变更

### 1. 导入模块变更
```python
# 修改前
import mysql.connector
from mysql.connector import Error

# 修改后
import MySQLdb
```

### 2. 数据库配置变更
```python
# 修改前
DB_CONFIG = {
    'host': '************',
    'database': 'dsender',
    'user': 'dsender',
    'password': 'dyZp7taLAxCZJGcX',
    'charset': 'utf8mb4',
    'autocommit': True,
    'pool_name': 'signaling_pool',
    'pool_size': 10,
    'pool_reset_session': True
}

# 修改后
DB_CONFIG = {
    'host': '************',
    'db': 'dsender',
    'user': 'dsender',
    'passwd': 'dyZp7taLAxCZJGcX',
    'charset': 'utf8mb4',
    'port': 3306
}
```

### 3. 连接池移除
```python
# 修改前 - 使用连接池
def init_database():
    global db_pool
    try:
        db_pool = mysql.connector.pooling.MySQLConnectionPool(**DB_CONFIG)
        logging.info("数据库连接池初始化成功")
        return True
    except Error as e:
        logging.error(f"数据库连接池初始化失败: {e}")
        return False

def get_db_connection():
    try:
        return db_pool.get_connection()
    except Error as e:
        logging.error(f"获取数据库连接失败: {e}")
        return None

# 修改后 - 直接连接
def init_database():
    try:
        # 测试数据库连接
        connection = MySQLdb.connect(**DB_CONFIG)
        connection.close()
        logging.info("数据库连接测试成功")
        return True
    except Exception as e:
        logging.error(f"数据库连接测试失败: {e}")
        return False

def get_db_connection():
    try:
        return MySQLdb.connect(**DB_CONFIG)
    except Exception as e:
        logging.error(f"获取数据库连接失败: {e}")
        return None
```

### 4. 异常处理变更
```python
# 修改前
except Error as e:
    logging.error(f"数据库操作失败: {e}")

# 修改后
except Exception as e:
    logging.error(f"数据库操作失败: {e}")
```

### 5. 连接检查简化
```python
# 修改前
if not db_pool:
    logging.error("数据库连接池未初始化")
    return False

connection = get_db_connection()
if not connection:
    return False

# 修改后
connection = get_db_connection()
if not connection:
    logging.error("无法获取数据库连接")
    return False
```

## 📊 配置参数对比

| 参数 | mysql-connector-python | mysqlclient |
|------|------------------------|-------------|
| 数据库名 | `database` | `db` |
| 密码 | `password` | `passwd` |
| 端口 | 自动检测 | `port` |
| 字符集 | `charset` | `charset` |
| 连接池 | 支持 | 不支持 |
| 自动提交 | `autocommit` | 手动控制 |

## 🚀 安装和部署

### 1. 安装mysqlclient
```bash
pip install mysqlclient
```

### 2. 卸载旧的连接器（可选）
```bash
pip uninstall mysql-connector-python
```

### 3. 重启信令服务器
```bash
# 停止服务
pkill -f enhanced_signaling_server.py

# 启动服务
python enhanced_signaling_server.py --ws-port 8765 --http-port 28080
```

## 🔍 验证连接

### 1. 检查启动日志
```
数据库连接测试成功
信令服务器启动成功，WebSocket端口: 8765, HTTP端口: 28080
```

### 2. 测试数据库操作
```bash
# 测试设备信息查询
curl http://localhost:28080/api/senders

# 测试设备信息更新
# 连接Android应用，观察日志
```

### 3. 监控数据库连接
```sql
-- 查看当前连接
SHOW PROCESSLIST;

-- 查看连接状态
SHOW STATUS LIKE 'Connections';
```

## ⚠️ 注意事项

### 1. 性能差异
- **mysqlclient**: 基于C扩展，性能更好
- **mysql-connector-python**: 纯Python实现，功能更丰富

### 2. 连接管理
- **mysqlclient**: 每次操作创建新连接，适合低并发
- **mysql-connector-python**: 支持连接池，适合高并发

### 3. 兼容性
- **mysqlclient**: 需要MySQL客户端库
- **mysql-connector-python**: 无额外依赖

### 4. 错误处理
- **mysqlclient**: 使用通用Exception
- **mysql-connector-python**: 使用专门的Error类

## 🔧 故障排除

### 1. 安装问题
```bash
# Ubuntu/Debian
sudo apt-get install python3-dev default-libmysqlclient-dev build-essential

# CentOS/RHEL
sudo yum install python3-devel mysql-devel gcc

# Windows
# 需要安装Microsoft C++ Build Tools
```

### 2. 连接问题
```python
# 测试连接
import MySQLdb

try:
    conn = MySQLdb.connect(
        host='************',
        db='dsender',
        user='dsender',
        passwd='dyZp7taLAxCZJGcX',
        charset='utf8mb4',
        port=3306
    )
    print("连接成功")
    conn.close()
except Exception as e:
    print(f"连接失败: {e}")
```

### 3. 字符集问题
```python
# 确保使用utf8mb4字符集
DB_CONFIG = {
    'charset': 'utf8mb4',
    # 其他配置...
}
```

## 🎯 预期效果

迁移完成后：
- ✅ 更好的数据库连接性能
- ✅ 更简单的配置管理
- ✅ 更稳定的连接处理
- ✅ 兼容现有的数据库操作
- ✅ 正常的设备信息存储和查询

现在信令服务器已成功迁移到mysqlclient！
