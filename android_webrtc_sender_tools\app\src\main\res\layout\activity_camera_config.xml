<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="摄像头配置"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 摄像头选择区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择摄像头:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <Spinner
                android:id="@+id/cameraSpinner"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/refreshButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="刷新"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 摄像头信息显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/rounded_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="摄像头信息:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/cameraInfoText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="请选择摄像头查看详细信息"
                android:textSize="14sp"
                android:lineSpacingExtra="4dp"
                android:fontFamily="monospace" />

        </ScrollView>

    </LinearLayout>

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存配置"
            android:textSize="16sp"
            android:layout_marginEnd="8dp" />

        <Button
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="返回"
            android:textSize="16sp"
            android:layout_marginStart="8dp"
            android:onClick="onBackPressed" />

    </LinearLayout>

</LinearLayout>
