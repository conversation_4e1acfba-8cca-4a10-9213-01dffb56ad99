plugins {
    id 'com.android.application'
    id 'kotlin-android'
}

android {
    compileSdkVersion 33

    lint {
        baseline = file("lint-baseline.xml")
        abortOnError false
        checkReleaseBuilds false
    }

    defaultConfig {
        applicationId "com.example.webrtcsender"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 48
        versionName "1.48"
        
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // Android基础库
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.4.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.1'
    
    // WebRTC库
    implementation 'org.webrtc:google-webrtc:1.0.32006'
    
    // 协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0'
    
    // WebSocket
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'
    
    // JSON处理
    implementation 'com.google.code.gson:gson:2.9.0'
    
    // 权限处理
    implementation 'com.github.permissions-dispatcher:permissionsdispatcher:4.9.2'
    
    // 测试库
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
