#!/usr/bin/env python3
# simple_video_server.py - 简单的HTTP视频流服务器
import cv2
import time
import threading
import argparse
import logging
import socket
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('simple-video-server')

# 解析命令行参数
parser = argparse.ArgumentParser(description="简单的HTTP视频流服务器")
parser.add_argument("--video", help="视频源URL (例如 http://*************:80/0.mp4 或 0 表示摄像头)", required=True)
parser.add_argument("--port", help="服务器端口", type=int, default=8000)
parser.add_argument("--host", help="服务器主机", default="0.0.0.0")
args = parser.parse_args()

# 全局变量
video_source = None
frame_buffer = None
last_frame_time = 0
is_running = False
lock = threading.Lock()

def get_video_source():
    """创建视频源"""
    global video_source
    try:
        # 检查是否是摄像头索引
        if args.video.isdigit():
            source = int(args.video)
        else:
            source = args.video
        
        # 打开视频源
        video_source = cv2.VideoCapture(source)
        
        if not video_source.isOpened():
            logger.error(f"无法打开视频源: {args.video}")
            return False
        
        width = int(video_source.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(video_source.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = video_source.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 30
        
        logger.info(f"视频源: {args.video}, 分辨率: {width}x{height}, FPS: {fps}")
        return True
    except Exception as e:
        logger.error(f"创建视频源失败: {e}")
        return False

def video_capture_thread():
    """视频捕获线程"""
    global frame_buffer, last_frame_time, is_running
    
    logger.info("启动视频捕获线程")
    is_running = True
    
    while is_running:
        try:
            # 读取视频帧
            ret, frame = video_source.read()
            
            if not ret:
                # 如果是视频文件，循环播放
                if not args.video.isdigit():
                    video_source.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    ret, frame = video_source.read()
                    if not ret:
                        logger.error("无法读取视频帧")
                        time.sleep(0.1)
                        continue
                else:
                    logger.error("无法读取视频帧")
                    time.sleep(0.1)
                    continue
            
            # 更新帧缓冲
            with lock:
                # 转换为JPEG
                ret, jpeg = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if ret:
                    frame_buffer = jpeg.tobytes()
                    last_frame_time = time.time()
            
            # 控制帧率
            time.sleep(0.03)  # 约30FPS
            
        except Exception as e:
            logger.error(f"视频捕获错误: {e}")
            time.sleep(0.1)
    
    logger.info("视频捕获线程已停止")

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
    """处理并发请求的HTTP服务器"""
    daemon_threads = True

class VideoHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            # 主页
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            # 获取本地IP
            local_ip = get_local_ip()
            
            # HTML页面
            html = f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>简单视频流服务器</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                    .container {{ max-width: 800px; margin: 0 auto; }}
                    .video-container {{ margin: 20px 0; }}
                    img {{ width: 100%; border: 1px solid #ddd; }}
                    .info {{ background: #f0f0f0; padding: 15px; border-radius: 5px; }}
                    .url {{ font-family: monospace; background: #ddd; padding: 5px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>简单视频流服务器</h1>
                    
                    <div class="video-container">
                        <h2>实时视频</h2>
                        <img src="/video_feed" alt="视频流">
                    </div>
                    
                    <div class="info">
                        <h2>访问信息</h2>
                        <p>视频流URL:</p>
                        <p class="url">http://{local_ip}:{args.port}/video_feed</p>
                        
                        <h3>本地网络访问</h3>
                        <p>在同一网络中的设备可以通过以下URL访问:</p>
                        <p class="url">http://{local_ip}:{args.port}/video_feed</p>
                        
                        <h3>使用说明</h3>
                        <p>1. 在浏览器中直接访问上述URL可以查看视频流</p>
                        <p>2. 在其他应用中使用上述URL作为视频源</p>
                        <p>3. 如需从外部网络访问，请在路由器中设置端口转发</p>
                    </div>
                </div>
                
                <script>
                    // 自动刷新视频
                    setInterval(function() {{
                        const img = document.querySelector('img');
                        img.src = '/video_feed?t=' + new Date().getTime();
                    }}, 30000);
                </script>
            </body>
            </html>
            '''
            
            self.wfile.write(html.encode())
            
        elif self.path.startswith('/video_feed'):
            # 视频流
            self.send_response(200)
            self.send_header('Content-type', 'multipart/x-mixed-replace; boundary=frame')
            self.end_headers()
            
            try:
                while True:
                    # 获取当前帧
                    with lock:
                        if frame_buffer is None:
                            time.sleep(0.1)
                            continue
                        
                        jpeg_data = frame_buffer
                    
                    # 发送MJPEG流的一帧
                    self.wfile.write(b'--frame\r\n')
                    self.wfile.write(b'Content-Type: image/jpeg\r\n')
                    self.wfile.write(f'Content-Length: {len(jpeg_data)}\r\n\r\n'.encode())
                    self.wfile.write(jpeg_data)
                    self.wfile.write(b'\r\n')
                    
                    # 控制帧率
                    time.sleep(0.03)  # 约30FPS
            except Exception as e:
                logger.debug(f"客户端断开连接: {e}")
                
        elif self.path == '/status':
            # 服务器状态
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            # 检查视频源是否正常
            if video_source is None or not video_source.isOpened():
                status = "error"
                message = "视频源未打开"
            elif time.time() - last_frame_time > 5:
                status = "warning"
                message = "视频流可能已冻结"
            else:
                status = "normal"
                message = "视频流正常"
            
            json_data = f'''{{
                "status": "{status}",
                "message": "{message}",
                "video_source": "{args.video}",
                "last_frame_time": {last_frame_time}
            }}'''
            
            self.wfile.write(json_data.encode())
            
        else:
            # 404 Not Found
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'404 Not Found')
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        if self.path != '/video_feed':  # 不记录视频流请求
            logger.info("%s - %s" % (self.address_string(), format % args))

def main():
    """主函数"""
    # 创建视频源
    if not get_video_source():
        return
    
    # 启动视频捕获线程
    capture_thread = threading.Thread(target=video_capture_thread)
    capture_thread.daemon = True
    capture_thread.start()
    
    try:
        # 启动HTTP服务器
        server = ThreadedHTTPServer((args.host, args.port), VideoHandler)
        local_ip = get_local_ip()
        logger.info(f"启动HTTP服务器在 http://{local_ip}:{args.port}")
        logger.info(f"视频流URL: http://{local_ip}:{args.port}/video_feed")
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("程序已终止")
    finally:
        # 停止视频捕获线程
        global is_running
        is_running = False
        capture_thread.join(timeout=1.0)
        
        # 释放视频源
        if video_source is not None:
            video_source.release()

if __name__ == "__main__":
    main()
