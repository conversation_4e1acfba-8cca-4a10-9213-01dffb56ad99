package com.example.webrtcsender.utils

import android.Manifest
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import androidx.core.app.ActivityCompat
import ZtlApi.ZtlManager
import com.example.webrtcsender.BuildConfig
import com.example.webrtcsender.utils.Constants
import org.json.JSONObject
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.NetworkInterface
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 设备信息收集器
 * 用于收集发送端设备的详细信息并上报给信令服务器
 */
class DeviceInfoCollector(private val context: Context) {
    
    companion object {
        private const val TAG = "DeviceInfoCollector"
    }
    
    /**
     * 收集完整的设备信息
     */
    suspend fun collectDeviceInfo(): JSONObject {
        val deviceInfo = JSONObject()
        
        try {
            // CPU唯一ID
            deviceInfo.put("cpu_unique_id", getCpuUniqueId())
            
            // 微信SN（如果有微信SDK集成）
            deviceInfo.put("wechat_sn", getWechatSN())
            
            // 主板型号
            deviceInfo.put("motherboard_model", getMotherboardModel())
            
            // 系统版本信息
            deviceInfo.put("android_version", getAndroidVersion())
            deviceInfo.put("system_version", getSystemVersion())
            
            // 存储信息（MB）
            val storageInfo = getStorageInfo()
            deviceInfo.put("available_storage", storageInfo.first)
            deviceInfo.put("total_storage", storageInfo.second)
            
            // 内存信息（MB）
            val memoryInfo = getMemoryInfo()
            deviceInfo.put("total_memory", memoryInfo.first)
            deviceInfo.put("available_memory", memoryInfo.second)
            
            // CPU温度
            deviceInfo.put("cpu_temperature", getCpuTemperature())
            
            // 网络信息
            deviceInfo.put("local_ip", getLocalIpAddress())
            deviceInfo.put("network_type", getNetworkType())
            deviceInfo.put("mac_address", getMacAddress())

            // 公网IP信息（异步获取）
            val publicIpInfo = getPublicIpInfo()
            deviceInfo.put("public_ip", publicIpInfo.getString("ip"))
            deviceInfo.put("public_ipv6", publicIpInfo.optString("ipv6", null))
            deviceInfo.put("city", publicIpInfo.optString("city", null))
            deviceInfo.put("region", publicIpInfo.optString("region", null))
            deviceInfo.put("country", publicIpInfo.optString("country", null))
            deviceInfo.put("location", publicIpInfo.optString("loc", null))
            deviceInfo.put("isp_org", publicIpInfo.optString("org", null))
            deviceInfo.put("postal_code", publicIpInfo.optString("postal", null))
            deviceInfo.put("timezone", publicIpInfo.optString("timezone", null))
            
            // 显示信息
            val displayInfo = getDisplayInfo()
            deviceInfo.put("screen_resolution", displayInfo.first)
            deviceInfo.put("screen_orientation", displayInfo.second)
            
            // 系统时间
            deviceInfo.put("system_time", getCurrentTime())
            
            // 发送端软件设置信息
            val settingsInfo = getAppSettings()
            deviceInfo.put("app_settings", settingsInfo)
            
            Log.i(TAG, "设备信息收集完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "收集设备信息时发生错误", e)
        }
        
        return deviceInfo
    }
    
    /**
     * 获取CPU唯一ID
     */
    @SuppressLint("HardwareIds")
    private fun getCpuUniqueId(): String {
        return try {
            // 方法1: 尝试从/proc/cpuinfo获取
            val cpuInfo = readCpuInfo()
            if (cpuInfo.isNotEmpty()) {
                return cpuInfo
            }
            
            // 方法2: 使用Android ID
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: "UNKNOWN_CPU_ID"
        } catch (e: Exception) {
            Log.e(TAG, "获取CPU唯一ID失败", e)
            "UNKNOWN_CPU_ID"
        }
    }
    
    private fun readCpuInfo(): String {
        return try {
            val file = File("/proc/cpuinfo")
            if (file.exists()) {
                val reader = BufferedReader(FileReader(file))
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    if (line!!.startsWith("Serial")) {
                        reader.close()
                        return line!!.split(":")[1].trim()
                    }
                }
                reader.close()
            }
            ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 获取设备序列号（使用ZtlManager接口）
     */
    private fun getWechatSN(): String {
        return try {
            // 使用ZtlManager获取生成的序列号
            val ztlManager = ZtlManager.GetInstance()
            val buildSerial = ztlManager.getBuildSerial()

            if (buildSerial.isNotEmpty()) {
                Logger.d(TAG, "✅ ZtlManager序列号获取成功: ${buildSerial.take(8)}...")
                "ZTL_SN_$buildSerial"
            } else {
                Logger.w(TAG, "⚠️ ZtlManager返回空序列号，使用备用方案")
                // 如果ZtlManager返回空值，使用备用方案
                generateFallbackSerial()
            }
        } catch (e: SecurityException) {
            Logger.w(TAG, "🔒 ZtlManager权限不足，使用备用方案: ${e.message}")
            // 权限不足时使用备用方案
            generateFallbackSerial()
        } catch (e: Exception) {
            Logger.w(TAG, "❌ ZtlManager序列号获取异常，使用备用方案: ${e.message}")
            // 其他异常情况下使用备用方案
            generateFallbackSerial()
        }
    }

    /**
     * 生成备用序列号
     */
    private fun generateFallbackSerial(): String {
        return try {
            // 尝试获取Android设备真实序列号
            val deviceSerial = getDeviceSerial()

            if (deviceSerial.isNotEmpty() && deviceSerial != "unknown") {
                Logger.d(TAG, "✅ 获取到设备序列号: ${deviceSerial.take(8)}...")
                "$deviceSerial"
            } else {
                Logger.w(TAG, "⚠️ 无法获取设备序列号，使用Android ID")
                // 备用方案：使用Android ID
                val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
                if (!androidId.isNullOrEmpty() && androidId != "9774d56d682e549c") {
                    Logger.d(TAG, "✅ 使用Android ID: ${androidId.take(8)}...")
                    "$androidId"
                } else {
                    Logger.w(TAG, "⚠️ Android ID不可用，使用最终备用方案")
                    "FALLBACK_${System.currentTimeMillis().toString().takeLast(8)}"
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 获取设备序列号失败: ${e.message}")
            // 最终备用方案
            "UNKNOWN_${System.currentTimeMillis().toString().takeLast(6)}"
        }
    }

    /**
     * 获取Android设备序列号
     */
    @SuppressLint("HardwareIds")
    private fun getDeviceSerial(): String {
        return try {
            when {
                // Android 9 (API 28) 及以上版本
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> {
                    try {
                        // 需要READ_PHONE_STATE权限，但通常会被拒绝
                        Build.getSerial()
                    } catch (e: SecurityException) {
                        Logger.d(TAG, "Build.getSerial()权限被拒绝: ${e.message}")
                        // 尝试使用反射获取
                        getSerialByReflection()
                    }
                }
                // Android 8 及以下版本
                else -> {
                    @Suppress("DEPRECATION")
                    val serial = Build.SERIAL
                    if (serial.isNotEmpty() && serial != "unknown") {
                        serial
                    } else {
                        getSerialByReflection()
                    }
                }
            }
        } catch (e: Exception) {
            Logger.d(TAG, "获取设备序列号异常: ${e.message}")
            ""
        }
    }

    /**
     * 通过反射获取序列号
     */
    private fun getSerialByReflection(): String {
        return try {
            val systemPropertiesClass = Class.forName("android.os.SystemProperties")
            val getMethod = systemPropertiesClass.getMethod("get", String::class.java, String::class.java)
            val serial = getMethod.invoke(null, "ro.serialno", "unknown") as String

            if (serial.isNotEmpty() && serial != "unknown") {
                Logger.d(TAG, "通过反射获取序列号成功: ${serial.take(8)}...")
                serial
            } else {
                Logger.d(TAG, "反射获取序列号失败或为unknown")
                ""
            }
        } catch (e: Exception) {
            Logger.d(TAG, "反射获取序列号异常: ${e.message}")
            ""
        }
    }


    
    /**
     * 获取主板型号
     */
    private fun getMotherboardModel(): String {
        return "${Build.MANUFACTURER} ${Build.BOARD} ${Build.HARDWARE}"
    }
    
    /**
     * 获取Android版本
     */
    private fun getAndroidVersion(): String {
        return "Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})"
    }
    
    /**
     * 获取系统版本
     */
    private fun getSystemVersion(): String {
        return "${Build.DISPLAY} | ${Build.FINGERPRINT}"
    }
    
    /**
     * 获取存储信息（返回可用空间和总空间，单位MB）
     */
    private fun getStorageInfo(): Pair<Long, Long> {
        return try {
            val stat = StatFs(Environment.getDataDirectory().path)
            val bytesAvailable = stat.blockSizeLong * stat.availableBlocksLong
            val bytesTotal = stat.blockSizeLong * stat.blockCountLong
            
            Pair(
                bytesAvailable / (1024 * 1024), // 转换为MB
                bytesTotal / (1024 * 1024)
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取存储信息失败", e)
            Pair(0L, 0L)
        }
    }
    
    /**
     * 获取内存信息（返回总内存和可用内存，单位MB）
     */
    private fun getMemoryInfo(): Pair<Long, Long> {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            val totalMemory = memoryInfo.totalMem / (1024 * 1024) // 转换为MB
            val availableMemory = memoryInfo.availMem / (1024 * 1024)
            
            Pair(totalMemory, availableMemory)
        } catch (e: Exception) {
            Log.e(TAG, "获取内存信息失败", e)
            Pair(0L, 0L)
        }
    }
    
    /**
     * 获取CPU温度
     */
    private fun getCpuTemperature(): Float? {
        return try {
            // 尝试从thermal zone读取温度
            val thermalFiles = arrayOf(
                "/sys/class/thermal/thermal_zone0/temp",
                "/sys/class/thermal/thermal_zone1/temp",
                "/sys/devices/virtual/thermal/thermal_zone0/temp"
            )
            
            for (path in thermalFiles) {
                val file = File(path)
                if (file.exists() && file.canRead()) {
                    val temp = file.readText().trim().toFloatOrNull()
                    if (temp != null) {
                        // 温度通常以毫度为单位，转换为摄氏度
                        return if (temp > 1000) temp / 1000f else temp
                    }
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "获取CPU温度失败", e)
            null
        }
    }
    
    /**
     * 获取本地IP地址
     */
    private fun getLocalIpAddress(): String {
        return try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (networkInterface in interfaces) {
                val addresses = networkInterface.inetAddresses
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address.hostAddress?.contains(':') == false) {
                        return address.hostAddress ?: "127.0.0.1"
                    }
                }
            }
            "127.0.0.1"
        } catch (e: Exception) {
            Log.e(TAG, "获取本地IP失败", e)
            "127.0.0.1"
        }
    }
    
    /**
     * 获取网络类型
     */
    private fun getNetworkType(): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "wifi"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "mobile"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "wired"
                else -> "unknown"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络类型失败", e)
            "unknown"
        }
    }
    
    /**
     * 获取MAC地址（优先获取以太网MAC地址）
     */
    @SuppressLint("HardwareIds")
    private fun getMacAddress(): String {
        return try {
            Log.i(TAG, "🌐 [MAC地址] 开始获取设备MAC地址...")

            // 方法1: 尝试从网络接口获取真实MAC地址
            val realMacAddress = getRealMacAddress()
            if (realMacAddress != "02:00:00:00:00:00" && realMacAddress.isNotEmpty()) {
                Log.i(TAG, "🌐 [MAC地址] ✅ 从网络接口获取到真实MAC: $realMacAddress")
                return realMacAddress
            }

            // 方法2: 尝试从系统文件获取
            val systemMacAddress = getMacFromSystemFiles()
            if (systemMacAddress != "02:00:00:00:00:00" && systemMacAddress.isNotEmpty()) {
                Log.i(TAG, "🌐 [MAC地址] ✅ 从系统文件获取到MAC: $systemMacAddress")
                return systemMacAddress
            }

            // 方法3: 尝试WiFi MAC地址（可能是虚拟地址）
            val wifiMacAddress = getWifiMacAddress()
            if (wifiMacAddress != "02:00:00:00:00:00" && wifiMacAddress.isNotEmpty()) {
                Log.w(TAG, "🌐 [MAC地址] ⚠️ 只能获取到WiFi MAC: $wifiMacAddress (可能是虚拟地址)")
                return wifiMacAddress
            }

            Log.w(TAG, "🌐 [MAC地址] ❌ 无法获取真实MAC地址，返回默认值")
            "02:00:00:00:00:00"

        } catch (e: Exception) {
            Log.e(TAG, "🌐 [MAC地址] 获取MAC地址失败", e)
            "02:00:00:00:00:00"
        }
    }

    /**
     * 从网络接口获取真实MAC地址
     */
    private fun getRealMacAddress(): String {
        try {
            Log.d(TAG, "🌐 [MAC地址] 遍历网络接口...")
            val interfaces = NetworkInterface.getNetworkInterfaces()

            // 优先级列表：以太网 > WiFi > 其他
            val priorityInterfaces = mutableListOf<Pair<String, String>>()
            val ethernetInterfaces = mutableListOf<Pair<String, String>>()
            val wifiInterfaces = mutableListOf<Pair<String, String>>()
            val otherInterfaces = mutableListOf<Pair<String, String>>()

            for (networkInterface in interfaces) {
                if (networkInterface.isUp && !networkInterface.isLoopback && !networkInterface.isVirtual) {
                    val macBytes = networkInterface.hardwareAddress
                    if (macBytes != null && macBytes.size == 6) {
                        val macAddress = macBytes.joinToString(":") { "%02x".format(it) }
                        val interfaceName = networkInterface.name.lowercase()

                        Log.d(TAG, "🌐 [MAC地址] 发现接口: $interfaceName = $macAddress")

                        // 跳过虚拟MAC地址
                        if (macAddress == "02:00:00:00:00:00" || macAddress.startsWith("00:00:00")) {
                            Log.d(TAG, "🌐 [MAC地址] 跳过虚拟MAC: $interfaceName = $macAddress")
                            continue
                        }

                        // 按接口类型分类
                        when {
                            interfaceName.contains("eth") || interfaceName.contains("lan") ||
                            interfaceName.contains("enp") || interfaceName.contains("eno") -> {
                                ethernetInterfaces.add(Pair(interfaceName, macAddress))
                                Log.i(TAG, "🌐 [MAC地址] 🔌 以太网接口: $interfaceName = $macAddress")
                            }
                            interfaceName.contains("wlan") || interfaceName.contains("wifi") ||
                            interfaceName.contains("wlp") -> {
                                wifiInterfaces.add(Pair(interfaceName, macAddress))
                                Log.i(TAG, "🌐 [MAC地址] 📶 WiFi接口: $interfaceName = $macAddress")
                            }
                            else -> {
                                otherInterfaces.add(Pair(interfaceName, macAddress))
                                Log.d(TAG, "🌐 [MAC地址] 🔗 其他接口: $interfaceName = $macAddress")
                            }
                        }
                    }
                }
            }

            // 按优先级返回MAC地址
            when {
                ethernetInterfaces.isNotEmpty() -> {
                    val selected = ethernetInterfaces.first()
                    Log.i(TAG, "🌐 [MAC地址] ✅ 选择以太网MAC: ${selected.first} = ${selected.second}")
                    return selected.second
                }
                wifiInterfaces.isNotEmpty() -> {
                    val selected = wifiInterfaces.first()
                    Log.i(TAG, "🌐 [MAC地址] ✅ 选择WiFi MAC: ${selected.first} = ${selected.second}")
                    return selected.second
                }
                otherInterfaces.isNotEmpty() -> {
                    val selected = otherInterfaces.first()
                    Log.i(TAG, "🌐 [MAC地址] ✅ 选择其他接口MAC: ${selected.first} = ${selected.second}")
                    return selected.second
                }
                else -> {
                    Log.w(TAG, "🌐 [MAC地址] ⚠️ 未找到有效的网络接口")
                    return "02:00:00:00:00:00"
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "🌐 [MAC地址] 从网络接口获取MAC失败", e)
            return "02:00:00:00:00:00"
        }
    }

    /**
     * 从系统文件获取MAC地址
     */
    private fun getMacFromSystemFiles(): String {
        try {
            Log.d(TAG, "🌐 [MAC地址] 尝试从系统文件获取MAC...")

            // 常见的MAC地址文件路径
            val macFilePaths = listOf(
                "/sys/class/net/eth0/address",
                "/sys/class/net/eth1/address",
                "/sys/class/net/wlan0/address",
                "/sys/class/net/wlan1/address"
            )

            for (filePath in macFilePaths) {
                try {
                    val file = File(filePath)
                    if (file.exists() && file.canRead()) {
                        val macAddress = file.readText().trim()
                        if (macAddress.isNotEmpty() && macAddress != "02:00:00:00:00:00") {
                            Log.i(TAG, "🌐 [MAC地址] ✅ 从文件获取MAC: $filePath = $macAddress")
                            return macAddress
                        }
                    }
                } catch (e: Exception) {
                    Log.d(TAG, "🌐 [MAC地址] 读取文件失败: $filePath - ${e.message}")
                }
            }

            Log.w(TAG, "🌐 [MAC地址] ⚠️ 系统文件中未找到有效MAC")
            return "02:00:00:00:00:00"

        } catch (e: Exception) {
            Log.e(TAG, "🌐 [MAC地址] 从系统文件获取MAC失败", e)
            return "02:00:00:00:00:00"
        }
    }

    /**
     * 获取WiFi MAC地址（可能是虚拟地址）
     */
    private fun getWifiMacAddress(): String {
        return try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_WIFI_STATE) == PackageManager.PERMISSION_GRANTED) {
                val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                val macAddress = wifiInfo.macAddress ?: "02:00:00:00:00:00"
                Log.d(TAG, "🌐 [MAC地址] WiFi MAC: $macAddress")
                macAddress
            } else {
                Log.w(TAG, "🌐 [MAC地址] ⚠️ 缺少WiFi状态权限")
                "02:00:00:00:00:00"
            }
        } catch (e: Exception) {
            Log.e(TAG, "🌐 [MAC地址] 获取WiFi MAC失败", e)
            "02:00:00:00:00:00"
        }
    }
    
    /**
     * 获取显示信息
     */
    private fun getDisplayInfo(): Pair<String, String> {
        return try {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val displayMetrics = DisplayMetrics()
            windowManager.defaultDisplay.getMetrics(displayMetrics)
            
            val resolution = "${displayMetrics.widthPixels}x${displayMetrics.heightPixels}"
            val orientation = when (context.resources.configuration.orientation) {
                Configuration.ORIENTATION_PORTRAIT -> "portrait"
                Configuration.ORIENTATION_LANDSCAPE -> "landscape"
                else -> "unknown"
            }
            
            Pair(resolution, orientation)
        } catch (e: Exception) {
            Log.e(TAG, "获取显示信息失败", e)
            Pair("unknown", "unknown")
        }
    }
    
    /**
     * 获取当前时间
     */
    private fun getCurrentTime(): String {
        val formatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        return formatter.format(Date())
    }
    
    /**
     * 获取应用设置信息
     */
    private fun getAppSettings(): JSONObject {
        val settings = JSONObject()

        try {
            val sharedPrefs = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)

            // 从Constants和SettingsActivity中获取的设置项
            settings.put("signaling_url", sharedPrefs.getString(Constants.PREF_SIGNALING_URL, Constants.DEFAULT_SIGNALING_URL))
            settings.put("sender_id", sharedPrefs.getString(Constants.PREF_SENDER_ID, Constants.DEFAULT_SENDER_ID))
            settings.put("video_source", sharedPrefs.getString(Constants.PREF_VIDEO_SOURCE, Constants.DEFAULT_VIDEO_SOURCE))
            settings.put("video_resolution", sharedPrefs.getString(Constants.PREF_VIDEO_RESOLUTION, Constants.DEFAULT_VIDEO_RESOLUTION))
            settings.put("video_bitrate", sharedPrefs.getInt(Constants.PREF_VIDEO_BITRATE, Constants.DEFAULT_VIDEO_BITRATE))
            settings.put("video_codec", sharedPrefs.getString(Constants.PREF_VIDEO_CODEC, Constants.DEFAULT_VIDEO_CODEC))
            settings.put("video_framerate", sharedPrefs.getInt(Constants.PREF_VIDEO_FRAMERATE, Constants.DEFAULT_VIDEO_FRAMERATE))
            settings.put("camera_id", sharedPrefs.getString(Constants.PREF_CAMERA_ID, Constants.DEFAULT_CAMERA_ID))
            settings.put("screen_capture_quality", sharedPrefs.getString(Constants.PREF_SCREEN_CAPTURE_QUALITY, Constants.DEFAULT_SCREEN_CAPTURE_QUALITY))
            settings.put("audio_source", sharedPrefs.getString(Constants.PREF_AUDIO_SOURCE, Constants.DEFAULT_AUDIO_SOURCE))
            settings.put("auto_start_game", sharedPrefs.getBoolean(Constants.PREF_AUTO_START_GAME, Constants.DEFAULT_AUTO_START_GAME))
            settings.put("auto_start_game_package", sharedPrefs.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE))
            settings.put("log_display_enabled", sharedPrefs.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED))
            settings.put("hdmi_device_path", sharedPrefs.getString(Constants.PREF_HDMI_DEVICE_PATH, Constants.DEFAULT_HDMI_DEVICE_PATH))
            settings.put("usb_capture_device_path", sharedPrefs.getString(Constants.PREF_USB_CAPTURE_DEVICE_PATH, Constants.DEFAULT_USB_CAPTURE_DEVICE_PATH))
            settings.put("has_manual_operation", sharedPrefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false))
            settings.put("first_install", sharedPrefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL))

            // 添加应用版本信息
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            settings.put("app_version", packageInfo.versionName)
            settings.put("app_version_code", packageInfo.longVersionCode)

            // 添加构建信息
            settings.put("build_type", if (BuildConfig.DEBUG) "debug" else "release")

            // 添加新的设置字段
            settings.put("has_manual_operation", sharedPrefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false))
            settings.put("first_install", sharedPrefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL))
            settings.put("hdmi_device_path", sharedPrefs.getString(Constants.PREF_HDMI_DEVICE_PATH, Constants.DEFAULT_HDMI_DEVICE_PATH))
            settings.put("usb_capture_device_path", sharedPrefs.getString(Constants.PREF_USB_CAPTURE_DEVICE_PATH, Constants.DEFAULT_USB_CAPTURE_DEVICE_PATH))

        } catch (e: Exception) {
            Log.e(TAG, "获取应用设置失败", e)
        }

        return settings
    }

    /**
     * 获取公网IP信息（包含地理位置）
     */
    private suspend fun getPublicIpInfo(): JSONObject = withContext(Dispatchers.IO) {
        val result = JSONObject()

        try {
            // 首先尝试从ipinfo.io获取详细信息
            val ipInfoResult = getIpInfoFromService("https://ipinfo.io/json")
            if (ipInfoResult.has("ip")) {
                val ip = ipInfoResult.getString("ip")
                // 确保返回的是IPv4地址
                if (isValidIPv4(ip)) {
                    return@withContext ipInfoResult
                }
            }

            // 备选方案1: 使用httpbin.org获取IPv4
            val httpbinResult = getIpInfoFromService("https://httpbin.org/ip")
            if (httpbinResult.has("origin")) {
                val ip = httpbinResult.getString("origin")
                if (isValidIPv4(ip)) {
                    result.put("ip", ip)
                }
            }

            // 备选方案2: 使用专门的IPv4服务
            if (!result.has("ip")) {
                val ipv4Services = listOf(
                    "https://ipv4.icanhazip.com",
                    "https://api.ipify.org",
                    "https://ipv4.jsonip.com"
                )

                for (service in ipv4Services) {
                    try {
                        val ip = if (service.contains("jsonip")) {
                            val jsonResult = getIpInfoFromService(service)
                            jsonResult.optString("ip", "")
                        } else {
                            getSimpleIp(service)
                        }

                        if (ip.isNotEmpty() && isValidIPv4(ip)) {
                            result.put("ip", ip)
                            break
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "从 $service 获取IPv4失败: ${e.message}")
                    }
                }
            }

            // 尝试获取IPv6（使用专门的IPv6服务）
            try {
                val ipv6Services = listOf(
                    "https://ipv6.icanhazip.com",
                    "https://api6.ipify.org"
                )

                for (service in ipv6Services) {
                    try {
                        val ipv6 = getSimpleIp(service)
                        if (ipv6.isNotEmpty() && isValidIPv6(ipv6)) {
                            result.put("ipv6", ipv6)
                            break
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "从 $service 获取IPv6失败: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "获取IPv6失败: ${e.message}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "获取公网IP信息失败", e)
            result.put("ip", "unknown")
        }

        return@withContext result
    }

    /**
     * 从指定服务获取IP信息
     */
    private suspend fun getIpInfoFromService(url: String): JSONObject = withContext(Dispatchers.IO) {
        try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.setRequestProperty("User-Agent", "Android-WebRTC-Sender")

            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText()
                reader.close()

                return@withContext JSONObject(response)
            }
        } catch (e: Exception) {
            Log.d(TAG, "从 $url 获取IP信息失败: ${e.message}")
        }

        return@withContext JSONObject()
    }

    /**
     * 获取简单的IP地址字符串
     */
    private suspend fun getSimpleIp(url: String): String = withContext(Dispatchers.IO) {
        try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.setRequestProperty("User-Agent", "Android-WebRTC-Sender")

            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText().trim()
                reader.close()

                return@withContext response
            }
        } catch (e: Exception) {
            Log.d(TAG, "从 $url 获取IP失败: ${e.message}")
        }

        return@withContext ""
    }

    /**
     * 验证是否为有效的IPv4地址
     */
    private fun isValidIPv4(ip: String): Boolean {
        if (ip.isBlank()) return false

        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false

            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 验证是否为有效的IPv6地址
     */
    private fun isValidIPv6(ip: String): Boolean {
        if (ip.isBlank()) return false

        return try {
            // 简单的IPv6格式检查
            ip.contains(":") &&
            ip.split(":").size >= 3 &&
            ip.split(":").size <= 8 &&
            !ip.contains("<") && // 排除HTML内容
            !ip.contains(">") &&
            !ip.contains("html", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
    }
}
