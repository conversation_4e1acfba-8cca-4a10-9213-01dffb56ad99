package com.example.webrtcsender.command

import android.app.AlarmManager
import android.app.DownloadManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.SystemClock
import androidx.core.content.FileProvider
import com.example.webrtcsender.utils.Logger
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest
import com.example.webrtcsender.utils.ZtlInstallHelper

/**
 * 升级命令处理器
 * 处理应用升级相关命令，带详细状态上报
 */
class UpgradeCommandHandler(
    private val context: Context,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "UpgradeCommandHandler"
    }

    // 目标版本
    private var targetVersion: String? = null
    
    override fun executeControlCommand(command: String, params: JSONObject): Boolean {
        // 升级命令处理器不处理控制命令
        Logger.w(TAG, "⚠️ 升级命令处理器不处理控制命令: $command")
        return false
    }
    
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        try {
            Logger.i(TAG, "📦 开始升级流程...")
            statusCallback("upgrade", "started", 0, "开始升级流程")
            
            // 检查当前版本
            val currentVersion = getCurrentVersion()
            statusCallback("upgrade", "checking_version", 10, "检查当前版本: $currentVersion")
            
            if (!force && version == currentVersion) {
                Logger.i(TAG, "⚠️ 版本相同，跳过升级")
                statusCallback("upgrade", "skipped", 100, "版本相同，跳过升级")
                return
            }
            
            Logger.i(TAG, "当前版本: $currentVersion, 目标版本: $version")
            statusCallback("upgrade", "version_check_complete", 20, "版本检查完成，需要升级到: $version")
            
            // 下载APK
            downloadAndInstallApk(apkUrl, version)
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 升级失败: ${e.message}")
            statusCallback("upgrade", "failed", 0, "升级失败: ${e.message}")
        }
    }
    
    /**
     * 获取当前版本
     */
    private fun getCurrentVersion(): String {
        return try {
            val pm = context.packageManager
            val packageName = context.packageName
            pm.getPackageInfo(packageName, 0).versionName
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 获取当前版本失败: ${e.message}")
            "unknown"
        }
    }
    
    /**
     * 下载并安装APK
     */
    private fun downloadAndInstallApk(apkUrl: String, version: String) {
        try {
            Logger.i(TAG, "📥 开始下载APK: $apkUrl")
            statusCallback("upgrade", "download_started", 30, "开始下载APK: $apkUrl")

            // 强制清理旧的APK文件
            cleanupOldApkFiles()

            // 判断是否为FTP地址
            if (apkUrl.startsWith("ftp://")) {
                Logger.i(TAG, "🌐 检测到FTP地址，使用FTP下载")
                downloadFromFtp(apkUrl, version)
                return
            }

            Logger.i(TAG, "🌐 使用HTTP下载")
            val dm = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            val request = DownloadManager.Request(Uri.parse(apkUrl))

            request.setTitle("应用升级")
            request.setDescription("正在下载版本 $version")
            // 使用版本号命名APK文件
            val apkFileName = "update_v${version}.apk"
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, apkFileName)
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)

            // 允许覆盖已存在的文件
            request.setAllowedOverRoaming(true)
            request.setAllowedOverMetered(true)

            val downloadId = dm.enqueue(request)
            statusCallback("upgrade", "downloading", 40, "APK下载中...")

            // 启动下载进度监控
            startDownloadProgressMonitor(dm, downloadId, version)
            
            // 监听下载完成
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                    if (id == downloadId) {
                        Logger.i(TAG, "📦 APK下载完成，开始验证")

                        // 检查下载状态
                        val query = DownloadManager.Query()
                        query.setFilterById(downloadId)
                        val cursor = dm.query(query)

                        if (cursor.moveToFirst()) {
                            val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                            val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
                            val status = cursor.getInt(statusIndex)
                            val reason = cursor.getInt(reasonIndex)

                            when (status) {
                                DownloadManager.STATUS_SUCCESSFUL -> {
                                    Logger.i(TAG, "✅ APK下载成功")
                                    statusCallback("upgrade", "download_completed", 70, "APK下载完成")

                                    // 立即验证下载的文件
                                    val apkFileName = "update_v${version}.apk"
                                    val apkFile = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkFileName)
                                    val validationError = validateApkFile(apkFile)
                                    if (validationError != null) {
                                        Logger.e(TAG, "❌ 下载的APK验证失败: $validationError")
                                        statusCallback("upgrade", "download_failed", 0, "下载的APK验证失败: $validationError")
                                    } else {
                                        Logger.i(TAG, "✅ 下载的APK验证通过")
                                        installApk(version)
                                    }
                                }
                                DownloadManager.STATUS_FAILED -> {
                                    Logger.e(TAG, "❌ APK下载失败，原因代码: $reason")
                                    val errorMsg = when (reason) {
                                        DownloadManager.ERROR_CANNOT_RESUME -> "无法恢复下载"
                                        DownloadManager.ERROR_DEVICE_NOT_FOUND -> "存储设备未找到"
                                        DownloadManager.ERROR_FILE_ALREADY_EXISTS -> "文件已存在"
                                        DownloadManager.ERROR_FILE_ERROR -> "文件错误"
                                        DownloadManager.ERROR_HTTP_DATA_ERROR -> "HTTP数据错误"
                                        DownloadManager.ERROR_INSUFFICIENT_SPACE -> "存储空间不足"
                                        DownloadManager.ERROR_TOO_MANY_REDIRECTS -> "重定向次数过多"
                                        DownloadManager.ERROR_UNHANDLED_HTTP_CODE -> "未处理的HTTP代码"
                                        DownloadManager.ERROR_UNKNOWN -> "未知错误"
                                        else -> "下载失败(代码:$reason)"
                                    }
                                    statusCallback("upgrade", "download_failed", 0, "APK下载失败: $errorMsg")
                                }
                                else -> {
                                    Logger.w(TAG, "⚠️ 下载状态异常: $status")
                                    statusCallback("upgrade", "download_failed", 0, "下载状态异常: $status")
                                }
                            }
                        }
                        cursor.close()
                        context.unregisterReceiver(this)
                    }
                }
            }
            
            context.registerReceiver(receiver, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE))
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 下载APK失败: ${e.message}")
            statusCallback("upgrade", "download_failed", 0, "下载APK失败: ${e.message}")
        }
    }
    
    /**
     * 监控下载进度
     */
    private fun startDownloadProgressMonitor(dm: DownloadManager, downloadId: Long, version: String) {
        val handler = Handler(Looper.getMainLooper())
        val progressRunnable = object : Runnable {
            override fun run() {
                try {
                    val query = DownloadManager.Query().setFilterById(downloadId)
                    val cursor = dm.query(query)
                    
                    if (cursor.moveToFirst()) {
                        val bytesDownloaded = cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR))
                        val bytesTotal = cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES))
                        val status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS))
                        
                        if (bytesTotal > 0) {
                            val progress = ((bytesDownloaded * 100) / bytesTotal).toInt()
                            val adjustedProgress = 40 + (progress * 30 / 100) // 40-70%范围
                            
                            statusCallback("upgrade", "downloading", adjustedProgress, 
                                "下载中: ${bytesDownloaded / 1024 / 1024}MB / ${bytesTotal / 1024 / 1024}MB")
                        }
                        
                        // 如果还在下载，继续监控
                        if (status == DownloadManager.STATUS_RUNNING) {
                            handler.postDelayed(this, 1000) // 每秒更新一次
                        }
                    }
                    cursor.close()
                } catch (e: Exception) {
                    Logger.e(TAG, "监控下载进度失败: ${e.message}")
                }
            }
        }
        
        handler.post(progressRunnable)
    }
    
    /**
     * 强制清理旧的APK文件
     */
    private fun cleanupOldApkFiles() {
        try {
            val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)

            // 获取所有可能的APK文件
            val apkFiles = mutableListOf<File>()

            // 添加固定名称的APK文件
            apkFiles.addAll(listOf(
                File(downloadDir, "update.apk"),
                File(downloadDir, "app-debug.apk"),
                File(downloadDir, "app-release.apk"),
                File(downloadDir, "webrtcsender.apk")
            ))

            // 查找所有带版本号的APK文件
            downloadDir.listFiles()?.forEach { file ->
                if (file.name.matches(Regex("update_v.*\\.apk"))) {
                    apkFiles.add(file)
                    Logger.d(TAG, "🔍 发现版本APK文件: ${file.name}")
                }
            }

            Logger.i(TAG, "🧹 开始清理旧APK文件...")

            for (apkFile in apkFiles) {
                if (apkFile.exists()) {
                    try {
                        // 尝试多次删除
                        var deleted = false
                        for (attempt in 1..3) {
                            if (apkFile.delete()) {
                                deleted = true
                                Logger.i(TAG, "🧹 清理文件成功: ${apkFile.name} (第${attempt}次尝试)")
                                break
                            } else {
                                Logger.w(TAG, "🧹 清理文件失败: ${apkFile.name} (第${attempt}次尝试)")
                                // 等待一下再重试
                                Thread.sleep(500)
                            }
                        }

                        if (!deleted) {
                            // 如果删除失败，尝试重命名为备份文件
                            val backupFile = File(downloadDir, "${apkFile.name}.backup.${System.currentTimeMillis()}")
                            if (apkFile.renameTo(backupFile)) {
                                Logger.i(TAG, "🧹 文件重命名为备份: ${apkFile.name} -> ${backupFile.name}")
                            } else {
                                Logger.e(TAG, "🧹 无法删除或重命名文件: ${apkFile.name}")
                                // 尝试清空文件内容
                                try {
                                    apkFile.writeText("")
                                    Logger.i(TAG, "🧹 已清空文件内容: ${apkFile.name}")
                                } catch (e: Exception) {
                                    Logger.e(TAG, "🧹 清空文件内容失败: ${apkFile.name} - ${e.message}")
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "🧹 清理文件时出错: ${apkFile.name} - ${e.message}")
                    }
                }
            }

            // 清理下载管理器中的旧下载记录
            try {
                val dm = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                val query = DownloadManager.Query()
                query.setFilterByStatus(DownloadManager.STATUS_SUCCESSFUL or DownloadManager.STATUS_FAILED)
                val cursor = dm.query(query)

                val idsToRemove = mutableListOf<Long>()
                while (cursor.moveToNext()) {
                    val titleIndex = cursor.getColumnIndex(DownloadManager.COLUMN_TITLE)
                    val idIndex = cursor.getColumnIndex(DownloadManager.COLUMN_ID)
                    val title = cursor.getString(titleIndex)
                    val id = cursor.getLong(idIndex)

                    if (title?.contains("应用升级") == true || title?.contains("update") == true) {
                        idsToRemove.add(id)
                    }
                }
                cursor.close()

                // 删除旧的下载记录
                for (id in idsToRemove) {
                    dm.remove(id)
                    Logger.d(TAG, "🧹 清理下载记录: $id")
                }

                if (idsToRemove.isNotEmpty()) {
                    Logger.i(TAG, "🧹 清理了 ${idsToRemove.size} 个旧下载记录")
                }

            } catch (e: Exception) {
                Logger.w(TAG, "🧹 清理下载记录时出错: ${e.message}")
            }

            Logger.i(TAG, "🧹 APK文件清理完成")

        } catch (e: Exception) {
            Logger.e(TAG, "🧹 清理APK文件时出错: ${e.message}")
        }
    }

    /**
     * 验证APK文件
     */
    private fun validateApkFile(apkFile: File): String? {
        try {
            // 1. 检查文件是否存在
            if (!apkFile.exists()) {
                return "APK文件不存在"
            }

            // 2. 检查文件大小
            val fileSize = apkFile.length()
            if (fileSize < 1024 * 1024) { // 小于1MB可能不是有效的APK
                return "APK文件太小，可能损坏 (${fileSize / 1024}KB)"
            }

            Logger.i(TAG, "📦 APK文件大小: ${fileSize / 1024 / 1024}MB")

            // 3. 尝试解析APK包信息
            val packageManager = context.packageManager
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageArchiveInfo(
                    apkFile.absolutePath,
                    PackageManager.PackageInfoFlags.of(0)
                )
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageArchiveInfo(apkFile.absolutePath, 0)
            }

            if (packageInfo == null) {
                return "APK文件解析失败，可能已损坏"
            }

            Logger.i(TAG, "📦 APK包名: ${packageInfo.packageName}")
            Logger.i(TAG, "📦 APK版本: ${packageInfo.versionName}")

            // 4. 检查包名是否匹配(暂不校验)
            //if (packageInfo.packageName != context.packageName) {
            //    return "APK包名不匹配，期望: ${context.packageName}，实际: ${packageInfo.packageName}"
            //}

            // 5. 检查文件完整性（简单的MD5校验）
            val md5 = calculateMD5(apkFile)
            Logger.i(TAG, "📦 APK MD5: $md5")

            return null // 验证通过

        } catch (e: Exception) {
            Logger.e(TAG, "❌ APK验证失败", e)
            return "APK验证异常: ${e.message}"
        }
    }

    /**
     * 计算文件MD5
     */
    private fun calculateMD5(file: File): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val fis = FileInputStream(file)
            val buffer = ByteArray(8192)
            var length: Int
            while (fis.read(buffer).also { length = it } != -1) {
                md.update(buffer, 0, length)
            }
            fis.close()

            val digest = md.digest()
            val sb = StringBuilder()
            for (b in digest) {
                sb.append(String.format("%02x", b))
            }
            sb.toString()
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 计算MD5失败", e)
            "unknown"
        }
    }

    /**
     * 安装APK
     */
    private fun installApk(version: String) {
        try {
            statusCallback("upgrade", "install_started", 80, "开始安装APK")

            val apkFileName = "update_v${version}.apk"
            val apkFile = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkFileName)

            // 验证APK文件
            val validationError = validateApkFile(apkFile)
            if (validationError != null) {
                Logger.e(TAG, "❌ APK验证失败: $validationError")
                statusCallback("upgrade", "install_failed", 0, "APK验证失败: $validationError")
                return
            }

            Logger.i(TAG, "✅ APK验证通过，开始调用ZtlManager安装")

            // 保存目标版本
            targetVersion = version

            statusCallback("upgrade", "installing", 90, "正在调用ZtlManager安装程序...")

            val pkgName = context.packageName
            var ztlSuccess = false
            try {
                ZtlInstallHelper.install(context, apkFile.absolutePath, pkgName)
                Logger.i(TAG, "✅ ZtlInstallHelper.install已调用")
                statusCallback("upgrade", "install_launched", 95, "ZtlManager已启动安装流程")
                ztlSuccess = true
            } catch (e: Exception) {
                Logger.e(TAG, "❌ ZtlManager安装调用失败: ${e.message}")
                statusCallback("upgrade", "install_failed", 0, "ZtlManager安装调用失败，尝试原生安装: ${e.message}")
            }

            if (!ztlSuccess) {
                // 回退到原有的Intent安装方式
                try {
                    Logger.i(TAG, "🔄 回退到原生Intent安装APK")
                    val intent = Intent(Intent.ACTION_VIEW)
                    val apkUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        FileProvider.getUriForFile(
                            context,
                            "${context.packageName}.fileprovider",
                            apkFile
                        )
                    } else {
                        Uri.fromFile(apkFile)
                    }
                    intent.setDataAndType(apkUri, "application/vnd.android.package-archive")
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    }
                    context.startActivity(intent)
                    Logger.i(TAG, "✅ 启动APK安装（原生Intent方式）")
                    statusCallback("upgrade", "install_launched", 95, "已回退到原生安装方式，请手动确认安装")
                } catch (e: Exception) {
                    Logger.e(TAG, "❌ 原生Intent安装失败: ${e.message}")
                    statusCallback("upgrade", "install_failed", 0, "原生Intent安装失败: ${e.message}")
                    return
                }
            }

            // 启动应用重启监控机制
            startAppRestartMonitoring(apkFile)

            // 启动延迟重启机制（备用方案）
            scheduleSystemReboot()

            // 发送状态更新
            Handler(Looper.getMainLooper()).postDelayed({
                statusCallback("upgrade", "reboot_scheduled", 97, "应用将自动重启，如未成功则系统将在30秒后重启")
            }, 2000)

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 安装APK失败: ${e.message}")
            statusCallback("upgrade", "install_failed", 0, "安装APK失败: ${e.message}")
        }
    }
    


    /**
     * 启动应用重启监控机制
     */
    private fun startAppRestartMonitoring(apkFile: File) {
        try {
            Logger.i(TAG, "🔄 启动应用重启监控机制")

            // 创建应用重启监控
            val handler = Handler(Looper.getMainLooper())

            // 监控安装完成并重启应用
            val restartRunnable = object : Runnable {
                private var checkCount = 0
                private val maxChecks = 20 // 最多检查20次（60秒）

                override fun run() {
                    checkCount++
                    Logger.d(TAG, "🔄 检查应用安装状态 (${checkCount}/${maxChecks})")

                    try {
                        // 检查新版本是否已安装
                        val packageManager = context.packageManager
                        val packageInfo = packageManager.getPackageInfo(context.packageName, 0)
                        val currentVersionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                            packageInfo.longVersionCode.toInt()
                        } else {
                            @Suppress("DEPRECATION")
                            packageInfo.versionCode
                        }

                        Logger.d(TAG, "🔄 当前版本: $currentVersionCode, 目标版本: $targetVersion")

                        // 如果版本已更新，删除APK文件并重启应用
                        val targetVersionInt = targetVersion?.toIntOrNull()
                        if (targetVersionInt != null && currentVersionCode >= targetVersionInt) {
                            Logger.i(TAG, "✅ 检测到应用已更新 ($currentVersionCode >= $targetVersionInt)，删除安装包并重启应用")
                            statusCallback("upgrade", "app_updated", 98, "应用已更新，清理安装包...")

                            // 删除APK安装包
                            deleteApkFile(apkFile)

                            // 延迟一下确保删除完成
                            Thread.sleep(1000)

                            // 重启应用
                            restartApplication()
                            return
                        }

                        // 如果还没更新且未达到最大检查次数，继续检查
                        if (checkCount < maxChecks) {
                            handler.postDelayed(this, 3000) // 每3秒检查一次
                        } else {
                            Logger.w(TAG, "⚠️ 应用重启监控超时，将依赖系统重启")
                            statusCallback("upgrade", "restart_timeout", 99, "应用重启监控超时，将使用系统重启")
                        }

                    } catch (e: Exception) {
                        Logger.e(TAG, "🔄 检查应用状态时出错: ${e.message}")
                        if (checkCount < maxChecks) {
                            handler.postDelayed(this, 3000)
                        }
                    }
                }
            }

            // 延迟5秒开始检查，给安装程序一些时间
            handler.postDelayed(restartRunnable, 5000)

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 启动应用重启监控失败: ${e.message}")
        }
    }

    /**
     * 删除APK安装包
     */
    private fun deleteApkFile(apkFile: File) {
        try {
            if (apkFile.exists()) {
                Logger.i(TAG, "🗑️ 开始删除APK安装包: ${apkFile.absolutePath}")

                // 尝试多次删除
                var deleted = false
                for (attempt in 1..5) { // 增加到5次尝试
                    if (apkFile.delete()) {
                        deleted = true
                        Logger.i(TAG, "✅ APK安装包删除成功: ${apkFile.name} (第${attempt}次尝试)")
                        break
                    } else {
                        Logger.w(TAG, "⚠️ APK安装包删除失败: ${apkFile.name} (第${attempt}次尝试)")
                        // 等待更长时间再重试
                        Thread.sleep(1000)
                    }
                }

                if (!deleted) {
                    Logger.w(TAG, "⚠️ 无法删除APK安装包，将在下次清理时处理: ${apkFile.absolutePath}")
                    // 记录到SharedPreferences，下次启动时清理
                    saveApkForCleanup(apkFile.absolutePath)
                }
            } else {
                Logger.d(TAG, "📁 APK安装包不存在，无需删除: ${apkFile.absolutePath}")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 删除APK安装包时出错: ${apkFile.absolutePath} - ${e.message}")
        }
    }

    /**
     * 保存需要清理的APK文件路径
     */
    private fun saveApkForCleanup(apkPath: String) {
        try {
            val prefs = context.getSharedPreferences("upgrade_cleanup", Context.MODE_PRIVATE)
            val existingPaths = prefs.getStringSet("apk_paths", mutableSetOf()) ?: mutableSetOf()
            existingPaths.add(apkPath)
            prefs.edit().putStringSet("apk_paths", existingPaths).apply()
            Logger.d(TAG, "📝 已记录待清理APK: $apkPath")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 保存待清理APK路径失败: ${e.message}")
        }
    }

    /**
     * 重启应用
     */
    private fun restartApplication() {
        try {
            Logger.i(TAG, "🔄 开始重启应用...")
            statusCallback("upgrade", "restarting_app", 99, "正在重启应用...")

            // 方法1: 使用PackageManager重启应用
            val packageManager = context.packageManager
            val intent = packageManager.getLaunchIntentForPackage(context.packageName)

            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)

                Logger.i(TAG, "🔄 使用PackageManager重启应用")

                // 延迟启动新应用实例
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        context.startActivity(intent)
                        Logger.i(TAG, "✅ 应用重启成功")
                        statusCallback("upgrade", "app_restarted", 100, "应用重启成功，升级完成")

                        // 退出当前进程
                        Handler(Looper.getMainLooper()).postDelayed({
                            android.os.Process.killProcess(android.os.Process.myPid())
                        }, 1000)

                    } catch (e: Exception) {
                        Logger.e(TAG, "🔄 PackageManager重启失败: ${e.message}")
                        // 尝试备用方法
                        restartApplicationAlternative()
                    }
                }, 2000)

            } else {
                Logger.w(TAG, "🔄 无法获取启动Intent，尝试备用方法")
                restartApplicationAlternative()
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 重启应用失败: ${e.message}")
            restartApplicationAlternative()
        }
    }

    /**
     * 备用应用重启方法
     */
    private fun restartApplicationAlternative() {
        try {
            Logger.i(TAG, "🔄 使用备用方法重启应用")

            // 方法2: 使用AlarmManager延迟启动
            val restartIntent = Intent(context, com.example.webrtcsender.ui.MainActivity::class.java)
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)

            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getActivity(
                    context,
                    0,
                    restartIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getActivity(
                    context,
                    0,
                    restartIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val restartTime = System.currentTimeMillis() + 3000 // 3秒后重启

            alarmManager.setExact(AlarmManager.RTC_WAKEUP, restartTime, pendingIntent)
            Logger.i(TAG, "✅ 已安排3秒后重启应用")
            statusCallback("upgrade", "restart_scheduled", 100, "已安排应用重启，升级即将完成")

            // 退出当前进程
            Handler(Looper.getMainLooper()).postDelayed({
                android.os.Process.killProcess(android.os.Process.myPid())
            }, 1000)

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 备用重启方法也失败: ${e.message}")
            statusCallback("upgrade", "restart_failed", 100, "应用重启失败，请手动重启应用")
        }
    }

    /**
     * 安排系统重启（使用AlarmManager系统级延迟）
     */
    private fun scheduleSystemReboot() {
        try {
            Logger.i(TAG, "⏰ 使用AlarmManager安排30秒后系统重启")

            // 创建重启Intent
            val rebootIntent = Intent("com.example.webrtcsender.DELAYED_REBOOT")
            rebootIntent.setPackage(context.packageName)

            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getBroadcast(
                    context,
                    0,
                    rebootIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                PendingIntent.getBroadcast(
                    context,
                    0,
                    rebootIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT
                )
            }

            // 获取AlarmManager
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            // 计算30秒后的时间
            val triggerTime = SystemClock.elapsedRealtime() + 30000 // 30秒

            // 尝试设置精确闹钟，如果失败则降级到普通闹钟
            try {
                // Android 12+ 需要检查精确闹钟权限
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.ELAPSED_REALTIME_WAKEUP,
                            triggerTime,
                            pendingIntent
                        )
                        Logger.i(TAG, "✅ 使用精确闹钟设置重启任务")
                    } else {
                        // 没有精确闹钟权限，使用普通闹钟
                        alarmManager.setAndAllowWhileIdle(
                            AlarmManager.ELAPSED_REALTIME_WAKEUP,
                            triggerTime,
                            pendingIntent
                        )
                        Logger.w(TAG, "⚠️ 没有精确闹钟权限，使用普通闹钟（可能有延迟）")
                    }
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                    Logger.i(TAG, "✅ 使用精确闹钟设置重启任务")
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    alarmManager.setExact(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                    Logger.i(TAG, "✅ 使用精确闹钟设置重启任务")
                } else {
                    alarmManager.set(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                    Logger.i(TAG, "✅ 使用普通闹钟设置重启任务")
                }

                Logger.i(TAG, "✅ AlarmManager重启任务已设置，30秒后执行")

            } catch (securityException: SecurityException) {
                Logger.e(TAG, "❌ AlarmManager权限不足，尝试备用方案", securityException)

                // 备用方案1: 使用普通闹钟
                try {
                    alarmManager.set(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                    Logger.w(TAG, "⚠️ 使用普通闹钟作为备用方案")
                } catch (e: Exception) {
                    Logger.e(TAG, "❌ 普通闹钟也失败，使用最终备用方案", e)

                    // 备用方案2: 立即重启（不等30秒）
                    scheduleImmediateReboot()
                    return
                }
            }

            // 简单的倒计时提醒（这个可能会因为进程被杀而中断，但不影响AlarmManager）
            var countdown = 30
            val countdownRunnable = object : Runnable {
                override fun run() {
                    if (countdown > 0) {
                        Logger.i(TAG, "🔄 系统将在 $countdown 秒后重启（AlarmManager）")
                        statusCallback("upgrade", "reboot_countdown", 98, "系统将在 $countdown 秒后重启")
                        countdown--
                        Handler(Looper.getMainLooper()).postDelayed(this, 1000)
                    }
                }
            }

            // 5秒后开始倒计时提醒
            Handler(Looper.getMainLooper()).postDelayed(countdownRunnable, 5000)

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 安排AlarmManager系统重启失败", e)

            // 最终备用方案：立即重启
            scheduleImmediateReboot()
        }
    }



    /**
     * 立即重启备用方案（当AlarmManager失败时）
     */
    private fun scheduleImmediateReboot() {
        try {
            Logger.w(TAG, "⚠️ 使用立即重启备用方案")
            statusCallback("upgrade", "immediate_reboot", 98, "AlarmManager失败，将立即重启系统")

            // 延迟5秒给用户一点准备时间
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    Logger.i(TAG, "🔄 执行立即重启")

                    // 方法1: PowerManager
                    try {
                        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                        powerManager.reboot("upgrade")
                        Logger.i(TAG, "✅ PowerManager立即重启命令已发送")
                        return@postDelayed
                    } catch (e: Exception) {
                        Logger.w(TAG, "⚠️ PowerManager立即重启失败: ${e.message}")
                    }

                    // 方法2: Shell命令
                    try {
                        Runtime.getRuntime().exec(arrayOf("su", "-c", "reboot"))
                        Logger.i(TAG, "✅ Shell立即重启命令已发送")
                        return@postDelayed
                    } catch (e: Exception) {
                        Logger.w(TAG, "⚠️ Shell立即重启失败: ${e.message}")
                    }

                    // 方法3: 系统广播
                    try {
                        val rebootIntent = Intent("android.intent.action.REBOOT")
                        context.sendBroadcast(rebootIntent)
                        Logger.i(TAG, "✅ 重启广播已发送")
                        return@postDelayed
                    } catch (e: Exception) {
                        Logger.w(TAG, "⚠️ 重启广播失败: ${e.message}")
                    }

                    // 所有方法都失败
                    Logger.e(TAG, "❌ 所有重启方法都失败，请手动重启")
                    statusCallback("upgrade", "reboot_failed", 99, "自动重启失败，请手动重启系统完成升级")

                } catch (e: Exception) {
                    Logger.e(TAG, "❌ 立即重启失败", e)
                    statusCallback("upgrade", "reboot_failed", 99, "重启失败: ${e.message}")
                }
            }, 5000) // 5秒延迟

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 立即重启备用方案失败", e)
        }
    }

    /**
     * 从FTP服务器下载APK
     */
    private fun downloadFromFtp(ftpUrl: String, version: String) {
        Thread {
            try {
                Logger.i(TAG, "🌐 开始FTP下载: $ftpUrl")
                statusCallback("upgrade", "ftp_connecting", 35, "连接FTP服务器...")

                // 解析FTP URL
                val uri = Uri.parse(ftpUrl)
                val host = uri.host ?: throw Exception("无效的FTP地址")
                val port = if (uri.port > 0) uri.port else 21
                val userInfo = uri.userInfo?.split(":") ?: listOf("anonymous", "")
                val username = userInfo.getOrNull(0) ?: "anonymous"
                val password = userInfo.getOrNull(1) ?: ""
                val remotePath = uri.path ?: "/"

                Logger.i(TAG, "🌐 FTP连接信息: $host:$port, 用户: $username, 路径: $remotePath")

                // 使用Apache Commons Net进行FTP下载
                val ftpClient = org.apache.commons.net.ftp.FTPClient()

                ftpClient.connect(host, port)
                val loginSuccess = ftpClient.login(username, password)

                if (!loginSuccess) {
                    throw Exception("FTP登录失败")
                }

                Logger.i(TAG, "✅ FTP登录成功")
                statusCallback("upgrade", "ftp_connected", 40, "FTP连接成功，开始下载...")

                // 设置传输模式
                ftpClient.enterLocalPassiveMode()
                ftpClient.setFileType(org.apache.commons.net.ftp.FTP.BINARY_FILE_TYPE)

                // 强制清理旧文件并创建本地文件
                cleanupOldApkFiles()
                val apkFileName = "update_v${version}.apk"
                val localFile = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkFileName)
                val outputStream = localFile.outputStream()

                // 下载文件
                val downloadSuccess = ftpClient.retrieveFile(remotePath, outputStream)
                outputStream.close()

                ftpClient.logout()
                ftpClient.disconnect()

                if (!downloadSuccess) {
                    throw Exception("FTP文件下载失败")
                }

                Logger.i(TAG, "✅ FTP下载完成")
                statusCallback("upgrade", "download_completed", 70, "FTP下载完成")

                // 验证下载的文件
                val validationError = validateApkFile(localFile)
                if (validationError != null) {
                    Logger.e(TAG, "❌ 下载的APK验证失败: $validationError")
                    statusCallback("upgrade", "download_failed", 0, "下载的APK验证失败: $validationError")
                } else {
                    Logger.i(TAG, "✅ 下载的APK验证通过")
                    statusCallback("upgrade", "download_verified", 80, "APK验证通过，准备安装")
                    installApk(version)
                }

            } catch (e: Exception) {
                Logger.e(TAG, "❌ FTP下载失败: ${e.message}")
                statusCallback("upgrade", "download_failed", 0, "FTP下载失败: ${e.message}")
            }
        }.start()
    }

    override fun updateServerConfig(configData: JSONObject) {
        // 升级命令处理器不处理配置更新
        Logger.w(TAG, "⚠️ 升级命令处理器不处理配置更新")
    }

    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success)
    }
}
