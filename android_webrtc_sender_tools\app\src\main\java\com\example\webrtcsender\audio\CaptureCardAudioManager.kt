package com.example.webrtcsender.audio

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioDeviceInfo
import android.media.AudioManager
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.example.webrtcsender.utils.Logger

/**
 * 采集卡音频管理器
 * 专门用于检测和管理来自采集卡的音频输入
 */
class CaptureCardAudioManager(private val context: Context) {
    
    companion object {
        private const val TAG = "CaptureCardAudioManager"
    }
    
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    
    /**
     * 检测可用的采集卡音频设备
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun detectCaptureCardDevices(): List<AudioDeviceInfo> {
        val captureCardDevices = mutableListOf<AudioDeviceInfo>()
        
        try {
            val audioDevices = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)
            
            for (device in audioDevices) {
                Logger.i(TAG, "检测到音频设备: ${device.productName} - 类型: ${getDeviceTypeName(device.type)}")
                
                // 检查是否是采集卡相关的设备
                if (isCaptureCardDevice(device)) {
                    captureCardDevices.add(device)
                    Logger.i(TAG, "✅ 发现采集卡音频设备: ${device.productName}")
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "检测采集卡设备失败", e)
        }
        
        return captureCardDevices
    }
    
    /**
     * 判断是否是采集卡设备
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun isCaptureCardDevice(device: AudioDeviceInfo): Boolean {
        val deviceType = device.type
        val productName = device.productName?.toString()?.lowercase() ?: ""
        
        // 检查设备类型
        val isCaptureCardType = when (deviceType) {
            AudioDeviceInfo.TYPE_USB_DEVICE -> true           // USB设备
            AudioDeviceInfo.TYPE_USB_ACCESSORY -> true        // USB配件
            AudioDeviceInfo.TYPE_HDMI -> true                 // HDMI设备
            AudioDeviceInfo.TYPE_LINE_ANALOG -> true          // 模拟线路输入
            AudioDeviceInfo.TYPE_LINE_DIGITAL -> true         // 数字线路输入
            else -> false
        }
        
        // 检查产品名称中的关键词
        val captureCardKeywords = listOf(
            "capture", "card", "hdmi", "usb", "video", "elgato", 
            "avermedia", "blackmagic", "magewell", "razer", "obs"
        )
        
        val hasKeyword = captureCardKeywords.any { keyword ->
            productName.contains(keyword)
        }
        
        Logger.d(TAG, "设备分析: $productName - 类型匹配: $isCaptureCardType, 关键词匹配: $hasKeyword")
        
        return isCaptureCardType || hasKeyword
    }
    
    /**
     * 获取设备类型名称
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun getDeviceTypeName(type: Int): String {
        return when (type) {
            AudioDeviceInfo.TYPE_USB_DEVICE -> "USB设备"
            AudioDeviceInfo.TYPE_USB_ACCESSORY -> "USB配件"
            AudioDeviceInfo.TYPE_HDMI -> "HDMI"
            AudioDeviceInfo.TYPE_LINE_ANALOG -> "模拟线路"
            AudioDeviceInfo.TYPE_LINE_DIGITAL -> "数字线路"
            AudioDeviceInfo.TYPE_BUILTIN_MIC -> "内置麦克风"
            AudioDeviceInfo.TYPE_WIRED_HEADSET -> "有线耳机"
            AudioDeviceInfo.TYPE_BLUETOOTH_A2DP -> "蓝牙A2DP"
            else -> "未知类型($type)"
        }
    }
    
    /**
     * 创建指定采集卡设备的AudioRecord
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun createCaptureCardAudioRecord(device: AudioDeviceInfo): AudioRecord? {
        try {
            val sampleRate = getSupportedSampleRate(device)
            val channelConfig = android.media.AudioFormat.CHANNEL_IN_STEREO
            val audioFormat = android.media.AudioFormat.ENCODING_PCM_16BIT
            
            val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Logger.e(TAG, "无法获取缓冲区大小")
                return null
            }

            // 检查录音权限
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
                Logger.e(TAG, "缺少录音权限")
                return null
            }

            val audioRecord = try {
                AudioRecord.Builder()
                .setAudioSource(MediaRecorder.AudioSource.UNPROCESSED)
                .setAudioFormat(
                    android.media.AudioFormat.Builder()
                        .setSampleRate(sampleRate)
                        .setChannelMask(channelConfig)
                        .setEncoding(audioFormat)
                        .build()
                )
                .setBufferSizeInBytes(bufferSize * 2)
                .build()
            } catch (e: SecurityException) {
                Logger.e(TAG, "创建AudioRecord失败，权限被拒绝", e)
                return null
            } catch (e: Exception) {
                Logger.e(TAG, "创建AudioRecord失败", e)
                return null
            }

            // 设置首选输入设备为采集卡
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val success = audioRecord.setPreferredDevice(device)
                Logger.i(TAG, "设置首选设备 ${device.productName}: $success")
            }
            
            Logger.i(TAG, "✅ 成功创建采集卡AudioRecord: ${device.productName}")
            return audioRecord
            
        } catch (e: Exception) {
            Logger.e(TAG, "创建采集卡AudioRecord失败", e)
            return null
        }
    }
    
    /**
     * 获取设备支持的采样率
     */
    @RequiresApi(Build.VERSION_CODES.M)
    private fun getSupportedSampleRate(device: AudioDeviceInfo): Int {
        val preferredRates = intArrayOf(48000, 44100, 32000, 22050, 16000, 8000)
        
        for (rate in preferredRates) {
            if (device.sampleRates?.contains(rate) == true) {
                Logger.i(TAG, "使用采样率: $rate Hz")
                return rate
            }
        }
        
        // 如果没有找到支持的采样率，使用默认值
        Logger.w(TAG, "未找到支持的采样率，使用默认48000 Hz")
        return 48000
    }
    
    /**
     * 获取推荐的采集卡设备
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun getRecommendedCaptureCardDevice(): AudioDeviceInfo? {
        val devices = detectCaptureCardDevices()
        
        if (devices.isEmpty()) {
            Logger.w(TAG, "未检测到采集卡设备")
            return null
        }
        
        // 优先选择USB设备
        val usbDevice = devices.find { it.type == AudioDeviceInfo.TYPE_USB_DEVICE }
        if (usbDevice != null) {
            Logger.i(TAG, "推荐USB采集卡: ${usbDevice.productName}")
            return usbDevice
        }
        
        // 其次选择HDMI设备
        val hdmiDevice = devices.find { it.type == AudioDeviceInfo.TYPE_HDMI }
        if (hdmiDevice != null) {
            Logger.i(TAG, "推荐HDMI采集卡: ${hdmiDevice.productName}")
            return hdmiDevice
        }
        
        // 最后选择第一个可用设备
        val firstDevice = devices.first()
        Logger.i(TAG, "推荐采集卡设备: ${firstDevice.productName}")
        return firstDevice
    }
    
    /**
     * 检查是否有可用的采集卡
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun hasCaptureCardAvailable(): Boolean {
        return detectCaptureCardDevices().isNotEmpty()
    }
    
    /**
     * 获取采集卡设备信息
     */
    @RequiresApi(Build.VERSION_CODES.M)
    fun getCaptureCardInfo(): String {
        val devices = detectCaptureCardDevices()
        
        if (devices.isEmpty()) {
            return "未检测到采集卡设备"
        }
        
        val info = StringBuilder()
        info.append("检测到 ${devices.size} 个采集卡设备:\n")
        
        devices.forEachIndexed { index, device ->
            info.append("${index + 1}. ${device.productName}\n")
            info.append("   类型: ${getDeviceTypeName(device.type)}\n")
            info.append("   ID: ${device.id}\n")
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                device.sampleRates?.let { rates ->
                    info.append("   支持采样率: ${rates.joinToString(", ")} Hz\n")
                }
                device.channelCounts?.let { counts ->
                    info.append("   支持声道数: ${counts.joinToString(", ")}\n")
                }
            }
            info.append("\n")
        }
        
        return info.toString()
    }
}
