1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.android_test"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
10
11    <application
11-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:5:5-18:19
12        android:allowBackup="true"
12-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:6:9-35
13        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
13-->[androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:24:18-86
14        android:debuggable="true"
15        android:extractNativeLibs="false"
16        android:icon="@mipmap/ic_launcher"
16-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:7:9-43
17        android:label="@string/app_name"
17-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:8:9-41
18        android:roundIcon="@mipmap/ic_launcher_round"
18-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:9:9-54
19        android:supportsRtl="true"
19-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:10:9-35
20        android:theme="@style/Theme.AndroidTest" >
20-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:11:9-49
21        <activity android:name="com.example.android_test.MainActivity" >
21-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:9-17:20
21-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:19-47
22            <intent-filter>
22-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:13:13-16:29
23                <action android:name="android.intent.action.MAIN" />
23-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:17-69
23-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:25-66
24
25                <category android:name="android.intent.category.LAUNCHER" />
25-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:17-77
25-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:27-74
26            </intent-filter>
27        </activity>
28    </application>
29
30</manifest>
