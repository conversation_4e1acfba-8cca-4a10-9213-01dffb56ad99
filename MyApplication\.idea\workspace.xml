<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/ic_launcher_background.xml">
        <config>
          <theme>@style/Theme.MyApplication</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/ic_launcher_foreground.xml">
        <config>
          <theme>@style/Theme.MyApplication</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/mipmap-anydpi/ic_launcher_round.xml">
        <config>
          <theme>@style/Theme.MyApplication</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a4393159-4310-4058-9224-fabb362bd995" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/CommandDispatcher.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/CommandDispatcher.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/ExtendedCommandHandler.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/ExtendedCommandHandler.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/UpgradeCommandHandler.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/command/UpgradeCommandHandler.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/signaling/SignalingClient.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/signaling/SignalingClient.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/ui/MainActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/ui/MainActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/AutoRebootManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/AutoRebootManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/DeviceInfoReporter.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/DeviceInfoReporter.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/LogManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/LogManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/ScreenshotManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/utils/ScreenshotManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/webrtc/WebRTCClient.kt" beforeDir="false" afterPath="$PROJECT_DIR$/../android_webrtc_sender_tools/app/src/main/java/com/example/webrtcsender/webrtc/WebRTCClient.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/../app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../enhanced_signaling_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/../enhanced_signaling_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../tools/gen_seat_sql.py" beforeDir="false" afterPath="$PROJECT_DIR$/../tools/gen_seat_sql.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web/admin.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web/admin.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web/admin.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web/admin.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web/boot_monitor.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web/boot_monitor.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=3bbe432da0d29d4e)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="My Application" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Google/AndroidStudio2025.1.1/device-explorer/rockchip rk3588_s/_/sdcard/Android/data/com.example.webrtcsender/files/Music/audio_source_test_20250729_162624/audio_test_report.txt" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30LgmDCR72bLRpCSWEAZWuB8NUK" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;android.gradle.sync.needed&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settingsdialog.IDE.editor.colors.Logcat Filter&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="My_Application.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a4393159-4310-4058-9224-fabb362bd995" name="Changes" comment="" />
      <created>1753412424691</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753412424691</updated>
    </task>
    <servers />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.ironnet.myapplication">
          <value>
            <CheckInfo lastCheckTimestamp="1755242863591" />
          </value>
        </entry>
        <entry key="com.ironnet.myapplication.test">
          <value>
            <CheckInfo lastCheckTimestamp="1755242863593" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>