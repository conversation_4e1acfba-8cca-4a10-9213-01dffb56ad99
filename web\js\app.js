// WebRTC视频流观看器应用
document.addEventListener('DOMContentLoaded', () => {
    // DOM元素
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const muteBtn = document.getElementById('mute-btn');
    const signalingUrlInput = document.getElementById('signaling-url');
    const clientIdInput = document.getElementById('client-id');
    const remoteVideo = document.getElementById('remote-video');
    const videoOverlay = document.getElementById('video-overlay');
    const statusIndicator = document.getElementById('status-indicator');
    const connectionStatus = document.getElementById('connection-status');
    const statusMessages = document.getElementById('status-messages');
    const sourcesList = document.getElementById('sources-list');
    const resolutionDisplay = document.getElementById('resolution');
    const fpsDisplay = document.getElementById('fps');
    const bitrateDisplay = document.getElementById('bitrate');

    // 应用状态
    let state = {
        signalingSocket: null,
        peerConnection: null,
        clientId: null,
        selectedSource: null,
        isConnected: false,
        isPlaying: false,
        lastBitrateCalculation: {
            timestamp: 0,
            bytes: 0
        },
        statsInterval: null
    };

    // STUN和TURN服务器配置 - 接收端使用完整配置，让发送端控制连接类型
    const iceServers = [
        {urls: 'stun:8.134.131.24:3478'},
        {urls: 'stun:[2408:400d:1130:7600:d73:4f58:bd1d:539e]:3479'},
        {
            urls: 'turn:8.134.131.24:3478?transport=udp',
            username: 'admin',
            credential: 'fpwe287534'
        },
        {
            urls: 'turn:[2408:400d:1130:7600:d73:4f58:bd1d:539e]?transport=udp',
            username: 'admin',
            credential: 'fpwe287534'
        }
    ];

    // 生成随机ID
    function generateRandomId() {
        return 'viewer-' + Math.random().toString(36).substr(2, 9);
    }

    // 初始化
    function init() {
        // 设置随机ID
        if (clientIdInput.value === 'viewer-') {
            clientIdInput.value = generateRandomId();
        }

        // 设置事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        fullscreenBtn.addEventListener('click', toggleFullscreen);
        muteBtn.addEventListener('click', toggleMute);
        remoteVideo.addEventListener('loadedmetadata', onVideoLoaded);

        // 检查WebRTC支持
        if (!hasWebRTCSupport()) {
            addStatusMessage('错误: 您的浏览器不支持WebRTC', 'error');
            connectBtn.disabled = true;
        }
    }

    // 检查WebRTC支持
    function hasWebRTCSupport() {
        return !!(navigator.mediaDevices &&
                 window.RTCPeerConnection &&
                 window.RTCSessionDescription);
    }

    // 在连接到信令服务器时添加更多日志
    function connect() {
        const signalingUrl = signalingUrlInput.value;
        state.clientId = clientIdInput.value;

        if (!signalingUrl || !state.clientId) {
            addStatusMessage('错误: 请填写信令服务器URL和客户端ID', 'error');
            return;
        }

        // 更新UI状态
        connectBtn.disabled = true;
        statusIndicator.className = 'status-indicator status-connecting';
        connectionStatus.textContent = '正在连接...';
        addStatusMessage(`正在连接到信令服务器: ${signalingUrl}`);
        console.log(`尝试连接到信令服务器: ${signalingUrl}`);

        // 创建WebSocket连接
        try {
            state.signalingSocket = new WebSocket(signalingUrl);
            
            state.signalingSocket.onopen = function() {
                console.log('WebSocket连接成功');
                addStatusMessage('已连接到信令服务器', 'success');
                statusIndicator.className = 'status-indicator status-online';
                connectionStatus.textContent = '已连接';
                disconnectBtn.disabled = false;
                
                // 注册客户端
                const registerMessage = {
                    type: 'register',
                    id: state.clientId,
                    role: 'viewer'
                };
                console.log('发送注册消息:', registerMessage);
                state.signalingSocket.send(JSON.stringify(registerMessage));
                
                state.isConnected = true;
            };
            
            state.signalingSocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('收到消息:', data);
                    onSignalingMessage(event);
                } catch (error) {
                    console.error('处理消息错误:', error);
                    addStatusMessage(`处理消息错误: ${error.message}`, 'error');
                }
            };
            
            state.signalingSocket.onerror = function(error) {
                console.error("WebSocket错误:", error);
                addStatusMessage(`信令服务器错误: ${error.message || '连接失败'}`, 'error');
            };
            
            state.signalingSocket.onclose = function(event) {
                console.log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason || '未提供'}`);
                addStatusMessage('信令服务器连接已关闭');
                resetConnection();
            };
        } catch (error) {
            console.error("创建WebSocket时出错:", error);
            addStatusMessage(`连接错误: ${error.message}`, 'error');
            resetConnection();
        }
    }

    // 断开连接
    function disconnect() {
        addStatusMessage('正在断开连接...');

        // 关闭WebRTC连接
        if (state.peerConnection) {
            state.peerConnection.close();
            state.peerConnection = null;
        }

        // 关闭信令连接
        if (state.signalingSocket && state.signalingSocket.readyState === WebSocket.OPEN) {
            state.signalingSocket.close();
        }

        resetConnection();
    }

    // 重置连接状态
    function resetConnection() {
        // 清除统计信息定时器
        if (state.statsInterval) {
            clearInterval(state.statsInterval);
            state.statsInterval = null;
        }

        // 重置视频
        remoteVideo.srcObject = null;
        remoteVideo.pause();
        videoOverlay.style.display = 'flex';
        videoOverlay.querySelector('.overlay-message').textContent = '等待连接视频源...';

        // 重置状态
        state.isConnected = false;
        state.isPlaying = false;
        state.selectedSource = null;

        // 更新UI
        statusIndicator.className = 'status-indicator status-offline';
        connectionStatus.textContent = '未连接';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        fullscreenBtn.disabled = true;
        muteBtn.disabled = true;

        // 重置统计信息
        resolutionDisplay.textContent = '-';
        fpsDisplay.textContent = '- FPS';
        bitrateDisplay.textContent = '- Kbps';

        // 清空源列表
        sourcesList.innerHTML = '<div class="no-sources">暂无可用视频源</div>';
    }

    // 信令服务器连接成功
    function onSignalingOpen() {
        addStatusMessage('已连接到信令服务器', 'success');
        statusIndicator.className = 'status-indicator status-online';
        connectionStatus.textContent = '已连接';
        disconnectBtn.disabled = false;

        // 注册客户端
        state.signalingSocket.send(JSON.stringify({
            type: 'register',
            id: state.clientId,
            role: 'viewer'
        }));

        state.isConnected = true;
    }

    // 处理信令消息
    function onSignalingMessage(event) {
        const data = JSON.parse(event.data);

        switch (data.type) {
            case 'registered':
                addStatusMessage(`注册成功，ID: ${data.id}`);
                // 获取可用源列表
                state.signalingSocket.send(JSON.stringify({
                    type: 'list'
                }));
                break;

            case 'client_list':
                updateSourcesList(data.sources || []);
                break;

            case 'client_joined':
                if (data.is_source) {
                    addStatusMessage(`新视频源加入: ${data.id}`);
                    // 刷新源列表
                    state.signalingSocket.send(JSON.stringify({
                        type: 'list'
                    }));
                }
                break;

            case 'client_left':
                if (data.was_source) {
                    addStatusMessage(`视频源离开: ${data.id}`);
                    // 如果是当前选中的源，断开连接
                    if (state.selectedSource === data.id) {
                        stopStream();
                        videoOverlay.style.display = 'flex';
                        videoOverlay.querySelector('.overlay-message').textContent = '视频源已断开连接';
                    }
                    // 刷新源列表
                    state.signalingSocket.send(JSON.stringify({
                        type: 'list'
                    }));
                }
                break;

            case 'offer':
                console.log('收到offer，来自:', data.from);
                console.log('Offer SDP:', data.sdp.substring(0, 100) + '...');  // 只打印前100个字符
                handleOffer(data);
                break;

            case 'answer':
                console.log('收到answer，来自:', data.from);
                console.log('Answer SDP:', data.sdp.substring(0, 100) + '...');  // 只打印前100个字符
        
                handleAnswer(data);
                break;

            case 'candidate':
                console.log('收到ICE候选，来自:', data.from);
                console.log('Candidate:', data.candidate);
                handleCandidate(data);
                break;

            default:
                addStatusMessage(`收到未知类型消息: ${data.type}`);
        }
    }

    // 信令服务器错误
    function onSignalingError(error) {
        addStatusMessage(`信令服务器错误: ${error.message}`, 'error');
        resetConnection();
    }

    // 信令服务器连接关闭
    function onSignalingClose() {
        addStatusMessage('信令服务器连接已关闭');
        resetConnection();
    }

    // 更新视频源列表
    function updateSourcesList(sources) {
        if (sources.length === 0) {
            sourcesList.innerHTML = '<div class="no-sources">暂无可用视频源</div>';
            return;
        }

        sourcesList.innerHTML = '';
        sources.forEach(source => {
            const sourceItem = document.createElement('div');
            sourceItem.className = 'source-item';
            if (state.selectedSource === source.id) {
                sourceItem.classList.add('active');
            }

            sourceItem.innerHTML = `
                <div class="source-name">${source.name || source.id}</div>
                <div class="source-info">${source.description || '无描述'}</div>
            `;

            sourceItem.addEventListener('click', () => {
                connectToSource(source.id);
            });

            sourcesList.appendChild(sourceItem);
        });
    }

    // 连接到视频源
    function connectToSource(sourceId) {
        if (state.selectedSource === sourceId) {
            return; // 已经连接到该源
        }

        // 如果已经有连接，先停止
        if (state.peerConnection) {
            stopStream();
        }

        state.selectedSource = sourceId;
        addStatusMessage(`正在连接到视频源: ${sourceId}`);

        // 更新UI
        const sourceItems = document.querySelectorAll('.source-item');
        sourceItems.forEach(item => {
            item.classList.remove('active');
            if (item.querySelector('.source-name').textContent === sourceId) {
                item.classList.add('active');
            }
        });

        // 显示等待消息
        videoOverlay.style.display = 'flex';
        videoOverlay.querySelector('.overlay-message').textContent = '正在连接视频源...';

        // 创建对等连接
        createPeerConnection(sourceId);
    }

    // 创建对等连接
    function createPeerConnection(targetId) {
        addStatusMessage('🔄 创建WebRTC连接');
        console.log('创建PeerConnection，ICE服务器:', iceServers);

        // 创建RTCPeerConnection - 使用完整ICE服务器配置，让发送端控制连接类型
        state.peerConnection = new RTCPeerConnection({
            iceServers: iceServers
        });

        // 设置事件处理
        state.peerConnection.onicecandidate = event => {
            if (event.candidate) {
                sendIceCandidate(targetId, event.candidate);
            }
        };

        state.peerConnection.ontrack = event => {
            if (event.streams && event.streams[0]) {
                remoteVideo.srcObject = event.streams[0];
            }
        };

        state.peerConnection.oniceconnectionstatechange = () => {
            addStatusMessage(`ICE连接状态: ${state.peerConnection.iceConnectionState}`);

            if (state.peerConnection.iceConnectionState === 'connected' ||
                state.peerConnection.iceConnectionState === 'completed') {
                // 连接成功
                addStatusMessage('✅ WebRTC连接成功建立');

                videoOverlay.style.display = 'none';
                fullscreenBtn.disabled = false;
                muteBtn.disabled = false;

                // 开始收集统计信息
                startStatsCollection();
            } else if (state.peerConnection.iceConnectionState === 'failed' ||
                       state.peerConnection.iceConnectionState === 'disconnected' ||
                       state.peerConnection.iceConnectionState === 'closed') {
                // 连接失败或断开 - 发送端会自动重试，接收端只需要等待
                addStatusMessage(`❌ 连接状态: ${state.peerConnection.iceConnectionState}`);
                videoOverlay.style.display = 'flex';
                videoOverlay.querySelector('.overlay-message').textContent = 'WebRTC连接失败或断开，等待重连...';
                fullscreenBtn.disabled = true;
                muteBtn.disabled = true;

                // 停止收集统计信息
                if (state.statsInterval) {
                    clearInterval(state.statsInterval);
                    state.statsInterval = null;
                }
            }
        };

        // 创建数据通道
        const dataChannel = state.peerConnection.createDataChannel('control');
        dataChannel.onopen = () => {
            addStatusMessage('数据通道已打开');
        };
        dataChannel.onmessage = event => {
            addStatusMessage(`收到数据通道消息: ${event.data}`);
        };

        // 创建offer
        state.peerConnection.createOffer()
            .then(offer => {
                return state.peerConnection.setLocalDescription(offer);
            })
            .then(() => {
                // 发送offer
                state.signalingSocket.send(JSON.stringify({
                    type: 'offer',
                    target: targetId,
                    sdp: state.peerConnection.localDescription.sdp
                }));
                addStatusMessage(`已发送offer到 ${targetId}`);
            })
            .catch(error => {
                addStatusMessage(`创建offer错误: ${error.message}`, 'error');
            });
    }

    // 处理offer
    function handleOffer(data) {
        if (!state.peerConnection) {
            addStatusMessage('收到offer，但未创建对等连接', 'warning');
            return;
        }

        const offer = new RTCSessionDescription({
            type: 'offer',
            sdp: data.sdp
        });

        state.peerConnection.setRemoteDescription(offer)
            .then(() => {
                return state.peerConnection.createAnswer();
            })
            .then(answer => {
                return state.peerConnection.setLocalDescription(answer);
            })
            .then(() => {
                // 发送answer
                state.signalingSocket.send(JSON.stringify({
                    type: 'answer',
                    target: data.from,
                    sdp: state.peerConnection.localDescription.sdp
                }));
                addStatusMessage(`已发送answer到 ${data.from}`);
            })
            .catch(error => {
                addStatusMessage(`处理offer错误: ${error.message}`, 'error');
            });
    }

    // 处理answer
    function handleAnswer(data) {
        if (!state.peerConnection) {
            addStatusMessage('收到answer，但未创建对等连接', 'warning');
            return;
        }

        const answer = new RTCSessionDescription({
            type: 'answer',
            sdp: data.sdp
        });

        state.peerConnection.setRemoteDescription(answer)
            .then(() => {
                addStatusMessage('已设置远程描述');
            })
            .catch(error => {
                addStatusMessage(`处理answer错误: ${error.message}`, 'error');
            });
    }

    // 处理ICE候选
    function handleCandidate(data) {
        if (!state.peerConnection) {
            addStatusMessage('收到ICE候选，但未创建对等连接', 'warning');
            return;
        }

        const candidate = new RTCIceCandidate(data.candidate);

        state.peerConnection.addIceCandidate(candidate)
            .catch(error => {
                addStatusMessage(`添加ICE候选错误: ${error.message}`, 'error');
            });
    }

    // 发送ICE候选
    function sendIceCandidate(targetId, candidate) {
        if (!state.signalingSocket || state.signalingSocket.readyState !== WebSocket.OPEN) {
            return;
        }

        state.signalingSocket.send(JSON.stringify({
            type: 'candidate',
            target: targetId,
            candidate: candidate.toJSON()
        }));
    }

    // 停止流
    function stopStream() {
        if (remoteVideo.srcObject) {
            const tracks = remoteVideo.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            remoteVideo.srcObject = null;
        }

        if (state.peerConnection) {
            state.peerConnection.close();
            state.peerConnection = null;
        }

        state.selectedSource = null;
        state.isPlaying = false;

        // 停止收集统计信息
        if (state.statsInterval) {
            clearInterval(state.statsInterval);
            state.statsInterval = null;
        }

        // 重置统计信息
        resolutionDisplay.textContent = '-';
        fpsDisplay.textContent = '- FPS';
        bitrateDisplay.textContent = '- Kbps';

        // 禁用控制按钮
        fullscreenBtn.disabled = true;
        muteBtn.disabled = true;
    }

    // 视频加载完成
    function onVideoLoaded() {
        state.isPlaying = true;
        addStatusMessage('视频流已加载', 'success');
    }

    // 切换全屏
    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            if (remoteVideo.requestFullscreen) {
                remoteVideo.requestFullscreen();
            } else if (remoteVideo.webkitRequestFullscreen) {
                remoteVideo.webkitRequestFullscreen();
            } else if (remoteVideo.mozRequestFullScreen) {
                remoteVideo.mozRequestFullScreen();
            } else if (remoteVideo.msRequestFullscreen) {
                remoteVideo.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    // 切换静音
    function toggleMute() {
        if (remoteVideo.srcObject) {
            const audioTracks = remoteVideo.srcObject.getAudioTracks();
            audioTracks.forEach(track => {
                track.enabled = !track.enabled;
            });

            if (audioTracks.length > 0 && !audioTracks[0].enabled) {
                muteBtn.innerHTML = '<svg viewBox="0 0 24 24"><path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/></svg>';
            } else {
                muteBtn.innerHTML = '<svg viewBox="0 0 24 24"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/></svg>';
            }
        }
    }

    // 开始收集统计信息
    function startStatsCollection() {
        if (state.statsInterval) {
            clearInterval(state.statsInterval);
        }

        state.lastBitrateCalculation = {
            timestamp: Date.now(),
            bytes: 0
        };

        state.statsInterval = setInterval(() => {
            if (!state.peerConnection) {
                return;
            }

            state.peerConnection.getStats(null).then(stats => {
                stats.forEach(report => {
                    if (report.type === 'inbound-rtp' && report.kind === 'video') {
                        // 分辨率
                        if (report.frameWidth && report.frameHeight) {
                            resolutionDisplay.textContent = `${report.frameWidth}x${report.frameHeight}`;
                        }

                        // 帧率
                        if (report.framesPerSecond) {
                            fpsDisplay.textContent = `${Math.round(report.framesPerSecond)} FPS`;
                        }

                        // 比特率
                        if (report.bytesReceived) {
                            const now = Date.now();
                            const bytes = report.bytesReceived;
                            const elapsed = now - state.lastBitrateCalculation.timestamp;

                            if (elapsed > 0) {
                                const bytesPerSec = 8 * (bytes - state.lastBitrateCalculation.bytes) / elapsed;
                                const kbps = Math.round(bytesPerSec * 1000);
                                bitrateDisplay.textContent = `${kbps} Kbps`;

                                state.lastBitrateCalculation = {
                                    timestamp: now,
                                    bytes: bytes
                                };
                            }
                        }
                    }
                });
            });
        }, 1000);
    }

    // 添加状态消息
    function addStatusMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `status-message ${type}`;
        messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;

        statusMessages.appendChild(messageElement);
        statusMessages.scrollTop = statusMessages.scrollHeight;

        // 限制消息数量
        while (statusMessages.childElementCount > 100) {
            statusMessages.removeChild(statusMessages.firstChild);
        }
    }

    // 自动设置信令服务器URL
    function autoSetSignalingUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;

        // 如果是域名 sling.91jdcd.com
        if (host === 'sling.91jdcd.com') {
            // 使用 /ws/ 路径，这样Nginx可以正确代理
            signalingUrlInput.value = `${protocol}//${host}/ws/`;
        } else {
            // 其他情况使用默认端口
            const port = 28765; // 默认WebSocket端口
            signalingUrlInput.value = `${protocol}//${host}:${port}`;
        }
    }

    // 初始化应用
    autoSetSignalingUrl();
    init();
});





