@echo off
chcp 65001 > nul
echo Starting video sender...

REM Check if the video source is accessible
echo Checking video source...
curl -s -I http://192.168.3.168:80/0.mp4 > nul
if %errorlevel% neq 0 (
    echo Warning: Cannot access video source at http://192.168.3.168:80/0.mp4
    echo Trying webcam or test video source...
    python sender.py --video 0 --id video-stream
) else (
    echo Video source is accessible
    python sender.py --video http://192.168.3.168:80/0.mp4 --id video-stream
)

REM If the program exits with an error, try the test video source
if %errorlevel% neq 0 (
    echo Trying test video source...
    python sender.py --video test --id video-stream
)
