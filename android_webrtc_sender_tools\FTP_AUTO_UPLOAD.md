# APK自动上传到FTP功能

## 功能描述

实现了编译后自动上传APK包到FTP服务器的功能，支持多种使用场景。

## FTP服务器配置

- **服务器地址**: *************:21
- **用户名**: testota
- **密码**: YM3GDxWHXGedfykY
- **上传目录**: /webrtc_sender_builds/
- **文件命名**: webrtc_sender.apk (固定文件名)

## 可用任务

### 1. 仅上传APK
```bash
./gradlew uploadToFtp
```
- 编译debug版本APK
- 上传到FTP服务器
- 使用固定文件名覆盖上传

### 2. 编译并上传
```bash
./gradlew buildAndUpload
```
- 编译debug版本APK
- 自动上传到FTP服务器
- 显示上传结果和文件信息

### 3. 编译、上传并安装
```bash
./gradlew buildUploadAndInstall
```
- 编译debug版本APK
- 上传到FTP服务器
- 安装到所有连接的Android设备

### 4. 现有任务（保持不变）
```bash
./gradlew installToAllDevices      # 编译并安装到所有设备
./gradlew cleanInstallToAllDevices # 清除数据并重新安装
./gradlew installAndRun            # 安装到指定设备
```

## 上传过程示例

```
📦 准备上传APK到FTP服务器...
📁 APK路径: C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\build\outputs\apk\debug\app-debug.apk
🔗 连接到FTP服务器: *************:21
✅ FTP登录成功
📁 创建远程目录: /webrtc_sender_builds/
📁 切换到远程目录: /webrtc_sender_builds/
⬆️ 开始上传文件: webrtc_sender.apk
✅ 文件上传成功!
🌐 FTP地址: ftp://*************/webrtc_sender_builds/webrtc_sender.apk
📊 文件大小: 45.67 MB
🎉 FTP上传完成!
```

## 技术实现

### 依赖库
```kotlin
buildscript {
    dependencies {
        classpath("commons-net:commons-net:3.9.0")
    }
}
```

### 核心功能
```kotlin
tasks.register("uploadToFtp") {
    dependsOn(":app:assembleDebug")
    
    doLast {
        // FTP服务器配置
        val ftpServer = "*************"
        val ftpPort = 21
        val ftpUser = "testota"
        val ftpPassword = "YM3GDxWHXGedfykY"
        val remotePath = "/webrtc_sender_builds/"
        
        // 使用固定的文件名
        val remoteFileName = "webrtc_sender.apk"
        
        // FTP上传逻辑
        val ftpClient = org.apache.commons.net.ftp.FTPClient()
        ftpClient.connect(ftpServer, ftpPort)
        ftpClient.login(ftpUser, ftpPassword)
        ftpClient.enterLocalPassiveMode()
        ftpClient.setFileType(org.apache.commons.net.ftp.FTP.BINARY_FILE_TYPE)
        
        // 上传文件
        val inputStream = apkFile.inputStream()
        val uploadSuccess = ftpClient.storeFile(remoteFileName, inputStream)
        inputStream.close()
        
        ftpClient.logout()
        ftpClient.disconnect()
    }
}
```

## 文件命名规则

### 固定文件名
- **文件名**: webrtc_sender.apk
- **特点**: 每次上传都会覆盖之前的文件

### 命名优势
1. **简洁性**: 固定的文件名，便于记忆和访问
2. **最新版本**: 始终保持最新编译的版本
3. **节省空间**: 不会积累大量历史文件
4. **便于集成**: 固定URL便于自动化下载

## 错误处理

### 常见错误及解决方案

1. **APK文件不存在**
   ```
   错误: APK文件不存在: /path/to/app-debug.apk
   解决: 确保先执行编译任务
   ```

2. **FTP连接失败**
   ```
   错误: FTP上传失败: Connection refused
   解决: 检查网络连接和FTP服务器状态
   ```

3. **FTP登录失败**
   ```
   错误: FTP登录失败
   解决: 检查用户名和密码是否正确
   ```

4. **目录权限问题**
   ```
   错误: 无法切换到远程目录
   解决: 检查FTP用户是否有目录访问权限
   ```

## 使用建议

### 开发流程
1. **日常开发**: 使用 `./gradlew installToAllDevices`
2. **测试版本**: 使用 `./gradlew buildAndUpload`
3. **发布版本**: 使用 `./gradlew buildUploadAndInstall`

### 自动化集成
可以在CI/CD流程中使用：
```bash
# 在构建脚本中
./gradlew buildAndUpload
```

### 版本管理
- FTP服务器始终保持最新版本
- 每次上传会覆盖之前的文件
- 固定URL便于自动化下载: ftp://*************/webrtc_sender_builds/webrtc_sender.apk

## 安全注意事项

1. **密码安全**: FTP密码已硬编码，生产环境建议使用环境变量
2. **网络安全**: FTP传输未加密，建议在安全网络环境使用
3. **访问控制**: 确保FTP服务器有适当的访问控制

## 扩展功能

### 可能的改进
1. **SFTP支持**: 使用加密传输
2. **多服务器**: 支持上传到多个FTP服务器
3. **版本清理**: 自动删除旧版本
4. **上传通知**: 上传完成后发送通知

现在编译后会自动上传APK到FTP服务器，方便版本管理和分发！
