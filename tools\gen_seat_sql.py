# 批量生成 fa_game_seat 插入SQL

def gen_sql(game_ids, seat_count=4):
    sql_tpl = "INSERT INTO `zhougame`.`fa_game_seat` (`game_id`, `number`, `status`, `createtime`, `updatetime`, `is_delete`, `multiplier`) VALUES ({gid}, {num}, {status}, NULL, {updt}, 0, {multi});"
    result = []
    import time
    now = int(time.time())
    for gid in game_ids:
        # 规则：number=1,status=0,multiplier=10.00; 其余status=1,multiplier=1.00
        result.append(sql_tpl.format(gid=gid, num=1, status=0, updt=now, multi="1.00"))
        for n in range(2, seat_count+1):
            result.append(sql_tpl.format(gid=gid, num=n, status=1, updt=now, multi="1.00"))
    return result

if __name__ == "__main__":
    # 直接在此处填写 game_id，每行一个字符串
    game_id_str = """
107711
107675
107676
107677
107705
107706
107707
107708
107709
107674
107659
107710
107666
107667
107668
107669
107670
107671
107672
107673
"""
    game_ids = [line.strip() for line in game_id_str.strip().splitlines() if line.strip()]
    # 设置每个game_id生成多少条记录（座位数），范围1-10
    seat_count = 1  # 可改为1-10
    if not (1 <= seat_count <= 10):
        raise ValueError("seat_count 必须在1-10之间")
    sqls = gen_sql(game_ids, seat_count=seat_count)
    for sql in sqls:
        print(sql)
    print(f"共生成SQL {len(sqls)} 条，每个game_id {seat_count} 条")
