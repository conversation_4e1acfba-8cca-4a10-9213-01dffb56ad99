<variant
    name="release"
    package="com.example.android_test"
    minSdkVersion="26"
    targetSdkVersion="30"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-7.0.2;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin"
        resDirectories="src\main\res"
        assetsDirectories="src\main\assets"/>
    <sourceProvider
        manifest="src\release\AndroidManifest.xml"
        javaDirectories="src\release\java;src\release\kotlin"
        resDirectories="src\release\res"
        assetsDirectories="src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifest="src\test\AndroidManifest.xml"
        javaDirectories="src\test\java;src\test\kotlin"
        resDirectories="src\test\res"
        assetsDirectories="src\test\assets"
        unitTest="true"/>
    <sourceProvider
        manifest="src\testRelease\AndroidManifest.xml"
        javaDirectories="src\testRelease\java;src\testRelease\kotlin"
        resDirectories="src\testRelease\res"
        assetsDirectories="src\testRelease\assets"
        unitTest="true"/>
  </testSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\release\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.example.android_test"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\aidl_source_output_dir\release\out;build\generated\source\buildConfig\release;build\generated\renderscript_source_output_dir\release\out"
      generatedResourceFolders="build\generated\res\rs\release;build\generated\res\resValues\release">
  </mainArtifact>
  <testArtifact>
  </testArtifact>
</variant>
