package com.example.webrtcsender.command

import android.content.Context
import com.example.webrtcsender.utils.*
import com.example.webrtcsender.webrtc.WebRTCManager
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * 扩展命令处理器
 * 处理新增的命令：设置自动启动游戏、开关日志输出、下载日志、截屏等
 */
class ExtendedCommandHandler(
    private val context: Context,
    private val webrtcManager: WebRTCManager,
    private val responseCallback: (String, Boolean, String?) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "ExtendedCommandHandler"
    }
    
    private val handlerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * 执行控制命令
     */
    override fun executeControlCommand(command: String, params: JSONObject): Boolean {
        Logger.i(TAG, "🔧 ExtendedCommandHandler 执行命令: '$command'")
        Logger.d(TAG, "🔍 命令参数: $params")

        val result = when (command) {
            "set_auto_start_game" -> {
                Logger.d(TAG, "✅ 处理自动启动游戏命令")
                handleSetAutoStartGame(params)
            }
            "toggle_log_display" -> {
                Logger.d(TAG, "✅ 处理切换日志显示命令")
                handleToggleLogDisplay(params)
            }
            "download_logs" -> {
                Logger.d(TAG, "✅ 处理下载日志命令")
                handleDownloadLogs(params)
            }
            "take_screenshot" -> {
                Logger.d(TAG, "✅ 处理截屏命令")
                handleTakeScreenshot(params)
            }
            else -> {
                Logger.w(TAG, "❓ ExtendedCommandHandler 未知命令: '$command'")
                Logger.w(TAG, "🔍 支持的命令: set_auto_start_game, toggle_log_display, download_logs, take_screenshot")
                false
            }
        }

        Logger.d(TAG, "📋 ExtendedCommandHandler 命令 '$command' 处理结果: $result")
        return result
    }
    
    /**
     * 处理设置自动启动游戏命令
     */
    private fun handleSetAutoStartGame(params: JSONObject): Boolean {
        return try {
            val enabled = params.optBoolean("enabled", false)
            val packageName = params.optString("package_name", "")
            
            Logger.i(TAG, "设置自动启动游戏: enabled=$enabled, package=$packageName")
            
            // 保存设置
            val preferences = WebRTCManager.getPreferences()
            val editor = preferences.edit()
            editor.putBoolean(Constants.PREF_AUTO_START_GAME, enabled)
            if (packageName.isNotEmpty()) {
                editor.putString(Constants.PREF_AUTO_START_GAME_PACKAGE, packageName)
            }
            editor.apply()
            
            statusCallback("set_auto_start_game", "自动启动游戏设置已更新", 100, "success")
            true
            
        } catch (e: Exception) {
            Logger.e(TAG, "设置自动启动游戏失败", e)
            false
        }
    }
    
    /**
     * 处理开关日志输出命令
     */
    private fun handleToggleLogDisplay(params: JSONObject): Boolean {
        return try {
            val enabled = params.optBoolean("enabled", false)

            Logger.i(TAG, "切换日志显示: enabled=$enabled")

            // 保存设置到SharedPreferences（持久化）
            val preferences = WebRTCManager.getPreferences()
            val editor = preferences.edit()
            editor.putBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, enabled)
            editor.apply()

            Logger.i(TAG, "日志显示设置已保存到SharedPreferences: $enabled")

            // 更新Logger的显示状态
            Logger.setDisplayEnabled(enabled)

            statusCallback("toggle_log_display", "日志显示已${if (enabled) "开启" else "关闭"}，设置已永久保存", 100, "success")

            // 验证设置是否保存成功
            val savedValue = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
            Logger.i(TAG, "验证保存的日志显示设置: $savedValue")

            true

        } catch (e: Exception) {
            Logger.e(TAG, "切换日志显示失败", e)
            false
        }
    }
    
    /**
     * 处理下载日志命令
     */
    private fun handleDownloadLogs(params: JSONObject): Boolean {
        return try {
            val uploadMethod = params.optString("method", "ftp") // ftp 或 http
            val deviceId = WebRTCManager.getSenderId()
            
            Logger.i(TAG, "开始下载日志: method=$uploadMethod, deviceId=$deviceId")
            
            // 异步上传日志
            handlerScope.launch {
                try {
                    statusCallback("download_logs", "正在收集日志文件...", 10, "processing")

                    val result = when (uploadMethod) {
                        "ftp" -> LogManager.uploadLogToFtp(context, deviceId)
                        "http" -> LogManager.uploadLogToHttp(context, deviceId)
                        else -> {
                            Logger.w(TAG, "未知的上传方法: $uploadMethod，使用FTP")
                            LogManager.uploadLogToFtp(context, deviceId)
                        }
                    }

                    if (result.success) {
                        statusCallback("download_logs", "日志上传成功", 100, "success")
                        responseCallback("download_logs", true, result.filename)

                        // 清理旧日志文件
                        LogManager.cleanOldLogFiles(context)
                    } else {
                        statusCallback("download_logs", "日志上传失败", 100, "error")
                        responseCallback("download_logs", false, null)
                    }
                    
                } catch (e: Exception) {
                    Logger.e(TAG, "上传日志异常", e)
                    statusCallback("download_logs", "日志上传异常: ${e.message}", 100, "error")
                    responseCallback("download_logs", false, null)
                }
            }
            
            true
            
        } catch (e: Exception) {
            Logger.e(TAG, "处理下载日志命令失败", e)
            false
        }
    }
    
    /**
     * 处理截屏命令
     */
    private fun handleTakeScreenshot(params: JSONObject): Boolean {
        return try {
            // 如果没有提供request_id，生成一个默认的
            val requestId = params.optString("request_id", "").ifEmpty {
                "screenshot_${System.currentTimeMillis()}"
            }
            val deviceId = WebRTCManager.getSenderId()
            val serverDomain = params.optString("room_server_domain", "")

            Logger.i(TAG, "开始截屏: requestId=$requestId, deviceId=$deviceId, serverDomain=$serverDomain")
            
            // 异步截屏
            handlerScope.launch {
                try {
                    statusCallback("take_screenshot", "正在截取屏幕画面...", 10, "processing")

                    // 获取MediaProjection
                    val mediaProjection = WebRTCManager.getMediaProjection()

                    val fullUrl = ScreenshotManager.captureScreenshot(
                        context, mediaProjection, deviceId, requestId, serverDomain
                    )

                    if (fullUrl != null) {
                        statusCallback("take_screenshot", "截屏成功", 100, "success")

                        // 发送截屏结果到信令服务器
                        sendScreenshotResult(requestId, fullUrl, true, "截屏成功")

                    } else {
                        statusCallback("take_screenshot", "截屏失败", 100, "error")
                        sendScreenshotResult(requestId, "", false, "截屏失败")
                    }

                } catch (e: Exception) {
                    Logger.e(TAG, "截屏异常", e)
                    statusCallback("take_screenshot", "截屏异常: ${e.message}", 100, "error")
                    sendScreenshotResult(requestId, "", false, "截屏异常: ${e.message}")
                }
            }
            
            true
            
        } catch (e: Exception) {
            Logger.e(TAG, "处理截屏命令失败", e)
            false
        }
    }
    
    /**
     * 发送截屏结果到信令服务器
     */
    private fun sendScreenshotResult(requestId: String, fullUrl: String, success: Boolean, message: String) {
        try {
            val result = mapOf(
                "type" to "screenshot_result",
                "request_id" to requestId,
                "success" to success,
                "full_url" to fullUrl,
                "message" to message,
                "timestamp" to (System.currentTimeMillis() / 1000)
            )
            
            // 通过WebRTC管理器发送消息
            WebRTCManager.signalingClient?.sendMessage(result)
            
            Logger.i(TAG, "截屏结果已发送: requestId=$requestId, success=$success")
            
        } catch (e: Exception) {
            Logger.e(TAG, "发送截屏结果失败", e)
        }
    }
    
    /**
     * 执行升级（不支持）
     */
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        Logger.w(TAG, "扩展命令处理器不支持升级功能")
    }

    /**
     * 更新服务器配置（不支持）
     */
    override fun updateServerConfig(configData: JSONObject) {
        Logger.w(TAG, "扩展命令处理器不支持服务器配置更新")
    }

    /**
     * 发送命令响应
     */
    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success, null)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        handlerScope.cancel()
    }
}
