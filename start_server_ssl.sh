#!/bin/bash
# 启动带SSL的信令服务器
# 证书路径


ps -aux | grep enhanced_signaling_server |  awk '{print $2}' | xargs kill -9


SSL_CERT="/www/server/panel/vhost/cert/sling.91jdcd.com/fullchain.pem"
SSL_KEY="/www/server/panel/vhost/cert/sling.91jdcd.com/privkey.pem"

# 检查证书文件是否存在
if [ ! -f "$SSL_CERT" ] || [ ! -f "$SSL_KEY" ]; then
    echo "错误: SSL证书文件不存在"
    echo "请确保已经获取了SSL证书，或者修改脚本中的证书路径"
    exit 1
fi

# 启动信令服务器
python3 /www/wwwroot/sj/enhanced_signaling_server.py \
    --ws-host 0.0.0.0 \
    --ws-port 28765 \
    --http-host 0.0.0.0 \
    --http-port 28080 \
    --web-dir /www/wwwroot/sj/web \
    # --use-ssl \
    # --ssl-cert "$SSL_CERT" \
    # --ssl-key "$SSL_KEY"

    
