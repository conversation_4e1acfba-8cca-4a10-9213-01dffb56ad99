#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信令服务器依赖检查脚本
检查并安装所需的Python模块
"""

import sys
import subprocess
import importlib

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print("Python版本: {}.{}.{}".format(version.major, version.minor, version.micro))
    
    if version.major == 2 and version.minor < 7:
        print("⚠️ 警告: Python版本过低，建议使用Python 2.7+或Python 3.x")
    elif version.major == 2:
        print("✅ Python 2.7 - 兼容")
    elif version.major >= 3:
        print("✅ Python 3.x - 推荐")
    
    return True

def check_module(module_name, package_name=None):
    """检查模块是否已安装"""
    if package_name is None:
        package_name = module_name
    
    try:
        importlib.import_module(module_name)
        print("✅ {} - 已安装".format(module_name))
        return True
    except ImportError:
        print("❌ {} - 未安装".format(module_name))
        return False

def install_module(package_name):
    """安装Python模块"""
    print("正在安装 {}...".format(package_name))
    
    try:
        # 尝试使用pip安装
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print("✅ {} 安装成功".format(package_name))
        return True
    except subprocess.CalledProcessError:
        try:
            # 如果上面失败，尝试直接使用pip
            subprocess.check_call(["pip", "install", package_name])
            print("✅ {} 安装成功".format(package_name))
            return True
        except subprocess.CalledProcessError as e:
            print("❌ {} 安装失败: {}".format(package_name, e))
            return False

def main():
    """主函数"""
    print("=== 信令服务器依赖检查 ===")
    print()
    
    # 检查Python版本
    check_python_version()
    print()
    
    # 需要检查的模块列表
    required_modules = [
        ("asyncio", None),      # Python 3.4+ 内置，Python 2.7 需要安装
        ("websockets", "websockets"),
        ("json", None),         # 内置模块
        ("logging", None),      # 内置模块
        ("pymysql", "PyMySQL"),
        ("hashlib", None),      # 内置模块
        ("hmac", None),         # 内置模块
        ("uuid", None),         # 内置模块
        ("datetime", None),     # 内置模块
    ]
    
    print("检查必需模块:")
    missing_modules = []
    
    for module_name, package_name in required_modules:
        if not check_module(module_name, package_name):
            if package_name:  # 只有非内置模块才需要安装
                missing_modules.append(package_name)
    
    print()
    
    # 安装缺失的模块
    if missing_modules:
        print("需要安装的模块: {}".format(", ".join(missing_modules)))
        print()
        
        for package in missing_modules:
            install_module(package)
        
        print()
        print("重新检查模块:")
        for module_name, package_name in required_modules:
            if package_name in missing_modules:
                check_module(module_name, package_name)
    else:
        print("✅ 所有必需模块都已安装")
    
    print()
    
    # 特殊处理asyncio（Python 2.7）
    if sys.version_info.major == 2:
        print("Python 2.7 特殊检查:")
        if not check_module("asyncio"):
            print("⚠️ Python 2.7 不支持asyncio，信令服务器可能无法正常运行")
            print("建议升级到Python 3.x")
        else:
            print("✅ asyncio可用（可能通过第三方包）")
    
    print()
    print("=== 检查完成 ===")
    print()
    print("启动信令服务器:")
    if sys.version_info.major >= 3:
        print("python3 enhanced_signaling_server.py --ws-port 8765 --http-port 28080")
    else:
        print("python enhanced_signaling_server.py --ws-port 8765 --http-port 28080")
    
    print()
    print("如果仍有问题:")
    print("1. 检查网络连接")
    print("2. 尝试使用管理员权限")
    print("3. 考虑使用虚拟环境")
    print("4. 升级到Python 3.x（推荐）")

if __name__ == "__main__":
    main()
