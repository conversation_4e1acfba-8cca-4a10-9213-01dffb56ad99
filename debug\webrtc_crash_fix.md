# WebRTC崩溃修复方案

## 🎯 问题分析

从崩溃日志分析，WebRTC native库在`libjingle_peerconnection_so.so`中崩溃，这通常由以下原因引起：

### 1. 崩溃位置
- 线程: `worker_thread`
- 库: `libjingle_peerconnection_so.so`
- 可能原因: 视频编码器初始化或配置问题

### 2. 设备信息
- 设备: `rockchip rk30sdk rk30board`
- Android版本: `Android 14 (API 34)`
- 芯片: `rk3576_u`
- 当前配置: 摄像头ID 125, 576p, 60fps, H264

## 🔧 修复方案

### 方案1: 降低视频参数
```kotlin
// 在Constants.kt中添加安全配置
const val SAFE_VIDEO_RESOLUTION = "480p"  // 降低分辨率
const val SAFE_VIDEO_FRAMERATE = 30       // 降低帧率
const val SAFE_VIDEO_BITRATE = 1500       // 降低码率
```

### 方案2: 使用软件编码器
```kotlin
// 在WebRTCClient.kt中强制使用软件编码器
private fun createVideoEncoderFactory(): VideoEncoderFactory {
    return SoftwareVideoEncoderFactory()  // 强制软件编码
}
```

### 方案3: 摄像头兼容性检查
```kotlin
// 在WebRTCClient.kt中添加摄像头检查
private fun isCameraSafe(cameraId: String): Boolean {
    try {
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        val characteristics = cameraManager.getCameraCharacteristics(cameraId)
        
        // 检查是否支持所需的分辨率和帧率
        val streamConfigMap = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
        val outputSizes = streamConfigMap?.getOutputSizes(MediaFormat.MIMETYPE_VIDEO_AVC)
        
        return outputSizes != null && outputSizes.isNotEmpty()
    } catch (e: Exception) {
        Logger.e(TAG, "摄像头检查失败: $e")
        return false
    }
}
```

### 方案4: 渐进式初始化
```kotlin
// 分步初始化，避免一次性加载过多资源
private fun initializeWebRTCGradually() {
    // 步骤1: 初始化PeerConnectionFactory
    Handler(Looper.getMainLooper()).post {
        initializePeerConnectionFactory()
        
        // 步骤2: 延迟初始化视频源
        Handler(Looper.getMainLooper()).postDelayed({
            initializeVideoSource()
            
            // 步骤3: 延迟初始化音频源
            Handler(Looper.getMainLooper()).postDelayed({
                initializeAudioSource()
            }, 1000)
        }, 1000)
    }
}
```

## 🛠️ 立即修复代码

### 1. 添加安全配置常量
```kotlin
// 在Constants.kt中添加
object SafeConfig {
    const val SAFE_VIDEO_RESOLUTION = "480p"
    const val SAFE_VIDEO_FRAMERATE = 30
    const val SAFE_VIDEO_BITRATE = 1500
    const val SAFE_CAMERA_ID = "0"  // 使用默认后置摄像头
    
    // RK3576芯片特殊配置
    const val RK3576_MAX_RESOLUTION = "720p"
    const val RK3576_MAX_FRAMERATE = 30
    const val RK3576_MAX_BITRATE = 2000
}
```

### 2. 修改WebRTCClient初始化
```kotlin
// 在WebRTCClient.kt的initialize方法中添加安全检查
fun initialize() {
    try {
        // 检查设备兼容性
        if (isRockchipDevice()) {
            Logger.w(TAG, "检测到Rockchip设备，使用安全配置")
            applySafeConfiguration()
        }
        
        // 原有初始化逻辑...
        
    } catch (e: Exception) {
        Logger.e(TAG, "WebRTC初始化失败，尝试安全模式", e)
        initializeSafeMode()
    }
}

private fun isRockchipDevice(): Boolean {
    return Build.BOARD.contains("rk", ignoreCase = true) ||
           Build.HARDWARE.contains("rk", ignoreCase = true)
}

private fun applySafeConfiguration() {
    // 应用安全配置
    currentVideoResolution = SafeConfig.SAFE_VIDEO_RESOLUTION
    currentVideoFramerate = SafeConfig.SAFE_VIDEO_FRAMERATE
    currentVideoBitrate = SafeConfig.SAFE_VIDEO_BITRATE
    currentCameraId = SafeConfig.SAFE_CAMERA_ID
}

private fun initializeSafeMode() {
    Logger.i(TAG, "启动安全模式初始化")
    
    // 使用最基本的配置
    currentVideoResolution = "360p"
    currentVideoFramerate = 15
    currentVideoBitrate = 500
    
    // 强制使用软件编码器
    useSoftwareEncoder = true
}
```

### 3. 添加编码器选择逻辑
```kotlin
private fun createVideoEncoderFactory(): VideoEncoderFactory {
    return if (useSoftwareEncoder || isRockchipDevice()) {
        Logger.i(TAG, "使用软件视频编码器")
        SoftwareVideoEncoderFactory()
    } else {
        Logger.i(TAG, "使用硬件视频编码器")
        DefaultVideoEncoderFactory(
            rootEglBase.eglBaseContext,
            true,  // enableIntelVp8Encoder
            true   // enableH264HighProfile
        )
    }
}
```

## 🔍 调试步骤

### 1. 启用详细日志
```kotlin
// 在Application类中添加
if (BuildConfig.DEBUG) {
    Logging.enableLogToDebugOutput(Logging.Severity.LS_VERBOSE)
}
```

### 2. 捕获崩溃信息
```kotlin
// 在MainActivity中添加
Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
    Logger.e("CRASH", "应用崩溃: ${exception.message}", exception)
    
    // 保存崩溃信息到文件
    saveCrashLog(exception)
    
    // 重启应用到安全模式
    restartInSafeMode()
}
```

### 3. 摄像头测试
```kotlin
// 测试摄像头125是否可用
private fun testCamera125() {
    try {
        val cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager
        val cameraIds = cameraManager.cameraIdList
        
        Logger.i(TAG, "可用摄像头: ${cameraIds.joinToString()}")
        
        if ("125" in cameraIds) {
            val characteristics = cameraManager.getCameraCharacteristics("125")
            val streamConfigMap = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            val outputSizes = streamConfigMap?.getOutputSizes(MediaFormat.MIMETYPE_VIDEO_AVC)
            
            Logger.i(TAG, "摄像头125支持的分辨率: ${outputSizes?.contentToString()}")
        } else {
            Logger.w(TAG, "摄像头125不可用，回退到摄像头0")
        }
    } catch (e: Exception) {
        Logger.e(TAG, "摄像头测试失败", e)
    }
}
```

## 🚀 紧急修复

如果需要立即修复，建议：

1. **降低视频参数**:
   - 分辨率: 576p → 480p
   - 帧率: 60fps → 30fps
   - 码率: 3000kbps → 1500kbps

2. **更换摄像头**:
   - 当前: 摄像头125
   - 建议: 摄像头0（默认后置）

3. **使用软件编码**:
   - 禁用硬件编码器
   - 强制使用软件编码器

4. **检查内存**:
   - 当前可用内存: 2152MB
   - 建议释放不必要的资源

## 📝 配置建议

针对RK3576设备的推荐配置：
```json
{
  "video_resolution": "480p",
  "video_framerate": 30,
  "video_bitrate": 1500,
  "video_codec": "H264",
  "camera_id": "0",
  "use_software_encoder": true,
  "audio_source": "microphone"
}
```

这些修复应该能解决WebRTC崩溃问题！
