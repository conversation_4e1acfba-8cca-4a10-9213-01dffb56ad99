-- 发送端设备信息表
CREATE TABLE IF NOT EXISTS sender_device_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- 设备标识信息
    sender_id VARCHAR(255) NOT NULL UNIQUE COMMENT '发送端ID（唯一标识）',
    cpu_unique_id VARCHAR(255) DEFAULT NULL COMMENT 'CPU唯一ID',
    wechat_sn VARCHAR(255) DEFAULT NULL COMMENT '微信SN',
    motherboard_model VARCHAR(255) DEFAULT NULL COMMENT '主板型号',
    
    -- 系统信息
    android_version VARCHAR(100) DEFAULT NULL COMMENT '安卓系统版本',
    system_version VARCHAR(100) DEFAULT NULL COMMENT '系统版本',
    
    -- 存储信息（单位：MB）
    available_storage BIGINT DEFAULT NULL COMMENT '可用存储空间(MB)',
    total_storage BIGINT DEFAULT NULL COMMENT '总存储空间(MB)',
    
    -- 内存信息（单位：MB）
    total_memory BIGINT DEFAULT NULL COMMENT '总内存(MB)',
    available_memory BIGINT DEFAULT NULL COMMENT '可用内存(MB)',
    
    -- 硬件状态
    cpu_temperature FLOAT DEFAULT NULL COMMENT 'CPU温度(摄氏度)',
    
    -- 网络信息
    public_ip VARCHAR(45) DEFAULT NULL COMMENT '公网IP地址',
    public_ipv6 VARCHAR(128) DEFAULT NULL COMMENT '公网IPv6地址',
    local_ip VARCHAR(45) DEFAULT NULL COMMENT '内网IP地址',
    network_type ENUM('wired', 'wifi', 'mobile', 'unknown') DEFAULT 'unknown' COMMENT '网络类型',
    mac_address VARCHAR(17) DEFAULT NULL COMMENT '网卡MAC地址',
    
    -- 地理位置信息（从ipinfo.io获取）
    city VARCHAR(100) DEFAULT NULL COMMENT '城市',
    region VARCHAR(100) DEFAULT NULL COMMENT '省份/地区',
    country VARCHAR(10) DEFAULT NULL COMMENT '国家代码',
    location VARCHAR(50) DEFAULT NULL COMMENT '经纬度坐标',
    isp_org VARCHAR(255) DEFAULT NULL COMMENT 'ISP组织',
    postal_code VARCHAR(20) DEFAULT NULL COMMENT '邮政编码',
    timezone VARCHAR(50) DEFAULT NULL COMMENT '时区',
    
    -- 显示信息
    screen_resolution VARCHAR(20) DEFAULT NULL COMMENT '屏幕分辨率(如1920x1080)',
    screen_orientation ENUM('portrait', 'landscape', 'unknown') DEFAULT 'unknown' COMMENT '屏幕方向',

    -- 应用设置信息
    app_signaling_url VARCHAR(255) DEFAULT NULL COMMENT '信令服务器地址',
    app_sender_id VARCHAR(255) DEFAULT NULL COMMENT '应用中配置的发送端ID',
    app_video_source VARCHAR(50) DEFAULT NULL COMMENT '视频源类型(camera/screen/hdmiin/usbcapture)',
    app_video_resolution VARCHAR(20) DEFAULT NULL COMMENT '应用视频分辨率设置',
    app_video_bitrate INT DEFAULT NULL COMMENT '应用视频码率设置(kbps)',
    app_video_codec VARCHAR(20) DEFAULT NULL COMMENT '视频编码器(H264/VP8/VP9)',
    app_video_framerate INT DEFAULT NULL COMMENT '应用视频帧率设置',
    app_camera_id VARCHAR(10) DEFAULT NULL COMMENT '摄像头ID',
    app_screen_capture_quality VARCHAR(20) DEFAULT NULL COMMENT '屏幕录制质量',
    app_audio_source VARCHAR(50) DEFAULT NULL COMMENT '音频源(microphone/system/both/none)',
    app_auto_start_game BOOLEAN DEFAULT FALSE COMMENT '是否自动启动游戏',
    app_auto_start_game_package VARCHAR(255) DEFAULT NULL COMMENT '自动启动的游戏包名',
    app_log_display_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用日志显示',
    app_version VARCHAR(50) DEFAULT NULL COMMENT '应用版本',
    app_version_code BIGINT DEFAULT NULL COMMENT '应用版本号',
    app_build_type VARCHAR(20) DEFAULT NULL COMMENT '构建类型(debug/release)',

    -- 时间信息
    system_time DATETIME DEFAULT NULL COMMENT '设备系统时间',
    first_online_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次上线时间',
    last_online_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后上线时间',
    last_offline_time DATETIME DEFAULT NULL COMMENT '最后离线时间',
    last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '信息最后更新时间',
    
    -- 状态信息
    is_online BOOLEAN DEFAULT TRUE COMMENT '是否在线',
    heartbeat_count BIGINT DEFAULT 0 COMMENT '心跳计数',
    
    -- 索引
    INDEX idx_sender_id (sender_id(191)),  -- 限制索引长度
    INDEX idx_last_online_time (last_online_time),
    INDEX idx_is_online (is_online),
    INDEX idx_public_ip (public_ip(45))    -- 限制索引长度
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发送端设备信息表';

-- 创建设备状态历史表（可选，用于记录状态变化历史）
CREATE TABLE IF NOT EXISTS sender_status_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id VARCHAR(255) NOT NULL COMMENT '发送端ID',
    status_type ENUM('online', 'offline', 'info_update') NOT NULL COMMENT '状态类型',
    event_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '事件时间',
    details JSON DEFAULT NULL COMMENT '详细信息（JSON格式）',
    
    INDEX idx_sender_id (sender_id(191)),  -- 限制索引长度
    INDEX idx_event_time (event_time),
    INDEX idx_status_type (status_type),

    FOREIGN KEY (sender_id) REFERENCES sender_device_info(sender_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发送端状态历史表';

-- 插入示例数据（可选）
INSERT INTO sender_device_info (
    sender_id, cpu_unique_id, wechat_sn, motherboard_model,
    android_version, system_version, available_storage, total_storage,
    total_memory, available_memory, cpu_temperature,
    public_ip, local_ip, network_type, mac_address,
    city, region, country, location, isp_org,
    screen_resolution, screen_orientation, system_time
) VALUES (
    'gamev-example001',
    'CPU123456789',
    'WX_SN_001',
    'Qualcomm SM8350',
    'Android 13',
    'MIUI 14.0.1',
    15360,  -- 15GB 可用存储
    65536,  -- 64GB 总存储
    8192,   -- 8GB 总内存
    4096,   -- 4GB 可用内存
    45.5,   -- CPU温度
    '*************',
    '*************',
    'wifi',
    '00:11:22:33:44:55',
    'Shenzhen',
    'Guangdong',
    'CN',
    '22.5455,114.0683',
    'AS4134 CHINANET-BACKBONE',
    '1920x1080',
    'landscape',
    NOW()
) ON DUPLICATE KEY UPDATE
    last_online_time = NOW(),
    last_update_time = NOW(),
    is_online = TRUE;
