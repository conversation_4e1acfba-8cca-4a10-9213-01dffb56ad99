# 日志显示持久化测试说明

## 功能说明

日志显示状态现在支持持久化保存，具体行为如下：

### 默认行为
- **首次安装**: 日志显示默认关闭
- **后续启动**: 保持上次设置的状态

### 持久化机制
1. 日志显示状态保存在 `SharedPreferences` 中
2. 键名: `PREF_LOG_DISPLAY_ENABLED`
3. 应用启动时自动加载上次保存的状态
4. 通过命令修改状态时立即保存到 `SharedPreferences`

## 测试步骤

### 测试1: 首次安装默认状态
1. 全新安装应用
2. 启动应用
3. **预期结果**: 日志显示关闭（控制台无日志输出）

### 测试2: 开启日志显示
1. 通过信令服务器发送命令开启日志显示:
   ```json
   {
     "type": "control_command",
     "command": "toggle_log_display",
     "params": {
       "enabled": true
     }
   }
   ```
2. **预期结果**: 
   - 立即开始显示日志
   - 收到确认消息："日志显示已开启，设置已永久保存"

### 测试3: 重启应用验证持久化
1. 完全关闭应用
2. 重新启动应用
3. **预期结果**: 日志显示仍然开启（能看到启动日志）

### 测试4: 关闭日志显示
1. 通过信令服务器发送命令关闭日志显示:
   ```json
   {
     "type": "control_command",
     "command": "toggle_log_display",
     "params": {
       "enabled": false
     }
   }
   ```
2. **预期结果**: 
   - 立即停止显示日志
   - 收到确认消息："日志显示已关闭，设置已永久保存"

### 测试5: 重启验证关闭状态
1. 完全关闭应用
2. 重新启动应用
3. **预期结果**: 日志显示保持关闭状态

### 测试6: 系统重启验证
1. 重启Android设备
2. 启动应用
3. **预期结果**: 日志显示状态保持上次设置的状态

## 技术实现细节

### 加载时机
1. **Application.onCreate()**: 最早加载，确保整个应用生命周期都有正确状态
2. **MainActivity.initWebRTC()**: 双重保险，确保UI层也有正确状态
3. **FirstInstallConfigManager**: 处理首次安装的默认设置

### 保存时机
- 通过 `toggle_log_display` 命令修改时立即保存
- 使用 `SharedPreferences.Editor.apply()` 异步保存

### 验证机制
- 每次设置后会验证保存是否成功
- 日志中会记录设置的加载和保存过程

## 关键代码位置

1. **Logger.kt**: 
   - `setDisplayEnabled()`: 设置显示状态
   - `loadDisplaySettingFromPreferences()`: 从SharedPreferences加载

2. **ExtendedCommandHandler.kt**:
   - `handleToggleLogDisplay()`: 处理切换命令并保存设置

3. **WebRTCSenderApp.kt**:
   - 应用启动时加载设置

4. **FirstInstallConfigManager.kt**:
   - 首次安装时设置默认值

## 注意事项

1. 日志显示状态一旦被开启，将永久保持开启状态，直到手动关闭
2. 设置保存在应用的私有存储中，卸载应用会清除设置
3. 多进程环境下SharedPreferences可能有延迟，但本应用是单进程应用
4. 日志显示状态不影响日志文件的写入，只影响控制台输出

## 故障排除

如果日志显示状态没有正确持久化，检查以下几点：

1. 确认SharedPreferences写入权限
2. 检查应用是否被系统强制停止
3. 查看日志中的设置加载和保存记录
4. 验证Constants中的键名是否正确
