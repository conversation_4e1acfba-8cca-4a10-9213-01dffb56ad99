package com.example.webrtcsender.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.graphics.Color
import android.media.projection.MediaProjection
import android.os.*
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.example.webrtcsender.R
import com.example.webrtcsender.ui.MainActivity
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import kotlinx.coroutines.*
import org.webrtc.*
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 线上线下服务台服务，负责在后台运行WebRTC连接
 */
class WebRTCSenderService : Service() {
    companion object {
        private const val TAG = "WebRTCSenderService"

        // 服务动作
        const val ACTION_START_SERVICE = "com.example.webrtcsender.START_SERVICE"
        const val ACTION_STOP_SERVICE = "com.example.webrtcsender.STOP_SERVICE"
        const val ACTION_START_SCREEN_CAPTURE = "com.example.webrtcsender.START_SCREEN_CAPTURE"
        const val ACTION_STOP_SCREEN_CAPTURE = "com.example.webrtcsender.STOP_SCREEN_CAPTURE"

        // 广播动作
        const val ACTION_SERVICE_STATUS = Constants.ACTION_SERVICE_STATUS
        const val ACTION_CONNECTION_STATUS = Constants.ACTION_CONNECTION_STATUS

        // 广播Extra
        const val EXTRA_SERVICE_STATUS = Constants.EXTRA_SERVICE_STATUS
        const val EXTRA_CONNECTION_STATUS = Constants.EXTRA_CONNECTION_STATUS
        const val EXTRA_ERROR_MESSAGE = Constants.EXTRA_ERROR_MESSAGE
        const val EXTRA_VIEWER_COUNT = Constants.EXTRA_VIEWER_COUNT
    }

    // 服务绑定器
    private val binder = LocalBinder()

    // 协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 屏幕捕获
    private var mediaProjection: MediaProjection? = null
    private var screenCapturer: VideoCapturer? = null
    private var videoSource: VideoSource? = null



    // 状态更新定时器
    private var statusUpdateExecutor: ScheduledExecutorService? = null

    // 服务状态
    private var isRunning = false
    private var isCapturing = false

    /**
     * 本地绑定器
     */
    inner class LocalBinder : Binder() {
        fun getService(): WebRTCSenderService = this@WebRTCSenderService
    }

    override fun onCreate() {
        super.onCreate()
        Logger.i(TAG, "服务已创建")

        // 创建通知通道
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.i(TAG, "服务已启动: ${intent?.action}")

        when (intent?.action) {
            ACTION_START_SERVICE -> {
                startForegroundService()
                startWebRTC()
            }
            ACTION_STOP_SERVICE -> {
                // 完全停止服务时，需要停止屏幕录制
                Logger.i(TAG, "🎥 [服务停止] 完全停止服务，停止屏幕录制")
                stopWebRTCCompletely()
                stopSelf()
            }
            ACTION_START_SCREEN_CAPTURE -> {
                startScreenCapture()
            }
            ACTION_STOP_SCREEN_CAPTURE -> {
                stopScreenCapture()
            }
        }

        return START_STICKY
    }



    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onDestroy() {
        Logger.i(TAG, "服务已销毁")

        stopWebRTC()
        serviceScope.cancel()

        super.onDestroy()
    }

    /**
     * 创建通知通道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                Constants.SERVICE_CHANNEL_ID,
                Constants.SERVICE_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "线上线下服务台服务通知"
                lightColor = Color.BLUE
                setSound(null, null)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        Logger.i(TAG, "启动前台服务")

        val notificationIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, Constants.SERVICE_CHANNEL_ID)
            .setContentTitle("线上线下服务台")
            .setContentText("正在运行...")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                Constants.SERVICE_NOTIFICATION_ID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION
            )
        } else {
            startForeground(Constants.SERVICE_NOTIFICATION_ID, notification)
        }

        isRunning = true
        broadcastServiceStatus("running")
    }

    /**
     * 启动WebRTC
     */
    private fun startWebRTC() {
        Logger.i(TAG, "启动WebRTC")

        try {
            // 检查WebRTC管理器是否已初始化
            if (!WebRTCManager.isInitialized()) {
                Logger.w(TAG, "WebRTC管理器未初始化，尝试初始化")

                // 初始化WebRTC管理器
                val listener = ServiceWebRTCListener()
                WebRTCManager.initialize(this, listener)
            }

            // 连接到信令服务器
            if (!WebRTCManager.isConnected()) {
                WebRTCManager.connectToSignalingServer()
            }

            // 启动状态更新定时器
            //startStatusUpdateTimer()
        } catch (e: Exception) {
            Logger.e(TAG, "启动WebRTC失败", e)
            broadcastServiceStatus("error", "启动WebRTC失败: ${e.message}")
        }
    }

    /**
     * 停止WebRTC
     */
    private fun stopWebRTC() {
        Logger.i(TAG, "停止WebRTC")

        try {
            // 🔧 修复：屏幕录制应该独立运行，不因WebRTC连接断开而停止
            // 只有在明确要求停止服务时才停止屏幕捕获
            Logger.i(TAG, "🎥 [屏幕录制独立] 保持屏幕录制运行，仅断开WebRTC连接")

            // 断开信令服务器连接
            WebRTCManager.disconnectFromSignalingServer()

            // 关闭所有对等连接
            WebRTCManager.closeAllPeerConnections()

            // 释放WebRTC资源（但不影响屏幕录制）
            WebRTCManager.release()

            // 停止状态更新定时器
            stopStatusUpdateTimer()

            // 停止前台服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                @Suppress("DEPRECATION")
                stopForeground(true)
            }

            isRunning = false
            // 注意：不设置 isCapturing = false，保持屏幕录制状态
            broadcastServiceStatus("stopped")
        } catch (e: Exception) {
            Logger.e(TAG, "停止WebRTC失败", e)
            broadcastServiceStatus("error", "停止WebRTC失败: ${e.message}")
        }
    }

    /**
     * 完全停止WebRTC和屏幕录制
     */
    private fun stopWebRTCCompletely() {
        Logger.i(TAG, "🎥 [完全停止] 停止WebRTC和屏幕录制")

        try {
            // 停止屏幕捕获
            if (isCapturing) {
                Logger.i(TAG, "🎥 [完全停止] 停止屏幕捕获")
                stopScreenCapture()
            }

            // 断开信令服务器连接
            WebRTCManager.disconnectFromSignalingServer()

            // 关闭所有对等连接
            WebRTCManager.closeAllPeerConnections()

            // 完全释放WebRTC资源（包括屏幕录制）
            WebRTCManager.releaseCompletely()

            // 停止状态更新定时器
            stopStatusUpdateTimer()

            // 停止前台服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                @Suppress("DEPRECATION")
                stopForeground(true)
            }

            isRunning = false
            isCapturing = false
            broadcastServiceStatus("stopped")
        } catch (e: Exception) {
            Logger.e(TAG, "完全停止WebRTC失败", e)
            broadcastServiceStatus("error", "完全停止WebRTC失败: ${e.message}")
        }
    }

    /**
     * 启动屏幕捕获
     */
    private fun startScreenCapture() {
        Logger.i(TAG, "启动屏幕捕获")

        if (isCapturing) {
            Logger.w(TAG, "屏幕捕获已经在运行")
            return
        }

        try {
            // 获取媒体投影
            mediaProjection = WebRTCManager.getMediaProjection()

            if (mediaProjection == null) {
                Logger.e(TAG, "媒体投影为空")
                broadcastServiceStatus("error", "媒体投影为空")
                return
            }

            // 获取屏幕尺寸
            val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val displayMetrics = DisplayMetrics()

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val display = windowManager.currentWindowMetrics
                displayMetrics.widthPixels = display.bounds.width()
                displayMetrics.heightPixels = display.bounds.height()
            } else {
                @Suppress("DEPRECATION")
                windowManager.defaultDisplay.getMetrics(displayMetrics)
            }

            // 获取分辨率
            val resolution = Constants.VIDEO_RESOLUTIONS[WebRTCManager.getVideoResolution()]
                ?: Constants.VIDEO_RESOLUTIONS["720p"]!!

            // 创建屏幕捕获器
            screenCapturer = createScreenCapturer(mediaProjection!!, resolution.first, resolution.second)

            // 创建视频源
            videoSource = WebRTCManager.webRTCClient?.createScreenCaptureVideoSource(screenCapturer!!)

            // 只使用共享模式设置视频源，避免冲突
            WebRTCManager.webRTCClient?.setSharedVideoSource(videoSource!!)
            Logger.i(TAG, "屏幕录制视频源已设置 (共享模式)")

            // 检查音频权限
            val hasAudioPermission = checkAudioPermission()
            Logger.i(TAG, "音频权限状态: $hasAudioPermission")

            // 创建音频源
            val audioSource = if (hasAudioPermission) {
                WebRTCManager.webRTCClient?.createAudioSource()
            } else {
                Logger.w(TAG, "没有音频权限，跳过音频源创建")
                null
            }

            // 设置音频源（如果不为null）
            if (audioSource != null) {
                WebRTCManager.webRTCClient?.setLocalAudioSource(audioSource)
                Logger.d(TAG, "已设置音频源: ${WebRTCManager.getAudioSource()}")
            } else {
                Logger.d(TAG, "音频源为null，不设置音频")
            }

            isCapturing = true
            broadcastServiceStatus("capturing")
        } catch (e: Exception) {
            Logger.e(TAG, "启动屏幕捕获失败", e)
            broadcastServiceStatus("error", "启动屏幕捕获失败: ${e.message}")
        }
    }

    /**
     * 创建屏幕捕获器
     */
    private fun createScreenCapturer(
        mediaProjection: MediaProjection,
        width: Int,
        height: Int
    ): VideoCapturer {
        Logger.d(TAG, "创建屏幕捕获器: $width x $height")

        // 获取媒体投影数据
        val mediaProjectionData = WebRTCManager.getMediaProjectionData()
        if (mediaProjectionData == null) {
            throw IllegalStateException("媒体投影数据为空")
        }

        return SurfaceTextureHelper.create(
            "ScreenCapturerThread",
            WebRTCManager.webRTCClient?.getEglContext()
        ).let { helper ->
            ScreenCapturerAndroid(
                mediaProjectionData,
                object : MediaProjection.Callback() {
                    override fun onStop() {
                        Logger.w(TAG, "⚠️ [MediaProjection] 媒体投影被外部应用停止（如scrcpy关闭）")
                        serviceScope.launch(Dispatchers.Main) {
                            // 简化处理：直接重启服务
                            handleMediaProjectionStoppedSimple()
                        }
                    }
                }
            )
        }
    }

    /**
     * 简化处理MediaProjection停止 - 直接重启服务
     */
    private fun handleMediaProjectionStoppedSimple() {
        Logger.w(TAG, "🔄 [简化恢复] MediaProjection停止，准备重启服务")

        try {
            // 检查当前是否在屏幕录制模式
            val currentVideoSource = WebRTCManager.getVideoSourceType()
            if (currentVideoSource != "screen") {
                Logger.i(TAG, "🔄 [简化恢复] 当前不是屏幕录制模式，无需恢复")
                return
            }

            // 广播状态
            broadcastServiceStatus("media_projection_stopped")

            // 延迟3秒后重启服务
            Handler(mainLooper).postDelayed({
                restartServiceForMediaProjection()
            }, 3000)

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [简化恢复] 处理MediaProjection停止失败", e)
            broadcastServiceStatus("recovery_failed", "处理失败: ${e.message}")
        }
    }

    /**
     * 为MediaProjection恢复重启服务
     */
    private fun restartServiceForMediaProjection() {
        try {
            Logger.i(TAG, "🔄 [服务重启] 开始重启服务以恢复屏幕录制")

            // 检查MediaProjection是否仍然可用
            val mediaProjection = WebRTCManager.getMediaProjection()
            val mediaProjectionData = WebRTCManager.getMediaProjectionData()

            if (mediaProjection == null || mediaProjectionData == null) {
                Logger.w(TAG, "🔄 [服务重启] MediaProjection数据不可用，需要用户重新授权")
                broadcastServiceStatus("recovery_failed", "需要重新授权屏幕录制")
                return
            }

            // 停止当前服务
            stopWebRTC()

            // 延迟1秒后重新启动
            Handler(mainLooper).postDelayed({
                try {
                    Logger.i(TAG, "🔄 [服务重启] 重新启动屏幕录制")
                    startScreenCapture()
                    broadcastServiceStatus("screen_capture_recovered")
                } catch (e: Exception) {
                    Logger.e(TAG, "🔄 [服务重启] 重启失败", e)
                    broadcastServiceStatus("recovery_failed", "重启失败: ${e.message}")
                }
            }, 1000)

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [服务重启] 重启服务失败", e)
            broadcastServiceStatus("recovery_failed", "重启服务失败: ${e.message}")
        }
    }

    /**
     * 处理MediaProjection被外部停止的情况（复杂版本，暂时不用）
     */
    private fun handleMediaProjectionStopped() {
        Logger.w(TAG, "🔄 [MediaProjection恢复] 开始处理MediaProjection意外停止")

        try {
            // 检查当前是否在屏幕录制模式
            val currentVideoSource = WebRTCManager.getVideoSourceType()
            if (currentVideoSource != "screen") {
                Logger.i(TAG, "🔄 [MediaProjection恢复] 当前不是屏幕录制模式，无需恢复")
                return
            }

            // 检查是否有活跃的WebRTC连接
            val activeConnections = WebRTCManager.getActiveConnectionCount()
            if (activeConnections == 0) {
                Logger.i(TAG, "🔄 [MediaProjection恢复] 没有活跃连接，直接停止屏幕录制")
                stopScreenCapture()
                return
            }

            Logger.w(TAG, "🔄 [MediaProjection恢复] 检测到 $activeConnections 个活跃连接，尝试恢复屏幕录制")

            // 暂时停止当前的屏幕录制，但保持连接
            stopScreenCaptureWithoutDisconnecting()

            // 延迟一段时间后尝试重新启动屏幕录制
            Handler(mainLooper).postDelayed({
                attemptScreenCaptureRecovery()
            }, 3000) // 3秒后尝试恢复，给更多时间让系统稳定

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [MediaProjection恢复] 处理MediaProjection停止失败", e)
            // 如果恢复失败，回退到完全停止
            stopScreenCapture()
        }
    }

    /**
     * 停止屏幕捕获但不断开WebRTC连接
     */
    private fun stopScreenCaptureWithoutDisconnecting() {
        Logger.i(TAG, "🔄 [MediaProjection恢复] 停止屏幕捕获但保持WebRTC连接")

        try {
            // 停止视频捕获器
            if (screenCapturer != null) {
                try {
                    screenCapturer?.stopCapture()
                } catch (e: Exception) {
                    Logger.e(TAG, "停止视频捕获失败，忽略错误", e)
                }

                try {
                    screenCapturer?.dispose()
                } catch (e: Exception) {
                    Logger.e(TAG, "释放视频捕获器失败，忽略错误", e)
                }

                screenCapturer = null
            }

            // 释放视频源但不清理WebRTC连接
            if (videoSource != null) {
                try {
                    val source = videoSource
                    if (source != null) {
                        try {
                            val field = source.javaClass.superclass?.getDeclaredField("nativeMediaSource")
                            field?.isAccessible = true
                            val nativePtr = field?.get(source) as? Long

                            if (nativePtr != null && nativePtr != 0L) {
                                source.dispose()
                                Logger.i(TAG, "✅ 视频源已安全释放")
                            } else {
                                Logger.w(TAG, "⚠️ 视频源已经被释放，跳过重复释放")
                            }
                        } catch (reflectionException: Exception) {
                            Logger.w(TAG, "无法检查MediaSource状态，尝试直接释放")
                            source.dispose()
                        }
                    }
                } catch (e: IllegalStateException) {
                    if (e.message?.contains("MediaSource has been disposed") == true) {
                        Logger.w(TAG, "⚠️ 视频源已经被释放，跳过重复释放")
                    } else {
                        Logger.e(TAG, "释放视频源失败", e)
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "释放视频源失败，忽略错误", e)
                }
                videoSource = null
            }

            isCapturing = false
            broadcastServiceStatus("media_projection_stopped")

        } catch (e: Exception) {
            Logger.e(TAG, "停止屏幕捕获（保持连接）失败", e)
        }
    }

    /**
     * 尝试恢复屏幕录制
     */
    private fun attemptScreenCaptureRecovery() {
        Logger.i(TAG, "🔄 [MediaProjection恢复] 尝试恢复屏幕录制")

        try {
            // 检查是否还有活跃连接
            val activeConnections = WebRTCManager.getActiveConnectionCount()
            if (activeConnections == 0) {
                Logger.i(TAG, "🔄 [MediaProjection恢复] 没有活跃连接，取消恢复")
                return
            }

            // 检查信令连接状态
            if (!WebRTCManager.isConnected()) {
                Logger.w(TAG, "🔄 [MediaProjection恢复] 信令连接已断开，尝试重新连接")
                WebRTCManager.connectToSignalingServer()

                // 等待连接建立
                Handler(mainLooper).postDelayed({
                    continueScreenCaptureRecovery()
                }, 2000) // 等待2秒让信令连接建立
                return
            }

            continueScreenCaptureRecovery()

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [MediaProjection恢复] 恢复屏幕录制失败", e)
            broadcastRecoveryFailed("恢复失败: ${e.message}")
        }
    }

    /**
     * 继续屏幕录制恢复流程
     */
    private fun continueScreenCaptureRecovery() {
        try {
            Logger.i(TAG, "🔄 [MediaProjection恢复] 继续恢复流程")

            // 检查MediaProjection是否仍然可用
            val mediaProjection = WebRTCManager.getMediaProjection()
            val mediaProjectionData = WebRTCManager.getMediaProjectionData()

            if (mediaProjection == null || mediaProjectionData == null) {
                Logger.w(TAG, "🔄 [MediaProjection恢复] MediaProjection数据不可用，需要用户重新授权")
                broadcastRecoveryFailed("需要重新授权屏幕录制")
                return
            }

            Logger.i(TAG, "🔄 [MediaProjection恢复] MediaProjection数据可用，尝试重新启动屏幕录制")

            // 尝试重新启动屏幕录制
            startScreenCapture()

            Logger.i(TAG, "🔄 [MediaProjection恢复] ✅ 屏幕录制恢复成功")
            broadcastServiceStatus("screen_capture_recovered")

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [MediaProjection恢复] 继续恢复流程失败", e)
            broadcastRecoveryFailed("恢复失败: ${e.message}")
        }
    }

    /**
     * 广播恢复失败消息
     */
    private fun broadcastRecoveryFailed(reason: String) {
        Logger.w(TAG, "🔄 [MediaProjection恢复] 恢复失败: $reason")

        // 如果是MediaProjection数据不可用，尝试完全重启服务作为最后手段
        if (reason.contains("需要重新授权") || reason.contains("MediaProjection数据不可用")) {
            Logger.i(TAG, "🔄 [MediaProjection恢复] 尝试完全重启服务作为最后恢复手段")

            Handler(mainLooper).postDelayed({
                attemptFullServiceRestart()
            }, 1000)
        } else {
            broadcastServiceStatus("recovery_failed", reason)
        }
    }

    /**
     * 尝试完全重启服务
     */
    private fun attemptFullServiceRestart() {
        try {
            Logger.i(TAG, "🔄 [完全重启] 开始完全重启服务")

            // 检查是否还有活跃连接
            val activeConnections = WebRTCManager.getActiveConnectionCount()
            if (activeConnections == 0) {
                Logger.i(TAG, "🔄 [完全重启] 没有活跃连接，取消重启")
                broadcastServiceStatus("recovery_failed", "没有活跃连接")
                return
            }

            // 保存当前状态
            val currentVideoSource = WebRTCManager.getVideoSourceType()

            // 停止当前服务
            stopWebRTC()

            // 延迟重启
            Handler(mainLooper).postDelayed({
                try {
                    Logger.i(TAG, "🔄 [完全重启] 重新启动WebRTC服务")

                    // 重新连接信令服务器
                    WebRTCManager.connectToSignalingServer()

                    // 根据之前的视频源类型重新启动
                    when (currentVideoSource) {
                        "screen" -> {
                            Logger.i(TAG, "🔄 [完全重启] 重新启动屏幕录制")
                            // 检查MediaProjection是否可用
                            val mediaProjection = WebRTCManager.getMediaProjection()
                            val mediaProjectionData = WebRTCManager.getMediaProjectionData()

                            if (mediaProjection != null && mediaProjectionData != null) {
                                startScreenCapture()
                                broadcastServiceStatus("screen_capture_recovered")
                            } else {
                                broadcastServiceStatus("recovery_failed", "MediaProjection权限已失效，需要重新授权")
                            }
                        }
                        "camera" -> {
                            Logger.i(TAG, "🔄 [完全重启] 摄像头模式需要MainActivity处理")
                            // 摄像头功能由MainActivity处理，这里只是通知恢复失败
                            broadcastServiceStatus("recovery_failed", "摄像头模式需要手动重启")
                        }
                        else -> {
                            Logger.w(TAG, "🔄 [完全重启] 未知的视频源类型: $currentVideoSource")
                            broadcastServiceStatus("recovery_failed", "未知的视频源类型")
                        }
                    }

                } catch (e: Exception) {
                    Logger.e(TAG, "🔄 [完全重启] 重启服务失败", e)
                    broadcastServiceStatus("recovery_failed", "完全重启失败: ${e.message}")
                }
            }, 2000) // 2秒后重启

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [完全重启] 尝试完全重启失败", e)
            broadcastServiceStatus("recovery_failed", "完全重启失败: ${e.message}")
        }
    }

    /**
     * 停止屏幕捕获
     */
    private fun stopScreenCapture() {
        Logger.i(TAG, "停止屏幕捕获")

        if (!isCapturing) {
            Logger.w(TAG, "屏幕捕获未运行")
            return
        }

        try {
            // 停止视频捕获
            if (screenCapturer != null) {
                try {
                    // 检查SurfaceTextureHelper是否已初始化
                    val capturer = screenCapturer
                    if (capturer != null) {
                        // 使用反射检查SurfaceTextureHelper是否为null
                        val field = capturer.javaClass.getDeclaredField("surfaceTextureHelper")
                        field.isAccessible = true
                        val surfaceTextureHelper = field.get(capturer)

                        if (surfaceTextureHelper != null) {
                            screenCapturer?.stopCapture()
                        } else {
                            Logger.w(TAG, "SurfaceTextureHelper为null，跳过stopCapture")
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "检查SurfaceTextureHelper失败，尝试安全停止", e)
                    try {
                        screenCapturer?.stopCapture()
                    } catch (e2: Exception) {
                        Logger.e(TAG, "停止视频捕获失败，忽略错误", e2)
                    }
                }

                try {
                    screenCapturer?.dispose()
                } catch (e: Exception) {
                    Logger.e(TAG, "释放视频捕获器失败，忽略错误", e)
                }

                screenCapturer = null
            }

            // 释放视频源 - 添加状态检查避免重复释放
            if (videoSource != null) {
                try {
                    // 检查MediaSource是否已经被释放
                    val source = videoSource
                    if (source != null) {
                        // 使用反射检查MediaSource的状态
                        try {
                            val field = source.javaClass.superclass?.getDeclaredField("nativeMediaSource")
                            field?.isAccessible = true
                            val nativePtr = field?.get(source) as? Long

                            if (nativePtr != null && nativePtr != 0L) {
                                // MediaSource还未释放，可以安全释放
                                source.dispose()
                                Logger.i(TAG, "✅ 视频源已安全释放")
                            } else {
                                Logger.w(TAG, "⚠️ 视频源已经被释放，跳过重复释放")
                            }
                        } catch (reflectionException: Exception) {
                            // 反射失败，尝试直接释放
                            Logger.w(TAG, "无法检查MediaSource状态，尝试直接释放")
                            source.dispose()
                        }
                    }
                } catch (e: IllegalStateException) {
                    if (e.message?.contains("MediaSource has been disposed") == true) {
                        Logger.w(TAG, "⚠️ 视频源已经被释放，跳过重复释放")
                    } else {
                        Logger.e(TAG, "释放视频源失败", e)
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "释放视频源失败，忽略错误", e)
                }
                videoSource = null
            }

            // 清理视频源 - 同时清理本地和共享模式
            try {
                WebRTCManager.webRTCClient?.setLocalVideoSource(null as org.webrtc.VideoSource?)
                // 注意：共享视频源的清理由WebRTCClient的release方法处理
                Logger.i(TAG, "视频源已清理")
            } catch (e: Exception) {
                Logger.e(TAG, "清理视频源失败，忽略错误", e)
            }

            isCapturing = false
            broadcastServiceStatus("stopped_capture")
        } catch (e: Exception) {
            Logger.e(TAG, "停止屏幕捕获失败", e)
            broadcastServiceStatus("error", "停止屏幕捕获失败: ${e.message}")

            // 即使失败，也设置为未捕获状态
            isCapturing = false
        }
    }

    /**
     * 启动状态更新定时器
     */
    private fun startStatusUpdateTimer() {
        Logger.d(TAG, "启动状态更新定时器")

        statusUpdateExecutor?.shutdown()

        statusUpdateExecutor = Executors.newSingleThreadScheduledExecutor().apply {
            scheduleAtFixedRate({
                try {
                    updateStatus()
                } catch (e: Exception) {
                    Logger.e(TAG, "更新状态失败", e)
                }
            }, 0, 5, TimeUnit.SECONDS)
        }
    }

    /**
     * 停止状态更新定时器
     */
    private fun stopStatusUpdateTimer() {
        Logger.d(TAG, "停止状态更新定时器")

        statusUpdateExecutor?.shutdown()
        statusUpdateExecutor = null
    }

    /**
     * 更新状态
     */
    private fun updateStatus() {
        if (!isRunning) return

        // 获取活跃连接数
        val activeConnections = WebRTCManager.getActiveConnectionCount()

        // 获取分辨率
        val resolution = Constants.VIDEO_RESOLUTIONS[WebRTCManager.getVideoResolution()]
            ?: Constants.VIDEO_RESOLUTIONS["720p"]!!
        val resolutionStr = "${resolution.first}x${resolution.second}"

        // 更新通知
        updateNotification(activeConnections)

        // 发送状态更新
        WebRTCManager.signalingClient?.sendStatusUpdate(
            if (isCapturing) "streaming" else "ready",
            activeConnections,
            resolutionStr,
            30
        )

        // 广播连接状态
        broadcastConnectionStatus(activeConnections)
    }

    /**
     * 更新通知
     */
    private fun updateNotification(viewerCount: Int) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val notificationIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val status = if (isCapturing) "正在推流" else "已连接"
        val viewers = if (viewerCount > 0) "，$viewerCount 个观众" else ""

        val notification = NotificationCompat.Builder(this, Constants.SERVICE_CHANNEL_ID)
            .setContentTitle("线上线下服务台")
            .setContentText("$status$viewers")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()

        notificationManager.notify(Constants.SERVICE_NOTIFICATION_ID, notification)
    }

    /**
     * 广播服务状态
     */
    private fun broadcastServiceStatus(status: String, errorMessage: String? = null) {
        val intent = Intent(ACTION_SERVICE_STATUS).apply {
            putExtra(EXTRA_SERVICE_STATUS, status)
            if (errorMessage != null) {
                putExtra(EXTRA_ERROR_MESSAGE, errorMessage)
            }
        }

        sendBroadcast(intent)
    }

    /**
     * 广播连接状态
     */
    private fun broadcastConnectionStatus(viewerCount: Int) {
        val intent = Intent(ACTION_CONNECTION_STATUS).apply {
            putExtra(EXTRA_CONNECTION_STATUS, if (isCapturing) "streaming" else "connected")
            putExtra(EXTRA_VIEWER_COUNT, viewerCount)
        }

        sendBroadcast(intent)
    }

    /**
     * 是否正在运行
     */
    fun isRunning(): Boolean {
        return isRunning
    }

    /**
     * 是否正在捕获
     */
    fun isCapturing(): Boolean {
        return isCapturing
    }

    /**
     * 检查音频权限
     */
    private fun checkAudioPermission(): Boolean {
        val permission = android.Manifest.permission.RECORD_AUDIO
        val result = androidx.core.content.ContextCompat.checkSelfPermission(this, permission)
        val hasPermission = result == android.content.pm.PackageManager.PERMISSION_GRANTED

        // 简化权限检查，只检查基本权限
        Logger.i(TAG, "音频权限检查结果: $hasPermission")
        return hasPermission
    }

    /**
     * 服务WebRTC监听器
     */
    inner class ServiceWebRTCListener : WebRTCManager.WebRTCManagerListener {
        override fun onWebRTCInitialized() {
            Logger.i(TAG, "WebRTC已初始化")
        }

        override fun onSignalingConnected() {
            Logger.i(TAG, "已连接到信令服务器")
        }

        override fun onSignalingDisconnected() {
            Logger.i(TAG, "已断开与信令服务器的连接")
        }

        override fun onLocalVideoTrackAvailable(videoTrack: VideoTrack?) {
            Logger.i(TAG, "本地视频轨道可用: ${videoTrack != null}")
        }

        override fun onVideoSourceStarted(sourceType: String) {
            Logger.i(TAG, "视频源已启动: $sourceType")
        }

        override fun onVideoSourceStopped() {
            Logger.i(TAG, "视频源已停止")
        }

        override fun onVideoSourcePaused() {
            Logger.i(TAG, "视频源已暂停")
        }

        override fun onVideoSourceResumed() {
            Logger.i(TAG, "视频源已恢复")
        }

        override fun onViewerJoined(viewerId: String) {
            Logger.i(TAG, "观众已加入: $viewerId")
        }

        override fun onViewerLeft(viewerId: String) {
            Logger.i(TAG, "观众已离开: $viewerId")
        }

        override fun onPeerConnected(peerId: String) {
            Logger.i(TAG, "对等端已连接: $peerId")
        }

        override fun onPeerDisconnected(peerId: String) {
            Logger.i(TAG, "对等端已断开: $peerId")
        }

        override fun onError(message: String) {
            Logger.e(TAG, "错误: $message")
        }
    }
}
