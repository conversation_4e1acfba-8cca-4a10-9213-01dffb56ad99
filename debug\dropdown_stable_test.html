<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单稳定性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible;
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .device-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
            position: relative;
        }
        
        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
        }
        
        .device-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .control-main-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            transition: all 0.2s;
        }
        
        .control-main-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
        }
        
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }
        
        .dropdown-toggle {
            position: relative;
            z-index: 100000;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .dropdown-toggle:hover:not(.active) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .dropdown-toggle.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transform: none !important; /* 激活状态时禁用transform，防止位置变化 */
        }
        
        .dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw;
            z-index: 999999;
            display: none;
            padding: 15px;
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
            visibility: hidden;
        }
        
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
        
        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }
        
        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }
        
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000000;
            max-width: 300px;
            font-family: monospace;
        }
        
        .mouse-tracker {
            position: fixed;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000001;
            pointer-events: none;
            display: none;
        }
        
        @media (max-width: 768px) {
            .dropdown-menu {
                min-width: 250px;
                right: auto;
            }
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debugPanel">
        <strong>🔧 稳定性测试</strong><br>
        <div id="debugInfo">等待操作...</div>
    </div>
    
    <div class="mouse-tracker" id="mouseTracker"></div>

    <div class="container">
        <h1>🔧 下拉菜单稳定性测试</h1>
        
        <div class="test-info">
            <h3>修复内容</h3>
            <ul>
                <li>✅ 添加位置锁定机制，防止重复计算</li>
                <li>✅ 禁用激活状态下的hover transform</li>
                <li>✅ 添加防抖机制，防止快速连续点击</li>
                <li>✅ 鼠标移动不会触发位置重新计算</li>
                <li>✅ 下拉菜单位置稳定，不会跳跃</li>
            </ul>
            <p><strong>测试方法</strong>: 点击⚙️按钮后，快速移动鼠标，观察下拉菜单是否稳定</p>
        </div>
        
        <div class="test-grid">
            <!-- 测试设备1 -->
            <div class="device-card">
                <h3>🎮 稳定性测试设备1</h3>
                <p>测试快速鼠标移动时的稳定性</p>
                
                <div class="device-controls">
                    <button class="control-main-btn">
                        🔄 重启服务
                    </button>
                    <div class="control-dropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('stable1')">
                            ⚙️
                        </button>
                        <div class="dropdown-menu" id="dropdown-stable1">
                            <div class="dropdown-columns">
                                <div class="dropdown-section">
                                    <h4>服务控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('启动服务')">🚀 启动服务</button>
                                    <button class="dropdown-btn" onclick="testClick('停止服务')">⏹️ 停止服务</button>
                                    <button class="dropdown-btn" onclick="testClick('重启服务')">🔄 重启服务</button>
                                    <h4>视频控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('视频参数设置')">🎥 视频参数设置</button>
                                    <button class="dropdown-btn" onclick="testClick('视频流截屏')">📸 视频流截屏</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>游戏控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('游戏设置')">🎮 游戏设置</button>
                                    <h4>日志管理</h4>
                                    <button class="dropdown-btn" onclick="testClick('开启日志显示')">📝 开启日志显示</button>
                                    <button class="dropdown-btn" onclick="testClick('关闭日志显示')">🚫 关闭日志显示</button>
                                    <button class="dropdown-btn" onclick="testClick('下载日志')">📥 下载日志(FTP)</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>网络配置</h4>
                                    <button class="dropdown-btn" onclick="testClick('STUN/TURN配置')">🌐 STUN/TURN配置</button>
                                    <button class="dropdown-btn" onclick="testClick('发送网络配置')">📡 发送网络配置</button>
                                    <h4>系统控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('重启设备')">🔄 重启设备</button>
                                    <button class="dropdown-btn" onclick="testClick('升级应用')">📦 升级应用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试设备2 -->
            <div class="device-card">
                <h3>🎮 稳定性测试设备2</h3>
                <p>测试连续快速点击的稳定性</p>
                
                <div class="device-controls">
                    <button class="control-main-btn">
                        🔄 重启服务
                    </button>
                    <div class="control-dropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('stable2')">
                            ⚙️
                        </button>
                        <div class="dropdown-menu" id="dropdown-stable2">
                            <div class="dropdown-columns">
                                <div class="dropdown-section">
                                    <h4>服务控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('启动服务')">🚀 启动服务</button>
                                    <button class="dropdown-btn" onclick="testClick('停止服务')">⏹️ 停止服务</button>
                                    <button class="dropdown-btn" onclick="testClick('重启服务')">🔄 重启服务</button>
                                    <h4>视频控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('视频参数设置')">🎥 视频参数设置</button>
                                    <button class="dropdown-btn" onclick="testClick('视频流截屏')">📸 视频流截屏</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>游戏控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('游戏设置')">🎮 游戏设置</button>
                                    <h4>日志管理</h4>
                                    <button class="dropdown-btn" onclick="testClick('开启日志显示')">📝 开启日志显示</button>
                                    <button class="dropdown-btn" onclick="testClick('关闭日志显示')">🚫 关闭日志显示</button>
                                    <button class="dropdown-btn" onclick="testClick('下载日志')">📥 下载日志(FTP)</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>网络配置</h4>
                                    <button class="dropdown-btn" onclick="testClick('STUN/TURN配置')">🌐 STUN/TURN配置</button>
                                    <button class="dropdown-btn" onclick="testClick('发送网络配置')">📡 发送网络配置</button>
                                    <h4>系统控制</h4>
                                    <button class="dropdown-btn" onclick="testClick('重启设备')">🔄 重启设备</button>
                                    <button class="dropdown-btn" onclick="testClick('升级应用')">📦 升级应用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let dropdownTimeout = null;
        let mousePosition = { x: 0, y: 0 };
        let debugStats = {
            toggleCount: 0,
            positionCalculations: 0,
            mouseMovements: 0
        };

        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>最新操作:</strong> ${message}<br>
                <strong>切换次数:</strong> ${debugStats.toggleCount}<br>
                <strong>位置计算:</strong> ${debugStats.positionCalculations}<br>
                <strong>鼠标移动:</strong> ${debugStats.mouseMovements}<br>
                <small>时间: ${new Date().toLocaleTimeString()}</small>
            `;
        }

        function toggleDropdown(deviceId) {
            debugStats.toggleCount++;
            updateDebugInfo(`点击设备: ${deviceId}`);
            
            // 防抖：如果有正在进行的操作，取消它
            if (dropdownTimeout) {
                clearTimeout(dropdownTimeout);
            }
            
            // 延迟执行，防止快速连续点击
            dropdownTimeout = setTimeout(() => {
                executeToggleDropdown(deviceId);
            }, 50);
        }
        
        function executeToggleDropdown(deviceId) {
            console.log('执行切换:', deviceId);

            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${deviceId}`) {
                    console.log('关闭其他菜单:', menu.id);
                    menu.classList.remove('show');
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                    menu.style.removeProperty('opacity');
                    menu.style.removeProperty('display');
                    menu.removeAttribute('data-positioned');
                }
            });
            document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                if (!btn.onclick.toString().includes(deviceId)) {
                    btn.classList.remove('active');
                }
            });

            const dropdown = document.getElementById(`dropdown-${deviceId}`);
            const button = document.querySelector(`[onclick="toggleDropdown('${deviceId}')"]`);

            if (!dropdown || !button) {
                console.error('找不到元素:', { dropdown, button });
                return;
            }

            const isShowing = dropdown.classList.contains('show');
            console.log('当前状态:', isShowing ? '显示' : '隐藏');

            if (isShowing) {
                // 隐藏菜单
                console.log('隐藏菜单');
                dropdown.classList.remove('show');
                button.classList.remove('active');
                dropdown.style.removeProperty('left');
                dropdown.style.removeProperty('top');
                dropdown.style.removeProperty('visibility');
                dropdown.style.removeProperty('opacity');
                dropdown.style.removeProperty('display');
                dropdown.removeAttribute('data-positioned');
                updateDebugInfo(`隐藏下拉菜单: ${deviceId}`);
            } else {
                // 显示菜单
                console.log('显示菜单');

                // 先清除可能存在的定位标记
                dropdown.removeAttribute('data-positioned');

                // 计算位置
                adjustDropdownPosition(dropdown, button);

                // 显示菜单
                dropdown.classList.add('show');
                button.classList.add('active');

                updateDebugInfo(`显示下拉菜单: ${deviceId}`);
            }
        }

        function adjustDropdownPosition(dropdown, button) {
            // 如果已经定位过，不要重复计算
            if (dropdown.hasAttribute('data-positioned')) {
                console.log('已定位，跳过重复计算');
                return;
            }

            debugStats.positionCalculations++;
            console.log('开始位置计算', debugStats.positionCalculations);

            // 先完全隐藏，获取真实尺寸
            dropdown.style.setProperty('visibility', 'hidden', 'important');
            dropdown.style.setProperty('display', 'block', 'important');
            dropdown.style.setProperty('opacity', '0', 'important');

            // 强制重新渲染
            dropdown.offsetHeight;

            const buttonRect = button.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            const dropdownWidth = dropdownRect.width || 720;
            const dropdownHeight = dropdownRect.height || 400;

            console.log('尺寸信息:', {
                button: { left: buttonRect.left, top: buttonRect.top, bottom: buttonRect.bottom, right: buttonRect.right },
                dropdown: { width: dropdownWidth, height: dropdownHeight },
                window: { width: windowWidth, height: windowHeight }
            });

            // 默认位置：按钮正下方，左对齐
            let left = buttonRect.left;
            let top = buttonRect.bottom + 8;

            // 检查右边界
            if (left + dropdownWidth > windowWidth - 20) {
                left = buttonRect.right - dropdownWidth;
                console.log('调整到右对齐:', left);
            }

            // 检查左边界
            if (left < 20) {
                left = 20;
                console.log('调整到最小左边距:', left);
            }

            // 检查下边界
            if (top + dropdownHeight > windowHeight - 20) {
                top = buttonRect.top - dropdownHeight - 8;
                console.log('调整到按钮上方:', top);
            }

            // 检查上边界
            if (top < 20) {
                top = 20;
                console.log('调整到最小上边距:', top);
            }

            console.log('最终位置:', { left, top });

            // 一次性设置所有样式，避免多次重排
            dropdown.style.setProperty('left', `${left}px`, 'important');
            dropdown.style.setProperty('top', `${top}px`, 'important');
            dropdown.style.setProperty('visibility', 'visible', 'important');
            dropdown.style.setProperty('opacity', '1', 'important');

            // 立即标记已定位，防止任何重复计算
            dropdown.setAttribute('data-positioned', 'true');

            console.log('位置设置完成，已锁定');
        }
        
        function testClick(action) {
            updateDebugInfo(`点击功能: ${action}`);
        }

        // 鼠标移动跟踪（仅用于调试，不触发任何下拉菜单逻辑）
        document.addEventListener('mousemove', (event) => {
            debugStats.mouseMovements++;
            mousePosition.x = event.clientX;
            mousePosition.y = event.clientY;

            const tracker = document.getElementById('mouseTracker');
            tracker.style.left = `${event.clientX + 10}px`;
            tracker.style.top = `${event.clientY - 30}px`;
            tracker.innerHTML = `${event.clientX}, ${event.clientY}`;
            tracker.style.display = 'block';

            // 每100次鼠标移动更新一次调试信息
            if (debugStats.mouseMovements % 100 === 0) {
                updateDebugInfo(`鼠标移动: ${mousePosition.x}, ${mousePosition.y}`);
            }

            // 重要：确保鼠标移动不会触发任何下拉菜单位置重新计算
            // 这里不调用任何下拉菜单相关的函数
        });

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                console.log('点击外部，关闭所有下拉菜单');
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                    menu.style.removeProperty('opacity');
                    menu.style.removeProperty('display');
                    menu.removeAttribute('data-positioned');
                });
                document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                    btn.classList.remove('active');
                });
                updateDebugInfo('点击外部，关闭所有下拉菜单');
            }
        });

        updateDebugInfo('页面加载完成，开始稳定性测试');
    </script>
</body>
</html>
