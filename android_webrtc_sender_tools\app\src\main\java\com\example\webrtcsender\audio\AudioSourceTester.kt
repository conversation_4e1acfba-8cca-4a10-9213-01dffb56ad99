package com.example.webrtcsender.audio

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Environment
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.Logger
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 音频来源测试器
 * 遍历所有可用的音频来源，录制测试音频以确定哪个来源有声音
 */
class AudioSourceTester(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioSourceTester"
    }
    
    private var testJob: Job? = null
    private var isTestRunning = false
    
    // 所有可能的音频来源 - 完整的系统音频通道列表
    private val audioSources = listOf(
        // 🎤 麦克风相关
        MediaRecorder.AudioSource.MIC to "MIC - 主麦克风",
        MediaRecorder.AudioSource.CAMCORDER to "CAMCORDER - 摄像头麦克风",
        MediaRecorder.AudioSource.VOICE_RECOGNITION to "VOICE_RECOGNITION - 语音识别麦克风",
        MediaRecorder.AudioSource.VOICE_COMMUNICATION to "VOICE_COMMUNICATION - 通话麦克风",
        MediaRecorder.AudioSource.VOICE_UPLINK to "VOICE_UPLINK - 通话上行",
        MediaRecorder.AudioSource.VOICE_DOWNLINK to "VOICE_DOWNLINK - 通话下行",
        MediaRecorder.AudioSource.VOICE_CALL to "VOICE_CALL - 通话音频",

        // 🔊 系统音频相关
        MediaRecorder.AudioSource.DEFAULT to "DEFAULT - 默认音频源",
        MediaRecorder.AudioSource.UNPROCESSED to "UNPROCESSED - 未处理音频",

        // 📱 Android 10+ 新增
        MediaRecorder.AudioSource.VOICE_PERFORMANCE to "VOICE_PERFORMANCE - 性能优化语音",

        // 🎵 系统内部音频（需要特殊权限）
        MediaRecorder.AudioSource.REMOTE_SUBMIX to "REMOTE_SUBMIX - 系统内部音频",
        // 注意：RADIO_TUNER 和 HOTWORD 在某些Android版本中可能不可用
        // MediaRecorder.AudioSource.RADIO_TUNER to "RADIO_TUNER - 收音机调谐器",
        // MediaRecorder.AudioSource.HOTWORD to "HOTWORD - 热词检测",

        // 🔧 扩展音频源（通过反射获取）
        *getExtendedAudioSources().toTypedArray()
    )

    /**
     * 通过反射获取扩展的音频源
     */
    private fun getExtendedAudioSources(): List<Pair<Int, String>> {
        val extendedSources = mutableListOf<Pair<Int, String>>()

        try {
            // 获取MediaRecorder.AudioSource的所有常量
            val audioSourceClass = MediaRecorder.AudioSource::class.java
            val fields = audioSourceClass.declaredFields

            fields.forEach { field ->
                try {
                    if (field.type == Int::class.javaPrimitiveType &&
                        java.lang.reflect.Modifier.isStatic(field.modifiers) &&
                        java.lang.reflect.Modifier.isFinal(field.modifiers)) {

                        field.isAccessible = true
                        val value = field.getInt(null)
                        val name = field.name

                        // 检查是否已经在基础列表中
                        val alreadyExists = audioSources.any { it.first == value }
                        if (!alreadyExists) {
                            extendedSources.add(value to "$name - 扩展音频源($value)")
                            Logger.d(TAG, "🎵 [音频源发现] 发现扩展音频源: $name = $value")
                        }
                    }
                } catch (e: Exception) {
                    // 忽略无法访问的字段
                }
            }

            // 尝试一些可能的隐藏音频源ID
            val hiddenSources = listOf(
                100 to "HIDDEN_100 - 隐藏音频源100",
                101 to "HIDDEN_101 - 隐藏音频源101",
                102 to "HIDDEN_102 - 隐藏音频源102",
                1998 to "AUDIO_SOURCE_FM_TUNER - FM调谐器",
                1999 to "AUDIO_SOURCE_ECHO_REFERENCE - 回声参考"
            )

            hiddenSources.forEach { (id, desc) ->
                if (!extendedSources.any { it.first == id }) {
                    extendedSources.add(id to desc)
                }
            }

            Logger.i(TAG, "🎵 [音频源发现] 发现 ${extendedSources.size} 个扩展音频源")

        } catch (e: Exception) {
            Logger.w(TAG, "🎵 [音频源发现] 获取扩展音频源失败: ${e.message}")
        }

        return extendedSources
    }
    
    /**
     * 开始音频来源测试
     */
    fun startAudioSourceTest() {
        if (!Constants.ENABLE_AUDIO_SOURCE_TEST) {
            Logger.i(TAG, "🎵 [音频测试] 音频来源测试已禁用")
            return
        }

        if (isTestRunning) {
            Logger.w(TAG, "🎵 [音频测试] 测试已在运行中，跳过")
            return
        }

        Logger.i(TAG, "🎵 [音频测试] 开始遍历所有音频来源...")
        Logger.i(TAG, "🎵 [音频测试] 测试配置: 时长=${Constants.AUDIO_TEST_DURATION_SECONDS}秒, 采样率=${Constants.AUDIO_TEST_SAMPLE_RATE}Hz")

        isTestRunning = true
        testJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                testAllAudioSources()
            } catch (e: Exception) {
                Logger.e(TAG, "🎵 [音频测试] 测试过程中发生错误", e)
            } finally {
                isTestRunning = false
            }
        }
    }

    /**
     * 开始全面音频频道测试 - 测试所有音频源和配置组合
     */
    fun startComprehensiveAudioChannelTest() {
        if (isTestRunning) {
            Logger.w(TAG, "🎵 [音频测试] 测试已在运行中，跳过")
            return
        }

        Logger.i(TAG, "🎵 [全面音频测试] 开始全面音频频道测试...")
        Logger.i(TAG, "🎵 [全面音频测试] 将测试所有音频源 × 所有采样率 × 所有声道配置")

        isTestRunning = true
        testJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                testAllAudioChannelCombinations()
            } catch (e: Exception) {
                Logger.e(TAG, "🎵 [全面音频测试] 测试过程中发生错误", e)
            } finally {
                isTestRunning = false
            }
        }
    }
    
    /**
     * 停止音频来源测试
     */
    fun stopAudioSourceTest() {
        testJob?.cancel()
        isTestRunning = false
        Logger.i(TAG, "🎵 [音频测试] 音频来源测试已停止")
    }
    
    /**
     * 测试所有音频来源
     */
    private suspend fun testAllAudioSources() {
        val testResults = mutableListOf<AudioTestResult>()
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())

        // 创建测试结果目录
        val testDir = File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), "audio_source_test_$timestamp")
        if (!testDir.exists()) {
            testDir.mkdirs()
        }

        Logger.i(TAG, "🎵 [音频测试] 测试结果将保存到: ${testDir.absolutePath}")
        Logger.i(TAG, "🎵 [音频测试] 开始测试 ${audioSources.size} 个音频来源...")

        audioSources.forEachIndexed { index, (audioSource, description) ->
            if (!isTestRunning) return@forEachIndexed

            Logger.i(TAG, "🎵 [音频测试] [${ index + 1}/${audioSources.size}] 测试音频源: $description")

            val result = testSingleAudioSource(audioSource, description, testDir)
            testResults.add(result)

            // 测试间隔，避免资源冲突
            delay(1000)
        }

        // 生成测试报告
        generateTestReport(testResults, testDir)
    }

    /**
     * 测试所有音频频道组合 - 全面测试
     */
    private suspend fun testAllAudioChannelCombinations() {
        val testResults = mutableListOf<ComprehensiveAudioTestResult>()
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())

        // 创建测试结果目录
        val testDir = File(context.getExternalFilesDir(Environment.DIRECTORY_MUSIC), "comprehensive_audio_test_$timestamp")
        if (!testDir.exists()) {
            testDir.mkdirs()
        }

        Logger.i(TAG, "🎵 [全面音频测试] 测试结果将保存到: ${testDir.absolutePath}")

        // 定义测试配置
        val sampleRates = listOf(8000, 16000, 22050, 44100, 48000)
        val channelConfigs = listOf(
            AudioFormat.CHANNEL_IN_MONO to "单声道",
            AudioFormat.CHANNEL_IN_STEREO to "立体声"
        )
        val audioFormats = listOf(
            AudioFormat.ENCODING_PCM_16BIT to "PCM_16BIT",
            AudioFormat.ENCODING_PCM_8BIT to "PCM_8BIT"
        )

        val totalTests = audioSources.size * sampleRates.size * channelConfigs.size * audioFormats.size
        var currentTest = 0

        Logger.i(TAG, "🎵 [全面音频测试] 总测试数量: $totalTests")

        // 遍历所有组合
        audioSources.forEach { (audioSource, sourceDescription) ->
            if (!isTestRunning) return@forEach

            sampleRates.forEach { sampleRate ->
                channelConfigs.forEach { (channelConfig, channelDescription) ->
                    audioFormats.forEach { (audioFormat, formatDescription) ->
                        if (!isTestRunning) return@forEach

                        currentTest++
                        Logger.i(TAG, "🎵 [全面音频测试] [$currentTest/$totalTests] 测试组合:")
                        Logger.i(TAG, "🎵 [全面音频测试]   音频源: $sourceDescription")
                        Logger.i(TAG, "🎵 [全面音频测试]   采样率: ${sampleRate}Hz")
                        Logger.i(TAG, "🎵 [全面音频测试]   声道: $channelDescription")
                        Logger.i(TAG, "🎵 [全面音频测试]   格式: $formatDescription")

                        val result = testAudioChannelCombination(
                            audioSource, sourceDescription,
                            sampleRate, channelConfig, channelDescription,
                            audioFormat, formatDescription,
                            testDir
                        )
                        testResults.add(result)

                        // 测试间隔，避免资源冲突
                        delay(500)
                    }
                }
            }
        }

        // 生成全面测试报告
        generateComprehensiveTestReport(testResults, testDir)
    }
    
    /**
     * 测试单个音频来源 - 使用MediaRecorder录制MP3
     */
    private suspend fun testSingleAudioSource(
        audioSource: Int,
        description: String,
        testDir: File
    ): AudioTestResult = withContext(Dispatchers.IO) {

        val fileName = "audio_source_${audioSource}_${description.replace(" - ", "_").replace(" ", "_")}.mp3"
        val outputFile = File(testDir, fileName)

        var mediaRecorder: MediaRecorder? = null
        var totalBytesRecorded = 0L
        var maxAmplitude = 0
        var hasAudio = false
        var errorMessage: String? = null
        
        try {
            // 创建MediaRecorder
            mediaRecorder = MediaRecorder().apply {
                setAudioSource(audioSource)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setAudioSamplingRate(Constants.AUDIO_TEST_SAMPLE_RATE)
                setAudioChannels(2) // 立体声
                setAudioEncodingBitRate(128000) // 128kbps
                setOutputFile(outputFile.absolutePath)
            }

            // 准备和开始录制
            mediaRecorder.prepare()
            mediaRecorder.start()

            Logger.i(TAG, "🎵 [音频测试] $description: 开始录制 ${Constants.AUDIO_TEST_DURATION_SECONDS} 秒...")

            val recordDurationMs = Constants.AUDIO_TEST_DURATION_SECONDS * 1000L
            val startTime = System.currentTimeMillis()

            // 定期检查振幅
            while (System.currentTimeMillis() - startTime < recordDurationMs && isTestRunning) {
                try {
                    val amplitude = mediaRecorder.maxAmplitude
                    if (amplitude > maxAmplitude) {
                        maxAmplitude = amplitude
                    }

                    // 如果振幅超过阈值，认为有音频
                    if (amplitude > 1000) {  // 可调整的阈值
                        hasAudio = true
                    }

                    delay(100) // 每100ms检查一次振幅
                } catch (e: Exception) {
                    // 某些音频源可能不支持getMaxAmplitude
                    break
                }
            }

            // 停止录制
            mediaRecorder.stop()

            // 获取文件大小作为数据量
            totalBytesRecorded = if (outputFile.exists()) outputFile.length() else 0

            Logger.i(TAG, "🎵 [音频测试] $description: 录制完成")
            Logger.i(TAG, "🎵 [音频测试] $description: 文件大小=${totalBytesRecorded}字节, 最大振幅=$maxAmplitude, 有声音=$hasAudio")
            
        } catch (e: SecurityException) {
            errorMessage = "权限不足: ${e.message}"
            Logger.w(TAG, "🎵 [音频测试] $description: $errorMessage")
        } catch (e: IllegalStateException) {
            errorMessage = "音频源不支持: ${e.message}"
            Logger.w(TAG, "🎵 [音频测试] $description: $errorMessage")
        } catch (e: Exception) {
            errorMessage = "录制失败: ${e.message}"
            Logger.e(TAG, "🎵 [音频测试] $description: $errorMessage")
        } finally {
            try {
                mediaRecorder?.stop()
                mediaRecorder?.release()
            } catch (e: Exception) {
                Logger.w(TAG, "🎵 [音频测试] $description: 清理资源时出错: ${e.message}")
            }
        }
        
        // 如果没有录制到数据或出错，删除空文件
        if (totalBytesRecorded == 0L || errorMessage != null) {
            if (outputFile.exists()) {
                outputFile.delete()
            }
        } else {
            // 如果录制成功但没有检测到声音，仍然保留文件供手动检查
            if (!hasAudio) {
                Logger.i(TAG, "🎵 [音频测试] $description: 录制成功但未检测到声音，文件已保存供手动检查")
            }
        }
        
        AudioTestResult(audioSource, description, hasAudio, totalBytesRecorded, maxAmplitude, errorMessage)
    }
    
    // calculateAmplitude方法已移除，因为MediaRecorder有内置的getMaxAmplitude()
    
    /**
     * 生成测试报告
     */
    private fun generateTestReport(results: List<AudioTestResult>, testDir: File) {
        val reportFile = File(testDir, "audio_test_report.txt")
        
        try {
            reportFile.writeText(buildString {
                appendLine("🎵 音频来源测试报告")
                appendLine("=".repeat(50))
                appendLine("测试时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
                appendLine("测试配置: 时长=${Constants.AUDIO_TEST_DURATION_SECONDS}秒, 采样率=${Constants.AUDIO_TEST_SAMPLE_RATE}Hz")
                appendLine("输出格式: MP3 (AAC编码, 128kbps, 立体声)")
                appendLine()
                
                appendLine("📊 测试结果汇总:")
                val successCount = results.count { it.errorMessage == null }
                val audioCount = results.count { it.hasAudio }
                appendLine("总测试数: ${results.size}")
                appendLine("成功录制: $successCount")
                appendLine("检测到音频: $audioCount")
                appendLine()
                
                appendLine("🔊 有声音的音频源:")
                results.filter { it.hasAudio }.forEach { result ->
                    appendLine("✅ ${result.description}")
                    appendLine("   - 音频源ID: ${result.audioSource}")
                    appendLine("   - 文件大小: ${result.totalBytes} 字节 (${String.format("%.2f", result.totalBytes / 1024.0)} KB)")
                    appendLine("   - 最大振幅: ${result.maxAmplitude}")
                    appendLine()
                }
                
                appendLine("🔇 无声音的音频源:")
                results.filter { !it.hasAudio && it.errorMessage == null }.forEach { result ->
                    appendLine("⚪ ${result.description}")
                    appendLine("   - 音频源ID: ${result.audioSource}")
                    appendLine("   - 文件大小: ${result.totalBytes} 字节 (${String.format("%.2f", result.totalBytes / 1024.0)} KB)")
                    appendLine("   - 最大振幅: ${result.maxAmplitude}")
                    appendLine()
                }
                
                appendLine("❌ 失败的音频源:")
                results.filter { it.errorMessage != null }.forEach { result ->
                    appendLine("❌ ${result.description}")
                    appendLine("   - 音频源ID: ${result.audioSource}")
                    appendLine("   - 错误: ${result.errorMessage}")
                    appendLine()
                }
            })
            
            Logger.i(TAG, "🎵 [音频测试] 测试报告已生成: ${reportFile.absolutePath}")
            
            // 在日志中也输出简要报告
            logTestSummary(results)
            
        } catch (e: IOException) {
            Logger.e(TAG, "🎵 [音频测试] 生成测试报告失败", e)
        }
    }
    
    /**
     * 在日志中输出测试摘要
     */
    private fun logTestSummary(results: List<AudioTestResult>) {
        Logger.i(TAG, "🎵 [音频测试] ==================== 测试完成 ====================")
        
        val audioSources = results.filter { it.hasAudio }
        if (audioSources.isNotEmpty()) {
            Logger.i(TAG, "🎵 [音频测试] 🔊 发现 ${audioSources.size} 个有声音的音频源:")
            audioSources.forEach { result ->
                Logger.i(TAG, "🎵 [音频测试]   ✅ ${result.description} (振幅: ${result.maxAmplitude})")
            }
        } else {
            Logger.w(TAG, "🎵 [音频测试] ⚠️ 未发现任何有声音的音频源!")
        }
        
        val failedSources = results.filter { it.errorMessage != null }
        if (failedSources.isNotEmpty()) {
            Logger.w(TAG, "🎵 [音频测试] ❌ ${failedSources.size} 个音频源测试失败:")
            failedSources.forEach { result ->
                Logger.w(TAG, "🎵 [音频测试]   ❌ ${result.description}: ${result.errorMessage}")
            }
        }
        
        Logger.i(TAG, "🎵 [音频测试] ================================================")
    }

    /**
     * 生成全面测试报告
     */
    private fun generateComprehensiveTestReport(results: List<ComprehensiveAudioTestResult>, testDir: File) {
        val reportFile = File(testDir, "comprehensive_audio_test_report.txt")

        try {
            reportFile.writeText(buildString {
                appendLine("🎵 全面音频频道测试报告")
                appendLine("=".repeat(60))
                appendLine("测试时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
                appendLine("测试类型: 全面音频频道组合测试")
                appendLine("测试说明: 测试所有音频源 × 采样率 × 声道配置 × 音频格式的组合")
                appendLine("输出格式: MP3 (AAC编码, 自适应比特率, 根据采样率和声道数优化)")
                appendLine("测试时长: ${Constants.COMPREHENSIVE_AUDIO_TEST_DURATION_SECONDS}秒/配置")
                appendLine()

                appendLine("📊 测试结果汇总:")
                val totalTests = results.size
                val successfulTests = results.count { it.errorMessage == null }
                val audioDetectedTests = results.count { it.hasAudio }
                val failedTests = results.count { it.errorMessage != null }

                appendLine("总测试数: $totalTests")
                appendLine("成功测试: $successfulTests")
                appendLine("检测到音频: $audioDetectedTests")
                appendLine("失败测试: $failedTests")
                appendLine("成功率: ${String.format("%.1f", successfulTests * 100.0 / totalTests)}%")
                appendLine("音频检出率: ${String.format("%.1f", audioDetectedTests * 100.0 / successfulTests)}%")
                appendLine()

                // 按音频源分组统计
                appendLine("📈 按音频源统计:")
                results.groupBy { it.audioSource }.forEach { (audioSource, sourceResults) ->
                    val sourceSuccess = sourceResults.count { it.errorMessage == null }
                    val sourceAudio = sourceResults.count { it.hasAudio }
                    val sourceDescription = sourceResults.first().sourceDescription

                    appendLine("🎤 $sourceDescription (ID: $audioSource)")
                    appendLine("   - 测试数: ${sourceResults.size}")
                    appendLine("   - 成功数: $sourceSuccess")
                    appendLine("   - 有声数: $sourceAudio")
                    if (sourceSuccess > 0) {
                        appendLine("   - 音频检出率: ${String.format("%.1f", sourceAudio * 100.0 / sourceSuccess)}%")
                    }
                    appendLine()
                }

                // 按采样率分组统计
                appendLine("📊 按采样率统计:")
                results.groupBy { it.sampleRate }.forEach { (sampleRate, rateResults) ->
                    val rateSuccess = rateResults.count { it.errorMessage == null }
                    val rateAudio = rateResults.count { it.hasAudio }

                    appendLine("🎵 ${sampleRate}Hz")
                    appendLine("   - 测试数: ${rateResults.size}")
                    appendLine("   - 成功数: $rateSuccess")
                    appendLine("   - 有声数: $rateAudio")
                    if (rateSuccess > 0) {
                        appendLine("   - 音频检出率: ${String.format("%.1f", rateAudio * 100.0 / rateSuccess)}%")
                    }
                    appendLine()
                }

                appendLine("🔊 检测到音频的配置组合:")
                val audioConfigs = results.filter { it.hasAudio }.sortedByDescending { it.maxAmplitude }
                if (audioConfigs.isNotEmpty()) {
                    audioConfigs.forEach { result ->
                        appendLine("✅ ${result.sourceDescription}")
                        appendLine("   - 采样率: ${result.sampleRate}Hz")
                        appendLine("   - 声道: ${result.channelDescription}")
                        appendLine("   - 格式: ${result.formatDescription}")
                        appendLine("   - 最大振幅: ${result.maxAmplitude}")
                        appendLine("   - 文件大小: ${result.totalBytes} 字节 (${String.format("%.2f", result.totalBytes / 1024.0)} KB)")
                        appendLine()
                    }
                } else {
                    appendLine("❌ 未检测到任何有效音频配置")
                    appendLine()
                }

                appendLine("🔇 成功但无音频的配置:")
                val silentConfigs = results.filter { !it.hasAudio && it.errorMessage == null }
                silentConfigs.take(10).forEach { result -> // 只显示前10个
                    appendLine("⚪ ${result.sourceDescription} | ${result.sampleRate}Hz | ${result.channelDescription} | ${result.formatDescription}")
                    appendLine("   - 最大振幅: ${result.maxAmplitude} | 文件大小: ${result.totalBytes} 字节")
                }
                if (silentConfigs.size > 10) {
                    appendLine("   ... 还有 ${silentConfigs.size - 10} 个无音频配置")
                }
                appendLine()

                appendLine("❌ 失败的配置:")
                val failedConfigs = results.filter { it.errorMessage != null }
                failedConfigs.groupBy { it.errorMessage }.forEach { (error, errorResults) ->
                    appendLine("❌ 错误: $error")
                    appendLine("   - 影响配置数: ${errorResults.size}")
                    errorResults.take(3).forEach { result ->
                        appendLine("   - ${result.sourceDescription} | ${result.sampleRate}Hz | ${result.channelDescription}")
                    }
                    if (errorResults.size > 3) {
                        appendLine("   - ... 还有 ${errorResults.size - 3} 个相同错误")
                    }
                    appendLine()
                }

                appendLine("💡 推荐配置:")
                val bestConfig = audioConfigs.maxByOrNull { it.maxAmplitude }
                if (bestConfig != null) {
                    appendLine("🏆 最佳音频配置:")
                    appendLine("   - 音频源: ${bestConfig.sourceDescription}")
                    appendLine("   - 采样率: ${bestConfig.sampleRate}Hz")
                    appendLine("   - 声道: ${bestConfig.channelDescription}")
                    appendLine("   - 格式: ${bestConfig.formatDescription}")
                    appendLine("   - 最大振幅: ${bestConfig.maxAmplitude}")
                } else {
                    appendLine("❌ 未找到有效的音频配置")
                }
            })

            Logger.i(TAG, "🎵 [全面音频测试] 测试报告已生成: ${reportFile.absolutePath}")

            // 在日志中输出简要报告
            logComprehensiveTestSummary(results)

        } catch (e: IOException) {
            Logger.e(TAG, "🎵 [全面音频测试] 生成测试报告失败", e)
        }
    }

    /**
     * 在日志中输出全面测试摘要
     */
    private fun logComprehensiveTestSummary(results: List<ComprehensiveAudioTestResult>) {
        Logger.i(TAG, "🎵 [全面音频测试] ==================== 全面测试完成 ====================")

        val audioConfigs = results.filter { it.hasAudio }
        val totalTests = results.size
        val successfulTests = results.count { it.errorMessage == null }

        Logger.i(TAG, "🎵 [全面音频测试] 📊 测试统计:")
        Logger.i(TAG, "🎵 [全面音频测试]   - 总测试数: $totalTests")
        Logger.i(TAG, "🎵 [全面音频测试]   - 成功测试: $successfulTests")
        Logger.i(TAG, "🎵 [全面音频测试]   - 检测到音频: ${audioConfigs.size}")
        Logger.i(TAG, "🎵 [全面音频测试]   - 成功率: ${String.format("%.1f", successfulTests * 100.0 / totalTests)}%")

        if (audioConfigs.isNotEmpty()) {
            Logger.i(TAG, "🎵 [全面音频测试] 🔊 发现 ${audioConfigs.size} 个有效音频配置:")

            // 显示前5个最佳配置
            audioConfigs.sortedByDescending { it.maxAmplitude }.take(5).forEachIndexed { index, result ->
                Logger.i(TAG, "🎵 [全面音频测试]   ${index + 1}. ${result.sourceDescription}")
                Logger.i(TAG, "🎵 [全面音频测试]      ${result.sampleRate}Hz | ${result.channelDescription} | ${result.formatDescription}")
                Logger.i(TAG, "🎵 [全面音频测试]      振幅: ${result.maxAmplitude} | 文件: ${String.format("%.1f", result.totalBytes / 1024.0)}KB")
            }

            // 推荐最佳配置
            val bestConfig = audioConfigs.maxByOrNull { it.maxAmplitude }
            if (bestConfig != null) {
                Logger.w(TAG, "🎵 [全面音频测试] 🏆 推荐最佳配置:")
                Logger.w(TAG, "🎵 [全面音频测试]   音频源: ${bestConfig.sourceDescription}")
                Logger.w(TAG, "🎵 [全面音频测试]   采样率: ${bestConfig.sampleRate}Hz")
                Logger.w(TAG, "🎵 [全面音频测试]   声道: ${bestConfig.channelDescription}")
                Logger.w(TAG, "🎵 [全面音频测试]   格式: ${bestConfig.formatDescription}")
            }
        } else {
            Logger.e(TAG, "🎵 [全面音频测试] ❌ 未发现任何有效音频配置!")
            Logger.e(TAG, "🎵 [全面音频测试] 💡 建议检查:")
            Logger.e(TAG, "🎵 [全面音频测试]   1. 音频权限是否已授予")
            Logger.e(TAG, "🎵 [全面音频测试]   2. 设备是否有可用的音频输入")
            Logger.e(TAG, "🎵 [全面音频测试]   3. 其他应用是否占用了音频资源")
        }

        Logger.i(TAG, "🎵 [全面音频测试] ========================================================")
    }
    
    /**
     * 测试单个音频频道组合 - 使用MediaRecorder录制MP3
     */
    private suspend fun testAudioChannelCombination(
        audioSource: Int,
        sourceDescription: String,
        sampleRate: Int,
        channelConfig: Int,
        channelDescription: String,
        audioFormat: Int,
        formatDescription: String,
        testDir: File
    ): ComprehensiveAudioTestResult = withContext(Dispatchers.IO) {

        val fileName = "audio_${audioSource}_${sampleRate}Hz_${channelDescription}_${formatDescription}.mp3"
        val outputFile = File(testDir, fileName)

        var mediaRecorder: MediaRecorder? = null
        var hasAudio = false
        var maxAmplitude = 0
        var totalBytes = 0L
        var errorMessage: String? = null

        try {
            // 创建MediaRecorder
            mediaRecorder = MediaRecorder().apply {
                setAudioSource(audioSource)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setAudioSamplingRate(sampleRate)

                // 根据声道配置设置声道数
                val channelCount = when (channelConfig) {
                    AudioFormat.CHANNEL_IN_MONO -> 1
                    AudioFormat.CHANNEL_IN_STEREO -> 2
                    else -> 1
                }
                setAudioChannels(channelCount)

                // 设置比特率（根据采样率和声道数调整）
                val bitrate = when {
                    sampleRate >= 44100 && channelCount == 2 -> 128000 // 128kbps 立体声高采样率
                    sampleRate >= 44100 && channelCount == 1 -> 96000  // 96kbps 单声道高采样率
                    sampleRate >= 22050 && channelCount == 2 -> 96000  // 96kbps 立体声中采样率
                    sampleRate >= 22050 && channelCount == 1 -> 64000  // 64kbps 单声道中采样率
                    channelCount == 2 -> 64000  // 64kbps 立体声低采样率
                    else -> 48000  // 48kbps 单声道低采样率
                }
                setAudioEncodingBitRate(bitrate)
                setOutputFile(outputFile.absolutePath)
            }

            // 准备和开始录制
            mediaRecorder.prepare()
            mediaRecorder.start()

            Logger.i(TAG, "🎵 [频道测试] $sourceDescription: 开始录制 ${Constants.COMPREHENSIVE_AUDIO_TEST_DURATION_SECONDS} 秒...")
            Logger.i(TAG, "🎵 [频道测试] 配置: ${sampleRate}Hz, $channelDescription, $formatDescription")

            val recordDurationMs = Constants.COMPREHENSIVE_AUDIO_TEST_DURATION_SECONDS * 1000L
            val startTime = System.currentTimeMillis()

            // 定期检查振幅
            while (System.currentTimeMillis() - startTime < recordDurationMs && isTestRunning) {
                try {
                    val amplitude = mediaRecorder.maxAmplitude
                    if (amplitude > maxAmplitude) {
                        maxAmplitude = amplitude
                    }

                    // 如果振幅超过阈值，认为有音频
                    if (amplitude > 1000) {  // 可调整的阈值
                        hasAudio = true
                    }

                    delay(100) // 每100ms检查一次振幅
                } catch (e: Exception) {
                    // 某些音频源可能不支持getMaxAmplitude
                    break
                }
            }

            // 停止录制
            mediaRecorder.stop()

            // 获取文件大小作为数据量
            totalBytes = if (outputFile.exists()) outputFile.length() else 0

            Logger.i(TAG, "🎵 [频道测试] $sourceDescription 完成:")
            Logger.i(TAG, "🎵 [频道测试]   文件大小=${totalBytes}字节, 最大振幅=$maxAmplitude, 有声音=$hasAudio")
            Logger.i(TAG, "🎵 [频道测试]   配置: ${sampleRate}Hz, $channelDescription, $formatDescription")

        } catch (e: SecurityException) {
            errorMessage = "权限不足: ${e.message}"
            Logger.w(TAG, "🎵 [频道测试] $sourceDescription: $errorMessage")
        } catch (e: IllegalStateException) {
            errorMessage = "音频源不支持: ${e.message}"
            Logger.w(TAG, "🎵 [频道测试] $sourceDescription: $errorMessage")
        } catch (e: IllegalArgumentException) {
            errorMessage = "参数无效: ${e.message}"
            Logger.w(TAG, "🎵 [频道测试] $sourceDescription: $errorMessage")
        } catch (e: Exception) {
            errorMessage = "录制失败: ${e.message}"
            Logger.e(TAG, "🎵 [频道测试] $sourceDescription: $errorMessage")
        } finally {
            try {
                mediaRecorder?.stop()
                mediaRecorder?.release()
            } catch (e: Exception) {
                Logger.w(TAG, "🎵 [频道测试] 清理资源失败: ${e.message}")
            }
        }

        // 如果没有录制到数据或出错，删除空文件
        if (totalBytes == 0L || errorMessage != null) {
            if (outputFile.exists()) {
                outputFile.delete()
            }
        } else {
            // 如果录制成功但没有检测到声音，仍然保留文件供手动检查
            if (!hasAudio) {
                Logger.i(TAG, "🎵 [频道测试] $sourceDescription: 录制成功但未检测到声音，MP3文件已保存供手动检查")
            }
        }

        ComprehensiveAudioTestResult(
            audioSource, sourceDescription, sampleRate, channelDescription,
            formatDescription, hasAudio, totalBytes, maxAmplitude, errorMessage
        )
    }



    /**
     * 音频测试结果数据类
     */
    data class AudioTestResult(
        val audioSource: Int,
        val description: String,
        val hasAudio: Boolean,
        val totalBytes: Long,
        val maxAmplitude: Int,
        val errorMessage: String?
    )

    /**
     * 全面音频测试结果数据类
     */
    data class ComprehensiveAudioTestResult(
        val audioSource: Int,
        val sourceDescription: String,
        val sampleRate: Int,
        val channelDescription: String,
        val formatDescription: String,
        val hasAudio: Boolean,
        val totalBytes: Long,
        val maxAmplitude: Int,
        val errorMessage: String?
    )
}
