package com.ironnet.http_live_game.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.media.MediaFormat
import android.media.projection.MediaProjection
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.ironnet.http_live_game.MainActivity
import com.ironnet.http_live_game.R
import com.ironnet.http_live_game.streaming.HttpServer
import com.ironnet.http_live_game.streaming.StreamConfig
import com.ironnet.http_live_game.streaming.VideoSource
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.net.NetworkInterface
import java.util.Collections

class StreamingService : Service() {
    private val TAG = "StreamingService"
    private val NOTIFICATION_ID = 1
    private val CHANNEL_ID = "streaming_service_channel"

    private val binder = LocalBinder()
    private val serviceJob = SupervisorJob()
    private val serviceScope = CoroutineScope(Dispatchers.IO + serviceJob)

    private var httpServer: HttpServer? = null
    private var videoSource: VideoSource? = null
    private var streamConfig = StreamConfig()
    private var isStreaming = false
    private var mediaProjection: MediaProjection? = null

    inner class LocalBinder : Binder() {
        fun getService(): StreamingService = this@StreamingService
    }

    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForeground()
        return START_STICKY
    }

    override fun onDestroy() {
        stopStreaming()
        serviceScope.cancel()
        super.onDestroy()
    }

    fun startStreaming(config: StreamConfig, mediaProjection: MediaProjection? = null) {
        if (isStreaming) {
            stopStreaming()
        }

        this.streamConfig = config
        this.mediaProjection = mediaProjection

        serviceScope.launch {
            try {
                // 创建HTTP服务器并尝试启动
                httpServer = HttpServer(config.port)
                val serverStarted = httpServer?.startServer() ?: false

                if (!serverStarted) {
                    throw Exception("Failed to start HTTP server on any available port")
                }

                // 更新配置中的端口
                val actualPort = httpServer?.getPort() ?: config.port
                val currentConfig = config.copy(port = actualPort)
                <EMAIL> = currentConfig

                // 启动视频源
                videoSource = VideoSource(
                    applicationContext,
                    currentConfig,
                    mediaProjection,
                    httpServer
                )
                videoSource?.start()

                isStreaming = true
                updateNotification()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting streaming: ${e.message}", e)
                stopStreaming()
            }
        }
    }

    fun stopStreaming() {
        serviceScope.launch {
            try {
                videoSource?.stop()
                videoSource = null

                httpServer?.stop()
                httpServer = null

                isStreaming = false
                updateNotification()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping streaming: ${e.message}", e)
            }
        }
    }

    fun getStreamUrl(): String {
        val ipAddress = getLocalIpAddress()
        val port = httpServer?.getPort() ?: streamConfig.port

        // 返回查看器URL，它是一个独立的HTML页面，可以直接在浏览器中打开
        return "http://$ipAddress:$port/viewer"
    }

    fun getMjpegUrl(): String {
        val ipAddress = getLocalIpAddress()
        val port = httpServer?.getPort() ?: streamConfig.port
        return "http://$ipAddress:$port/stream.mjpeg"
    }

    fun getVideoUrl(): String {
        val ipAddress = getLocalIpAddress()
        val port = httpServer?.getPort() ?: streamConfig.port
        val extension = when (streamConfig.videoFormat) {
            MediaFormat.MIMETYPE_VIDEO_VP8, MediaFormat.MIMETYPE_VIDEO_VP9 -> "webm"
            else -> "mp4"
        }
        return "http://$ipAddress:$port/stream.$extension"
    }

    fun isStreaming(): Boolean {
        return isStreaming
    }

    fun updateConfig(config: StreamConfig) {
        if (isStreaming) {
            stopStreaming()
            startStreaming(config, mediaProjection)
        } else {
            this.streamConfig = config
        }
    }

    fun switchSource(useCamera: Boolean) {
        val newConfig = streamConfig.copy(useCamera = useCamera)
        updateConfig(newConfig)
    }

    private fun startForeground() {
        val notification = createNotification()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NOTIFICATION_ID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION
            )
        } else {
            startForeground(NOTIFICATION_ID, notification)
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "HTTP Streaming Service"
            val descriptionText = "Provides HTTP video streaming"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        val status = if (isStreaming) "Streaming active" else "Streaming inactive"
        val url = if (isStreaming) {
            val port = httpServer?.getPort() ?: streamConfig.port
            val ipAddress = getLocalIpAddress()
            "http://$ipAddress:$port/"
        } else ""

        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("HTTP Video Streaming")
            .setContentText("$status\n$url")
            .setSmallIcon(android.R.drawable.ic_media_play)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)

        // 如果正在流式传输，添加更多信息
        if (isStreaming) {
            val mjpegUrl = getMjpegUrl()
            val videoUrl = getVideoUrl()
            val style = NotificationCompat.BigTextStyle()
                .bigText("$status\n\nWeb UI: $url\n\nMJPEG: $mjpegUrl\n\nVideo: $videoUrl")
            builder.setStyle(style)
        }

        return builder.build()
    }

    private fun updateNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification())
    }

    private fun getLocalIpAddress(): String {
        try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (intf in interfaces) {
                val addrs = Collections.list(intf.inetAddresses)
                for (addr in addrs) {
                    if (!addr.isLoopbackAddress && addr.hostAddress?.contains(':') == false) {
                        return addr.hostAddress ?: "127.0.0.1"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting IP address: ${e.message}", e)
        }
        return "127.0.0.1"
    }
}
