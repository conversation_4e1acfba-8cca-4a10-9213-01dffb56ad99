{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-mr\\values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7314", "endColumns": "100", "endOffsets": "7410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,297,397,513,595,658,749,814,873,961,1023,1083,1150,1213,1267,1381,1438,1499,1553,1623,1742,1823,1908,2013,2090,2167,2253,2320,2386,2456,2534,2621,2691,2767,2838,2907,3003,3077,3175,3271,3345,3415,3517,3572,3639,3726,3819,3882,3946,4009,4109,4212,4306,4410", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "209,292,392,508,590,653,744,809,868,956,1018,1078,1145,1208,1262,1376,1433,1494,1548,1618,1737,1818,1903,2008,2085,2162,2248,2315,2381,2451,2529,2616,2686,2762,2833,2902,2998,3072,3170,3266,3340,3410,3512,3567,3634,3721,3814,3877,3941,4004,4104,4207,4301,4405,4483"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2957,3040,3140,3256,3338,3401,3492,3557,3616,3704,3766,3826,3893,3956,4010,4124,4181,4242,4296,4366,4485,4566,4651,4756,4833,4910,4996,5063,5129,5199,5277,5364,5434,5510,5581,5650,5746,5820,5918,6014,6088,6158,6260,6315,6382,6469,6562,6625,6689,6752,6852,6955,7049,7153", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "259,3035,3135,3251,3333,3396,3487,3552,3611,3699,3761,3821,3888,3951,4005,4119,4176,4237,4291,4361,4480,4561,4646,4751,4828,4905,4991,5058,5124,5194,5272,5359,5429,5505,5576,5645,5741,5815,5913,6009,6083,6153,6255,6310,6377,6464,6557,6620,6684,6747,6847,6950,7044,7148,7226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2213,2322,2424,2538,2695,2798", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,98,108,101,113,156,102,82", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2208,2317,2419,2533,2690,2793,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,375,481,588,678,780,892,970,1047,1138,1231,1324,1421,1521,1614,1709,1803,1894,1985,2065,2172,2273,2372,2481,2583,2697,2854,7231", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,98,108,101,113,156,102,82", "endOffsets": "370,476,583,673,775,887,965,1042,1133,1226,1319,1416,1516,1609,1704,1798,1889,1980,2060,2167,2268,2367,2476,2578,2692,2849,2952,7309"}}]}]}