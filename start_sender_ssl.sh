#!/bin/bash
# 启动带SSL的视频发送端

# 信令服务器URL
SIGNALING_URL="wss://sling.91jdcd.com/ws/"

# 检查视频源
echo "正在检查视频源..."
VIDEO_SOURCE="http://192.168.3.168:80/0.mp4"
curl -s -I $VIDEO_SOURCE > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法访问视频源 $VIDEO_SOURCE"
    echo "使用测试视频源..."
    VIDEO_SOURCE="test"
else
    echo "视频源可访问: $VIDEO_SOURCE"
fi

# 启动视频发送端
echo "正在启动WebRTC视频流发送端..."
echo "发送端信息:"
echo "- 信令服务器: $SIGNALING_URL"
echo "- 视频源: $VIDEO_SOURCE"
echo ""
echo "按Ctrl+C停止发送端"
echo ""

python3 web_sender_adapter.py \
    --video "$VIDEO_SOURCE" \
    --id "video-stream" \
    --name "视频流" \
    --description "WebRTC视频流" \
    --signaling "$SIGNALING_URL"
