{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-in\\values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,389,493,598,685,789,905,988,1066,1157,1250,1345,1439,1539,1632,1727,1821,1912,2003,2089,2192,2297,2398,2502,2611,2719,2879,7269", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "384,488,593,680,784,900,983,1061,1152,1245,1340,1434,1534,1627,1722,1816,1907,1998,2084,2187,2292,2393,2497,2606,2714,2874,2973,7349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7354", "endColumns": "100", "endOffsets": "7450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,303,409,525,608,673,767,832,891,978,1040,1100,1166,1228,1282,1394,1451,1512,1566,1638,1764,1850,1934,2043,2124,2205,2295,2362,2428,2500,2584,2667,2742,2818,2891,2966,3051,3126,3218,3312,3386,3459,3553,3605,3674,3759,3846,3908,3972,4035,4138,4238,4333,4435", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "219,298,404,520,603,668,762,827,886,973,1035,1095,1161,1223,1277,1389,1446,1507,1561,1633,1759,1845,1929,2038,2119,2200,2290,2357,2423,2495,2579,2662,2737,2813,2886,2961,3046,3121,3213,3307,3381,3454,3548,3600,3669,3754,3841,3903,3967,4030,4133,4233,4328,4430,4510"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2978,3057,3163,3279,3362,3427,3521,3586,3645,3732,3794,3854,3920,3982,4036,4148,4205,4266,4320,4392,4518,4604,4688,4797,4878,4959,5049,5116,5182,5254,5338,5421,5496,5572,5645,5720,5805,5880,5972,6066,6140,6213,6307,6359,6428,6513,6600,6662,6726,6789,6892,6992,7087,7189", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "269,3052,3158,3274,3357,3422,3516,3581,3640,3727,3789,3849,3915,3977,4031,4143,4200,4261,4315,4387,4513,4599,4683,4792,4873,4954,5044,5111,5177,5249,5333,5416,5491,5567,5640,5715,5800,5875,5967,6061,6135,6208,6302,6354,6423,6508,6595,6657,6721,6784,6887,6987,7082,7184,7264"}}]}]}