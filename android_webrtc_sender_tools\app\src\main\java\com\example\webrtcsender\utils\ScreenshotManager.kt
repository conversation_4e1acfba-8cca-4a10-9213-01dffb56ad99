package com.example.webrtcsender.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.HandlerThread
import kotlinx.coroutines.*
import okhttp3.*
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

import ZtlApi.ZtlManager

/**
 * 截屏管理器
 * 负责从视频流中抽取当前帧并上传
 */
object ScreenshotManager {
    private const val TAG = "ScreenshotManager"
    
    /**
     * 从视频流截取当前帧
     */
    suspend fun captureScreenshot(
        context: Context,
        mediaProjection: MediaProjection?,
        deviceId: String,
        requestId: String,
        serverDomain: String? = null
    ): String? = withContext(Dispatchers.IO) {

        Logger.i(TAG, "🎥 开始从视频流抽取当前帧")

        // 首先尝试从WebRTC视频流获取当前帧
        val videoFrameBitmap = captureFromVideoStream()
        if (videoFrameBitmap != null) {
            Logger.i(TAG, "✅ 成功从视频流获取帧: ${videoFrameBitmap.width}x${videoFrameBitmap.height}")
            return@withContext saveAndUploadBitmap(context, videoFrameBitmap, deviceId, requestId, "video_stream")
        }

        Logger.w(TAG, "⚠️ 无法从视频流获取帧，尝试使用MediaProjection方式")

        if (mediaProjection == null) {
            Logger.w(TAG, "⚠️ MediaProjection为空，尝试使用ZtlManager截屏")
            return@withContext captureWithZtlManager(context, deviceId, requestId, serverDomain)
        }
        
        try {
            // 获取屏幕尺寸
            val displayMetrics = context.resources.displayMetrics
            val width = displayMetrics.widthPixels
            val height = displayMetrics.heightPixels
            val density = displayMetrics.densityDpi
            
            Logger.i(TAG, "开始截屏: ${width}x${height}, density: $density")
            
            // 创建ImageReader
            val imageReader = ImageReader.newInstance(width, height, PixelFormat.RGBA_8888, 1)
            
            // 创建后台线程处理图像
            val handlerThread = HandlerThread("ScreenshotThread")
            handlerThread.start()
            val handler = Handler(handlerThread.looper)
            
            // 使用CompletableDeferred等待截屏完成
            val screenshotDeferred = CompletableDeferred<Bitmap?>()
            
            imageReader.setOnImageAvailableListener({
                try {
                    val image = imageReader.acquireLatestImage()
                    if (image != null) {
                        val bitmap = imageToBitmap(image)
                        image.close()
                        screenshotDeferred.complete(bitmap)
                    } else {
                        screenshotDeferred.complete(null)
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "处理截屏图像失败", e)
                    screenshotDeferred.complete(null)
                }
            }, handler)
            
            // 创建VirtualDisplay
            val virtualDisplay = mediaProjection.createVirtualDisplay(
                "Screenshot",
                width, height, density,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.surface,
                null, null
            )
            
            // 等待截屏完成（最多等待3秒）
            val bitmap = withTimeoutOrNull(3000) {
                screenshotDeferred.await()
            }
            
            // 清理资源
            virtualDisplay?.release()
            imageReader.close()
            handlerThread.quitSafely()
            
            if (bitmap == null) {
                Logger.w(TAG, "MediaProjection截屏失败，尝试使用ZtlManager截屏")
                return@withContext captureWithZtlManager(context, deviceId, requestId, serverDomain)
            }
            
            Logger.i(TAG, "截屏成功: ${bitmap.width}x${bitmap.height}")

            return@withContext saveAndUploadBitmap(context, bitmap, deviceId, requestId, "media_projection")
            
        } catch (e: Exception) {
            Logger.w(TAG, "MediaProjection截屏异常，尝试使用ZtlManager截屏: ${e.message}")
            return@withContext captureWithZtlManager(context, deviceId, requestId, serverDomain)
        }
    }
    
    /**
     * 使用ZtlManager截屏
     */
    private suspend fun captureWithZtlManager(
        context: Context,
        deviceId: String,
        @Suppress("UNUSED_PARAMETER") requestId: String,
        serverDomain: String? = null
    ): String? = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.i(TAG, "📱 使用ZtlManager进行截屏")

            // 生成截屏文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "${deviceId}_ztl_screenshot_${timestamp}.png"
            val screenshotPath = "/sdcard/$fileName"

            // 调用ZtlManager截屏
            val success = try {
                ZtlManager.GetInstance().startScreenShot( "/sdcard/", fileName)
                Logger.i(TAG, "✅ ZtlManager截屏调用成功")
                true
            } catch (e: Exception) {
                Logger.e(TAG, "❌ ZtlManager反射调用失败: ${e.message}")
                false
            }

            if (success) {
                Logger.i(TAG, "✅ ZtlManager截屏调用成功: $screenshotPath")

                // 等待截屏文件生成
                val screenshotFile = File(screenshotPath)
                var waitCount = 0
                val maxWait = 10 // 最多等待10秒

                while (!screenshotFile.exists() && waitCount < maxWait) {
                    delay(1000)
                    waitCount++
                    Logger.d(TAG, "⏳ 等待截屏文件生成... (${waitCount}/${maxWait})")
                }

                if (screenshotFile.exists() && screenshotFile.length() > 0) {
                    Logger.i(TAG, "📁 截屏文件已生成: ${screenshotFile.absolutePath}, 大小: ${screenshotFile.length()} bytes")

                    // 压缩PNG为JPG质量60
                    val compressedFile = compressPngToJpg(context, screenshotFile, deviceId, timestamp)

                    if (compressedFile != null) {
                        // 上传压缩后的截屏文件到指定服务器
                        val fullUrl = uploadScreenshotToServer(compressedFile, deviceId, serverDomain)

                        // 清理临时文件
                        try {
                            screenshotFile.delete()
                            compressedFile.delete()
                            Logger.d(TAG, "🗑️ 已清理临时截屏文件")
                        } catch (e: Exception) {
                            Logger.w(TAG, "清理临时文件失败: ${e.message}")
                        }

                        fullUrl
                    } else {
                        Logger.e(TAG, "❌ 压缩截屏文件失败")
                        // 清理原文件
                        try {
                            screenshotFile.delete()
                        } catch (e: Exception) {
                            Logger.w(TAG, "清理原文件失败: ${e.message}")
                        }
                        null
                    }
                } else {
                    Logger.e(TAG, "❌ ZtlManager截屏文件未生成或为空")
                    null
                }
            } else {
                Logger.e(TAG, "❌ ZtlManager截屏调用失败")
                null
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ ZtlManager截屏异常", e)
            null
        }
    }

    /**
     * 压缩PNG文件为JPG格式，质量60
     */
    private suspend fun compressPngToJpg(
        context: Context,
        pngFile: File,
        deviceId: String,
        timestamp: String
    ): File? = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.i(TAG, "🗜️ 开始压缩PNG为JPG (质量60): ${pngFile.name}")

            // 读取PNG文件为Bitmap
            val bitmap = android.graphics.BitmapFactory.decodeFile(pngFile.absolutePath)
            if (bitmap == null) {
                Logger.e(TAG, "❌ 无法读取PNG文件为Bitmap")
                return@withContext null
            }

            // 创建JPG文件
            val jpgFileName = "${deviceId}_screenshot_${timestamp}.jpg"
            val jpgFile = File(context.cacheDir, jpgFileName)

            // 压缩为JPG格式，质量60
            FileOutputStream(jpgFile).use { output ->
                val success = bitmap.compress(Bitmap.CompressFormat.JPEG, 60, output)
                if (!success) {
                    Logger.e(TAG, "❌ 压缩为JPG失败")
                    return@withContext null
                }
            }

            // 回收Bitmap
            bitmap.recycle()

            Logger.i(TAG, "✅ 压缩完成: ${jpgFile.name}, 大小: ${jpgFile.length()} bytes (原文件: ${pngFile.length()} bytes)")
            jpgFile

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 压缩PNG为JPG异常: ${e.message}")
            null
        }
    }

    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * image.width
            
            // 创建Bitmap
            val bitmap = Bitmap.createBitmap(
                image.width + rowPadding / pixelStride,
                image.height,
                Bitmap.Config.ARGB_8888
            )
            
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有行填充，需要裁剪
            return if (rowPadding > 0) {
                Bitmap.createBitmap(bitmap, 0, 0, image.width, image.height)
            } else {
                bitmap
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "转换Image到Bitmap失败", e)
            return null
        }
    }
    
    /**
     * 上传截屏到服务器
     */
    private suspend fun uploadScreenshot(file: File, deviceId: String, timestamp: String): String? {
        return uploadScreenshotFile(file, deviceId, timestamp, "video_stream")
    }

    /**
     * 上传截屏文件到指定服务器
     */
    private suspend fun uploadScreenshotToServer(file: File, deviceId: String, serverDomain: String?): String? {
        return try {
            val uploadUrl = if (serverDomain.isNullOrEmpty()) {
                Constants.UPLOAD_SCREENSHOT_URL
            } else {
                "${serverDomain.trimEnd('/')}/api/common/upload_screenshot"
            }

            Logger.i(TAG, "📤 上传截屏到服务器: $uploadUrl")

            val client = OkHttpClient()
            val requestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(
                    "file",
                    file.name,
                    RequestBody.create(MediaType.parse("image/jpeg"), file)
                )
                .addFormDataPart("category", "screenshot")  // 添加分类参数
                .build()

            val request = Request.Builder()
                .url(uploadUrl)
                .post(requestBody)
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body()?.string()
                if (responseBody != null) {
                    val jsonResponse = JSONObject(responseBody)
                    val code = jsonResponse.optString("code")

                    if (code == "1") {
                        val data = jsonResponse.optJSONObject("data")
                        val fullUrl = data?.optString("fullurl")

                        if (fullUrl != null) {
                            Logger.i(TAG, "截屏上传成功 (方式: ztl_manager): $fullUrl")
                            return fullUrl
                        } else {
                            Logger.e(TAG, "截屏上传响应中没有fullurl字段")
                        }
                    } else {
                        val msg = jsonResponse.optString("msg", "未知错误")
                        Logger.e(TAG, "截屏上传失败: $msg")
                    }
                } else {
                    Logger.e(TAG, "截屏上传响应为空")
                }
            } else {
                Logger.e(TAG, "截屏上传HTTP错误: ${response.code()}")
            }

            null

        } catch (e: Exception) {
            Logger.e(TAG, "上传截屏失败", e)
            null
        }
    }

    /**
     * 上传截屏文件到服务器（带截图方式标注）
     */
    private suspend fun uploadScreenshotFile(file: File, deviceId: String, timestamp: String, method: String): String? {
        return try {
            val client = OkHttpClient()
            val requestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(
                    "file",
                    "${deviceId}_screenshot_${timestamp}.${if (method == "ztl_manager") "png" else "jpg"}",
                    RequestBody.create(MediaType.parse(if (method == "ztl_manager") "image/png" else "image/jpeg"), file)
                )
                .addFormDataPart("method", method)  // 添加截图方式参数
                .addFormDataPart("device_id", deviceId)  // 添加设备ID参数
                .build()
            
            val request = Request.Builder()
                .url(Constants.UPLOAD_SCREENSHOT_URL)
                .post(requestBody)
                .build()
            
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body()?.string()
                if (responseBody != null) {
                    val jsonResponse = JSONObject(responseBody)
                    val code = jsonResponse.optString("code")
                    
                    if (code == "1") {
                        val data = jsonResponse.optJSONObject("data")
                        val fullUrl = data?.optString("fullurl")
                        
                        if (fullUrl != null) {
                            Logger.i(TAG, "截屏上传成功 (方式: $method): $fullUrl")
                            return fullUrl
                        } else {
                            Logger.e(TAG, "截屏上传响应中没有fullurl字段")
                        }
                    } else {
                        val msg = jsonResponse.optString("msg", "未知错误")
                        Logger.e(TAG, "截屏上传失败: $msg")
                    }
                } else {
                    Logger.e(TAG, "截屏上传响应为空")
                }
            } else {
                Logger.e(TAG, "截屏上传HTTP错误: ${response.code()}")
            }
            
            null
            
        } catch (e: Exception) {
            Logger.e(TAG, "上传截屏失败", e)
            null
        }
    }

    /**
     * 从WebRTC视频流抽取当前帧
     */
    private fun captureFromVideoStream(): Bitmap? {
        return try {
            Logger.d(TAG, "🎥 尝试从WebRTC视频流获取当前帧")

            // 获取WebRTC客户端
            val webrtcClient = com.example.webrtcsender.webrtc.WebRTCManager.webRTCClient
            if (webrtcClient == null) {
                Logger.w(TAG, "⚠️ WebRTC客户端为空")
                return null
            }

            // 尝试获取当前视频帧
            val currentFrame = webrtcClient.getCurrentVideoFrame()
            if (currentFrame != null) {
                Logger.i(TAG, "✅ 成功从WebRTC流获取视频帧")
                return currentFrame
            } else {
                Logger.w(TAG, "⚠️ WebRTC流中没有可用的视频帧")
                return null
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 从视频流获取帧失败: ${e.message}")
            null
        }
    }

    /**
     * 保存Bitmap并上传
     */
    private suspend fun saveAndUploadBitmap(
        context: Context,
        bitmap: Bitmap,
        deviceId: String,
        @Suppress("UNUSED_PARAMETER") requestId: String,
        method: String
    ): String? {
        return try {
            // 保存截屏到临时文件
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val screenshotFile = File(context.cacheDir, "${deviceId}_screenshot_${timestamp}.jpg")

            FileOutputStream(screenshotFile).use { output ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 85, output)
            }

            Logger.i(TAG, "📁 截屏已保存: ${screenshotFile.absolutePath}, 大小: ${screenshotFile.length()} bytes")

            // 上传截屏
            val fullUrl = uploadScreenshotFile(screenshotFile, deviceId, timestamp, method)

            // 清理临时文件
            screenshotFile.delete()

            fullUrl

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 保存和上传截屏失败: ${e.message}")
            null
        }
    }
}
