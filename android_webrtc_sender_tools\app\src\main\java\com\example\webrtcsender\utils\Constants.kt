package com.example.webrtcsender.utils

object Constants {
    // 共享偏好设置
    const val PREF_NAME = "webrtc_sender_prefs"

    // 偏好设置键
    const val PREF_SIGNALING_URL = "signaling_url"
    const val PREF_SENDER_ID = "sender_id"
    const val PREF_VIDEO_SOURCE = "video_source"
    const val PREF_VIDEO_RESOLUTION = "video_resolution"
    const val PREF_VIDEO_BITRATE = "video_bitrate"
    const val PREF_VIDEO_CODEC = "video_codec"
    const val PREF_VIDEO_FRAMERATE = "video_framerate"
    const val PREF_CAMERA_ID = "camera_id"
    const val PREF_SCREEN_CAPTURE_QUALITY = "screen_capture_quality"
    const val PREF_AUDIO_SOURCE = "audio_source"
    const val PREF_AUTO_START_GAME = "auto_start_game"
    const val PREF_AUTO_START_GAME_PACKAGE = "auto_start_game_package"
    const val PREF_HAS_MANUAL_OPERATION = "true" // 是否有过手动操作
    const val PREF_LOG_DISPLAY_ENABLED = "log_display_enabled" // 日志显示开关
    const val PREF_FIRST_INSTALL = "first_install" // 是否首次安装

    // 默认值
    const val DEFAULT_SIGNALING_URL = "wss://sling.91jdcd.com/ws/"
    const val DEFAULT_SENDER_ID = "android-sender"
    const val DEFAULT_VIDEO_SOURCE = "camera" // camera, screen, hdmiin, usbcapture - 首次安装默认摄像头
    const val DEFAULT_VIDEO_RESOLUTION = "576p" // 480p, 576p, 720p, 1080p - 首次安装默认576p
    const val DEFAULT_VIDEO_BITRATE = 3000 // kbps - 首次安装默认3000
    const val DEFAULT_VIDEO_CODEC = "H264" // VP8, VP9, H264
    const val DEFAULT_VIDEO_FRAMERATE = 60 // fps - 首次安装默认60帧
    const val DEFAULT_CAMERA_ID = "0" // 0: 后置摄像头, 1: 前置摄像头 - 避免使用特殊摄像头ID如125
    const val DEFAULT_SCREEN_CAPTURE_QUALITY = "medium" // low, medium, high
    const val DEFAULT_AUDIO_SOURCE = "video_input" // video_input, microphone, system, both, none - 首次安装默认来自视频输入
    const val DEFAULT_AUTO_START_GAME = false
    const val DEFAULT_AUTO_START_GAME_PACKAGE = ""
    const val DEFAULT_LOG_DISPLAY_ENABLED = true // 首次安装默认关闭日志显示

    // 摄像头策略错误恢复配置
    const val CAMERA_POLICY_ERROR_RETRY_DELAY = 30000L // 30秒后重试
    const val CAMERA_POLICY_ERROR_MAX_RETRIES = 3 // 最大重试次数（降低以更快触发自动重启）
    const val CAMERA_POLICY_ERROR_BACKOFF_MULTIPLIER = 1.5f // 退避倍数
    const val DEFAULT_FIRST_INSTALL = true

    // 新增视频源相关
    const val PREF_HDMI_DEVICE_PATH = "hdmi_device_path"
    const val PREF_USB_CAPTURE_DEVICE_PATH = "usb_capture_device_path"
    const val DEFAULT_HDMI_DEVICE_PATH = "/dev/video0"
    const val DEFAULT_USB_CAPTURE_DEVICE_PATH = "/dev/video1"

    // 服务相关
    const val SERVICE_NOTIFICATION_ID = 1001
    const val SERVICE_CHANNEL_ID = "webrtc_sender_channel"
    const val SERVICE_CHANNEL_NAME = "WebRTC Sender Service"

    // 广播Action
    const val ACTION_SERVICE_STATUS = "com.example.webrtcsender.SERVICE_STATUS"
    const val ACTION_CONNECTION_STATUS = "com.example.webrtcsender.CONNECTION_STATUS"

    // 视频分辨率映射
    val VIDEO_RESOLUTIONS = mapOf(
        "480p" to Pair(854, 480),
        "576p" to Pair(1024, 576),
        "720p" to Pair(1280, 720),
        "1080p" to Pair(1920, 1080)
    )

    // 内置游戏关键字列表（不分大小写）
    val BUILTIN_GAME_KEYWORDS = listOf(
        "BirdKing",
        "ocean3",
        ".example.demo",
        "WaterMargin"
    )

    // FTP配置
    const val FTP_SERVER = "******************************************"
    const val FTP_HOST = "************"
    const val FTP_PORT = 21
    const val FTP_USERNAME = "gdevice"
    const val FTP_PASSWORD = "DtNNXThtp7W2"

    // 上传接口配置
    const val UPLOAD_LOG_URL = "https://testva2.91jdcd.com/api/common/upload_log"
    const val UPLOAD_SCREENSHOT_URL = "https://testva2.91jdcd.com/api/common/upload_log"

    // 广播Extra
    const val EXTRA_SERVICE_STATUS = "service_status"
    const val EXTRA_CONNECTION_STATUS = "connection_status"
    const val EXTRA_ERROR_MESSAGE = "error_message"
    const val EXTRA_VIEWER_COUNT = "viewer_count"

    // WebRTC相关 - 添加多个STUN服务器提高连接成功率
    val ICE_SERVER_URLS = listOf(
        // 🔧 IPv4服务器（备选）
        //"stun:stun.l.google.com:19302",
        "stun:************:3478",
        "turn:************:3478?transport=udp",

        // 🔧 IPv6服务器（主要测试）
        "stun:[2408:400d:1130:7600:d73:4f58:bd1d:539e]:3478",
        "turn:[2408:400d:1130:7600:d73:4f58:bd1d:539e]:3478?transport=udp"
    )
    // TURN服务器认证信息 - 请替换为你的TURN服务器提供的用户名和密码
    const val ICE_USERNAME = "admin"  // 默认用户名，请根据实际情况修改
    const val ICE_CREDENTIAL = "fpwe287534"  // 默认密码，请根据实际情况修改

    // 🎵 音频调试配置
    const val ENABLE_AUDIO_SOURCE_TEST = false  // 启用音频来源遍历测试（已完成测试，暂时禁用）
    const val ENABLE_COMPREHENSIVE_AUDIO_TEST = false  // 启用全面音频频道测试
    const val AUDIO_TEST_DURATION_SECONDS = 10  // 每个音频来源录制时长（秒）
    const val COMPREHENSIVE_AUDIO_TEST_DURATION_SECONDS = 3  // 全面测试每个配置的录制时长（秒）

    // 🔧 根据测试结果，推荐的音频源优先级
    val RECOMMENDED_AUDIO_SOURCES = listOf(
        "remote_submix",      // 系统内部音频 - 最有希望录制到系统声音
        "voice_performance",  // 性能优化语音 - 振幅较高
        "voice_recognition",  // 语音识别麦克风 - 可能有系统音频
        "echo_reference"      // 回声参考 - 可能包含播放音频
    )
    const val AUDIO_TEST_SAMPLE_RATE = 44100  // 音频采样率
    const val AUDIO_TEST_CHANNEL_CONFIG = android.media.AudioFormat.CHANNEL_IN_STEREO  // 声道配置
    const val AUDIO_TEST_AUDIO_FORMAT = android.media.AudioFormat.ENCODING_PCM_16BIT  // 音频格式



    // 视频比特率 (kbps)
    val VIDEO_BITRATES = mapOf(
        "very_low" to 500,
        "low" to 1000,
        "medium" to 2000,
        "high" to 4000,
        "very_high" to 8000,
        "ultra" to 16000
    )

    // 视频帧率 (fps)
    val VIDEO_FRAMERATES = listOf(15, 24, 30, 60)

    // 音频来源
    val AUDIO_SOURCES = mapOf(
        "video_input" to "来自视频输入",
        "microphone" to "麦克风",
        "system" to "系统声音",
        "both" to "麦克风和系统声音",
        "none" to "无音频",
        "voice_call" to "通话音频",
        "voice_downlink" to "下行通话音频",
        "voice_uplink" to "上行通话音频",
        "voice_recognition" to "语音识别",
        "camcorder" to "摄像机",
        "voice_communication" to "语音通信",
        "default" to "默认音频",
        "mic" to "麦克风 (MIC)",
        "voice_performance" to "语音表演",
        "unprocessed" to "未处理音频",
        "remote_submix" to "远程混音",
        "radio_tuner" to "收音机",
        "hotword" to "热词检测",
        "echo_reference" to "回声参考",
        "ultrasound" to "超声波"
    )

    // 音频通道
    val AUDIO_CHANNELS = mapOf(
        "mono" to "单声道",
        "stereo" to "立体声",
        "5.1" to "5.1环绕声",
        "7.1" to "7.1环绕声"
    )

    // 音频采样率
    val AUDIO_SAMPLE_RATES = listOf(8000, 16000, 22050, 32000, 44100, 48000, 96000)

    // 音频编码
    val AUDIO_ENCODINGS = mapOf(
        "pcm" to "PCM 16位",
        "pcm_float" to "PCM 浮点",
        "pcm_8bit" to "PCM 8位",
        "pcm_24bit" to "PCM 24位",
        "pcm_32bit" to "PCM 32位",
        "aac" to "AAC",
        "opus" to "Opus"
    )

    // 视频源类型
    val VIDEO_SOURCE_TYPES = mapOf(
        "camera" to "摄像头",
        "screen" to "屏幕录制",
        "hdmiin" to "HDMI输入",
        "usbcapture" to "USB采集卡"
    )
}
