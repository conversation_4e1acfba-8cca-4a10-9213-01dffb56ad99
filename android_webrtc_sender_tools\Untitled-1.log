2025-08-26 15:41:13.114  4314-4720  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.120  4314-4720  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.123  4314-4718  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:13.127  4314-4720  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.133   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:13.134   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.134   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:13.134   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:13.138   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:13.138   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:13.138   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:13.138   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:13.138   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:13.138   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:13.139  4314-4719  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:13.139   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:13.139   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:13.139   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.143  4314-4720  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:13.145  4314-4720  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:13.148  4314-4720  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.151  4314-4720  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:13.153  4314-4720  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:13.155  4314-4720  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:13.158  4314-4720  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.160  4314-4720  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:13.196  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:13.198  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:13.199  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:13.201  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:13.202  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:13.203  4314-4720  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:13.205  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:13.207  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:13.208  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:13.210  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:13.212  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:13.213  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:13.215  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.217  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:13.218  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:13.220  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:13.222  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:13.248  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:13.250  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:13.253  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:13.254  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:13.255  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.256  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:13.257  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:13.258  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.259  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:13.261  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.263   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:13.264   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.264   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:13.264   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:13.267   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:13.267   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:13.267   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:13.267   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:13.267   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:13.267   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:13.269   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:13.269   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:13.269   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.272  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:13.274  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:13.336  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.338  4314-4720  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.338  4314-4719  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.338  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.340  4314-4719  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.340  4314-4720  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.342  4314-4718  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.343  4314-4716  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.343  4314-4718  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.345  4314-4716  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.345  4314-4714  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.348  4314-4714  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:13.352  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [资源清理] ✅ 摄像头资源清理完成
2025-08-26 15:41:13.354  4314-4714  MessageQueue            com.example.webrtcsender             W  Handler (android.os.Handler) {4439041} sending message to a Handler on a dead thread (Ask Gemini)
java.lang.IllegalStateException: Handler (android.os.Handler) {4439041} sending message to a Handler on a dead thread
	at android.os.MessageQueue.enqueueMessage(MessageQueue.java:560)
	at android.os.Handler.enqueueMessage(Handler.java:810)
	at android.os.Handler.sendMessageAtTime(Handler.java:759)
	at android.os.Handler.sendMessageDelayed(Handler.java:729)
	at android.os.Handler.post(Handler.java:434)
	at org.webrtc.ThreadUtils.invokeAtFrontUninterruptibly(ThreadUtils.java:169)
	at org.webrtc.ThreadUtils.invokeAtFrontUninterruptibly(ThreadUtils.java:196)
	at org.webrtc.SurfaceTextureHelper.dispose(SurfaceTextureHelper.java:311)
	at com.example.webrtcsender.webrtc.WebRTCClient.handleCameraResourceCleanup(WebRTCClient.kt:3368)
	at com.example.webrtcsender.webrtc.WebRTCClient.access$handleCameraResourceCleanup(WebRTCClient.kt:22)
	at com.example.webrtcsender.webrtc.WebRTCClient$createCamera2VideoSourceWithSmartHandling$videoCapturer$1.onCameraClosed(WebRTCClient.kt:3279)
	at org.webrtc.CameraCapturer$2.onCameraClosed(CameraCapturer.java:150)
	at org.webrtc.Camera2Session$CameraStateCallback.onClosed(Camera2Session.java:136)
	at android.hardware.camera2.impl.CameraDeviceImpl$5.run(CameraDeviceImpl.java:237)
	at android.os.Handler.handleCallback(Handler.java:974)
	at android.os.Handler.dispatchMessage(Handler.java:99)
	at android.os.Looper.loopOnce(Looper.java:201)
	at android.os.Looper.loop(Looper.java:288)
	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-08-26 15:41:13.776  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.780  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:13.789  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:13.792   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:13.804   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.804   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:13.804   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:13.820   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:13.820   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:13.820   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:13.820   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:13.820   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:13.820   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:13.821   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:13.821   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:13.821   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:13.825  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:13.827  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:14.336  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:14.341  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:14.347  4314-4724  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:14.353   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:14.355   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:14.355   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:14.356   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:14.358   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:14.358   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:14.359   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:14.359   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:14.359   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:14.359   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:14.361   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:14.361   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:14.361   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:14.364  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:14.365  4314-4724  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:14.367  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:14.370  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:14.373  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:14.381  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:14.384  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:14.387  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:14.422  4314-4724  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:18.359  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:18.361  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:18.363  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:18.366  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:18.369  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:18.371  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:18.373  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:18.376  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:18.378  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:18.381  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:18.383  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:18.386  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:18.388  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:18.390  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:18.419  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:18.421  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:18.423  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:18.426  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.426  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:18.429  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:18.430  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:18.431  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:18.434  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.434  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:18.437   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:18.437  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:18.438   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.438   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:18.438   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:18.439  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:18.439   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:18.440   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:18.440   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:18.440   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:18.440   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:18.440   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:18.441   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:18.441   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:18.441   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.442  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:18.444  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:18.444  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:18.447  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:18.447  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:18.449  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:18.450  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:18.452  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:18.454  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:18.455  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:18.457  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:18.458  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:18.460  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:18.462  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:18.464  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:18.489  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:18.490  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:18.493  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:18.495  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:18.496  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.497  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:18.499  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:18.500  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:18.501  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:18.503  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.506   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:18.506   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.506   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:18.506   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:18.508   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:18.508   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:18.509   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:18.509   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:18.509   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:18.509   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:18.509   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:18.509   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:18.509   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.511  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:18.513  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:18.950  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.955  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:18.959  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:18.963   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:18.964   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.965   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:18.965   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:18.968   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:18.968   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:18.968   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:18.968   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:18.968   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:18.968   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:18.969   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:18.970   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:18.970   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:18.973  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:18.977  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:19.017  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.024  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:19.030  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.033   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:19.035   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.035   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:19.035   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:19.037   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:19.037   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:19.037   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:19.038   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:19.038   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:19.038   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:19.038   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:19.038   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:19.038   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.043  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:19.046  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:19.481  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.487  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:19.492  4314-4726  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.496   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:19.499   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.499   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:19.499   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:19.503   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:19.504   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:19.504   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:19.504   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:19.504   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:19.504   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:19.505   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:19.506   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:19.506   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.511  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:19.512  4314-4726  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:19.515  4314-4726  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:19.519  4314-4726  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:19.522  4314-4726  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:19.525  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:19.527  4314-4726  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:19.531  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:19.550  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.557  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:19.562  4314-4727  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:19.574   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:19.576   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.576   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:19.576   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:19.579   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:19.579   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:19.579   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:19.579   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:19.579   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:19.579   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:19.579   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:19.579   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:19.580   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:19.585  4314-4726  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:19.590  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:19.591  4314-4727  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:19.594  4314-4727  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:19.597  4314-4727  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:19.601  4314-4727  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:19.603  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:19.606  4314-4727  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:19.609  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:19.660  4314-4727  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.124  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:23.127  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:23.131  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:23.134  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:23.137  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:23.140  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:23.143  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:23.145  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.148  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:23.151  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:23.153  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:23.156  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.158  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:23.161  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:23.164  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:23.167  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:23.194  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:23.196  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:23.199  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:23.201  4314-4731  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.202  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:23.204  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:23.205  4314-4731  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.207  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.208  4314-4731  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.208  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:23.210   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:23.211   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.211   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:23.211   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:23.211  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:23.213  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:23.214  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:23.215   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:23.215   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:23.215   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:23.215   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:23.215   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:23.215   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:23.216   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:23.216   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:23.216   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.217  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:23.218  4314-4731  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:23.219  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:23.221  4314-4731  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.221  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:23.222  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:23.224  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.225  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:23.229  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:23.230  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:23.232  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.234  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:23.236  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:23.238  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:23.239  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:23.261  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:23.262  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:23.266  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:23.268  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:23.269  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.270  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:23.272  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.273  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.274  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:23.277  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:23.277  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.278  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:23.279   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:23.281  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:23.281   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.281   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:23.281   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:23.283  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:23.285   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:23.285   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:23.285   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:23.285   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:23.285   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:23.285   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:23.286   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:23.286   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:23.286   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.286  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:23.288  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:23.288  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:23.289  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.290  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:23.291  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.293  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:23.295  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:23.297  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:23.298  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.300  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:23.302  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:23.304  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:23.306  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:23.327  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:23.329  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:23.333  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:23.334  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:23.335  4314-4733  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.336  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:23.338  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:23.338  4314-4733  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.340  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:23.342  4314-4733  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.344   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:23.345   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.345   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:23.345   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:23.347   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:23.347   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:23.347   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:23.347   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:23.347   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:23.347   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:23.348   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:23.348   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:23.348   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.350  4314-4733  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:23.352  4314-4733  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.724  4314-4731  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.727  4314-4731  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.732  4314-4731  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.734   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:23.735   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.735   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:23.735   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:23.738   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:23.738   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:23.738   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:23.738   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:23.738   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:23.738   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:23.739   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:23.739   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:23.740   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.742  4314-4731  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:23.744  4314-4731  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.793  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.797  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.803  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:23.807   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:23.808   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.808   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:23.808   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:23.813   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:23.813   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:23.813   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:23.813   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:23.813   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:23.813   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:23.813   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:23.813   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:23.814   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:23.815  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:23.818  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:23.823  4314-4731  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.824  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.825  4314-4731  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.825  4314-4732  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.826  4314-4727  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.827  4314-4726  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.828  4314-4726  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.829  4314-4733  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.829  4314-4727  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.832  4314-4724  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头已关闭 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:23.833  4314-4733  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.833  4314-4724  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [资源清理] 开始清理摄像头资源: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:23.846  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [资源清理] ✅ 摄像头资源清理完成
2025-08-26 15:41:24.322  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:24.331  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:24.338  4314-4732  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:24.342   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:24.343   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:24.344   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:24.344   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:24.346   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:24.346   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:24.346   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:24.346   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:24.346   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:24.347   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:24.347   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:24.347   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:24.347   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:24.352  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:24.353  4314-4732  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:24.356  4314-4732  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:24.359  4314-4732  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:24.361  4314-4732  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:24.363  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:24.365  4314-4732  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:24.369  4314-4732  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:24.404  4314-4732  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:24.423  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:24.425  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:24.427  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:24.430  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:24.432  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:24.435  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:24.437  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:24.439  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:24.441  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:24.443  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:24.445  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:24.447  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:24.449  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:24.452  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:24.454  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:24.457  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:24.479  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:24.481  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:24.484  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:24.487  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:24.487  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:24.490  4314-4736  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:24.490  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:24.492  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:24.494  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:24.494  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:24.496   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:24.497   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:24.497   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:24.497   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:24.499   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:24.499   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:24.499   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:24.499   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:24.499   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:24.499   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:24.499   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:24.500   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:24.500   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:24.501  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:24.503  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:25.007  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:25.011  4314-4736  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:25.017  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:25.019   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:25.020   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:25.022   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:25.022   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:25.026   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:25.026   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:25.026   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:25.026   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:25.026   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:25.026   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:25.027   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:25.027   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:25.027   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:25.031  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:25.033  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:25.538  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:25.544  4314-4736  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:25.550  4314-4736  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:25.553   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:25.555   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:25.555   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:25.555   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:25.557   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:25.557   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:25.558   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:25.558   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:25.558   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:25.558   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:25.559   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:25.560   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:25.560   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:25.565  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:25.567  4314-4736  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:25.571  4314-4736  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:25.574  4314-4736  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:25.578  4314-4736  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:25.581  4314-4736  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:25.584  4314-4736  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:25.586  4314-4736  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:25.626  4314-4736  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:28.856  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:28.859  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:28.861  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:28.864  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:28.868  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:28.872  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:28.875  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:28.877  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:28.880  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:28.884  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:28.886  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:28.890  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:28.892  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:28.895  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:28.922  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:28.923  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:28.926  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:28.929  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:28.929  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:28.931  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:28.932  4314-4738  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:28.933  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:28.936  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:28.938   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:28.939   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:28.939   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:28.939   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:28.941   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:28.942   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:28.942   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:28.942   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:28.942   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:28.942   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:28.943   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:28.943   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:28.943   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:28.946  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:28.947  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:29.450  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.455  4314-4738  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:29.462  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.465   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:29.466   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.466   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:29.466   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:29.469   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:29.469   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:29.469   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:29.469   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:29.469   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:29.469   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:29.470   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:29.470   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:29.470   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.474  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:29.475  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:29.585  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:29.588  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:29.591  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:29.594  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:29.597  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:29.599  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:29.601  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:29.603  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:29.607  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:29.611  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:29.613  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:29.616  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:29.618  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:29.620  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:29.624  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:29.627  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:29.653  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:29.655  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:29.658  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:29.661  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.661  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:29.663  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:29.664  4314-4739  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:29.665  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:29.666  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:29.667  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.668  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重置] 开始重新初始化摄像头系统
2025-08-26 15:41:29.669   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:29.669  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] 开始系统摄像头重新初始化: /dev/video0
2025-08-26 15:41:29.670   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.670   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:29.670   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:29.671  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 尝试直接使用USB摄像头设备路径
2025-08-26 15:41:29.672   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:29.672  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] 开始检查USB设备路径: /dev/video0, /dev/video1, /dev/video2, /dev/video3
2025-08-26 15:41:29.672   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:29.672   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:29.672   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:29.672   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:29.672   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:29.673   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:29.674   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:29.674   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.674  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 检查设备路径: /dev/video0
2025-08-26 15:41:29.677  4314-4314  WebRTCSender            com.example.webrtcsender             D  [WebRTCClient] DEBUG: 🎥 [USB摄像头] 设备路径 /dev/video0: exists=true, canRead=true
2025-08-26 15:41:29.677  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:29.679  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 发现USB摄像头设备: /dev/video0
2025-08-26 15:41:29.681  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] 🎯 强制使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:29.681  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:29.683  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 🎯 使用Camera2 API智能处理异常ID: /dev/video0
2025-08-26 15:41:29.684  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] Camera2可用设备: 4294967196
2025-08-26 15:41:29.686  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] 尝试设备 [0]: 4294967196
2025-08-26 15:41:29.687  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 智能创建Camera2摄像头: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:29.689  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 🎯 创建摄像头捕获器: 4294967196
2025-08-26 15:41:29.691  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoSource已创建
2025-08-26 15:41:29.692  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 摄像头支持 10 种格式
2025-08-26 15:41:29.695  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 使用分辨率: 1280x720@30000fps
2025-08-26 15:41:29.716  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] SurfaceTextureHelper已创建
2025-08-26 15:41:29.718  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] VideoCapturer已初始化
2025-08-26 15:41:29.721  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] ✅ 智能Camera2摄像头捕获已启动: 1280x720@30000fps
2025-08-26 15:41:29.723  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能] ✅ 设备 4294967196 创建成功
2025-08-26 15:41:29.724  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.725  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [设备路径] ✅ Camera2智能ID处理成功
2025-08-26 15:41:29.726  4314-4740  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:29.727  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [USB摄像头] ✅ 成功使用设备路径创建视频源: /dev/video0
2025-08-26 15:41:29.728  4314-4314  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [系统重新初始化] ✅ 系统摄像头重新初始化成功
2025-08-26 15:41:29.730  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.732   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:29.732   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.732   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:29.733   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:29.734   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:29.734   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:29.734   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:29.734   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:29.734   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:29.734   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:29.735   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:29.735   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:29.735   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.737  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:29.738  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:29.980  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.985  4314-4738  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:29.992  4314-4738  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:29.996   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:29.998   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:29.998   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:29.998   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:30.001   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:30.001   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:30.001   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:30.001   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:30.001   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:30.001   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:30.002   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:30.003   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:30.003   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.006  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.007  4314-4738  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.010  4314-4738  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.012  4314-4738  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:30.014  4314-4738  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:30.016  4314-4738  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:30.019  4314-4738  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:30.021  4314-4738  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:30.061  4314-4738  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:30.184  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.188  4314-4739  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.195  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.197   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:30.198   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.198   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:30.198   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:30.200   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:30.201   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:30.201   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:30.201   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:30.201   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:30.201   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:30.201   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:30.201   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:30.201   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.203  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.205  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:30.242  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.245  4314-4740  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.250  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.252   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:30.255   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.255   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:30.255   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:30.258   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:30.258   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:30.258   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:30.258   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:30.258   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:30.258   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:30.259   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:30.259   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:30.259   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.261  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.262  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:30.709  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.715  4314-4739  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.720  4314-4739  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.723   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:30.725   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.725   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:30.725   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:30.728   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:30.728   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:30.728   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:30.728   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:30.728   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:30.728   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:30.729   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:30.729   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:30.729   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.733  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.734  4314-4739  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.738  4314-4739  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.741  4314-4739  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:30.745  4314-4739  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:30.751  4314-4739  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:30.753  4314-4739  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:30.756  4314-4739  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:30.766  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.770  4314-4740  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [Camera2智能创建] 正在打开摄像头: 4294967196 (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.774  4314-4740  CameraManager           com.example.webrtcsender             V  Failed to parse camera Id 4294967196 to integer
2025-08-26 15:41:30.777   409-3671  CameraService           cameraserver                         I  CameraService::connect call (PID 4314 "com.example.webrtcsender", camera ID 4294967196) and Camera API version 2
2025-08-26 15:41:30.778   409-3671  Camera2ClientBase       cameraserver                         I  Camera 4294967196: Opened. Client: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.778   409-3671  CameraDeviceClient      cameraserver                         I  CameraDeviceClient 4294967196: Opened
2025-08-26 15:41:30.778   409-3671  CameraService           cameraserver                         I  makeClient: Camera2 API, override to portrait 0
2025-08-26 15:41:30.781   409-3671  CameraService           cameraserver                         I  Camera 4294967196: Access for "com.example.webrtcsender" has been restricted
2025-08-26 15:41:30.782   409-3671  CameraService           cameraserver                         E  connectHelper: Could not initialize client from HAL.
2025-08-26 15:41:30.782   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to disconnect
2025-08-26 15:41:30.782   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: serializationLock acquired
2025-08-26 15:41:30.782   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: Shutting down
2025-08-26 15:41:30.782   409-3671  Camera2ClientBase       cameraserver                         D  Camera 4294967196: start to cacheDump
2025-08-26 15:41:30.783   409-3671  CameraService           cameraserver                         E  unregisterMonitorUid: Trying to unregister uid: 10097 which is not monitored!
2025-08-26 15:41:30.783   409-3671  CameraService           cameraserver                         I  disconnect: Disconnected client for camera 4294967196 for PID 4314
2025-08-26 15:41:30.783   409-3671  Camera2ClientBase       cameraserver                         I  Closed Camera 4294967196. Client was: com.example.webrtcsender (PID 4314, UID 10097)
2025-08-26 15:41:30.787  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.788  4314-4740  WebRTCSender            com.example.webrtcsender             E  [WebRTCClient] ERROR: 🎥 [Camera2智能创建] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy
2025-08-26 15:41:30.790  4314-4740  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 摄像头错误: Failed to open camera: android.hardware.camera2.CameraAccessException: CAMERA_DISABLED (1): connectHelper:1998: Camera "4294967196" disabled by policy (设备: 4294967196, 路径: /dev/video0)
2025-08-26 15:41:30.794  4314-4740  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [智能错误处理] 检测到策略错误，启动系统级摄像头重置
2025-08-26 15:41:30.796  4314-4740  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [重试控制] 停止所有摄像头重试机制
2025-08-26 15:41:30.797  4314-4739  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device policy.
2025-08-26 15:41:30.798  4314-4740  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [重试控制] ✅ 所有摄像头重试已停止
2025-08-26 15:41:30.800  4314-4740  WebRTCSender            com.example.webrtcsender             W  [WebRTCClient] WARN: 🎥 [系统重置] 开始系统级摄像头重置: 4294967196 (路径: /dev/video0)
2025-08-26 15:41:30.801  4314-4740  WebRTCSender            com.example.webrtcsender             I  [WebRTCClient] INFO: 🎥 [完全清理] 开始完全清理所有摄像头资源
2025-08-26 15:41:30.837  4314-4740  Camera2Session          com.example.webrtcsender             E  Error: Camera device could not be opened due to a device 