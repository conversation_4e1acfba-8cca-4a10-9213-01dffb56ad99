package com.example.webrtcsender.command

import com.example.webrtcsender.utils.Logger
import org.json.JSONObject
import java.security.MessageDigest

/**
 * 命令处理器基类
 * 负责接收、验证、分发命令
 */
abstract class CommandHandler {
    companion object {
        private const val TAG = "CommandHandler"
        // 与服务器共享的密码
        private const val SHARED_PASSWORD = "fa0p23497fh-397rgf3f#"
    }
    
    /**
     * 处理收到的命令消息
     */
    fun handleMessage(message: String, deviceId: String) {
        try {
            val jsonObject = JSONObject(message)
            val messageType = jsonObject.optString("type")
            
            Logger.i(TAG, "📨 收到消息: $messageType")
            
            when (messageType) {
                "control_command" -> handleControlCommand(jsonObject, deviceId)
                "upgrade_command" -> handleUpgradeCommand(jsonObject, deviceId)
                "server_config" -> handleServerConfig(jsonObject, deviceId)
                else -> {
                    Logger.w(TAG, "❓ 未知消息类型: $messageType")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 消息解析失败: ${e.message}")
        }
    }
    
    /**
     * 处理控制命令
     */
    private fun handleControlCommand(messageData: JSONObject, deviceId: String) {
        try {
            // 验证签名
            if (!verifySignature(messageData, deviceId)) {
                Logger.e(TAG, "❌ 签名验证失败，拒绝执行命令")
                return
            }
            
            val command = messageData.optString("command")
            val params = messageData.optJSONObject("params") ?: JSONObject()
            
            Logger.i(TAG, "🎮 执行控制命令: $command")
            
            // 执行命令
            val success = executeControlCommand(command, params)
            
            // 发送执行结果
            sendCommandResponse(command, success)
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理控制命令异常: ${e.message}")
        }
    }
    
    /**
     * 处理升级命令
     */
    private fun handleUpgradeCommand(messageData: JSONObject, deviceId: String) {
        try {
            // 验证签名
            if (!verifySignature(messageData, deviceId)) {
                Logger.e(TAG, "❌ 签名验证失败，拒绝执行升级")
                return
            }
            
            val apkUrl = messageData.optString("apk_url")
            val version = messageData.optString("version")
            val force = messageData.optBoolean("force", false)
            
            Logger.i(TAG, "📦 执行升级命令:")
            Logger.i(TAG, "   APK地址: $apkUrl")
            Logger.i(TAG, "   版本: $version")
            Logger.i(TAG, "   强制升级: $force")
            
            // 执行升级
            executeUpgrade(apkUrl, version, force)
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理升级命令异常: ${e.message}")
        }
    }
    
    /**
     * 处理服务器配置
     */
    private fun handleServerConfig(messageData: JSONObject, deviceId: String) {
        try {
            // 验证签名
            if (!verifySignature(messageData, deviceId)) {
                Logger.e(TAG, "❌ 签名验证失败，拒绝更新配置")
                return
            }
            
            Logger.i(TAG, "⚙️ 收到服务器配置更新")
            
            // 更新配置
            updateServerConfig(messageData)
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理服务器配置异常: ${e.message}")
        }
    }
    
    /**
     * 验证消息签名：密码 + 设备号 + 命令JSON + 时间戳 -> MD5
     */
    private fun verifySignature(messageData: JSONObject, deviceId: String): Boolean {
        try {
            val signature = messageData.optString("signature")
            if (signature.isEmpty()) {
                Logger.w(TAG, "⚠️ 消息未包含签名")
                return false
            }
            
            // 移除签名字段，生成验证用的JSON
            val verifyData = JSONObject(messageData.toString())
            verifyData.remove("signature")
            
            // 生成期望的签名
            val expectedSignature = generateMD5Signature(verifyData, deviceId)
            
            // 比较签名
            val isValid = signature == expectedSignature
            
            if (isValid) {
                Logger.i(TAG, "✅ 签名验证成功")
            } else {
                Logger.e(TAG, "❌ 签名验证失败:")
                Logger.e(TAG, "   收到签名: $signature")
                Logger.e(TAG, "   期望签名: $expectedSignature")
            }
            
            return isValid
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 签名验证异常: ${e.message}")
            return false
        }
    }
    
    /**
     * 生成MD5签名：密码 + 设备号 + 命令JSON + 时间戳（兼容浮点数和整数时间戳）
     */
    private fun generateMD5Signature(messageData: JSONObject, deviceId: String): String? {
        try {
            // 获取原始时间戳并保持原始格式
            val timestampValue = messageData.opt("timestamp")

            // 创建用于签名验证的JSON副本，保持原始时间戳格式
            val signatureData = JSONObject()
            val keys = messageData.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                if (key != "signature") {  // 排除签名字段
                    signatureData.put(key, messageData.get(key))
                }
            }

            // 生成JSON字符串（使用与服务器相同的排序方式）
            val messageJson = generateSortedJson(signatureData)

            // 构造签名字符串：密码 + 设备号 + 命令JSON + 时间戳
            val signString = "$SHARED_PASSWORD$deviceId$messageJson$timestampValue"
            
            // 计算MD5
            val md = MessageDigest.getInstance("MD5")
            val hashBytes = md.digest(signString.toByteArray(Charsets.UTF_8))
            
            // 转换为十六进制字符串
            val sb = StringBuilder()
            for (b in hashBytes) {
                sb.append(String.format("%02x", b))
            }
            
            val md5Hash = sb.toString()
            
            Logger.d(TAG, "🔐 签名生成:")
            Logger.d(TAG, "   密码: ${SHARED_PASSWORD.substring(0, 3)}***")
            Logger.d(TAG, "   设备ID: $deviceId")
            Logger.d(TAG, "   时间戳: $timestampValue")
            // Logger.d(TAG, "   签名字符串: ${signString.substring(0, 50)}...")
            Logger.d(TAG, "   MD5签名: $md5Hash")
            
            return md5Hash
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 生成MD5签名失败: ${e.message}")
            return null
        }
    }

    /**
     * 生成排序的JSON字符串（与服务器端保持一致）
     */
    private fun generateSortedJson(jsonObject: JSONObject): String {
        try {
            // 获取所有键并排序
            val keys = mutableListOf<String>()
            val iterator = jsonObject.keys()
            while (iterator.hasNext()) {
                keys.add(iterator.next())
            }
            keys.sort()

            // 构建排序的JSON字符串
            val sortedJson = StringBuilder("{")
            for (i in keys.indices) {
                if (i > 0) sortedJson.append(", ")
                val key = keys[i]
                val value = jsonObject.get(key)

                // 添加键
                sortedJson.append("\"$key\": ")

                // 添加值
                when (value) {
                    is String -> sortedJson.append("\"$value\"")
                    is Number -> sortedJson.append(value.toString())
                    is Boolean -> sortedJson.append(value.toString())
                    is JSONObject -> sortedJson.append(generateSortedJson(value))
                    else -> sortedJson.append("\"$value\"")
                }
            }
            sortedJson.append("}")

            return sortedJson.toString()
        } catch (e: Exception) {
            Logger.e(TAG, "生成排序JSON失败: ${e.message}")
            return jsonObject.toString()
        }
    }

    // 抽象方法，由具体实现类重写
    abstract fun executeControlCommand(command: String, params: JSONObject): Boolean
    abstract fun executeUpgrade(apkUrl: String, version: String, force: Boolean)
    abstract fun updateServerConfig(configData: JSONObject)
    abstract fun sendCommandResponse(command: String, success: Boolean)
}
