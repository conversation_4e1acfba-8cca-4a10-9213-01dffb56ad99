#!/bin/bash

echo "=== 信令服务器依赖安装脚本 ==="

# 检测Python版本
python_version=$(python --version 2>&1)
python3_version=$(python3 --version 2>&1)

echo "当前Python版本: $python_version"
if command -v python3 &> /dev/null; then
    echo "Python3版本: $python3_version"
fi

# 函数：安装PyMySQL
install_pymysql() {
    echo "正在安装PyMySQL..."
    
    if command -v python3 &> /dev/null; then
        echo "使用Python3安装PyMySQL"
        python3 -m pip install PyMySQL
        if [ $? -eq 0 ]; then
            echo "✅ PyMySQL安装成功（Python3）"
            return 0
        fi
    fi
    
    echo "使用Python2安装PyMySQL"
    pip install PyMySQL
    if [ $? -eq 0 ]; then
        echo "✅ PyMySQL安装成功（Python2）"
        return 0
    fi
    
    echo "❌ PyMySQL安装失败，尝试手动安装"
    return 1
}

# 函数：手动安装PyMySQL
manual_install_pymysql() {
    echo "正在手动下载安装PyMySQL..."
    
    # 创建临时目录
    mkdir -p /tmp/pymysql_install
    cd /tmp/pymysql_install
    
    # 下载PyMySQL
    wget https://files.pythonhosted.org/packages/source/P/PyMySQL/PyMySQL-1.0.2.tar.gz
    if [ $? -ne 0 ]; then
        echo "❌ 下载失败，请检查网络连接"
        return 1
    fi
    
    # 解压安装
    tar -xzf PyMySQL-1.0.2.tar.gz
    cd PyMySQL-1.0.2
    
    if command -v python3 &> /dev/null; then
        python3 setup.py install
    else
        python setup.py install
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ PyMySQL手动安装成功"
        cd /
        rm -rf /tmp/pymysql_install
        return 0
    else
        echo "❌ PyMySQL手动安装失败"
        return 1
    fi
}

# 函数：安装websockets
install_websockets() {
    echo "正在安装websockets..."
    
    if command -v python3 &> /dev/null; then
        python3 -m pip install websockets
        if [ $? -eq 0 ]; then
            echo "✅ websockets安装成功（Python3）"
            return 0
        fi
    fi
    
    pip install websockets
    if [ $? -eq 0 ]; then
        echo "✅ websockets安装成功（Python2）"
        return 0
    fi
    
    echo "❌ websockets安装失败"
    return 1
}

# 函数：测试导入
test_imports() {
    echo "正在测试模块导入..."
    
    if command -v python3 &> /dev/null; then
        python3 -c "import pymysql; print('✅ PyMySQL导入成功')" 2>/dev/null
        python3 -c "import websockets; print('✅ websockets导入成功')" 2>/dev/null
    else
        python -c "import pymysql; print('✅ PyMySQL导入成功')" 2>/dev/null
        python -c "import websockets; print('✅ websockets导入成功')" 2>/dev/null
    fi
}

# 主安装流程
echo "开始安装依赖..."

# 安装PyMySQL
install_pymysql
if [ $? -ne 0 ]; then
    manual_install_pymysql
fi

# 安装websockets
install_websockets

# 测试导入
test_imports

echo ""
echo "=== 安装完成 ==="
echo ""
echo "启动信令服务器："
if command -v python3 &> /dev/null; then
    echo "python3 enhanced_signaling_server.py --ws-port 8765 --http-port 28080"
else
    echo "python enhanced_signaling_server.py --ws-port 8765 --http-port 28080"
fi
echo ""
echo "如果仍有问题，请检查："
echo "1. 网络连接是否正常"
echo "2. 是否有足够的权限"
echo "3. 考虑升级到Python 3"
