package com.example.webrtcsender.command

import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import org.json.JSONArray
import org.json.JSONObject

/**
 * 配置更新命令处理器
 * 处理服务器配置更新
 */
class ConfigCommandHandler(
    private val webrtcManager: WebRTCManager,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "ConfigCommandHandler"
    }
    
    override fun executeControlCommand(command: String, params: JSONObject): Boolean {
        // 配置命令处理器不处理控制命令
        Logger.w(TAG, "⚠️ 配置命令处理器不处理控制命令: $command")
        return false
    }
    
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        // 配置命令处理器不处理升级命令
        Logger.w(TAG, "⚠️ 配置命令处理器不处理升级命令")
    }
    
    override fun updateServerConfig(configData: JSONObject) {
        try {
            Logger.i(TAG, "⚙️ 更新服务器配置")
            statusCallback("config", "updating", 50, "正在更新服务器配置...")
            
            val stunServers = configData.optJSONArray("stun_servers")
            val turnServers = configData.optJSONArray("turn_servers")
            
            if (stunServers != null || turnServers != null) {
                updateIceServers(stunServers, turnServers)
            }
            
            // 处理其他配置项
            updateOtherConfigs(configData)
            
            Logger.i(TAG, "✅ 服务器配置更新成功")
            statusCallback("config", "updated", 100, "服务器配置更新成功")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 更新服务器配置失败: ${e.message}")
            statusCallback("config", "update_failed", 0, "更新服务器配置失败: ${e.message}")
        }
    }
    
    /**
     * 更新ICE服务器配置
     */
    private fun updateIceServers(stunServers: JSONArray?, turnServers: JSONArray?) {
        try {
            val iceServers = mutableListOf<String>()
            
            // 处理STUN服务器
            stunServers?.let { stuns ->
                for (i in 0 until stuns.length()) {
                    val stunServer = stuns.getString(i)
                    iceServers.add(stunServer)
                    Logger.i(TAG, "添加STUN服务器: $stunServer")
                }
            }
            
            // 处理TURN服务器
            turnServers?.let { turns ->
                for (i in 0 until turns.length()) {
                    val turnServer = turns.getJSONObject(i)
                    val urls = turnServer.optString("urls")
                    val username = turnServer.optString("username")
                    val credential = turnServer.optString("credential")
                    
                    if (urls.isNotEmpty()) {
                        iceServers.add(urls)
                        Logger.i(TAG, "添加TURN服务器: $urls (用户: $username)")
                    }
                }
            }
            
            // 更新WebRTC管理器的ICE服务器
            if (iceServers.isNotEmpty()) {
                // webrtcManager.updateIceServers(iceServers)
                Logger.i(TAG, "✅ ICE服务器配置已更新，共 ${iceServers.size} 个服务器")
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 更新ICE服务器失败: ${e.message}")
        }
    }
    
    /**
     * 更新其他配置项
     */
    private fun updateOtherConfigs(configData: JSONObject) {
        try {
            // 处理视频配置
            val videoConfig = configData.optJSONObject("video_config")
            videoConfig?.let { config ->
                val defaultBitrate = config.optInt("default_bitrate", -1)
                val defaultResolution = config.optString("default_resolution")
                val defaultCodec = config.optString("default_codec")
                
                if (defaultBitrate > 0) {
                    Logger.i(TAG, "更新默认码率: $defaultBitrate kbps")
                    webrtcManager.setVideoBitrate(defaultBitrate)
                }
                
                if (defaultResolution.isNotEmpty()) {
                    Logger.i(TAG, "更新默认分辨率: $defaultResolution")
                    webrtcManager.setVideoResolution(defaultResolution)
                }
                
                if (defaultCodec.isNotEmpty()) {
                    Logger.i(TAG, "更新默认编码: $defaultCodec")
                    webrtcManager.setVideoCodec(defaultCodec)
                }
            }
            
            // 处理连接配置
            val connectionConfig = configData.optJSONObject("connection_config")
            connectionConfig?.let { config ->
                val heartbeatInterval = config.optInt("heartbeat_interval", -1)
                val reconnectInterval = config.optInt("reconnect_interval", -1)
                
                if (heartbeatInterval > 0) {
                    Logger.i(TAG, "更新心跳间隔: $heartbeatInterval 秒")
                    // 更新心跳间隔配置
                }
                
                if (reconnectInterval > 0) {
                    Logger.i(TAG, "更新重连间隔: $reconnectInterval 秒")
                    // 更新重连间隔配置
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 更新其他配置失败: ${e.message}")
        }
    }
    
    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success)
    }
}
