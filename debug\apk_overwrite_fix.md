# APK文件覆盖问题修复方案

## 🎯 问题分析

从日志可以看到自动升级失败的问题：
```
Unable to open '/storage/emulated/0/Download/update.apk': Permission denied
Failed to parse /storage/emulated/0/Download/update.apk
APK文件解析失败，可能已损坏
```

### 根本原因
1. **文件已存在**: 之前下载的`update.apk`文件仍然存在
2. **无法覆盖**: DownloadManager无法覆盖已存在的文件
3. **权限问题**: 文件可能被其他进程占用或权限不足
4. **文件损坏**: 不完整的下载导致文件损坏但仍然存在

## 🔧 修复方案

### 1. 强化文件清理机制

```kotlin
private fun cleanupOldApkFiles() {
    val apkFiles = listOf(
        File(downloadDir, "update.apk"),
        File(downloadDir, "app-debug.apk"),
        File(downloadDir, "app-release.apk"),
        File(downloadDir, "webrtcsender.apk")
    )
    
    for (apkFile in apkFiles) {
        if (apkFile.exists()) {
            // 多次尝试删除
            for (attempt in 1..3) {
                if (apkFile.delete()) {
                    Logger.i(TAG, "清理文件成功: ${apkFile.name}")
                    break
                } else {
                    Thread.sleep(500) // 等待后重试
                }
            }
        }
    }
}
```

### 2. 备用清理策略

```kotlin
// 如果删除失败，尝试重命名为备份文件
val backupFile = File(downloadDir, "${apkFile.name}.backup.${System.currentTimeMillis()}")
if (apkFile.renameTo(backupFile)) {
    Logger.i(TAG, "文件重命名为备份: ${apkFile.name}")
} else {
    // 最后手段：清空文件内容
    apkFile.writeText("")
    Logger.i(TAG, "已清空文件内容: ${apkFile.name}")
}
```

### 3. 下载管理器记录清理

```kotlin
// 清理下载管理器中的旧下载记录
val dm = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
val query = DownloadManager.Query()
query.setFilterByStatus(DownloadManager.STATUS_SUCCESSFUL or DownloadManager.STATUS_FAILED)
val cursor = dm.query(query)

val idsToRemove = mutableListOf<Long>()
while (cursor.moveToNext()) {
    val title = cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_TITLE))
    val id = cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_ID))
    
    if (title?.contains("应用升级") == true) {
        idsToRemove.add(id)
    }
}

// 删除旧的下载记录
for (id in idsToRemove) {
    dm.remove(id)
}
```

### 4. DownloadManager配置优化

```kotlin
val request = DownloadManager.Request(Uri.parse(apkUrl))
request.setTitle("应用升级")
request.setDescription("正在下载版本 $version")
request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, "update.apk")
request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)

// 允许覆盖和网络下载
request.setAllowedOverRoaming(true)
request.setAllowedOverMetered(true)
```

## 📊 修复流程

### 修复前的问题流程
```
1. 开始下载APK
2. 发现update.apk已存在
3. DownloadManager无法覆盖 ❌
4. 下载失败或文件损坏
5. 验证失败：Permission denied
```

### 修复后的正常流程
```
1. 开始下载APK
2. 强制清理旧文件 ✅
   - 尝试删除update.apk
   - 清理下载记录
   - 备用策略：重命名或清空
3. 下载新APK ✅
4. 验证成功 ✅
5. 安装升级 ✅
```

## 🛡️ 多层防护机制

### 1. 预防性清理
```kotlin
// HTTP下载前
cleanupOldApkFiles()

// FTP下载前
cleanupOldApkFiles()
```

### 2. 渐进式删除策略
```
第1层: 直接删除文件
第2层: 多次重试删除
第3层: 重命名为备份文件
第4层: 清空文件内容
```

### 3. 下载记录管理
```
- 清理成功/失败的旧下载记录
- 避免下载管理器冲突
- 释放系统资源
```

## 📝 详细日志

### 成功清理
```
🧹 开始清理旧APK文件...
🧹 清理文件成功: update.apk (第1次尝试)
🧹 清理了 2 个旧下载记录
🧹 APK文件清理完成
📥 开始下载APK: http://example.com/app.apk
✅ APK下载成功
✅ 下载的APK验证通过
```

### 备用策略
```
🧹 清理文件失败: update.apk (第3次尝试)
🧹 文件重命名为备份: update.apk -> update.apk.backup.1692612345678
📥 开始下载APK: http://example.com/app.apk
```

### 最后手段
```
🧹 无法删除或重命名文件: update.apk
🧹 已清空文件内容: update.apk
📥 开始下载APK: http://example.com/app.apk
```

## ⚠️ 注意事项

### 1. 权限要求
- 需要存储权限
- 需要网络权限
- 需要安装未知来源权限

### 2. 文件路径
- 使用公共下载目录
- 避免应用私有目录
- 确保路径可访问

### 3. 错误处理
```kotlin
try {
    cleanupOldApkFiles()
} catch (e: Exception) {
    Logger.e(TAG, "清理APK文件时出错: ${e.message}")
    // 继续下载，不因清理失败而中断
}
```

## 🚀 测试验证

### 1. 模拟场景
```bash
# 创建一个损坏的update.apk文件
adb shell "echo 'fake apk' > /storage/emulated/0/Download/update.apk"

# 触发升级
# 观察是否能成功清理并重新下载
```

### 2. 验证日志
```bash
adb logcat | grep "🧹\|📥\|✅"
```

### 3. 检查文件
```bash
# 升级前检查
adb shell ls -la /storage/emulated/0/Download/update.apk

# 升级后检查
adb shell ls -la /storage/emulated/0/Download/update.apk
```

## 🎯 预期效果

### 1. 解决覆盖问题
- ✅ 自动清理旧APK文件
- ✅ 清理下载管理器记录
- ✅ 支持多种清理策略

### 2. 提高成功率
- ✅ 减少权限错误
- ✅ 避免文件冲突
- ✅ 处理各种边界情况

### 3. 用户体验
- ✅ 自动处理，无需手动干预
- ✅ 详细日志便于故障排除
- ✅ 多重保障确保升级成功

## 📋 清理文件列表

当前会清理的文件：
- `update.apk` - 主要升级文件
- `app-debug.apk` - 调试版本
- `app-release.apk` - 发布版本
- `webrtcsender.apk` - 应用特定文件

如果需要清理其他文件，可以添加到`apkFiles`列表中。

现在自动升级应该能够正确处理APK文件覆盖问题，确保每次升级都能成功下载和安装！
