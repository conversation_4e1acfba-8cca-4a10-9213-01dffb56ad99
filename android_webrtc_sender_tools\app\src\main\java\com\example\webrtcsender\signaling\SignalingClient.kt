package com.example.webrtcsender.signaling

import android.content.Context
import android.net.ConnectivityManager
import com.example.webrtcsender.WebRTCSenderApp
import com.example.webrtcsender.command.CommandDispatcher
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import com.google.gson.JsonParser
import kotlinx.coroutines.*
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONObject
import org.webrtc.IceCandidate
import org.webrtc.SessionDescription
import java.net.URI
import java.util.Collections
import javax.net.ssl.SSLSocketFactory

/**
 * 信令客户端，负责与信令服务器通信
 */
class SignalingClient(
    private val context: Context,
    private val webrtcManager: WebRTCManager,
    private val listener: SignalingClientListener
) {
    companion object {
        private const val TAG = "SignalingClient"
        private const val RECONNECT_DELAY_MS = 5000L // 5秒重连延迟（更快重连）
        private const val MAX_RECONNECT_ATTEMPTS = Int.MAX_VALUE // 无限重连
        private const val NETWORK_CHECK_INTERVAL = 15000L // 15秒网络检查间隔（更频繁）
        private const val PING_INTERVAL = 20000L // 20秒ping间隔（更频繁）
        private const val PING_TIMEOUT = 30000L // 30秒ping超时
        private const val FORCE_RECONNECT_INTERVAL = 60000L // 60秒强制重连检查间隔
        private const val RECONNECT_COOLDOWN = 10000L // 10秒重连冷却时间
    }

    // WebSocket客户端
    private var webSocket: WebSocketClient? = null

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 连接状态
    private var isConnected = false
    private var isConnecting = false

    // 已注册的ID
    private var registeredId: String? = null

    // 当前客户端角色
    private var currentRole: String? = null

    // 连接开始时间
    private var connectionStartTime: Long = 0

    // 连接的对等端
    private val connectedPeers = Collections.synchronizedSet(mutableSetOf<String>())

    // 重连相关
    private var reconnectAttempts = 0
    private var isReconnecting = false
    private var lastServerUrl: String? = null
    private var reconnectJob: Job? = null

    // 网络状态监控
    private var networkCheckJob: Job? = null
    private var isNetworkAvailable = true

    // 连接有效性检查
    private var pingJob: Job? = null
    private var lastPongTime = 0L
    private var pendingPingCount = 0

    // 强制重连监控
    private var forceReconnectJob: Job? = null

    /**
     * 获取上次重连时间
     */
    private fun getLastReconnectTime(): Long {
        return WebRTCSenderApp.instance.getSharedPreferences("signaling_state", Context.MODE_PRIVATE)
            .getLong("last_reconnect_time", 0)
    }

    /**
     * 保存重连时间
     */
    private fun saveLastReconnectTime(time: Long) {
        WebRTCSenderApp.instance.getSharedPreferences("signaling_state", Context.MODE_PRIVATE)
            .edit()
            .putLong("last_reconnect_time", time)
            .apply()
    }

    // 命令分发器
    private var commandDispatcher: CommandDispatcher? = null

    /**
     * 连接到信令服务器
     */
    fun connect(serverUrl: String) {
        Logger.i(TAG, "连接到信令服务器: $serverUrl")

        if (isConnected) {
            Logger.w(TAG, "已经连接到信令服务器")
            return
        }

        if (isConnecting) {
            Logger.w(TAG, "正在连接到信令服务器，跳过重复连接")
            return
        }

        // 设置连接中状态
        isConnecting = true

        // 保存服务器URL用于重连
        lastServerUrl = serverUrl

        // 简化网络检查 - 直接尝试连接，让WebSocket处理网络问题
        Logger.d(TAG, "开始WebSocket连接")

        try {
            val uri = URI(serverUrl)

            webSocket = object : WebSocketClient(uri) {
                override fun onOpen(handshakedata: ServerHandshake) {
                    Logger.i(TAG, "WebSocket已打开")
                    isConnected = true
                    isConnecting = false // 清除连接中状态
                    reconnectAttempts = 0 // 重置重连次数
                    isReconnecting = false
                    lastPongTime = System.currentTimeMillis()
                    pendingPingCount = 0
                    connectionStartTime = System.currentTimeMillis() // 记录连接开始时间

                    // 启动网络状态监控和连接有效性检查
                    startNetworkMonitoring()
                    startPingMonitoring()
                    startForceReconnectMonitoring()

                    coroutineScope.launch(Dispatchers.Main) {
                        listener.onSignalingConnected()
                    }
                }

                override fun onMessage(message: String) {
                    //Logger.d(TAG, "收到WebSocket消息: $message")
                    Logger.d(TAG, "收到WebSocket消息: **")

                    // 检查是否是心跳响应消息
                    try {
                        val jsonMessage = JSONObject(message)
                        if (jsonMessage.optString("type") == "heartbeat_ack") {
                            lastPongTime = System.currentTimeMillis()
                            pendingPingCount = 0
                            val serverTimestamp = jsonMessage.optLong("timestamp", 0)
                            Logger.d(TAG, "💓 心跳响应: 服务器 -> ${registeredId ?: "unknown"} | 连接正常 | 服务器时间: $serverTimestamp")
                            return
                        }
                    } catch (e: Exception) {
                        // 如果不是JSON格式，继续处理其他消息
                    }

                    coroutineScope.launch {
                        processMessage(message)
                    }
                }

                override fun onClose(code: Int, reason: String, remote: Boolean) {
                    Logger.i(TAG, "WebSocket已关闭: $code, $reason, $remote")
                    isConnected = false
                    isConnecting = false // 清除连接中状态

                    coroutineScope.launch(Dispatchers.Main) {
                        listener.onSignalingDisconnected()
                    }

                    // 如果不是主动断开，检查是否需要重连（添加冷却机制）
                    if (!isReconnecting && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                        val currentTime = System.currentTimeMillis()
                        val lastReconnectTime = getLastReconnectTime()

                        // 添加10秒冷却时间，避免与外部重连冲突
                        if (currentTime - lastReconnectTime > 10000) {
                            Logger.i(TAG, "连接意外断开，准备重连")
                            saveLastReconnectTime(currentTime)
                            scheduleReconnect()
                        } else {
                            Logger.d(TAG, "连接断开但在冷却期内，跳过自动重连")
                        }
                    }
                }

                override fun onError(ex: Exception) {
                    Logger.e(TAG, "WebSocket错误", ex)
                    isConnecting = false // 清除连接中状态

                    coroutineScope.launch(Dispatchers.Main) {
                        // 不显示具体的服务器地址或详细错误信息
                        listener.onSignalingError("无法连接到服务器")
                    }

                    // 如果是网络相关错误，尝试重连
                    if (!isReconnecting && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                        Logger.i(TAG, "连接错误，准备重连")
                        scheduleReconnect()
                    }
                }
            }

            // 如果是WSS，设置SSL
            if (serverUrl.startsWith("wss")) {
                val socketFactory = SSLSocketFactory.getDefault() as SSLSocketFactory
                webSocket?.setSocketFactory(socketFactory)
            }

            // 连接到服务器
            webSocket?.connect()
        } catch (e: Exception) {
            Logger.e(TAG, "连接到信令服务器失败", e)

            coroutineScope.launch(Dispatchers.Main) {
                listener.onSignalingError("无法连接到服务器")
            }

            // 尝试重连
            if (!isReconnecting && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect()
            }
        }
    }

    /**
     * 发送退出消息并断开连接
     */
    fun disconnectWithExitMessage() {
        Logger.i(TAG, "发送退出消息并断开与信令服务器的连接")

        if (!isConnected) {
            Logger.w(TAG, "未连接到信令服务器")
            return
        }

        try {
            // 发送退出消息
            if (registeredId != null) {
                val exitMessage = mapOf(
                    "type" to "gamev_unregister",
                    "id" to registeredId!!
                )
                sendMessageInternal(exitMessage)
                Logger.i(TAG, "已发送退出消息")

                // 等待一小段时间确保消息发送完成
                Thread.sleep(500)
            }

            // 断开连接
            disconnect()
        } catch (e: Exception) {
            Logger.e(TAG, "发送退出消息失败", e)
            // 即使发送退出消息失败，也要断开连接
            disconnect()
        }
    }

    /**
     * 断开与信令服务器的连接
     */
    fun disconnect() {
        Logger.i(TAG, "🔌 断开与信令服务器的连接")

        // 停止重连、网络监控和ping监控
        stopReconnect()
        stopNetworkMonitoring()
        stopPingMonitoring()
        stopForceReconnectMonitoring()

        if (!isConnected) {
            Logger.w(TAG, "🔌 未连接到信令服务器")
            return
        }

        try {
            webSocket?.close()
            webSocket = null
            isConnected = false
            isConnecting = false
            registeredId = null
            connectedPeers.clear()

            // 注意：不要在这里设置 isReconnecting = true
            // 因为这会阻止后续的自动重连
            Logger.i(TAG, "🔌 连接已断开，保持重连能力")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 断开连接失败", e)
        }
    }

    /**
     * 永久断开连接（不再自动重连）
     */
    fun disconnectPermanently() {
        Logger.i(TAG, "🔌 永久断开与信令服务器的连接")

        // 标记为主动断开，避免自动重连
        isReconnecting = true

        // 执行断开操作
        disconnect()

        // 重置重连次数，确保不会再重连
        reconnectAttempts = MAX_RECONNECT_ATTEMPTS

        Logger.i(TAG, "🔌 已永久断开连接，不会自动重连")
    }

    /**
     * 安排重连
     */
    private fun scheduleReconnect() {
        // 检查重连状态和次数
        if (isReconnecting) {
            Logger.w(TAG, "🔄 重连已在进行中，跳过重复安排: ${registeredId ?: "unknown"}")
            return
        }

        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            Logger.e(TAG, "🔄 重连次数已达上限，停止重连: ${registeredId ?: "unknown"} | ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}")
            // 重置重连次数，避免永久无法重连
            Logger.i(TAG, "🔄 重置重连次数，允许后续重连")
            reconnectAttempts = 0
            return
        }

        // 检查网络状态（但不阻止重连）
        val networkDelay = if (!isNetworkAvailable) {
            Logger.w(TAG, "🔄 网络状态未知或不可用，使用延长延迟: ${registeredId ?: "unknown"}")
            RECONNECT_DELAY_MS * 2 // 双倍延迟
        } else {
            RECONNECT_DELAY_MS // 正常延迟
        }

        isReconnecting = true
        reconnectAttempts++

        Logger.i(TAG, "🔄 重连安排: ${registeredId ?: "unknown"} | 尝试 ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} | ${networkDelay/1000}秒后执行 | 网络状态: ${if (isNetworkAvailable) "可用" else "未知"}")

        reconnectJob = coroutineScope.launch {
            try {
                delay(networkDelay)

                // 检查是否仍需要重连
                if (!isConnected && lastServerUrl != null) {
                    Logger.i(TAG, "🔄 重连执行: ${registeredId ?: "unknown"} | 第${reconnectAttempts}次尝试")
                    isReconnecting = false
                    connect(lastServerUrl!!)
                } else {
                    Logger.w(TAG, "🔄 重连跳过: isConnected=$isConnected, lastServerUrl=${lastServerUrl != null}")
                    isReconnecting = false
                }
            } catch (e: Exception) {
                Logger.e(TAG, "🔄 重连执行失败: ${registeredId ?: "unknown"}", e)
                isReconnecting = false

                // 如果重连执行失败，继续尝试（永不放弃）
                coroutineScope.launch {
                    delay(5000) // 5秒后再试
                    if (!isConnected) {
                        Logger.i(TAG, "🔄 重连失败后继续尝试")
                        scheduleReconnect()
                    }
                }
            }
        }
    }

    /**
     * 停止重连
     */
    private fun stopReconnect() {
        reconnectJob?.cancel()
        reconnectJob = null
        isReconnecting = false
    }

    /**
     * 启动网络状态监控
     */
    private fun startNetworkMonitoring() {
        stopNetworkMonitoring()

        networkCheckJob = coroutineScope.launch {
            while (isActive) {
                delay(NETWORK_CHECK_INTERVAL)

                val networkAvailable = isNetworkAvailable()
                if (networkAvailable != isNetworkAvailable) {
                    isNetworkAvailable = networkAvailable
                    Logger.i(TAG, "网络状态变化: ${if (networkAvailable) "可用" else "不可用"}")

                    if (!networkAvailable && isConnected) {
                        Logger.w(TAG, "网络不可用，连接可能会断开")
                    } else if (networkAvailable && !isConnected && !isReconnecting) {
                        Logger.i(TAG, "网络恢复，尝试重连")
                        scheduleReconnect()
                    }
                }
            }
        }
    }

    /**
     * 停止网络状态监控
     */
    private fun stopNetworkMonitoring() {
        networkCheckJob?.cancel()
        networkCheckJob = null
    }

    /**
     * 启动ping监控
     */
    private fun startPingMonitoring() {
        stopPingMonitoring()

        pingJob = coroutineScope.launch {
            while (isActive && isConnected) {
                delay(PING_INTERVAL)

                if (isConnected) {
                    try {
                        // 发送心跳消息（JSON格式）
                        val heartbeatMessage = JSONObject().apply {
                            put("type", "heartbeat")
                            put("timestamp", System.currentTimeMillis())
                            put("from", registeredId ?: "unknown")
                        }
                        webSocket?.send(heartbeatMessage.toString())
                        pendingPingCount++
                        Logger.d(TAG, "💓 心跳发送: ${registeredId ?: "unknown"} -> 服务器 | 待响应数: $pendingPingCount")

                        // 检查是否有太多未响应的ping
                        if (pendingPingCount >= 3) {
                            Logger.w(TAG, "💓 心跳超时: ${registeredId ?: "unknown"} -> 服务器 | 连续${pendingPingCount}次无响应")
                            handleConnectionLost()
                            break
                        }

                        // 检查上次心跳响应时间（只有在发送过心跳后才检查）
                        if (lastPongTime > 0) {
                            val timeSinceLastHeartbeat = System.currentTimeMillis() - lastPongTime
                            if (timeSinceLastHeartbeat > PING_TIMEOUT) {
                                Logger.w(TAG, "💓 心跳超时: ${registeredId ?: "unknown"} -> 服务器 | ${timeSinceLastHeartbeat/1000}秒未收到响应")
                                handleConnectionLost()
                                break
                            }
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "发送ping失败", e)
                        handleConnectionLost()
                        break
                    }
                }
            }
        }
    }

    /**
     * 停止ping监控
     */
    private fun stopPingMonitoring() {
        pingJob?.cancel()
        pingJob = null
        pendingPingCount = 0
    }

    /**
     * 启动强制重连监控
     */
    private fun startForceReconnectMonitoring() {
        stopForceReconnectMonitoring()

        forceReconnectJob = coroutineScope.launch {
            while (isActive) {
                delay(FORCE_RECONNECT_INTERVAL) // 每60秒检查一次

                // 如果未连接且没有在重连，强制启动重连
                if (!isConnected && !isReconnecting && lastServerUrl != null) {
                    Logger.w(TAG, "🔄 强制重连监控: 检测到未连接状态，启动重连")
                    scheduleReconnect()
                }

                // 如果重连状态异常（重连中但时间过长），重置状态
                if (isReconnecting && (System.currentTimeMillis() - connectionStartTime) > 300000) { // 5分钟
                    Logger.w(TAG, "🔄 强制重连监控: 重连状态异常，重置状态")
                    forceResetReconnectState()
                    if (lastServerUrl != null) {
                        scheduleReconnect()
                    }
                }
            }
        }
    }

    /**
     * 停止强制重连监控
     */
    private fun stopForceReconnectMonitoring() {
        forceReconnectJob?.cancel()
        forceReconnectJob = null
    }

    /**
     * 处理连接丢失
     */
    private fun handleConnectionLost() {
        Logger.w(TAG, "🔌 连接丢失: ${registeredId ?: "unknown"} -> 服务器 | 准备重连")

        isConnected = false

        coroutineScope.launch(Dispatchers.Main) {
            listener.onSignalingDisconnected()
        }

        // 如果不是主动断开，尝试重连
        if (!isReconnecting && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            Logger.i(TAG, "🔄 重连准备: ${registeredId ?: "unknown"} | 尝试次数: ${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS}")
            scheduleReconnect()
        }
    }

    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            // 使用Android系统的ConnectivityManager检查网络状态
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                val hasInternet = networkCapabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
                val hasValidated = networkCapabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_VALIDATED) == true

                Logger.d(TAG, "🌐 网络检查: hasInternet=$hasInternet, hasValidated=$hasValidated")
                hasInternet && hasValidated
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                val isConnected = networkInfo?.isConnected == true
                Logger.d(TAG, "🌐 网络检查(旧版): isConnected=$isConnected")
                isConnected
            }
        } catch (e: Exception) {
            Logger.d(TAG, "🌐 网络检查失败: ${e.message}")
            // 如果网络检查失败，假设网络可用，让WebSocket自己处理连接
            // 这样可以避免网络检查错误阻止重连
            true
        }
    }

    /**
     * 注册客户端
     */
    fun register(id: String, role: String, name: String, description: String) {
        Logger.i(TAG, "注册客户端: $id, $role")

        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法注册")
            return
        }

        // 保存当前角色
        currentRole = role

        try {
            val message = mapOf(
                "type" to "register",
                "id" to id,
                "role" to role,
                "name" to name,
                "description" to description
            )

            sendMessageInternal(message)
            registeredId = id
        } catch (e: Exception) {
            Logger.e(TAG, "注册客户端失败", e)
        }
    }

    /**
     * 发送Offer
     */
    fun sendOffer(targetId: String, sdp: SessionDescription) {
        Logger.d(TAG, "发送Offer到 $targetId")

        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送Offer")
            return
        }

        try {
            val message = mapOf(
                "type" to "offer",
                "target" to targetId,
                "sdp" to sdp.description
            )

            sendMessageInternal(message)
        } catch (e: Exception) {
            Logger.e(TAG, "发送Offer失败", e)
        }
    }

    /**
     * 发送Answer
     */
    fun sendAnswer(targetId: String, sdp: SessionDescription) {
        Logger.d(TAG, "发送Answer到 $targetId")

        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送Answer")
            return
        }

        try {
            val message = mapOf(
                "type" to "answer",
                "target" to targetId,
                "sdp" to sdp.description
            )

            sendMessageInternal(message)
        } catch (e: Exception) {
            Logger.e(TAG, "发送Answer失败", e)
        }
    }

    /**
     * 发送ICE候选
     */
    fun sendIceCandidate(targetId: String, candidate: IceCandidate) {
        Logger.d(TAG, "发送ICE候选到 $targetId")

        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送ICE候选")
            return
        }

        try {
            val candidateMap = mapOf(
                "candidate" to candidate.sdp,
                "sdpMid" to candidate.sdpMid,
                "sdpMLineIndex" to candidate.sdpMLineIndex
            )

            val message = mapOf(
                "type" to "candidate",
                "target" to targetId,
                "candidate" to candidateMap
            )

            sendMessageInternal(message)
        } catch (e: Exception) {
            Logger.e(TAG, "发送ICE候选失败", e)
        }
    }

    /**
     * 发送状态更新
     */
    fun sendStatusUpdate(status: String, viewers: Int, resolution: String, fps: Int) {
        Logger.d(TAG, "发送状态更新: $status, 观众: $viewers")

        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送状态更新")
            return
        }

        try {
            // 获取设备IP地址
            val deviceIp = getDeviceIpAddress()

            // 获取游戏包名
            val gamePackage = webrtcManager?.getGamePackage() ?: ""

            // 获取连接时间
            val connectionTime = if (connectionStartTime > 0) connectionStartTime / 1000 else 0

            val message = mapOf(
                "type" to "status_update",
                "status" to status,
                "viewers" to viewers,
                "resolution" to resolution,
                "fps" to fps,
                "ip" to deviceIp,
                "game_package" to gamePackage,
                "connection_time" to connectionTime,
                "last_heartbeat" to (System.currentTimeMillis() / 1000)
            )

            sendMessageInternal(message)
        } catch (e: Exception) {
            Logger.e(TAG, "发送状态更新失败", e)
        }
    }

    /**
     * 获取设备IP地址
     */
    private fun getDeviceIpAddress(): String {
        try {
            val interfaces = java.util.Collections.list(java.net.NetworkInterface.getNetworkInterfaces())
            for (intf in interfaces) {
                val addrs = java.util.Collections.list(intf.inetAddresses)
                for (addr in addrs) {
                    if (!addr.isLoopbackAddress && addr.hostAddress?.contains(':') == false) {
                        return addr.hostAddress ?: "未知"
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "获取IP地址失败: ${e.message}")
        }
        return "未知"
    }

    /**
     * 发送消息（公共方法）
     */
    fun sendMessage(message: Map<String, Any>) {
        sendMessageInternal(message)
    }

    /**
     * 发送消息（字符串格式）
     */
    fun sendMessage(message: String) {
        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送消息")
            return
        }

        try {
            webSocket?.send(message)
            Logger.d(TAG, "📤 发送消息: $message")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 发送消息失败", e)
        }
    }

    /**
     * 发送消息（内部方法）
     */
    private fun sendMessageInternal(message: Map<String, Any>) {
        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送消息")
            return
        }

        try {
            val json = WebRTCManager.gson.toJson(message)
            //Logger.i(TAG, "发送WebSocket消息: $json")
            Logger.i(TAG, "发送WebSocket消息: **")
            webSocket?.send(json)
        } catch (e: Exception) {
            Logger.e(TAG, "发送消息失败", e)
        }
    }

    /**
     * 发送广播消息
     */
    fun sendBroadcast(message: String) {
        if (!isConnected) {
            Logger.e(TAG, "未连接到信令服务器，无法发送广播消息")
            return
        }

        try {
            Logger.i(TAG, "发送广播消息: $message")
            webSocket?.send(message)
        } catch (e: Exception) {
            Logger.e(TAG, "发送广播消息失败", e)
        }
    }

    /**
     * 处理接收到的消息
     */
    private suspend fun processMessage(message: String) {
        try {
            Logger.i(TAG, "处理接收到的消息: $message")
            //Logger.i(TAG, "处理接收到的消息: **")
            val data = WebRTCManager.gson.fromJson(message, Map::class.java)
            val type = data["type"] as? String

            if (type == null) {
                Logger.w(TAG, "消息类型为空: $message")
                return
            }

            // Logger.i(TAG, "消息类型: $type, 数据: $data")

            when (type) {
                "registered" -> {
                    // 检查是否有success字段，如果没有则根据是否有id字段判断成功
                    val success = data["success"] as? Boolean ?: (data["id"] != null)
                    val message = data["message"] as? String
                    val error = data["error"] as? String
                    val id = data["id"] as? String

                    Logger.i(TAG, "🔗 [信令] 客户端注册结果: $success")
                    if (message != null) {
                        Logger.i(TAG, "🔗 [信令] 注册消息: $message")
                    }
                    if (error != null) {
                        Logger.e(TAG, "🔗 [信令] 注册错误: $error")
                    }
                    if (id != null) {
                        Logger.i(TAG, "🔗 [信令] 注册ID: $id")
                        registeredId = id  // 更新注册ID
                    }

                    if (success) {
                        Logger.i(TAG, "🔗 [信令] ✅ 注册成功，开始广播发送端信息")

                        // 初始化命令分发器
                        if (id != null) {
                            initializeCommandDispatcher(id)
                        }

                        // 注册成功后，广播发送端信息
                        WebRTCManager.broadcastSourceInfo()

                        // 发送设备配置信息
                        coroutineScope.launch {
                            delay(1000) // 延迟1秒发送配置信息
                            sendDeviceConfig()
                        }
                    } else {
                        Logger.e(TAG, "🔗 [信令] ❌ 注册失败，请检查服务器状态和参数")
                        Logger.e(TAG, "🔗 [信令] 注册失败详情: $data")
                    }
                }
                "client_joined" -> {
                    val clientId = data["id"] as? String
                    if (clientId == null) {
                        Logger.w(TAG, "客户端ID为空: $data")
                        return
                    }

                    Logger.i(TAG, "客户端加入: $clientId, 角色: ${data["role"]}, 名称: ${data["name"]}")

                    connectedPeers.add(clientId)

                    withContext(Dispatchers.Main) {
                        listener.onClientJoined(clientId)
                    }
                }
                "client_left" -> {
                    val clientId = data["id"] as? String
                    if (clientId == null) {
                        Logger.w(TAG, "客户端ID为空: $data")
                        return
                    }

                    Logger.i(TAG, "客户端离开: $clientId")

                    connectedPeers.remove(clientId)

                    withContext(Dispatchers.Main) {
                        listener.onClientLeft(clientId)
                    }
                }
                "peer_disconnected" -> {
                    val peerId = data["peer_id"] as? String
                    if (peerId == null) {
                        Logger.w(TAG, "对等端ID为空: $data")
                        return
                    }

                    Logger.i(TAG, "收到对等端断开通知: $peerId")

                    // 检查当前客户端角色
                    if (currentRole == "receiver") {
                        Logger.i(TAG, "接收端处理对等端断开: $peerId")
                        connectedPeers.remove(peerId)

                        withContext(Dispatchers.Main) {
                            listener.onClientLeft(peerId)
                        }
                    } else if (currentRole == "source") {
                        Logger.i(TAG, "发送端收到接收端断开通知: $peerId")
                        // 发送端需要清理对应的连接
                        connectedPeers.remove(peerId)

                        withContext(Dispatchers.Main) {
                            listener.onClientLeft(peerId)
                        }
                    } else {
                        Logger.d(TAG, "未知角色忽略对等端断开消息: $peerId")
                    }
                }
                "offer" -> {
                    try {
                        // 使用JsonParser直接解析原始JSON
                        val jsonObject = JsonParser.parseString(message).asJsonObject
                        val clientId = if (jsonObject.has("source")) jsonObject.get("source").asString else null
                        var sdpString = if (jsonObject.has("sdp")) jsonObject.get("sdp").asString else null

                        // 如果from字段存在，使用from字段作为clientId
                        val finalClientId = if (jsonObject.has("from")) jsonObject.get("from").asString else clientId

                        if (finalClientId == null) {
                            Logger.w(TAG, "Offer数据不完整，缺少clientId: $jsonObject")
                            return
                        }

                        if (sdpString == null) {
                            Logger.w(TAG, "Offer数据不完整，缺少SDP: $jsonObject")
                            return
                        }

                        // 处理SDP中的转义字符
                        sdpString = sdpString.replace("\\n", "\n").replace("\\r", "")

                        // 检查SDP是否为空
                        if (sdpString.isBlank()) {
                            Logger.w(TAG, "Offer SDP为空: $jsonObject")
                            return
                        }

                        Logger.i(TAG, "收到Offer: 来自 $finalClientId")
                       // Logger.d(TAG, "Offer SDP: $sdpString")

                        // 创建SessionDescription对象
                        val sdp = SessionDescription(SessionDescription.Type.OFFER, sdpString)

                        withContext(Dispatchers.Main) {
                            listener.onOfferReceived(finalClientId, sdp)
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "处理Offer失败: ${e.message}", e)
                        Logger.e(TAG, "原始消息: $message")

                        // 尝试备用方法
                        try {
                            // 从原始消息中提取关键信息
                            val fromPattern = "\"from\"\\s*:\\s*\"([^\"]*)\""
                            val sourcePattern = "\"source\"\\s*:\\s*\"([^\"]*)\""

                            val fromRegex = fromPattern.toRegex()
                            val sourceRegex = sourcePattern.toRegex()

                            val fromMatch = fromRegex.find(message)
                            val sourceMatch = sourceRegex.find(message)

                            val clientId = fromMatch?.groupValues?.get(1) ?: sourceMatch?.groupValues?.get(1)

                            if (clientId == null) {
                                Logger.w(TAG, "无法提取clientId: $message")
                                return
                            }

                            // 提取SDP
                            val sdpStart = message.indexOf("\"sdp\"")
                            if (sdpStart == -1) {
                                Logger.w(TAG, "无法找到SDP开始位置: $message")
                                return
                            }

                            // 找到SDP值的开始位置
                            val valueStart = message.indexOf(":", sdpStart) + 1

                            // 找到SDP值的结束位置（下一个双引号或逗号）
                            var valueEnd = message.indexOf("\"", valueStart)
                            if (valueEnd == -1) {
                                valueEnd = message.indexOf(",", valueStart)
                            }
                            if (valueEnd == -1) {
                                valueEnd = message.indexOf("}", valueStart)
                            }
                            if (valueEnd == -1) {
                                valueEnd = message.length
                            }

                            // 提取SDP值，处理可能的多行SDP
                            var sdpString = if (valueEnd > valueStart) {
                                message.substring(valueStart, valueEnd).trim()
                            } else {
                                message.substring(valueStart).trim()
                            }

                            // 处理转义字符
                            sdpString = sdpString.replace("\\n", "\n").replace("\\r", "")

                            // 如果SDP以引号开始和结束，去掉引号
                            if (sdpString.startsWith("\"") && sdpString.endsWith("\"")) {
                                sdpString = sdpString.substring(1, sdpString.length - 1)
                            }

                            // 检查SDP是否为空
                            if (sdpString.isBlank()) {
                                Logger.w(TAG, "提取的SDP为空")
                                return
                            }

                            Logger.i(TAG, "备用方法提取的Offer: clientId=$clientId")
                            Logger.d(TAG, "备用方法提取的SDP: $sdpString")

                            val sdp = SessionDescription(SessionDescription.Type.OFFER, sdpString)

                            withContext(Dispatchers.Main) {
                                listener.onOfferReceived(clientId, sdp)
                            }

                            Logger.i(TAG, "备用方法成功处理Offer")
                        } catch (e2: Exception) {
                            Logger.e(TAG, "备用方法处理Offer失败: ${e2.message}", e2)
                        }
                    }
                }
                "answer" -> {
                    try {
                        // 使用JsonParser直接解析原始JSON
                        val jsonObject = JsonParser.parseString(message).asJsonObject
                        val clientId = if (jsonObject.has("source")) jsonObject.get("source").asString else null
                        val sdpString = if (jsonObject.has("sdp")) jsonObject.get("sdp").asString else null

                        // 如果from字段存在，使用from字段作为clientId
                        val finalClientId = if (jsonObject.has("from")) jsonObject.get("from").asString else clientId

                        if (finalClientId == null || sdpString == null) {
                            Logger.w(TAG, "Answer数据不完整: $jsonObject")
                            return
                        }

                        Logger.i(TAG, "收到Answer: 来自 $finalClientId")
                        Logger.d(TAG, "Answer SDP: $sdpString")

                        val sdp = SessionDescription(SessionDescription.Type.ANSWER, sdpString)

                        withContext(Dispatchers.Main) {
                            listener.onAnswerReceived(finalClientId, sdp)
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "处理Answer失败: ${e.message}", e)
                        Logger.e(TAG, "原始消息: $message")

                        // 尝试备用方法
                        try {
                            // 从原始消息中提取关键信息
                            val fromPattern = "\"from\"\\s*:\\s*\"([^\"]*)\""
                            val sourcePattern = "\"source\"\\s*:\\s*\"([^\"]*)\""

                            val fromRegex = fromPattern.toRegex()
                            val sourceRegex = sourcePattern.toRegex()

                            val fromMatch = fromRegex.find(message)
                            val sourceMatch = sourceRegex.find(message)

                            val clientId = fromMatch?.groupValues?.get(1) ?: sourceMatch?.groupValues?.get(1)

                            if (clientId == null) {
                                Logger.w(TAG, "无法提取clientId: $message")
                                return
                            }

                            // 提取SDP
                            val sdpStart = message.indexOf("\"sdp\"")
                            if (sdpStart == -1) {
                                Logger.w(TAG, "无法找到SDP开始位置: $message")
                                return
                            }

                            // 找到SDP值的开始和结束位置
                            val valueStart = message.indexOf(":", sdpStart) + 1

                            // 提取SDP值，处理可能的多行SDP
                            val sdpString = message.substring(valueStart).trim()
                                .replace("\\n", "\n")  // 替换转义的换行符
                                .replace("\\r", "")    // 替换转义的回车符

                            Logger.i(TAG, "备用方法提取的Answer: clientId=$clientId, sdp=$sdpString")

                            val sdp = SessionDescription(SessionDescription.Type.ANSWER, sdpString)

                            withContext(Dispatchers.Main) {
                                listener.onAnswerReceived(clientId, sdp)
                            }

                            Logger.i(TAG, "备用方法成功处理Answer")
                        } catch (e2: Exception) {
                            Logger.e(TAG, "备用方法处理Answer失败: ${e2.message}", e2)
                        }
                    }
                }
                "candidate" -> {
                    // 从from字段获取clientId，如果没有则尝试从source字段获取
                    val clientId = data["from"] as? String ?: data["source"] as? String

                    if (clientId == null) {
                        Logger.w(TAG, "Candidate数据不完整，缺少from或source字段: $data")
                        return
                    }

                    // 直接从原始JSON解析，完全绕过类型检查
                    try {
                        // 使用Gson直接解析原始JSON
                        val jsonObject = JsonParser.parseString(message).asJsonObject
                        val candidateObj = jsonObject.getAsJsonObject("candidate")

                        if (candidateObj == null) {
                            Logger.w(TAG, "Candidate数据不完整，缺少candidate对象: $message")
                            return
                        }

                        // 直接获取字符串值
                        val sdp = if (candidateObj.has("candidate")) candidateObj.get("candidate").asString else ""
                        val sdpMid = if (candidateObj.has("sdpMid")) candidateObj.get("sdpMid").asString else "0"

                        // 获取sdpMLineIndex，确保它是一个整数
                        val sdpMLineIndex = if (candidateObj.has("sdpMLineIndex")) {
                            try {
                                candidateObj.get("sdpMLineIndex").asInt
                            } catch (e: Exception) {
                                // 如果不是整数，尝试作为Double解析然后转换为Int
                                try {
                                    candidateObj.get("sdpMLineIndex").asDouble.toInt()
                                } catch (e2: Exception) {
                                    // 如果所有尝试都失败，使用默认值0
                                    0
                                }
                            }
                        } else {
                            0
                        }

                        Logger.i(TAG, "收到ICE候选: 来自 $clientId")
                        Logger.i(TAG, "ICE候选: sdpMid=$sdpMid, sdpMLineIndex=$sdpMLineIndex, sdp=$sdp")

                        // 验证ICE候选的有效性
                        if (!isValidIceCandidate(sdp, sdpMid, sdpMLineIndex)) {
                            Logger.w(TAG, "ICE候选格式无效，跳过处理")
                            return
                        }

                        // 创建IceCandidate对象
                        val candidate = IceCandidate(sdpMid, sdpMLineIndex, sdp)

                        // 在主线程上通知监听器
                        withContext(Dispatchers.Main) {
                            listener.onIceCandidateReceived(clientId, candidate)
                        }

                        Logger.i(TAG, "成功处理ICE候选")
                    } catch (e: Exception) {
                        Logger.e(TAG, "处理ICE候选失败: ${e.message}", e)
                        Logger.e(TAG, "原始消息: $message")

                        // 尝试备用方法
                        try {
                            Logger.i(TAG, "尝试备用方法处理ICE候选")

                            // 从原始消息中提取关键信息
                            val candidatePattern = "\"candidate\"\\s*:\\s*\"([^\"]*)\""
                            val sdpMidPattern = "\"sdpMid\"\\s*:\\s*\"([^\"]*)\""
                            val sdpMLineIndexPattern = "\"sdpMLineIndex\"\\s*:\\s*(\\d+)"

                            val candidateRegex = candidatePattern.toRegex()
                            val sdpMidRegex = sdpMidPattern.toRegex()
                            val sdpMLineIndexRegex = sdpMLineIndexPattern.toRegex()

                            val candidateMatch = candidateRegex.find(message)
                            val sdpMidMatch = sdpMidRegex.find(message)
                            val sdpMLineIndexMatch = sdpMLineIndexRegex.find(message)

                            val sdp = candidateMatch?.groupValues?.get(1) ?: ""
                            val sdpMid = sdpMidMatch?.groupValues?.get(1) ?: "0"
                            val sdpMLineIndex = sdpMLineIndexMatch?.groupValues?.get(1)?.toIntOrNull() ?: 0

                            Logger.i(TAG, "备用方法提取的ICE候选: sdpMid=$sdpMid, sdpMLineIndex=$sdpMLineIndex, sdp=$sdp")

                            // 创建IceCandidate对象
                            val candidate = IceCandidate(sdpMid, sdpMLineIndex, sdp)

                            // 在主线程上通知监听器
                            withContext(Dispatchers.Main) {
                                listener.onIceCandidateReceived(clientId, candidate)
                            }

                            Logger.i(TAG, "备用方法成功处理ICE候选")
                        } catch (e2: Exception) {
                            Logger.e(TAG, "备用方法处理ICE候选失败: ${e2.message}", e2)
                        }
                    }
                }
                "error" -> {
                    val errorMessage = data["message"] as? String ?: "未知错误"
                    Logger.e(TAG, "信令错误: $errorMessage")

                    withContext(Dispatchers.Main) {
                        listener.onSignalingError(errorMessage)
                    }
                }
                "control_command", "upgrade_command", "server_config" -> {
                    // 分发命令到命令处理器
                    commandDispatcher?.dispatchMessage(message)
                }
                "device_log" -> {
                    // 设备日志消息已发送到服务器，这里只记录确认
                    Logger.d(TAG, "📝 设备日志已发送到信令服务器")
                }
                else -> {
                    Logger.w(TAG, "未知消息类型: $type, 数据: $data")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "处理消息失败: ${e.message}", e)
            Logger.e(TAG, "原始消息: $message")

            withContext(Dispatchers.Main) {
                listener.onSignalingError("处理消息失败: ${e.message}")
            }
        }
    }

    /**
     * 验证ICE候选的有效性
     */
    private fun isValidIceCandidate(sdp: String, sdpMid: String?, sdpMLineIndex: Int): Boolean {
        // 检查SDP是否为空（空SDP可能是结束标志，应该允许）
        if (sdp.isBlank()) {
            Logger.d(TAG, "ICE候选SDP为空，可能是结束标志")
            return true
        }

        // 检查SDP格式是否正确（应该以"candidate:"开头）
        if (!sdp.startsWith("candidate:")) {
            Logger.w(TAG, "ICE候选SDP格式不正确: $sdp")
            return false
        }

        // 检查sdpMLineIndex是否有效
        if (sdpMLineIndex < 0) {
            Logger.w(TAG, "ICE候选sdpMLineIndex无效: $sdpMLineIndex")
            return false
        }

        // 检查SDP是否包含必要的信息
        val parts = sdp.split(" ")
        if (parts.size < 6) {
            Logger.w(TAG, "ICE候选SDP格式不完整: $sdp")
            return false
        }

        return true
    }

    /**
     * 获取已连接的对等端
     */
    fun getConnectedPeers(): List<String> {
        return connectedPeers.toList()
    }

    /**
     * 是否已连接
     */
    fun isConnected(): Boolean {
        return isConnected
    }

    /**
     * 是否正在重连
     */
    fun isReconnecting(): Boolean {
        return isReconnecting
    }

    /**
     * 是否正在连接中
     */
    fun isConnecting(): Boolean {
        return isConnecting
    }

    /**
     * 获取注册的ID
     */
    fun getRegisteredId(): String? {
        return registeredId
    }

    /**
     * 手动重连
     */
    fun reconnect() {
        Logger.i(TAG, "🔄 手动重连信令服务器: ${registeredId ?: "unknown"}")

        if (lastServerUrl == null) {
            Logger.e(TAG, "❌ 没有保存的服务器URL，无法重连")
            return
        }

        // 强制重置所有重连状态
        forceResetReconnectState()

        // 断开当前连接
        if (isConnected) {
            Logger.i(TAG, "🔄 断开当前连接")
            disconnect()
        }

        // 延迟一下再重连
        coroutineScope.launch {
            delay(1000)
            Logger.i(TAG, "🔄 开始手动重连")
            connect(lastServerUrl!!)
        }
    }

    /**
     * 强制重置重连状态
     */
    fun forceResetReconnectState() {
        Logger.i(TAG, "🔄 强制重置重连状态: ${registeredId ?: "unknown"}")

        // 停止当前重连任务
        stopReconnect()

        // 重置所有状态
        reconnectAttempts = 0
        isReconnecting = false
        isConnecting = false

        Logger.i(TAG, "🔄 重连状态已重置: attempts=0, isReconnecting=false, isConnecting=false")
    }

    /**
     * 检查并修复重连状态
     */
    fun checkAndFixReconnectState() {
        val currentTime = System.currentTimeMillis()

        // 如果连接状态异常超过2分钟，强制重置
        if (!isConnected && isReconnecting && (currentTime - connectionStartTime) > 120000) {
            Logger.w(TAG, "🔄 检测到重连状态异常，强制重置: ${registeredId ?: "unknown"}")
            forceResetReconnectState()

            // 如果网络可用，立即尝试重连
            if (isNetworkAvailable && lastServerUrl != null) {
                Logger.i(TAG, "🔄 网络可用，立即尝试重连")
                coroutineScope.launch {
                    delay(1000)
                    connect(lastServerUrl!!)
                }
            }
        }

        // 如果重连次数过多，重置计数器
        if (reconnectAttempts > MAX_RECONNECT_ATTEMPTS * 0.8) {
            Logger.w(TAG, "🔄 重连次数过多，重置计数器: ${reconnectAttempts} -> 0")
            reconnectAttempts = 0
        }
    }

    /**
     * 获取连接状态信息
     */
    fun getConnectionInfo(): String {
        return "连接状态: ${if (isConnected) "已连接" else "未连接"}, " +
                "重连次数: $reconnectAttempts/$MAX_RECONNECT_ATTEMPTS, " +
                "网络状态: ${if (isNetworkAvailable) "可用" else "不可用"}"
    }

    /**
     * 初始化命令分发器
     */
    private fun initializeCommandDispatcher(deviceId: String) {
        try {
            Logger.i(TAG, "🔧 初始化命令分发器，设备ID: $deviceId")

            commandDispatcher = CommandDispatcher(
                context = context,
                webrtcManager = webrtcManager,
                deviceId = deviceId,
                responseCallback = { command, success ->
                    sendCommandResponse(command, success)
                },
                statusCallback = { type, step, progress, message ->
                    sendStatusUpdate(type, step, progress, message)
                },
                extendedResponseCallback = { command, success, filename ->
                    sendCommandResponse(command, success, filename)
                }
            )

            Logger.i(TAG, "✅ 命令分发器初始化成功")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 初始化命令分发器失败: ${e.message}")
        }
    }

    /**
     * 发送命令执行响应
     */
    private fun sendCommandResponse(command: String, success: Boolean, filename: String? = null) {
        try {
            val response = mutableMapOf(
                "type" to "command_response",
                "command" to command,
                "success" to success,
                "message" to if (success) "命令执行成功" else "命令执行失败",
                "timestamp" to (System.currentTimeMillis() / 1000) // 使用整数时间戳
            )

            // 如果有文件名，添加到响应中
            if (filename != null) {
                response["filename"] = filename
                Logger.i(TAG, "📁 命令响应包含文件名: $filename")
            }

            sendMessageInternal(response)
            Logger.i(TAG, if (success) "✅ 命令执行成功: $command" else "❌ 命令执行失败: $command")

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 发送命令响应失败: ${e.message}")
        }
    }

    /**
     * 发送状态更新（升级进度等）
     */
    private fun sendStatusUpdate(type: String, step: String, progress: Int, message: String) {
        try {
            val statusUpdate = mapOf(
                "type" to "status_update",
                "status_type" to type,
                "step" to step,
                "progress" to progress,
                "message" to message,
                "timestamp" to (System.currentTimeMillis() / 1000) // 使用整数时间戳
            )

            sendMessageInternal(statusUpdate)
            Logger.i(TAG, "📊 发送状态更新: $type - $step ($progress%) - $message")

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 发送状态更新失败: ${e.message}")
        }
    }

    /**
     * 发送设备配置信息
     */
    private fun sendDeviceConfig() {
        try {
            Logger.i(TAG, "📋 发送设备配置信息")

            // 获取应用版本信息
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            val appVersion = packageInfo.versionName

            // 获取WebRTC配置
            val resolution = webrtcManager.getVideoResolution()
            val bitrate = webrtcManager.getVideoBitrate()
            val codec = webrtcManager.getVideoCodec()
            val videoSource = webrtcManager.getVideoSourceType()
            val cameraId = webrtcManager.getCameraId()

            // 获取系统信息
            val deviceModel = android.os.Build.MODEL
            val androidVersion = android.os.Build.VERSION.RELEASE
            val apiLevel = android.os.Build.VERSION.SDK_INT

            val configInfo = mapOf(
                "type" to "device_config",
                "device_info" to mapOf(
                    "device_id" to registeredId,
                    "device_model" to deviceModel,
                    "android_version" to androidVersion,
                    "api_level" to apiLevel,
                    "app_version" to appVersion
                ),
                "video_config" to mapOf(
                    "resolution" to resolution,
                    "bitrate" to bitrate,
                    "codec" to codec,
                    "fps" to 30, // 默认帧率
                    "video_source" to videoSource,
                    "camera_id" to cameraId
                ),
                "audio_config" to mapOf(
                    "enabled" to true, // 假设音频已启用
                    "codec" to "OPUS", // 默认音频编码
                    "sample_rate" to 48000,
                    "channels" to 1
                ),
                "connection_config" to mapOf(
                    "signaling_url" to lastServerUrl,
                    "ice_servers" to listOf("stun:stun.l.google.com:19302") // 默认ICE服务器
                ),
                "timestamp" to (System.currentTimeMillis() / 1000) // 使用整数时间戳
            )

            sendMessageInternal(configInfo)
            Logger.i(TAG, "✅ 设备配置信息发送成功")
            Logger.i(TAG, "   版本: $appVersion")
            Logger.i(TAG, "   分辨率: $resolution")
            Logger.i(TAG, "   码率: $bitrate kbps")
            Logger.i(TAG, "   编码: $codec")
            Logger.i(TAG, "   视频源: $videoSource")

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 发送设备配置信息失败: ${e.message}")
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        Logger.i(TAG, "释放信令客户端资源")

        stopReconnect()
        stopNetworkMonitoring()
        stopPingMonitoring()
        disconnect()
        coroutineScope.cancel()
    }
}
