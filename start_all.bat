@echo off
chcp 65001 > nul
echo ===================================
echo    WebRTC视频流系统一键启动脚本
echo ===================================
echo.

REM 获取本地IP地址
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set LOCAL_IP=%%a
    goto :found_ip
)
:found_ip
set LOCAL_IP=%LOCAL_IP:~1%

REM 检查视频源
echo 正在检查视频源...
set VIDEO_SOURCE=http://*************:80/0.mp4
curl -s -I %VIDEO_SOURCE% > nul
if %errorlevel% neq 0 (
    echo 警告: 无法访问视频源 %VIDEO_SOURCE%
    echo 将使用测试视频源...
    set VIDEO_SOURCE=test
) else (
    echo 视频源可访问: %VIDEO_SOURCE%
)

REM 步骤1: 启动信令服务器
echo.
echo 步骤1: 启动信令服务器...
start cmd /k "python enhanced_signaling_server.py --ws-port 8765 --http-port 8080 --web-dir ./web"
echo 信令服务器已启动，请保持该窗口运行

REM 等待信令服务器初始化
timeout /t 3 /nobreak > nul

REM 步骤2: 启动视频发送端
echo.
echo 步骤2: 启动视频发送端...
start cmd /k "python web_sender_adapter.py --video %VIDEO_SOURCE% --id video-stream --name \"视频流\" --description \"WebRTC视频流\" --signaling ws://%LOCAL_IP%:8765"
echo 视频发送端已启动

echo.
echo 所有组件已启动!
echo.
echo 访问信息:
echo - Web界面: http://%LOCAL_IP%:8080
echo - 本机访问: http://localhost:8080
echo.
echo 按任意键打开浏览器查看...
pause > nul

start http://localhost:8080
