<lint-module
    format="1"
    dir="C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test"
    name=":"
    type="APP"
    maven=":android_test:"
    gradle="7.0.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-30\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.2\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-30"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
