{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,879,970,1063,1155,1249,1349,1442,1537,1630,1721,1815,1894,1999,2097,2195,2303,2403,2506,2661,2758", "endColumns": "107,103,106,81,100,113,79,77,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,874,965,1058,1150,1244,1344,1437,1532,1625,1716,1810,1889,1994,2092,2190,2298,2398,2501,2656,2753,2835"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "470,578,682,789,871,972,1086,1166,1244,1335,1428,1520,1614,1714,1807,1902,1995,2086,2180,2259,2364,2462,2560,2668,2768,2871,3026,7436", "endColumns": "107,103,106,81,100,113,79,77,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "573,677,784,866,967,1081,1161,1239,1330,1423,1515,1609,1709,1802,1897,1990,2081,2175,2254,2359,2457,2555,2663,2763,2866,3021,3118,7513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,420,504,605,724,801,864,955,1024,1091,1191,1256,1317,1385,1447,1505,1619,1679,1740,1797,1870,1993,2074,2154,2272,2353,2434,2523,2590,2656,2734,2814,2898,2970,3044,3117,3187,3278,3349,3439,3534,3608,3691,3784,3833,3902,3988,4073,4135,4199,4262,4371,4463,4560,4653", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "415,499,600,719,796,859,950,1019,1086,1186,1251,1312,1380,1442,1500,1614,1674,1735,1792,1865,1988,2069,2149,2267,2348,2429,2518,2585,2651,2729,2809,2893,2965,3039,3112,3182,3273,3344,3434,3529,3603,3686,3779,3828,3897,3983,4068,4130,4194,4257,4366,4458,4555,4648,4728"}, "to": {"startLines": "2,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3123,3207,3308,3427,3504,3567,3658,3727,3794,3894,3959,4020,4088,4150,4208,4322,4382,4443,4500,4573,4696,4777,4857,4975,5056,5137,5226,5293,5359,5437,5517,5601,5673,5747,5820,5890,5981,6052,6142,6237,6311,6394,6487,6536,6605,6691,6776,6838,6902,6965,7074,7166,7263,7356", "endLines": "9,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "465,3202,3303,3422,3499,3562,3653,3722,3789,3889,3954,4015,4083,4145,4203,4317,4377,4438,4495,4568,4691,4772,4852,4970,5051,5132,5221,5288,5354,5432,5512,5596,5668,5742,5815,5885,5976,6047,6137,6232,6306,6389,6482,6531,6600,6686,6771,6833,6897,6960,7069,7161,7258,7351,7431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "92", "startColumns": "4", "startOffsets": "7518", "endColumns": "100", "endOffsets": "7614"}}]}]}