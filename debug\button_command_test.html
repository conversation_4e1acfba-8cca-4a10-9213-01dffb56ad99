<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮命令测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .device-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 20px 0;
        }
        
        .device-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        
        .device-info {
            margin-bottom: 15px;
        }
        
        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .device-info-value {
            color: #495057;
            font-weight: 600;
        }
        
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }
        
        .control-main-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.2s;
        }
        
        .control-main-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .command-log {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
        }
        
        .log-command {
            color: #68d391;
            font-weight: bold;
        }
        
        .log-device {
            color: #63b3ed;
        }
        
        .log-time {
            color: #a0aec0;
            font-size: 11px;
        }
        
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 按钮命令测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <ul>
                <li>✅ <strong>在线设备</strong>: 主按钮显示"重启服务"，发送 <code>restart_service</code> 命令</li>
                <li>✅ <strong>离线设备</strong>: 主按钮显示"启动服务"，发送 <code>start_service</code> 命令</li>
                <li>✅ 点击按钮会在下方日志中显示实际发送的命令</li>
                <li>✅ 验证命令与按钮文本的一致性</li>
            </ul>
        </div>
        
        <h2>📊 模拟设备状态测试</h2>
        
        <div class="device-grid">
            <!-- 在线设备 -->
            <div class="device-card">
                <h3>🎮 gamev-online001</h3>
                <div class="device-info">
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">ocean3</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">最近上线:</span>
                        <span class="device-info-value">在线 (2024-01-15 14:30:25)</span>
                    </div>
                </div>
                
                <button class="control-main-btn" onclick="testCommand('gamev-online001', 'restart_service', '重启服务')">
                    🔄 重启服务
                </button>
                
                <div style="font-size: 12px; color: #6c757d; text-align: center;">
                    预期命令: <code>restart_service</code>
                </div>
            </div>
            
            <!-- 离线设备 -->
            <div class="device-card">
                <h3>🎮 gamev-offline002</h3>
                <div class="device-info">
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-offline">● 离线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">无</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">最近上线:</span>
                        <span class="device-info-value">2024-01-15 12:20:15 (2小时前)</span>
                    </div>
                </div>
                
                <button class="control-main-btn" onclick="testCommand('gamev-offline002', 'start_service', '启动服务')">
                    🚀 启动服务
                </button>
                
                <div style="font-size: 12px; color: #6c757d; text-align: center;">
                    预期命令: <code>start_service</code>
                </div>
            </div>
        </div>
        
        <h2>📝 命令日志</h2>
        <button class="clear-btn" onclick="clearLog()">清空日志</button>
        <div class="command-log" id="commandLog">
            <div class="log-entry">
                <span class="log-time">[系统]</span> 
                <span style="color: #a0aec0;">点击按钮测试命令发送...</span>
            </div>
        </div>
        
        <h2>🔧 下拉菜单命令测试</h2>
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 20px 0;">
            <button class="control-main-btn" onclick="testCommand('test-device', 'start_service', '启动服务')">
                🚀 启动服务
            </button>
            <button class="control-main-btn" onclick="testCommand('test-device', 'stop_service', '停止服务')">
                ⏹️ 停止服务
            </button>
            <button class="control-main-btn" onclick="testCommand('test-device', 'restart_service', '重启服务')">
                🔄 重启服务
            </button>
        </div>
        
        <h2>📊 测试结果统计</h2>
        <div id="testStats" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <div>总测试次数: <span id="totalTests">0</span></div>
            <div>start_service 命令: <span id="startCount">0</span></div>
            <div>stop_service 命令: <span id="stopCount">0</span></div>
            <div>restart_service 命令: <span id="restartCount">0</span></div>
        </div>
    </div>

    <script>
        let testCounts = {
            total: 0,
            start_service: 0,
            stop_service: 0,
            restart_service: 0
        };

        function testCommand(deviceId, command, buttonText) {
            // 记录统计
            testCounts.total++;
            testCounts[command]++;
            
            // 更新统计显示
            updateStats();
            
            // 添加到日志
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-time">[${timeStr}]</span>
                <span class="log-device">${deviceId}</span>: 
                <span style="color: #fbb6ce;">"${buttonText}"</span> → 
                <span class="log-command">${command}</span>
            `;
            
            const logContainer = document.getElementById('commandLog');
            logContainer.appendChild(logEntry);
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 模拟发送命令（实际环境中这里会发送HTTP请求）
            console.log(`发送命令: ${command} 到设备: ${deviceId}`);
            
            // 验证命令正确性
            validateCommand(buttonText, command);
        }
        
        function validateCommand(buttonText, command) {
            let isCorrect = false;
            let expectedCommand = '';
            
            if (buttonText === '启动服务') {
                expectedCommand = 'start_service';
                isCorrect = (command === 'start_service');
            } else if (buttonText === '停止服务') {
                expectedCommand = 'stop_service';
                isCorrect = (command === 'stop_service');
            } else if (buttonText === '重启服务') {
                expectedCommand = 'restart_service';
                isCorrect = (command === 'restart_service');
            }
            
            const logContainer = document.getElementById('commandLog');
            const validationEntry = document.createElement('div');
            validationEntry.className = 'log-entry';
            
            if (isCorrect) {
                validationEntry.innerHTML = `
                    <span style="color: #68d391;">✅ 验证通过</span>: 
                    "${buttonText}" 正确发送了 <code>${command}</code> 命令
                `;
            } else {
                validationEntry.innerHTML = `
                    <span style="color: #fc8181;">❌ 验证失败</span>: 
                    "${buttonText}" 应该发送 <code>${expectedCommand}</code>，但实际发送了 <code>${command}</code>
                `;
            }
            
            logContainer.appendChild(validationEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = testCounts.total;
            document.getElementById('startCount').textContent = testCounts.start_service;
            document.getElementById('stopCount').textContent = testCounts.stop_service;
            document.getElementById('restartCount').textContent = testCounts.restart_service;
        }
        
        function clearLog() {
            const logContainer = document.getElementById('commandLog');
            logContainer.innerHTML = `
                <div class="log-entry">
                    <span class="log-time">[系统]</span> 
                    <span style="color: #a0aec0;">日志已清空，点击按钮测试命令发送...</span>
                </div>
            `;
            
            // 重置统计
            testCounts = {
                total: 0,
                start_service: 0,
                stop_service: 0,
                restart_service: 0
            };
            updateStats();
        }
        
        // 页面加载完成后显示初始状态
        window.addEventListener('DOMContentLoaded', () => {
            updateStats();
        });
    </script>
</body>
</html>
