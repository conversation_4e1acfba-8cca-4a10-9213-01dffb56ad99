<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }
        
        .dropdown-toggle {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .dropdown-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            min-width: 720px;
            z-index: 9999;
            display: none;
            padding: 15px;
            margin-top: 5px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
        }
        
        .dropdown-menu.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .dropdown-menu.adjust-position {
            right: auto;
            left: -600px;
        }
        
        .dropdown-menu.adjust-left {
            right: auto;
            left: 0;
        }
        
        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }
        
        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }
        
        .modal-overlay.show {
            display: flex;
        }
        
        .modal-dialog {
            background: white;
            border-radius: 12px;
            padding: 25px;
            max-width: 600px;
            width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>下拉菜单位置测试</h1>
    <p>测试下拉菜单在不同位置的显示效果，确保不会被遮挡</p>
    
    <div class="test-container">
        <!-- 左侧测试 -->
        <div class="test-card">
            <h3>左侧位置测试</h3>
            <p>设备ID: test-left</p>
            <div class="control-dropdown">
                <button class="dropdown-toggle" onclick="toggleDropdown('left')">
                    ⚙️ 功能菜单
                </button>
                <div class="dropdown-menu" id="dropdown-left">
                    <div class="dropdown-columns">
                        <div class="dropdown-section">
                            <h4>服务控制</h4>
                            <button class="dropdown-btn">🚀 启动服务</button>
                            <button class="dropdown-btn">⏹️ 停止服务</button>
                            <button class="dropdown-btn">🔄 重启服务</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>游戏控制</h4>
                            <button class="dropdown-btn">🎮 游戏设置</button>
                            <button class="dropdown-btn">📝 日志管理</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>网络配置</h4>
                            <button class="dropdown-btn" onclick="showModal()">🌐 STUN/TURN配置</button>
                            <button class="dropdown-btn">📡 发送配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 中间测试 -->
        <div class="test-card">
            <h3>中间位置测试</h3>
            <p>设备ID: test-center</p>
            <div class="control-dropdown">
                <button class="dropdown-toggle" onclick="toggleDropdown('center')">
                    ⚙️ 功能菜单
                </button>
                <div class="dropdown-menu" id="dropdown-center">
                    <div class="dropdown-columns">
                        <div class="dropdown-section">
                            <h4>服务控制</h4>
                            <button class="dropdown-btn">🚀 启动服务</button>
                            <button class="dropdown-btn">⏹️ 停止服务</button>
                            <button class="dropdown-btn">🔄 重启服务</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>游戏控制</h4>
                            <button class="dropdown-btn">🎮 游戏设置</button>
                            <button class="dropdown-btn">📝 日志管理</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>网络配置</h4>
                            <button class="dropdown-btn" onclick="showModal()">🌐 STUN/TURN配置</button>
                            <button class="dropdown-btn">📡 发送配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧测试 -->
        <div class="test-card">
            <h3>右侧位置测试</h3>
            <p>设备ID: test-right</p>
            <div class="control-dropdown">
                <button class="dropdown-toggle" onclick="toggleDropdown('right')">
                    ⚙️ 功能菜单
                </button>
                <div class="dropdown-menu" id="dropdown-right">
                    <div class="dropdown-columns">
                        <div class="dropdown-section">
                            <h4>服务控制</h4>
                            <button class="dropdown-btn">🚀 启动服务</button>
                            <button class="dropdown-btn">⏹️ 停止服务</button>
                            <button class="dropdown-btn">🔄 重启服务</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>游戏控制</h4>
                            <button class="dropdown-btn">🎮 游戏设置</button>
                            <button class="dropdown-btn">📝 日志管理</button>
                        </div>
                        <div class="dropdown-section">
                            <h4>网络配置</h4>
                            <button class="dropdown-btn" onclick="showModal()">🌐 STUN/TURN配置</button>
                            <button class="dropdown-btn">📡 发送配置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 测试模态框 -->
    <div class="modal-overlay" id="testModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>🌐 STUN/TURN服务器配置</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>这是一个测试模态框，用于验证STUN/TURN配置功能是否正常工作。</p>
                <textarea rows="4" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px;" placeholder="stun:stun.l.google.com:19302"></textarea>
            </div>
            <div class="modal-footer" style="margin-top: 20px;">
                <button class="btn" onclick="closeModal()">取消</button>
                <button class="btn">保存配置</button>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(position) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${position}`) {
                    menu.classList.remove('show');
                    menu.classList.remove('adjust-position');
                    menu.classList.remove('adjust-left');
                }
            });

            const dropdown = document.getElementById(`dropdown-${position}`);
            const isShowing = dropdown.classList.contains('show');

            if (isShowing) {
                dropdown.classList.remove('show');
                dropdown.classList.remove('adjust-position');
                dropdown.classList.remove('adjust-left');
            } else {
                dropdown.classList.add('show');
                
                // 调整位置
                setTimeout(() => {
                    adjustDropdownPosition(dropdown);
                }, 10);
            }
        }

        function adjustDropdownPosition(dropdown) {
            const rect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;

            // 重置位置类
            dropdown.classList.remove('adjust-position');
            dropdown.classList.remove('adjust-left');

            // 检查右边界
            if (rect.right > windowWidth) {
                dropdown.classList.add('adjust-position');
            }

            // 重新获取调整后的位置
            const newRect = dropdown.getBoundingClientRect();
            
            // 检查左边界
            if (newRect.left < 0) {
                dropdown.classList.remove('adjust-position');
                dropdown.classList.add('adjust-left');
            }
        }

        function showModal() {
            document.getElementById('testModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('testModal').classList.remove('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.classList.remove('adjust-position');
                    menu.classList.remove('adjust-left');
                });
            }
        });
    </script>
</body>
</html>
