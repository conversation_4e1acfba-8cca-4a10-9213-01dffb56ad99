-- 添加房间信息字段到 fa_sender_device_info 表
-- 执行前请确保已连接到正确的数据库

-- 检查表是否存在
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'fa_sender_device_info';

-- 添加房间信息相关字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN IF NOT EXISTS room_server_domain VARCHAR(255) DEFAULT '' COMMENT '关联的服务器域名',
ADD COLUMN IF NOT EXISTS room_id INT DEFAULT 0 COMMENT '关联的房间ID',
ADD COLUMN IF NOT EXISTS room_name VARCHAR(100) DEFAULT '' COMMENT '关联的房间名称',
ADD COLUMN IF NOT EXISTS room_category_id INT DEFAULT 0 COMMENT '房间分类ID',
ADD COLUMN IF NOT EXISTS room_sort_order INT DEFAULT 0 COMMENT '房间排序',
ADD COLUMN IF NOT EXISTS room_last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '房间信息最后更新时间';

-- 为新字段添加索引以提高查询性能
ALTER TABLE fa_sender_device_info 
ADD INDEX IF NOT EXISTS idx_room_server_domain (room_server_domain),
ADD INDEX IF NOT EXISTS idx_room_category_id (room_category_id),
ADD INDEX IF NOT EXISTS idx_room_sort_order (room_sort_order);

-- 查看表结构确认字段已添加
DESCRIBE fa_sender_device_info;

-- 查看新增字段的详细信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE table_schema = DATABASE() 
AND table_name = 'fa_sender_device_info'
AND COLUMN_NAME LIKE 'room_%'
ORDER BY ORDINAL_POSITION;

-- 统计当前设备数量
SELECT COUNT(*) as total_devices FROM fa_sender_device_info;

-- 统计有房间信息的设备数量
SELECT 
    COUNT(*) as devices_with_room_info,
    COUNT(CASE WHEN room_name != '' THEN 1 END) as devices_with_room_name,
    COUNT(CASE WHEN room_server_domain != '' THEN 1 END) as devices_with_server_domain
FROM fa_sender_device_info;

-- 按服务器域名统计设备分布
SELECT 
    CASE 
        WHEN room_server_domain = '' THEN '未分配服务器'
        ELSE room_server_domain 
    END as server_domain,
    COUNT(*) as device_count
FROM fa_sender_device_info 
GROUP BY room_server_domain
ORDER BY device_count DESC;

-- 按房间分类统计设备分布
SELECT 
    CASE room_category_id
        WHEN 0 THEN '未分类'
        WHEN 1 THEN '街机游戏'
        WHEN 2 THEN '休闲游戏'
        WHEN 3 THEN '棋牌游戏'
        WHEN 4 THEN '体感游戏'
        WHEN 5 THEN 'VR游戏'
        WHEN 6 THEN '娃娃机'
        WHEN 7 THEN '测试设备'
        ELSE CONCAT('分类', room_category_id)
    END as category_name,
    COUNT(*) as device_count
FROM fa_sender_device_info 
GROUP BY room_category_id
ORDER BY room_category_id;

-- 显示有房间信息的设备详情（前10个）
SELECT 
    sender_id,
    room_server_domain,
    room_name,
    room_category_id,
    room_sort_order,
    is_online,
    last_online_time,
    room_last_update
FROM fa_sender_device_info 
WHERE room_name != '' 
ORDER BY room_sort_order, room_name
LIMIT 10;
