-- 第二步：设置监控密码
-- 请确保已执行 add_monitor_password_field.sql

-- 为测试服务器V2的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'test6743' 
WHERE room_server_domain = 'http://testva2.91jdcd.com';

-- 为银梦科技服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'yinmeng3623' 
WHERE room_server_domain = 'http://bsth5.yinmengkj.cn';

-- 为游戏服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'game8754' 
WHERE room_server_domain = 'http://yx.yhdyc.com';

-- 查看更新结果
SELECT 
    room_server_domain,
    monitor_password,
    COUNT(*) as device_count
FROM fa_sender_device_info 
WHERE room_server_domain != '' 
GROUP BY room_server_domain, monitor_password
ORDER BY room_server_domain;

-- 显示受影响的行数
SELECT 
    'test6743' as password,
    COUNT(*) as affected_rows
FROM fa_sender_device_info 
WHERE monitor_password = 'test6743'

UNION ALL

SELECT 
    'yinmeng3623' as password,
    COUNT(*) as affected_rows
FROM fa_sender_device_info 
WHERE monitor_password = 'yinmeng3623'

UNION ALL

SELECT 
    'game8754' as password,
    COUNT(*) as affected_rows
FROM fa_sender_device_info 
WHERE monitor_password = 'game8754';
