<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC视频流观看</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>WebRTC视频流观看</h1>
            <div class="connection-status">
                <span id="status-indicator" class="status-offline"></span>
                <span id="connection-status">未连接</span>
            </div>
        </header>

        <main>
            <div class="video-container">
                <video id="remote-video" autoplay playsinline poster="img/waiting.jpg"></video>
                <div id="video-overlay" class="video-overlay">
                    <div class="overlay-message">等待连接视频源...</div>
                </div>
                <div class="video-controls">
                    <button id="fullscreen-btn" class="control-btn" title="全屏" disabled>
                        <svg viewBox="0 0 24 24"><path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/></svg>
                    </button>
                    <button id="mute-btn" class="control-btn" title="静音" disabled>
                        <svg viewBox="0 0 24 24"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/></svg>
                    </button>
                    <div class="stream-info">
                        <span id="resolution">-</span> | 
                        <span id="fps">- FPS</span> | 
                        <span id="bitrate">- Kbps</span>
                    </div>
                </div>
            </div>

            <div class="sources-panel">
                <div class="panel-header">
                    <h2>可用视频源</h2>
                    <button id="refresh-sources-btn" class="secondary-btn small-btn">刷新</button>
                </div>
                <div id="sources-list" class="sources-list">
                    <div class="no-sources">暂无可用视频源</div>
                </div>
            </div>

            <div class="connection-panel">
                <h2>连接设置</h2>
                <div class="form-group">
                    <label for="signaling-url">信令服务器:</label>
                    <input type="text" id="signaling-url" value="ws://8.134.131.24:28765">
                </div>
                <div class="form-group">
                    <label for="client-id">观众ID:</label>
                    <input type="text" id="client-id" value="viewer-">
                </div>
                <button id="connect-btn" class="primary-btn">连接</button>
                <button id="disconnect-btn" class="secondary-btn" disabled>断开</button>
            </div>
        </main>

        <div class="status-panel">
            <h2>连接状态</h2>
            <div id="status-messages" class="status-messages"></div>
        </div>

        <footer>
            <p>WebRTC视频流观看器 &copy; 2025</p>
        </footer>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
