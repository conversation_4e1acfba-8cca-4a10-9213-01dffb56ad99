1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.android_test"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
10
11    <application
11-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:5:5-18:19
12        android:allowBackup="true"
12-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:6:9-35
13        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
13-->[androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:24:18-86
14        android:extractNativeLibs="false"
15        android:icon="@mipmap/ic_launcher"
15-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:7:9-43
16        android:label="@string/app_name"
16-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:8:9-41
17        android:roundIcon="@mipmap/ic_launcher_round"
17-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:9:9-54
18        android:supportsRtl="true"
18-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:10:9-35
19        android:theme="@style/Theme.AndroidTest" >
19-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:11:9-49
20        <activity android:name="com.example.android_test.MainActivity" >
20-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:9-17:20
20-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:19-47
21            <intent-filter>
21-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:13:13-16:29
22                <action android:name="android.intent.action.MAIN" />
22-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:17-69
22-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:25-66
23
24                <category android:name="android.intent.category.LAUNCHER" />
24-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:17-77
24-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:27-74
25            </intent-filter>
26        </activity>
27    </application>
28
29</manifest>
