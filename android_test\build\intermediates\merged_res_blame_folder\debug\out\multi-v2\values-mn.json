{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-mn\\values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,299,397,516,601,666,764,845,904,997,1060,1118,1189,1251,1305,1426,1483,1544,1598,1669,1802,1886,1969,2072,2154,2232,2322,2389,2455,2526,2604,2690,2765,2843,2923,3006,3094,3173,3263,3356,3430,3500,3591,3645,3712,3796,3881,3943,4007,4070,4174,4280,4377,4482", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "215,294,392,511,596,661,759,840,899,992,1055,1113,1184,1246,1300,1421,1478,1539,1593,1664,1797,1881,1964,2067,2149,2227,2317,2384,2450,2521,2599,2685,2760,2838,2918,3001,3089,3168,3258,3351,3425,3495,3586,3640,3707,3791,3876,3938,4002,4065,4169,4275,4372,4477,4561"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3041,3139,3258,3343,3408,3506,3587,3646,3739,3802,3860,3931,3993,4047,4168,4225,4286,4340,4411,4544,4628,4711,4814,4896,4974,5064,5131,5197,5268,5346,5432,5507,5585,5665,5748,5836,5915,6005,6098,6172,6242,6333,6387,6454,6538,6623,6685,6749,6812,6916,7022,7119,7224", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "265,3036,3134,3253,3338,3403,3501,3582,3641,3734,3797,3855,3926,3988,4042,4163,4220,4281,4335,4406,4539,4623,4706,4809,4891,4969,5059,5126,5192,5263,5341,5427,5502,5580,5660,5743,5831,5910,6000,6093,6167,6237,6328,6382,6449,6533,6618,6680,6744,6807,6911,7017,7114,7219,7303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7389", "endColumns": "100", "endOffsets": "7485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,384,484,593,679,785,899,982,1063,1154,1247,1342,1438,1535,1628,1722,1814,1905,1995,2075,2182,2285,2382,2489,2591,2704,2863,7308", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "379,479,588,674,780,894,977,1058,1149,1242,1337,1433,1530,1623,1717,1809,1900,1990,2070,2177,2280,2377,2484,2586,2699,2858,2957,7384"}}]}]}