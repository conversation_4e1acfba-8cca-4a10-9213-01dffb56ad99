package com.example.webrtcsender.utils

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper

object GameLauncher {
    private const val TAG = "GameLauncher"

    /**
     * 检查是否启用了自动启动游戏
     */
    fun isAutoStartGameEnabled(context: Context): Boolean {
        val preferences = context.getSharedPreferences("webrtc_sender_prefs", Context.MODE_PRIVATE)
        return preferences.getBoolean(Constants.PREF_AUTO_START_GAME, Constants.DEFAULT_AUTO_START_GAME)
    }

    /**
     * 获取自动启动的游戏包名
     */
    fun getAutoStartGamePackage(context: Context): String {
        val preferences = context.getSharedPreferences("webrtc_sender_prefs", Context.MODE_PRIVATE)
        return preferences.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""
    }

    /**
     * 启动游戏
     */
    fun launchGame(context: Context, packageName: String): Boolean {
        return try {
            Logger.i(TAG, "尝试启动游戏: $packageName")

            val packageManager = context.packageManager
            val launchIntent = packageManager.getLaunchIntentForPackage(packageName)

            if (launchIntent != null) {
                // 使用更温和的启动标志位，避免强制清除任务栈
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                // 移除 FLAG_ACTIVITY_CLEAR_TOP，避免干扰游戏的任务栈

                // 添加额外的标志位确保启动稳定性
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)

                Logger.i(TAG, "启动游戏Intent标志位: ${launchIntent.flags}")
                context.startActivity(launchIntent)

                // 添加短暂延迟，让系统有时间处理启动请求
                Thread.sleep(500)

                // 验证游戏是否真的启动了
                val isRunning = isAppRunning(context, packageName)
                Logger.i(TAG, "游戏启动验证: $packageName, 运行状态: $isRunning")

                if (isRunning) {
                    Logger.i(TAG, "✅ 成功启动游戏: $packageName")
                } else {
                    Logger.w(TAG, "⚠️ 游戏可能启动失败或立即退出: $packageName")
                }

                true
            } else {
                Logger.w(TAG, "无法找到游戏的启动Intent: $packageName")
                false
            }
        } catch (e: Exception) {
            Logger.e(TAG, "启动游戏失败: $packageName", e)
            false
        }
    }

    /**
     * 自动启动游戏（如果启用了自动启动）
     */
    fun autoLaunchGameIfEnabled(context: Context, delayMs: Long = 2000) {
        try {
            if (!isAutoStartGameEnabled(context)) {
                Logger.d(TAG, "自动启动游戏未启用")
                return
            }

            val gamePackage = getAutoStartGamePackage(context)
            if (gamePackage.isEmpty()) {
                Logger.w(TAG, "未设置自动启动的游戏")
                return
            }

            Logger.i(TAG, "将在 ${delayMs}ms 后自动启动游戏: $gamePackage")

            // 延迟启动游戏，确保服务已完全启动
            Handler(Looper.getMainLooper()).postDelayed({
                Logger.i(TAG, "🎮 [游戏启动] 开始自动启动游戏: $gamePackage")

                // 检查游戏是否已经在运行
                if (isAppRunning(context, gamePackage)) {
                    Logger.i(TAG, "🎮 [游戏启动] 游戏已在运行，跳过启动")
                    return@postDelayed
                }

                val success = launchGame(context, gamePackage)
                if (success) {
                    Logger.i(TAG, "🎮 [游戏启动] ✅ 自动启动游戏成功")

                    // 延迟检查游戏是否稳定运行
                    Handler(Looper.getMainLooper()).postDelayed({
                        val stillRunning = isAppRunning(context, gamePackage)
                        if (stillRunning) {
                            Logger.i(TAG, "🎮 [游戏启动] ✅ 游戏运行稳定")
                        } else {
                            Logger.w(TAG, "🎮 [游戏启动] ⚠️ 游戏可能已退出或崩溃")
                        }
                    }, 3000) // 3秒后检查
                } else {
                    Logger.e(TAG, "🎮 [游戏启动] ❌ 自动启动游戏失败")
                }
            }, delayMs)

        } catch (e: Exception) {
            Logger.e(TAG, "自动启动游戏过程中发生错误", e)
        }
    }

    /**
     * 检查应用是否已安装
     */
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * 获取应用名称
     */
    fun getAppName(context: Context, packageName: String): String? {
        return try {
            val packageManager = context.packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检查应用是否正在运行
     */
    private fun isAppRunning(context: Context, packageName: String): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningApps = activityManager.runningAppProcesses

            runningApps?.any { processInfo ->
                processInfo.processName == packageName &&
                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
            } ?: false
        } catch (e: Exception) {
            Logger.w(TAG, "检查应用运行状态失败: $packageName - ${e.message}")
            false
        }
    }
}
