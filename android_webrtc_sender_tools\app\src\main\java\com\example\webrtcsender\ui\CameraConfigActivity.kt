package com.example.webrtcsender.ui

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.example.webrtcsender.R
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager

/**
 * 摄像头配置页面
 */
class CameraConfigActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "CameraConfigActivity"
    }
    
    private lateinit var cameraSpinner: Spinner
    private lateinit var refreshButton: Button
    private lateinit var saveButton: Button
    private lateinit var cameraInfoText: TextView
    
    private var availableCameras = listOf<WebRTCManager.CameraInfo>()
    private lateinit var cameraAdapter: ArrayAdapter<String>
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_camera_config)
        
        initViews()
        setupListeners()
        loadAvailableCameras()
    }
    
    private fun initViews() {
        cameraSpinner = findViewById(R.id.cameraSpinner)
        refreshButton = findViewById(R.id.refreshButton)
        saveButton = findViewById(R.id.saveButton)
        cameraInfoText = findViewById(R.id.cameraInfoText)
        
        // 设置标题
        supportActionBar?.title = "摄像头配置"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }
    
    private fun setupListeners() {
        // 刷新按钮
        refreshButton.setOnClickListener {
            loadAvailableCameras()
            Toast.makeText(this, "已刷新摄像头列表", Toast.LENGTH_SHORT).show()
        }
        
        // 保存按钮
        saveButton.setOnClickListener {
            saveSelectedCamera()
        }
        
        // 摄像头选择监听
        cameraSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: android.view.View?, position: Int, id: Long) {
                if (position < availableCameras.size) {
                    showCameraInfo(availableCameras[position])
                }
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {
                cameraInfoText.text = "请选择摄像头"
            }
        }
    }
    
    /**
     * 加载可用摄像头列表
     */
    private fun loadAvailableCameras() {
        try {
            Logger.i(TAG, "🎥 [配置页面] 开始加载可用摄像头列表")
            
            availableCameras = WebRTCManager.getAvailableCameras(this)
            
            if (availableCameras.isEmpty()) {
                Logger.w(TAG, "🎥 [配置页面] 没有找到可用摄像头")
                Toast.makeText(this, "没有找到可用摄像头", Toast.LENGTH_LONG).show()
                
                cameraAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, listOf("没有可用摄像头"))
                cameraAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                cameraSpinner.adapter = cameraAdapter
                
                cameraInfoText.text = "没有找到可用摄像头\n请检查摄像头权限和硬件状态"
                saveButton.isEnabled = false
                return
            }
            
            // 创建显示名称列表
            val displayNames = availableCameras.map { it.displayName }
            
            cameraAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, displayNames)
            cameraAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            cameraSpinner.adapter = cameraAdapter
            
            // 选择当前使用的摄像头
            val currentCameraId = WebRTCManager.getCameraId()
            val currentIndex = availableCameras.indexOfFirst { it.id == currentCameraId }
            if (currentIndex >= 0) {
                cameraSpinner.setSelection(currentIndex)
            }
            
            saveButton.isEnabled = true
            
            Logger.i(TAG, "🎥 [配置页面] 成功加载 ${availableCameras.size} 个摄像头")
            availableCameras.forEachIndexed { index, camera ->
                Logger.i(TAG, "🎥 [配置页面] [$index] ${camera.displayName} (ID: ${camera.id}, API: ${camera.apiType})")
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [配置页面] 加载摄像头列表失败", e)
            Toast.makeText(this, "加载摄像头列表失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 显示摄像头详细信息
     */
    private fun showCameraInfo(camera: WebRTCManager.CameraInfo) {
        val info = buildString {
            appendLine("摄像头信息:")
            appendLine("名称: ${camera.displayName}")
            appendLine("ID: ${camera.id}")
            appendLine("API: ${camera.apiType}")
            appendLine("类型: ${when {
                camera.isFrontFacing -> "前置摄像头"
                camera.isBackFacing -> "后置摄像头"
                else -> "其他摄像头"
            }}")
            
            // 显示当前状态
            val currentCameraId = WebRTCManager.getCameraId()
            if (camera.id == currentCameraId) {
                appendLine("状态: 当前使用中 ✅")
            } else {
                appendLine("状态: 可选择")
            }
        }
        
        cameraInfoText.text = info
        
        Logger.d(TAG, "🎥 [配置页面] 显示摄像头信息: ${camera.displayName}")
    }
    
    /**
     * 保存选择的摄像头
     */
    private fun saveSelectedCamera() {
        try {
            val selectedPosition = cameraSpinner.selectedItemPosition
            
            if (selectedPosition < 0 || selectedPosition >= availableCameras.size) {
                Toast.makeText(this, "请选择有效的摄像头", Toast.LENGTH_SHORT).show()
                return
            }
            
            val selectedCamera = availableCameras[selectedPosition]
            
            Logger.i(TAG, "🎥 [配置页面] 保存选择的摄像头: ${selectedCamera.displayName} (ID: ${selectedCamera.id})")
            
            // 保存到配置
            WebRTCManager.setCameraId(selectedCamera.id)
            
            Toast.makeText(this, "已保存摄像头配置: ${selectedCamera.displayName}", Toast.LENGTH_SHORT).show()
            
            // 更新显示
            showCameraInfo(selectedCamera)
            
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [配置页面] 保存摄像头配置失败", e)
            Toast.makeText(this, "保存配置失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 处理返回按钮点击事件（用于XML onClick）
     */
    fun onBackPressed(view: android.view.View) {
        onBackPressed()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onResume() {
        super.onResume()
        // 页面恢复时刷新摄像头列表
        loadAvailableCameras()
    }
}
