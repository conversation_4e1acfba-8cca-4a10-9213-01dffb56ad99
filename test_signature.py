#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试签名机制
"""

import time
import json
import hashlib

# 与服务器和客户端相同的密码
SHARED_PASSWORD = "fa0p23497fh-397rgf3f#"

def generate_md5_signature(password, device_id, command_json, timestamp):
    """生成MD5签名：密码 + 设备号 + 命令JSON + 时间戳"""
    try:
        # 构造签名字符串
        sign_string = f"{password}{device_id}{command_json}{timestamp}"
        
        # 计算MD5
        md5_hash = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
        
        print(f"签名字符串: {sign_string}")
        print(f"MD5签名: {md5_hash}")
        
        return md5_hash
    except Exception as e:
        print(f"生成签名失败: {e}")
        return None

def test_signature():
    """测试签名生成"""
    print("🔐 测试签名机制")
    print("=" * 50)
    
    # 测试数据
    device_id = "gamev-81216f2a"
    command_data = {
        "type": "control_command",
        "command": "reboot_device",
        "params": {},
        "timestamp": int(time.time())
    }
    
    print(f"设备ID: {device_id}")
    print(f"命令数据: {command_data}")
    
    # 生成JSON（排序键）
    command_json = json.dumps(command_data, sort_keys=True)
    print(f"命令JSON: {command_json}")
    
    # 生成签名
    signature = generate_md5_signature(
        SHARED_PASSWORD,
        device_id,
        command_json,
        command_data['timestamp']
    )
    
    # 添加签名到命令
    command_data['signature'] = signature
    
    print(f"最终命令: {json.dumps(command_data, indent=2)}")
    
    # 测试浮点数时间戳
    print("\n" + "=" * 50)
    print("🔐 测试浮点数时间戳")
    
    float_timestamp = time.time()
    command_data_float = {
        "type": "control_command",
        "command": "reboot_device",
        "params": {},
        "timestamp": float_timestamp
    }
    
    print(f"浮点数时间戳: {float_timestamp}")
    
    command_json_float = json.dumps(command_data_float, sort_keys=True)
    print(f"浮点数JSON: {command_json_float}")
    
    signature_float = generate_md5_signature(
        SHARED_PASSWORD,
        device_id,
        command_json_float,
        float_timestamp
    )
    
    print(f"浮点数签名: {signature_float}")
    
    # 比较两种签名
    print("\n" + "=" * 50)
    print("📊 签名比较")
    print(f"整数时间戳签名: {signature}")
    print(f"浮点数时间戳签名: {signature_float}")
    print(f"签名是否相同: {signature == signature_float}")

if __name__ == "__main__":
    test_signature()
