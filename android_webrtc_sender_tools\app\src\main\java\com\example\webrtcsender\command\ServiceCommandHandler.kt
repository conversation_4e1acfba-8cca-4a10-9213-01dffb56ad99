package com.example.webrtcsender.command

import android.content.Context
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import org.json.JSONObject

/**
 * 服务控制命令处理器
 * 处理 start_service, stop_service, restart_service 命令
 */
class ServiceCommandHandler(
    private val context: Context,
    private val webrtcManager: WebRTCManager,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "ServiceCommandHandler"
    }
    
    override fun executeControlCommand(command: String, params: JSONObject): Bo<PERSON>an {
        return when (command) {
            "start_service" -> startService(params)
            "stop_service" -> stopService(params)
            "restart_service" -> restartService(params)
            else -> {
                Logger.w(TAG, "❓ 不支持的服务命令: $command")
                false
            }
        }
    }
    
    /**
     * 开始服务
     */
    private fun startService(params: JSONObject): Boolean {
        return try {
            Logger.i(TAG, "🚀 开始服务")
            statusCallback("service", "starting", 50, "正在启动推流服务...")
            
            // 启动WebRTC推流
            webrtcManager.startVideoSource(context)

            Logger.i(TAG, "✅ 服务启动成功")
            statusCallback("service", "started", 100, "推流服务已启动")

            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 启动服务失败: ${e.message}")
            statusCallback("service", "start_failed", 0, "启动服务异常: ${e.message}")
            false
        }
    }
    
    /**
     * 停止服务
     */
    private fun stopService(params: JSONObject): Boolean {
        return try {
            Logger.i(TAG, "🛑 停止服务")
            statusCallback("service", "stopping", 50, "正在停止推流服务...")
            
            // 停止WebRTC推流
            webrtcManager.stopVideoSource(context)

            Logger.i(TAG, "✅ 服务停止成功")
            statusCallback("service", "stopped", 100, "推流服务已停止")
            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 停止服务失败: ${e.message}")
            statusCallback("service", "stop_failed", 0, "停止服务异常: ${e.message}")
            false
        }
    }
    
    /**
     * 重启服务
     */
    private fun restartService(params: JSONObject): Boolean {
        return try {
            Logger.i(TAG, "🔄 重启服务")
            statusCallback("service", "restarting", 25, "正在重启推流服务...")
            
            // 先停止再启动
            val stopSuccess = stopService(params)
            if (stopSuccess) {
                statusCallback("service", "restarting", 75, "服务已停止，正在重新启动...")
                // 等待一秒
                Thread.sleep(1000)
                val startSuccess = startService(params)
                if (startSuccess) {
                    statusCallback("service", "restarted", 100, "推流服务已重启")
                }
                return startSuccess
            }
            false
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 重启服务失败: ${e.message}")
            statusCallback("service", "restart_failed", 0, "重启服务异常: ${e.message}")
            false
        }
    }
    
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        // 服务命令处理器不处理升级命令
        Logger.w(TAG, "⚠️ 服务命令处理器不处理升级命令")
    }
    
    override fun updateServerConfig(configData: JSONObject) {
        // 服务命令处理器不处理配置更新
        Logger.w(TAG, "⚠️ 服务命令处理器不处理配置更新")
    }
    
    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success)
    }
}
