# PyMySQL配置说明

## 🎯 迁移到PyMySQL

已将信令服务器的MySQL连接器完全迁移到PyMySQL，这是一个纯Python实现的MySQL客户端，兼容性更好。

## 🔧 主要变更

### 1. 导入模块
```python
# 最终版本
import pymysql

# 不再需要复杂的兼容性处理
```

### 2. 数据库配置
```python
# PyMySQL配置（与mysqlclient兼容）
DB_CONFIG = {
    'host': '************',
    'db': 'dsender',
    'user': 'dsender',
    'passwd': 'dyZp7taLAxCZJGcX',
    'charset': 'utf8mb4',
    'port': 3306
}
```

### 3. 连接函数
```python
def init_database():
    """初始化数据库连接"""
    try:
        # 测试数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        connection.close()
        logging.info("数据库连接测试成功")
        return True
    except Exception as e:
        logging.error(f"数据库连接测试失败: {e}")
        return False

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logging.error(f"获取数据库连接失败: {e}")
        return None
```

## 📊 PyMySQL特性

### 优势
- ✅ **纯Python实现**: 无需编译，安装简单
- ✅ **兼容性好**: 支持Python 2.7和Python 3.x
- ✅ **API兼容**: 与MySQLdb API兼容
- ✅ **功能完整**: 支持所有MySQL功能
- ✅ **维护活跃**: 持续更新和维护

### 配置参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `host` | 数据库主机 | `'************'` |
| `db` | 数据库名 | `'dsender'` |
| `user` | 用户名 | `'dsender'` |
| `passwd` | 密码 | `'dyZp7taLAxCZJGcX'` |
| `charset` | 字符集 | `'utf8mb4'` |
| `port` | 端口 | `3306` |

## 🚀 安装和部署

### 1. 安装PyMySQL
```bash
# Python 2.7
pip install PyMySQL

# Python 3.x
pip3 install PyMySQL

# 或者指定版本
python -m pip install PyMySQL
python3 -m pip install PyMySQL
```

### 2. 验证安装
```bash
# 测试导入
python -c "import pymysql; print('PyMySQL安装成功')"
python3 -c "import pymysql; print('PyMySQL安装成功')"
```

### 3. 启动服务器
```bash
# Python 2.7
python enhanced_signaling_server.py --ws-port 8765 --http-port 28080

# Python 3.x
python3 enhanced_signaling_server.py --ws-port 8765 --http-port 28080
```

## 🔍 连接测试

### 1. 独立测试脚本
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'db': 'dsender',
    'user': 'dsender',
    'passwd': 'dyZp7taLAxCZJGcX',
    'charset': 'utf8mb4',
    'port': 3306
}

def test_connection():
    try:
        print("正在连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        
        # 测试查询
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"MySQL版本: {version[0]}")
        
        # 测试表是否存在
        cursor.execute("SHOW TABLES LIKE 'fa_sender_device_info'")
        table_exists = cursor.fetchone()
        if table_exists:
            print("✅ 设备信息表存在")
        else:
            print("⚠️ 设备信息表不存在，请先创建表")
        
        cursor.close()
        connection.close()
        print("✅ 连接测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    test_connection()
```

### 2. 运行测试
```bash
# 保存上面的代码为test_db.py，然后运行
python test_db.py
```

## ⚠️ 注意事项

### 1. 字符集设置
```python
# 确保使用utf8mb4支持中文和emoji
DB_CONFIG = {
    'charset': 'utf8mb4',
    # 其他配置...
}
```

### 2. 连接超时
```python
# 如果需要设置连接超时
DB_CONFIG = {
    'connect_timeout': 10,
    'read_timeout': 10,
    'write_timeout': 10,
    # 其他配置...
}
```

### 3. 自动重连
```python
# PyMySQL不支持自动重连，需要手动处理
def get_db_connection_with_retry(max_retries=3):
    for i in range(max_retries):
        try:
            return pymysql.connect(**DB_CONFIG)
        except Exception as e:
            if i == max_retries - 1:
                raise e
            time.sleep(1)
```

## 🔧 故障排除

### 1. 导入错误
```bash
# 如果提示找不到pymysql模块
pip list | grep -i mysql
pip install PyMySQL
```

### 2. 连接错误
```python
# 常见连接错误及解决方案
try:
    connection = pymysql.connect(**DB_CONFIG)
except pymysql.err.OperationalError as e:
    if e.args[0] == 1045:
        print("用户名或密码错误")
    elif e.args[0] == 2003:
        print("无法连接到MySQL服务器")
    elif e.args[0] == 1049:
        print("数据库不存在")
    else:
        print(f"连接错误: {e}")
```

### 3. 字符集问题
```python
# 如果出现中文乱码
DB_CONFIG = {
    'charset': 'utf8mb4',
    'use_unicode': True,
    # 其他配置...
}
```

## 🎯 预期效果

配置完成后：
- ✅ 信令服务器正常启动
- ✅ 数据库连接成功
- ✅ 设备信息正常存储
- ✅ API查询正常工作
- ✅ 支持中文字符
- ✅ 兼容Python 2.7和3.x

## 📝 启动检查清单

1. ✅ PyMySQL已安装
2. ✅ 数据库配置正确
3. ✅ 网络连接正常
4. ✅ 数据库表已创建
5. ✅ 字符集设置为utf8mb4
6. ✅ 服务器启动无错误

现在信令服务器已完全使用PyMySQL，应该能够稳定运行！
