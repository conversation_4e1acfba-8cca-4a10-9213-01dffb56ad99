# 摄像头Try-Catch安全修复

## 🎯 问题分析

用户指出了我之前修复方案的问题：
- 系统只有摄像头125
- 我强制回退到摄像头0，但摄像头0根本不存在
- 导致"连默认摄像头0也不可用"错误
- 用户建议使用try-catch来安全处理

## 🔧 新的修复方案

### 1. 移除强制回退逻辑
```kotlin
// 修复前 - 错误的强制回退
if (isProblematicCameraId(cameraId)) {
    Logger.w(TAG, "检测到问题摄像头ID $cameraId，强制使用默认摄像头0")
    cameraId = "0"  // 错误：摄像头0可能不存在
}

// 修复后 - 直接使用用户指定的摄像头
private fun createCameraVideoSourceWithId(cameraEnumerator: Camera2Enumerator, cameraId: String): VideoSource {
    Logger.i(TAG, "🎥 [摄像头] 使用摄像头ID创建视频源: $cameraId")
    
    // 只做基本验证，不强制改变摄像头ID
    if (!performFinalCameraValidation(cameraId)) {
        throw IllegalStateException("摄像头ID $cameraId 验证失败，不可用")
    }
}
```

### 2. 安全的摄像头创建
```kotlin
// 修复后 - 安全的try-catch处理
val videoCapturer = try {
    Logger.i(TAG, "🎥 [摄像头] 尝试创建摄像头捕获器: $cameraId")
    
    cameraEnumerator.createCapturer(cameraId, object : CameraVideoCapturer.CameraEventsHandler {
        override fun onCameraError(errorDescription: String?) {
            Logger.e(TAG, "🎥 [摄像头] 摄像头错误: $errorDescription")
            // 记录错误但不抛出异常，让上层处理
        }
        // ... 其他回调
    })
} catch (e: Exception) {
    Logger.e(TAG, "🎥 [摄像头] Camera2创建失败: ${e.message}")
    // 如果失败，尝试Camera1或抛出异常
}
```

### 3. 安全的视频捕获启动
```kotlin
// 修复后 - 安全的启动处理
try {
    val surfaceTextureHelper = SurfaceTextureHelper.create("CaptureThread", rootEglBase.eglBaseContext)
    Logger.i(TAG, "🎥 [摄像头] SurfaceTextureHelper已创建")

    videoCapturer.initialize(surfaceTextureHelper, context, videoSource.capturerObserver)
    Logger.i(TAG, "🎥 [摄像头] VideoCapturer已初始化")

    videoCapturer.startCapture(actualResolution.first, actualResolution.second, framerate)
    Logger.i(TAG, "🎥 [摄像头] ✅ 摄像头捕获已启动")
    
} catch (e: Exception) {
    Logger.e(TAG, "🎥 [摄像头] 启动摄像头捕获失败: ${e.message}", e)
    
    // 安全地停止和清理资源
    try {
        videoCapturer.stopCapture()
        videoCapturer.dispose()
    } catch (cleanupException: Exception) {
        Logger.w(TAG, "🎥 [摄像头] 清理资源时出错: ${cleanupException.message}")
    }
    
    throw IllegalStateException("摄像头$cameraId启动失败: ${e.message}", e)
}
```

## 📊 修复对比

| 方面 | 修复前（强制回退） | 修复后（Try-Catch） |
|------|------------------|-------------------|
| 摄像头选择 | 强制改为摄像头0 | 使用用户指定的摄像头 |
| 错误处理 | 预防性拒绝 | 尝试使用，失败时安全处理 |
| 兼容性 | 可能拒绝可用摄像头 | 最大化兼容性 |
| 用户体验 | 可能无法使用想要的摄像头 | 尊重用户选择 |

## 🛡️ 安全措施

### 1. 多层Try-Catch保护
- **创建层**: 捕获摄像头创建异常
- **初始化层**: 捕获初始化异常
- **启动层**: 捕获启动异常
- **清理层**: 安全清理资源

### 2. 资源清理
```kotlin
// 确保在异常时也能清理资源
try {
    videoCapturer.stopCapture()
    videoCapturer.dispose()
} catch (cleanupException: Exception) {
    Logger.w(TAG, "清理资源时出错: ${cleanupException.message}")
}
```

### 3. 详细日志记录
```kotlin
Logger.e(TAG, "🎥 [摄像头] 启动摄像头捕获失败: ${e.message}", e)
```

## 🎯 修复原理

### 1. 尊重用户选择
- 不再强制改变用户配置的摄像头ID
- 如果用户配置了摄像头125，就尝试使用摄像头125
- 只有在真正失败时才报错

### 2. 渐进式错误处理
```
用户选择摄像头125
    ↓
验证摄像头125是否存在
    ↓
尝试创建摄像头125捕获器
    ↓
尝试初始化摄像头125
    ↓
尝试启动摄像头125捕获
    ↓
如果任何步骤失败，安全清理并报告具体错误
```

### 3. 最大化成功率
- 不预先判断摄像头是否"有问题"
- 让系统和硬件自己决定是否支持
- 只在真正失败时才处理错误

## 🚀 预期效果

修复后应该：
- ✅ 允许使用摄像头125（如果硬件支持）
- ✅ 在摄像头125真正出现问题时安全处理
- ✅ 提供详细的错误信息
- ✅ 正确清理资源，避免内存泄漏
- ✅ 不再出现"连默认摄像头0也不可用"错误

## 📝 测试验证

### 1. 正常情况
- 摄像头125能正常工作
- 视频流正常传输
- 没有崩溃

### 2. 异常情况
- 如果摄像头125真的有问题，会得到具体的错误信息
- 资源被正确清理
- 应用不会崩溃，而是优雅地报告错误

### 3. 日志验证
**成功时**:
```
🎥 [摄像头] 使用摄像头ID创建视频源: 125
🎥 [摄像头] 尝试创建摄像头捕获器: 125
🎥 [摄像头] SurfaceTextureHelper已创建
🎥 [摄像头] VideoCapturer已初始化
🎥 [摄像头] ✅ 摄像头捕获已启动
```

**失败时**:
```
🎥 [摄像头] 启动摄像头捕获失败: [具体错误信息]
🎥 [摄像头] 清理资源...
```

## ⚠️ 注意事项

1. **不再预判**: 不再预先判断哪些摄像头ID是"有问题的"
2. **用户选择**: 完全尊重用户的摄像头选择
3. **错误透明**: 如果真的有问题，会提供具体的错误信息
4. **资源安全**: 确保在任何情况下都能正确清理资源

这种方法更加灵活和安全，让硬件和系统自己决定是否支持，而不是我们预先判断！
