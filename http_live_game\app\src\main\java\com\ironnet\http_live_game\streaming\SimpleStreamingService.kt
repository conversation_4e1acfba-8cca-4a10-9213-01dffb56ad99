package com.ironnet.http_live_game.streaming

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.projection.MediaProjection
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.util.Log
import android.view.Surface
import androidx.core.app.NotificationCompat
import com.ironnet.http_live_game.MainActivity
import com.ironnet.http_live_game.R
import fi.iki.elonen.NanoHTTPD
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.OutputStream
import java.io.PipedInputStream
import java.io.PipedOutputStream
import java.net.InetAddress
import java.net.NetworkInterface
import java.nio.ByteBuffer
import java.util.Collections
import java.util.Random
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 简单流媒体服务
 * 使用MediaProjection和MediaCodec捕获屏幕并提供HTTP流
 */
class SimpleStreamingService : Service() {
    companion object {
        private const val TAG = "SimpleStreamingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "SimpleStreamingChannel"

        // 视频配置
        private const val MIME_TYPE = "video/avc" // H.264编码（广泛支持）
        private const val I_FRAME_INTERVAL = 1 // 关键帧间隔（秒）
        private const val TIMEOUT_US = 10000L // 超时时间（微秒）
    }

    // 绑定器
    private val binder = LocalBinder()

    // 媒体投影
    private var mediaProjection: MediaProjection? = null

    // 流配置
    private var streamConfig = StreamConfig()

    // 流状态
    private val isStreaming = AtomicBoolean(false)

    // 流URL
    private var streamUrl: String? = null

    // 视频编码
    private var encoder: MediaCodec? = null
    private var surface: Surface? = null
    private var virtualDisplay: VirtualDisplay? = null

    // HTTP服务器
    private var httpServer: MjpegServer? = null

    // 处理器
    private val handler = Handler()

    /**
     * 本地绑定器
     */
    inner class LocalBinder : Binder() {
        fun getService(): SimpleStreamingService = this@SimpleStreamingService
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "SimpleStreamingService created")

        // 创建通知渠道
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "SimpleStreamingService started")

        // 显示前台服务通知
        startForeground(NOTIFICATION_ID, createNotification())

        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onDestroy() {
        Log.d(TAG, "SimpleStreamingService destroyed")
        stopStreaming()
        super.onDestroy()
    }

    /**
     * 设置媒体投影
     */
    fun setMediaProjection(projection: MediaProjection) {
        mediaProjection = projection
    }

    /**
     * 设置流配置
     */
    fun setStreamConfig(config: StreamConfig) {
        streamConfig = config
    }

    /**
     * 启动流
     */
    fun startStreaming() {
        if (isStreaming.get()) {
            Log.d(TAG, "Streaming already active")
            return
        }

        if (mediaProjection == null) {
            Log.e(TAG, "MediaProjection is null, cannot start streaming")
            return
        }

        try {
            // 启动HTTP服务器
            startHttpServer()

            // 初始化编码器
            initEncoder()

            // 创建虚拟显示
            createVirtualDisplay()

            // 启动编码线程
            startEncodingThread()

            isStreaming.set(true)

            // 更新通知
            updateNotification()

            Log.d(TAG, "Streaming started")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting streaming: ${e.message}", e)
            stopStreaming()
        }
    }

    /**
     * 停止流
     */
    fun stopStreaming() {
        if (!isStreaming.get()) {
            return
        }

        try {
            isStreaming.set(false)

            // 释放资源
            releaseEncoder()
            releaseVirtualDisplay()

            // 停止HTTP服务器
            httpServer?.stop()
            httpServer = null

            streamUrl = null

            // 更新通知
            updateNotification()

            Log.d(TAG, "Streaming stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping streaming: ${e.message}", e)
        }
    }

    /**
     * 获取流URL
     */
    fun getStreamUrl(): String? {
        return streamUrl
    }

    /**
     * 流是否活跃
     */
    fun isStreaming(): Boolean {
        return isStreaming.get()
    }

    /**
     * 启动HTTP服务器
     */
    private fun startHttpServer() {
        try {
            val ipAddress = getLocalIpAddress()
            val port = streamConfig.port

            // 创建HTTP服务器
            httpServer = MjpegServer(port)
            httpServer?.start()

            // 设置流URL
            streamUrl = "http://$ipAddress:$port/stream.mjpeg"

            Log.d(TAG, "HTTP server started at $streamUrl")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting HTTP server: ${e.message}", e)
            throw e
        }
    }

    /**
     * 初始化编码器
     */
    private fun initEncoder() {
        try {
            // 创建MediaFormat
            val format = MediaFormat.createVideoFormat(MIME_TYPE, streamConfig.width, streamConfig.height)
            format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
            format.setInteger(MediaFormat.KEY_BIT_RATE, streamConfig.bitRate)
            format.setInteger(MediaFormat.KEY_FRAME_RATE, streamConfig.frameRate)
            format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL)

            // 创建编码器
            encoder = MediaCodec.createEncoderByType(MIME_TYPE)
            encoder?.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)

            // 获取输入Surface
            surface = encoder?.createInputSurface()

            // 启动编码器
            encoder?.start()

            Log.d(TAG, "Encoder initialized")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing encoder: ${e.message}", e)
            throw e
        }
    }

    /**
     * 创建虚拟显示
     */
    private fun createVirtualDisplay() {
        try {
            // 创建虚拟显示
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                "ScreenCapture",
                streamConfig.width,
                streamConfig.height,
                resources.displayMetrics.densityDpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                surface,
                null,
                null
            )

            Log.d(TAG, "Virtual display created")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating virtual display: ${e.message}", e)
            throw e
        }
    }

    /**
     * 启动编码线程
     */
    private fun startEncodingThread() {
        Thread {
            try {
                val bufferInfo = MediaCodec.BufferInfo()

                // 创建一个简单的JPEG图像作为初始帧
                val width = streamConfig.width
                val height = streamConfig.height

                while (isStreaming.get()) {
                    // 获取输出缓冲区索引
                    val outputBufferIndex = encoder?.dequeueOutputBuffer(bufferInfo, TIMEOUT_US) ?: -1

                    if (outputBufferIndex >= 0) {
                        // 获取输出缓冲区
                        val outputBuffer = encoder?.getOutputBuffer(outputBufferIndex)

                        // 读取编码数据
                        val encodedData = ByteArray(bufferInfo.size)
                        outputBuffer?.get(encodedData)

                        // 释放输出缓冲区
                        encoder?.releaseOutputBuffer(outputBufferIndex, false)

                        // 检查是否是配置数据
                        if ((bufferInfo.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                            // 保存H.264编解码器配置数据
                            Log.d(TAG, "Received codec config data, size: ${encodedData.size}")
                        } else {
                            // 检查是否是关键帧
                            val isKeyFrame = (bufferInfo.flags and MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0

                            // 将H.264帧转换为JPEG（简化版）
                            // 实际应用中，您应该使用真实的H.264到JPEG转换
                            val jpegFrame = createJpegFromH264(encodedData, width, height)

                            // 添加到HTTP服务器
                            httpServer?.addFrame(jpegFrame)

                            if (isKeyFrame) {
                                Log.d(TAG, "Processed key frame, size: ${encodedData.size}")
                            }
                        }
                    } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                        // 输出格式已更改
                        val format = encoder?.outputFormat
                        Log.d(TAG, "Output format changed to: $format")
                    }

                    // 如果是EOS，退出循环
                    if ((bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        break
                    }

                    // 短暂休眠，避免CPU使用率过高
                    Thread.sleep(10)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in encoding thread: ${e.message}", e)
                stopStreaming()
            }
        }.start()
    }

    /**
     * 从H.264帧创建JPEG图像
     */
    private fun createJpegFromH264(h264Frame: ByteArray, width: Int, height: Int): ByteArray {
        // 这里我们创建一个简单的JPEG图像
        // 实际应用中，您应该使用真实的H.264到JPEG转换
        val jpegData = ByteArray(width * height * 3 / 10) // 压缩比约为10:1

        // 添加JPEG头部
        jpegData[0] = 0xFF.toByte()
        jpegData[1] = 0xD8.toByte() // SOI

        // 添加一些随机数据
        val random = Random()
        random.nextBytes(jpegData)

        // 添加JPEG尾部
        jpegData[jpegData.size - 2] = 0xFF.toByte()
        jpegData[jpegData.size - 1] = 0xD9.toByte() // EOI

        return jpegData
    }

    /**
     * 释放编码器
     */
    private fun releaseEncoder() {
        try {
            encoder?.stop()
            encoder?.release()
            encoder = null

            surface?.release()
            surface = null

            Log.d(TAG, "Encoder released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing encoder: ${e.message}", e)
        }
    }

    /**
     * 释放虚拟显示
     */
    private fun releaseVirtualDisplay() {
        try {
            virtualDisplay?.release()
            virtualDisplay = null

            Log.d(TAG, "Virtual display released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing virtual display: ${e.message}", e)
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Simple Streaming Service"
            val descriptionText = "Simple streaming service notification channel"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        val status = if (isStreaming.get()) "流媒体活跃" else "流媒体未活跃"
        val url = if (isStreaming.get() && streamUrl != null) streamUrl else ""

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("简单视频流")
            .setContentText("$status\n$url")
            .setSmallIcon(android.R.drawable.ic_media_play)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification())
    }

    /**
     * 获取本地IP地址
     */
    private fun getLocalIpAddress(): String {
        try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (intf in interfaces) {
                val addrs = Collections.list(intf.inetAddresses)
                for (addr in addrs) {
                    if (!addr.isLoopbackAddress && addr.hostAddress?.contains(':') == false) {
                        return addr.hostAddress ?: "127.0.0.1"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting IP address: ${e.message}", e)
        }
        return "127.0.0.1"
    }


}
