# 日志显示持久化实现说明

## 需求描述

日志显示状态需要支持持久化保存：
- **默认状态**: 首次安装时关闭
- **持久化**: 如果被打开了，将会永久打开
- **重启保持**: 不论重启应用或重新打开应用，都保持上次设置的状态

## 实现方案

### 1. 存储机制
使用 `SharedPreferences` 来持久化保存日志显示状态：
- **键名**: `PREF_LOG_DISPLAY_ENABLED`
- **默认值**: `false` (关闭)
- **存储位置**: 应用私有存储空间

### 2. 加载时机
在多个关键位置加载日志显示设置，确保状态一致性：

#### 2.1 Application启动时 (WebRTCSenderApp.onCreate)
```kotlin
// 确保日志显示设置在应用启动时正确加载
Logger.loadDisplaySettingFromPreferences(this)
```

#### 2.2 首次安装配置 (FirstInstallConfigManager)
```kotlin
// 首次安装时设置默认值
editor.putBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)

// 非首次安装时加载保存的设置
val logDisplayEnabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
Logger.setDisplayEnabled(logDisplayEnabled)
```

#### 2.3 MainActivity初始化时
```kotlin
// 加载日志显示设置（确保在MainActivity中也能正确加载）
val preferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
val logDisplayEnabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
Logger.setDisplayEnabled(logDisplayEnabled)
```

### 3. 保存时机
通过远程命令修改日志显示状态时立即保存：

```kotlin
// ExtendedCommandHandler.handleToggleLogDisplay()
val preferences = WebRTCManager.getPreferences()
val editor = preferences.edit()
editor.putBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, enabled)
editor.apply() // 异步保存

// 更新Logger的显示状态
Logger.setDisplayEnabled(enabled)
```

### 4. Logger类增强
添加了专门的方法来处理持久化：

```kotlin
// 设置显示状态（带日志记录）
fun setDisplayEnabled(enabled: Boolean) {
    displayEnabled = enabled
    Log.i(TAG, "日志显示状态已设置为: $enabled")
}

// 从SharedPreferences加载设置
fun loadDisplaySettingFromPreferences(context: Context) {
    val preferences = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
    val enabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
    setDisplayEnabled(enabled)
    Log.i(TAG, "从SharedPreferences加载日志显示设置: $enabled")
}
```

## 工作流程

### 首次安装流程
1. `WebRTCSenderApp.onCreate()` 启动
2. `FirstInstallConfigManager.setupFirstInstallDefaults()` 检测首次安装
3. 设置 `PREF_LOG_DISPLAY_ENABLED = false` (默认关闭)
4. `Logger.setDisplayEnabled(false)` 设置Logger状态
5. 标记 `PREF_FIRST_INSTALL = false`

### 后续启动流程
1. `WebRTCSenderApp.onCreate()` 启动
2. `FirstInstallConfigManager.setupFirstInstallDefaults()` 检测非首次安装
3. 从SharedPreferences加载 `PREF_LOG_DISPLAY_ENABLED` 的值
4. `Logger.setDisplayEnabled(savedValue)` 恢复上次的状态
5. `Logger.loadDisplaySettingFromPreferences()` 双重确保

### 远程命令修改流程
1. 接收 `toggle_log_display` 命令
2. `ExtendedCommandHandler.handleToggleLogDisplay()` 处理
3. 保存新状态到SharedPreferences
4. `Logger.setDisplayEnabled(newValue)` 立即生效
5. 验证保存是否成功
6. 返回确认消息

## 验证机制

### 日志记录
每个关键步骤都有详细的日志记录：
- 设置加载: "从SharedPreferences加载日志显示设置: true/false"
- 状态设置: "日志显示状态已设置为: true/false"
- 保存验证: "验证保存的日志显示设置: true/false"

### 双重保险
- Application层加载一次
- MainActivity层再加载一次
- 确保即使某一层失败，另一层也能正确设置

### 异常处理
```kotlin
try {
    // 加载设置
} catch (e: Exception) {
    Log.e(TAG, "加载日志显示设置失败", e)
    // 使用默认值
    setDisplayEnabled(Constants.DEFAULT_LOG_DISPLAY_ENABLED)
}
```

## 测试要点

1. **首次安装**: 确认日志显示默认关闭
2. **开启持久化**: 开启后重启应用，确认仍然开启
3. **关闭持久化**: 关闭后重启应用，确认仍然关闭
4. **系统重启**: 重启设备后确认状态保持
5. **异常恢复**: 删除SharedPreferences后确认使用默认值

## 注意事项

1. **影响范围**: 只影响控制台日志输出，不影响日志文件写入
2. **存储位置**: 保存在应用私有存储，卸载应用会清除
3. **线程安全**: SharedPreferences本身是线程安全的
4. **性能影响**: 使用apply()异步保存，不会阻塞UI线程

## 相关文件

- `Logger.kt`: 核心日志管理和持久化逻辑
- `ExtendedCommandHandler.kt`: 远程命令处理
- `FirstInstallConfigManager.kt`: 首次安装配置
- `WebRTCSenderApp.kt`: 应用启动时加载
- `MainActivity.kt`: UI层双重保险
- `Constants.kt`: 配置常量定义
