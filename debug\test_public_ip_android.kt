/**
 * Android端公网IP获取测试工具
 * 用于验证公网IP获取功能是否正常工作
 */

import kotlinx.coroutines.*
import org.json.JSONObject
import java.net.URL
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection

class PublicIpTester {
    
    companion object {
        private const val TAG = "PublicIpTester"
    }
    
    /**
     * 测试公网IP获取功能
     */
    suspend fun testPublicIpRetrieval() {
        println("🌐 开始测试公网IP获取功能...")
        
        // 测试ipinfo.io
        println("\n📍 测试 ipinfo.io...")
        val ipInfoResult = getIpInfoFromService("https://ipinfo.io/json")
        if (ipInfoResult.has("ip")) {
            println("✅ ipinfo.io 成功:")
            println("   IP: ${ipInfoResult.optString("ip", "N/A")}")
            println("   城市: ${ipInfoResult.optString("city", "N/A")}")
            println("   地区: ${ipInfoResult.optString("region", "N/A")}")
            println("   国家: ${ipInfoResult.optString("country", "N/A")}")
            println("   ISP: ${ipInfoResult.optString("org", "N/A")}")
            println("   时区: ${ipInfoResult.optString("timezone", "N/A")}")
        } else {
            println("❌ ipinfo.io 失败")
        }
        
        // 测试httpbin.org
        println("\n📍 测试 httpbin.org...")
        val httpbinResult = getIpInfoFromService("https://httpbin.org/ip")
        if (httpbinResult.has("origin")) {
            println("✅ httpbin.org 成功:")
            println("   IP: ${httpbinResult.optString("origin", "N/A")}")
        } else {
            println("❌ httpbin.org 失败")
        }
        
        // 测试ifconfig.me
        println("\n📍 测试 ifconfig.me...")
        val ifconfigIp = getSimpleIp("https://ifconfig.me/ip")
        if (ifconfigIp.isNotEmpty()) {
            println("✅ ifconfig.me 成功:")
            println("   IP: $ifconfigIp")
        } else {
            println("❌ ifconfig.me 失败")
        }
        
        // 测试IPv6
        println("\n📍 测试 IPv6...")
        val ipv6 = getSimpleIp("https://ifconfig.me")
        if (ipv6.isNotEmpty() && ipv6.contains(":")) {
            println("✅ IPv6 成功:")
            println("   IPv6: $ipv6")
        } else {
            println("❌ IPv6 获取失败或不支持")
        }
        
        // 综合测试
        println("\n🔄 综合测试...")
        val finalResult = getPublicIpInfo()
        println("📊 最终结果:")
        println("   IP: ${finalResult.optString("ip", "unknown")}")
        println("   IPv6: ${finalResult.optString("ipv6", "N/A")}")
        println("   城市: ${finalResult.optString("city", "N/A")}")
        println("   地区: ${finalResult.optString("region", "N/A")}")
        println("   国家: ${finalResult.optString("country", "N/A")}")
        println("   位置: ${finalResult.optString("location", "N/A")}")
        println("   ISP: ${finalResult.optString("isp_org", "N/A")}")
        println("   时区: ${finalResult.optString("timezone", "N/A")}")
        
        println("\n✅ 公网IP获取测试完成!")
    }
    
    /**
     * 获取公网IP信息（包含地理位置）
     */
    private suspend fun getPublicIpInfo(): JSONObject = withContext(Dispatchers.IO) {
        val result = JSONObject()
        
        try {
            // 首先尝试从ipinfo.io获取详细信息
            val ipInfoResult = getIpInfoFromService("https://ipinfo.io/json")
            if (ipInfoResult.has("ip")) {
                return@withContext ipInfoResult
            }
            
            // 备选方案1: 使用httpbin.org
            val httpbinResult = getIpInfoFromService("https://httpbin.org/ip")
            if (httpbinResult.has("origin")) {
                result.put("ip", httpbinResult.getString("origin"))
            }
            
            // 备选方案2: 使用ifconfig.me
            if (!result.has("ip")) {
                val ifconfigIp = getSimpleIp("https://ifconfig.me/ip")
                if (ifconfigIp.isNotEmpty()) {
                    result.put("ip", ifconfigIp)
                }
            }
            
            // 尝试获取IPv6
            try {
                val ipv6 = getSimpleIp("https://ifconfig.me")
                if (ipv6.isNotEmpty() && ipv6.contains(":")) {
                    result.put("ipv6", ipv6)
                }
            } catch (e: Exception) {
                println("获取IPv6失败: ${e.message}")
            }
            
        } catch (e: Exception) {
            println("获取公网IP信息失败: ${e.message}")
            result.put("ip", "unknown")
        }
        
        return@withContext result
    }
    
    /**
     * 从指定服务获取IP信息
     */
    private suspend fun getIpInfoFromService(url: String): JSONObject = withContext(Dispatchers.IO) {
        try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.setRequestProperty("User-Agent", "Android-WebRTC-Sender-Test")
            
            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText()
                reader.close()
                
                return@withContext JSONObject(response)
            }
        } catch (e: Exception) {
            println("从 $url 获取IP信息失败: ${e.message}")
        }
        
        return@withContext JSONObject()
    }
    
    /**
     * 获取简单的IP地址字符串
     */
    private suspend fun getSimpleIp(url: String): String = withContext(Dispatchers.IO) {
        try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.setRequestProperty("User-Agent", "Android-WebRTC-Sender-Test")
            
            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText().trim()
                reader.close()
                
                return@withContext response
            }
        } catch (e: Exception) {
            println("从 $url 获取IP失败: ${e.message}")
        }
        
        return@withContext ""
    }
}

/**
 * 测试主函数
 */
suspend fun main() {
    val tester = PublicIpTester()
    tester.testPublicIpRetrieval()
}

/**
 * 在Android应用中的使用示例:
 * 
 * class MainActivity : AppCompatActivity() {
 *     override fun onCreate(savedInstanceState: Bundle?) {
 *         super.onCreate(savedInstanceState)
 *         
 *         // 测试公网IP获取
 *         lifecycleScope.launch {
 *             val tester = PublicIpTester()
 *             tester.testPublicIpRetrieval()
 *         }
 *     }
 * }
 */
