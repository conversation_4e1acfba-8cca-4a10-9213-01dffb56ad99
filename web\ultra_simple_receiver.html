<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>简易WebRTC接收端</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }
        video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            z-index: 1;
        }
        #play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            font-size: 24px;
            cursor: pointer;
            z-index: 2;
        }
        #play-button.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <video id="video" autoplay playsinline muted></video>
    <button id="play-button">▶</button>

    <script>
        // 配置
        const SIGNALING_URL = 'wss://sling.91jdcd.com/ws/';
        const SENDER_ID = 'android-5799e3cb'; // 固定的发送端ID
        
        // 元素
        const videoEl = document.getElementById('video');
        const playButton = document.getElementById('play-button');
        
        // 变量
        let pc = null;
        let ws = null;
        let dataChannel = null;
        let receiverId = `receiver-${Math.random().toString(36).substring(2, 10)}`;
        let connected = false;
        
        // 初始化
        playButton.addEventListener('click', () => {
            playButton.classList.add('hidden');
            startConnection();
        });
        
        // 开始连接
        function startConnection() {
            // 连接到信令服务器
            ws = new WebSocket(SIGNALING_URL);
            
            ws.onopen = () => {
                console.log('已连接到信令服务器');
                
                // 注册接收端
                ws.send(JSON.stringify({
                    type: 'register',
                    id: receiverId,
                    role: 'viewer',
                    name: '超简易接收端'
                }));
            };
            
            ws.onmessage = async (event) => {
                const data = JSON.parse(event.data);
                console.log('收到消息:', data.type);
                
                if (data.type === 'registered') {
                    connectToPeer();
                }
                else if (data.type === 'offer' && data.from === SENDER_ID) {
                    try {
                        if (!pc) {
                            setupPeerConnection();
                        }
                        
                        const offer = new RTCSessionDescription({
                            type: 'offer',
                            sdp: data.sdp
                        });
                        
                        await pc.setRemoteDescription(offer);
                        
                        const answer = await pc.createAnswer();
                        await pc.setLocalDescription(answer);
                        
                        ws.send(JSON.stringify({
                            type: 'answer',
                            target: SENDER_ID,
                            sdp: pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n')
                        }));
                    } catch (e) {
                        console.error('处理offer错误:', e);
                    }
                }
                else if (data.type === 'candidate' && data.from === SENDER_ID) {
                    try {
                        if (pc && pc.remoteDescription) {
                            await pc.addIceCandidate(new RTCIceCandidate(data.candidate));
                        }
                    } catch (e) {
                        console.error('添加ICE候选错误:', e);
                    }
                }
                else if (data.type === 'answer' && data.from === SENDER_ID) {
                        
                    try {
                        const answer = new RTCSessionDescription({
                            type: 'answer',
                            sdp: data.sdp
                        });
                        await pc.setRemoteDescription(answer);
                        console.log('已设置远程描述(answer)');

                        
                        if (window.pendingIceCandidates && window.pendingIceCandidates.length > 0) {
                            console.log(`添加 ${window.pendingIceCandidates.length} 个缓存的ICE候选`);
                            for (const candidate of window.pendingIceCandidates) {
                                try {
                                    await pc.addIceCandidate(candidate);
                                    console.log('已添加缓存的ICE候选');
                                } catch (e) {
                                    console.log(`添加缓存的ICE候选错误: ${e.message}`, 'error');
                                }
                            }
                            window.pendingIceCandidates = [];
                        }

                        
                        if (connectionTimeout) {
                            clearTimeout(connectionTimeout);
                            connectionTimeout = null;
                        }
                    } catch (e) {
                        console.log(`设置远程描述错误: ${e.message}`, 'error');
                    }
                }
                else if (data.type === 'candidate' && data.from === activeSenderId) {
                    
                    try {
                        const candidate = data.candidate;
                        if (candidate) {
                            
                            if (!pc || !pc.remoteDescription) {
                                if (!window.pendingIceCandidates) {
                                    window.pendingIceCandidates = [];
                                }
                                window.pendingIceCandidates.push(candidate);
                                console.log('缓存ICE候选，等待远程描述设置');
                            } else {
                                await pc.addIceCandidate(candidate);
                                console.log('已添加ICE候选');
                            }
                        }
                    } catch (e) {
                        console.log(`添加ICE候选错误: ${e.message}`, 'error');
                    }
                }



            };
            
            ws.onclose = () => {
                console.log('信令服务器连接已关闭');
                setTimeout(startConnection, 3000);
            };
        }


        function logWebRTCVideoStats(peerConnection) {
            if (!(peerConnection instanceof RTCPeerConnection)) {
                console.error("Invalid RTCPeerConnection instance.");
                return;
            }

            peerConnection.getStats(null).then(stats => {
                let result = {
                    codec: null,
                    fps: null,
                    width: null,
                    height: null
                };

                // 遍历所有统计信息
                stats.forEach(report => {
                    // 获取帧率 (framesPerSecond)
                    if ((report.type === 'inbound-rtp' || report.type === 'outbound-rtp') && 'framesPerSecond' in report) {
                        result.fps = report.framesPerSecond;
                    }

                    // 获取分辨率 (frameWidth & frameHeight)
                    if (report.type === 'inbound-rtp' && report.kind === 'video') {
                        if (report.frameWidth !== undefined && report.frameHeight !== undefined) {
                            result.width = report.frameWidth;
                            result.height = report.frameHeight;
                        }
                    }

                    // 获取编码器类型 (mimeType)
                    if (report.type === 'codec' && report.payloadType && report.mimeType?.startsWith('video/')) {
                        result.codec = report.mimeType; // e.g., "video/VP8", "video/H264", "video/VP9"
                    }
                });

                // 打印结果
                console.log(`Codec: ${result.codec}`);
                console.log(`Resolution: ${result.width}x${result.height}`);
                console.log(`Frame Rate: ${result.fps ? `${result.fps.toFixed(2)} FPS` : 'N/A'}`);
            }).catch(error => {
                console.error("Failed to get stats:", error);
            });
        }
        
        // 设置对等连接
        function setupPeerConnection() {
            pc = new RTCPeerConnection({
                iceServers: [
                    { urls: ['stun:stun.l.google.com:19302', 'stun:stun1.l.google.com:19302'] },
                    {
                        urls: 'turn:numb.viagenie.ca',
                        username: '<EMAIL>',
                        credential: 'muazkh'
                    }
                ]
            });
            
            // 创建数据通道
            dataChannel = pc.createDataChannel('control');
            
            dataChannel.onopen = () => {
                console.log('数据通道已打开');
                // 请求视频状态
                dataChannel.send(JSON.stringify({
                    command: 'get_status'
                }));
            };
            
            dataChannel.onmessage = (event) => {
                console.log('收到数据通道消息:', event.data);
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'status' && data.state === 'playing') {
                        // 收到播放状态，确保视频播放
                        if (videoEl.paused && videoEl.srcObject) {
                            videoEl.play().catch(e => console.error('播放失败:', e));
                        }
                    }
                } catch (e) {}
            };
            
            pc.onicecandidate = (event) => {
                if (event.candidate) {
                    ws.send(JSON.stringify({
                        type: 'candidate',
                        target: SENDER_ID,
                        candidate: event.candidate
                    }));
                }
            };
            
            pc.ontrack = (event) => {
                console.log('收到轨道:', event.track.kind);
                if (event.track.kind === 'video') {
                    videoEl.srcObject = event.streams[0];
                    
                    // 尝试播放视频
                    videoEl.play().catch(() => {
                        
                        // 如果自动播放失败，显示播放按钮
                        playButton.classList.remove('hidden');
                        
                        // 添加点击事件处理
                        document.body.addEventListener('click', () => {
                            videoEl.play().catch(e => console.error('播放失败:', e));
                            playButton.classList.add('hidden');
                        }, { once: true });
                    });
                }
            };
            
            pc.ondatachannel = (event) => {
                dataChannel = event.channel;
                
                dataChannel.onopen = () => {
                    console.log('数据通道已打开');
                    // 请求视频状态
                    dataChannel.send(JSON.stringify({
                        command: 'get_status'
                    }));
                };
                
                dataChannel.onmessage = (event) => {
                    console.log('收到数据通道消息:', event.data);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'status' && data.state === 'playing') {
                            // 收到播放状态，确保视频播放
                            if (videoEl.paused && videoEl.srcObject) {
                                videoEl.play().catch(e => console.error('播放失败:', e));
                            }
                        }
                    } catch (e) {}
                };
            };
            
            setInterval(() => {
                logWebRTCVideoStats(pc);
                                    }, 2000);
        }
        
        // 连接到对等端
        function connectToPeer() {
            console.log('正在连接到发送端...');
            
            if (pc) {
                pc.close();
                pc = null;
            }
            
            setupPeerConnection();
            
            // 创建并发送offer
            pc.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            }).then(offer => {
                return pc.setLocalDescription(offer);
            }).then(() => {
                ws.send(JSON.stringify({
                    type: 'offer',
                    target: SENDER_ID,
                    sdp: pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n')
                }));
            }).catch(e => {
                console.error('创建offer错误:', e);
            });
        }
        
        // 防止视频暂停
        videoEl.addEventListener('pause', () => {
            if (connected) {
                videoEl.play().catch(e => console.error('重新播放失败:', e));
            }
        });
        
        // 防止视频控制
        videoEl.addEventListener('contextmenu', e => e.preventDefault());
        
        // 自动开始连接
        playButton.click();
    </script>
</body>
</html>
