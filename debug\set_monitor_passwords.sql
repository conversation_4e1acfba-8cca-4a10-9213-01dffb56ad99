-- 设置监控密码示例
-- 为不同服务器域名的设备设置不同的监控密码

-- 首先确保monitor_password字段已存在（兼容旧版MySQL）
-- 如果字段已存在会报错，但不影响后续操作
ALTER TABLE fa_sender_device_info
ADD COLUMN monitor_password VARCHAR(100) DEFAULT '' COMMENT '监控密码，用于权限控制';

-- 为测试服务器V2的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'test6743' 
WHERE room_server_domain = 'http://testva2.91jdcd.com';

-- 为银梦科技服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'yinmeng3623' 
WHERE room_server_domain = 'http://bsth5.yinmengkj.cn';

-- 为游戏服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'game8754' 
WHERE room_server_domain = 'http://yx.yhdyc.com';

-- 查看设置结果
SELECT 
    room_server_domain,
    monitor_password,
    COUNT(*) as device_count
FROM fa_sender_device_info 
WHERE room_server_domain != '' 
GROUP BY room_server_domain, monitor_password
ORDER BY room_server_domain;

-- 查看所有设备的监控密码设置
SELECT 
    sender_id,
    room_name,
    room_server_domain,
    monitor_password,
    is_online,
    last_online_time
FROM fa_sender_device_info 
WHERE room_server_domain != ''
ORDER BY room_server_domain, room_name
LIMIT 20;

-- 测试密码权限查询
SELECT DISTINCT room_server_domain 
FROM fa_sender_device_info 
WHERE monitor_password = 'test123' AND room_server_domain != '';

SELECT DISTINCT room_server_domain 
FROM fa_sender_device_info 
WHERE monitor_password = 'yinmeng456' AND room_server_domain != '';

SELECT DISTINCT room_server_domain 
FROM fa_sender_device_info 
WHERE monitor_password = 'game789' AND room_server_domain != '';
