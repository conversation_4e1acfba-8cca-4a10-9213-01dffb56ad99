<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单简化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            overflow: visible;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            position: relative;
            margin: 20px;
        }
        
        .test-button.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        
        .dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 600px;
            height: 300px;
            z-index: 999999;
            display: none;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .dropdown-menu.show {
            display: block !important;
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000000;
            max-width: 400px;
        }
        
        .content {
            padding: 20px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <strong>🔧 调试信息</strong><br>
        <div id="debugContent">等待操作...</div>
    </div>

    <div class="container">
        <h1>🔧 下拉菜单简化测试</h1>
        
        <div class="content">
            <p>这是一个简化的测试页面，专门测试下拉菜单的位置计算逻辑。</p>
            
            <h3>测试说明：</h3>
            <ul>
                <li>点击按钮应该在按钮正下方显示下拉菜单</li>
                <li>鼠标移动不应该影响菜单位置</li>
                <li>菜单应该稳定显示，不闪烁</li>
            </ul>
            
            <div style="display: flex; gap: 20px; flex-wrap: wrap; margin: 40px 0;">
                <button class="test-button" onclick="toggleDropdown('test1', this)">
                    测试按钮 1
                </button>
                
                <button class="test-button" onclick="toggleDropdown('test2', this)">
                    测试按钮 2
                </button>
                
                <button class="test-button" onclick="toggleDropdown('test3', this)">
                    测试按钮 3
                </button>
            </div>
            
            <div style="margin-top: 200px;">
                <button class="test-button" onclick="toggleDropdown('test4', this)">
                    底部测试按钮
                </button>
            </div>
            
            <div style="margin-left: 800px; margin-top: 50px;">
                <button class="test-button" onclick="toggleDropdown('test5', this)">
                    右侧测试按钮
                </button>
            </div>
        </div>
    </div>

    <!-- 下拉菜单 -->
    <div class="dropdown-menu" id="dropdown-test1">
        <h3>测试菜单 1</h3>
        <p>这是测试菜单的内容。菜单应该稳定显示在按钮下方。</p>
        <button onclick="alert('功能1')">功能按钮 1</button>
        <button onclick="alert('功能2')">功能按钮 2</button>
    </div>
    
    <div class="dropdown-menu" id="dropdown-test2">
        <h3>测试菜单 2</h3>
        <p>这是第二个测试菜单。</p>
        <button onclick="alert('功能A')">功能按钮 A</button>
        <button onclick="alert('功能B')">功能按钮 B</button>
    </div>
    
    <div class="dropdown-menu" id="dropdown-test3">
        <h3>测试菜单 3</h3>
        <p>这是第三个测试菜单。</p>
        <button onclick="alert('功能X')">功能按钮 X</button>
        <button onclick="alert('功能Y')">功能按钮 Y</button>
    </div>
    
    <div class="dropdown-menu" id="dropdown-test4">
        <h3>底部测试菜单</h3>
        <p>这个菜单应该显示在按钮上方。</p>
        <button onclick="alert('底部功能1')">底部功能 1</button>
        <button onclick="alert('底部功能2')">底部功能 2</button>
    </div>
    
    <div class="dropdown-menu" id="dropdown-test5">
        <h3>右侧测试菜单</h3>
        <p>这个菜单应该调整位置避免超出屏幕。</p>
        <button onclick="alert('右侧功能1')">右侧功能 1</button>
        <button onclick="alert('右侧功能2')">右侧功能 2</button>
    </div>

    <script>
        let currentDropdown = null;
        let positionCalculations = 0;
        
        function updateDebugInfo(message) {
            const debugContent = document.getElementById('debugContent');
            debugContent.innerHTML = `
                <strong>最新操作:</strong> ${message}<br>
                <strong>位置计算次数:</strong> ${positionCalculations}<br>
                <strong>当前显示:</strong> ${currentDropdown || '无'}<br>
                <small>时间: ${new Date().toLocaleTimeString()}</small>
            `;
        }
        
        function toggleDropdown(dropdownId, button) {
            console.log('点击按钮:', dropdownId);
            updateDebugInfo(`点击按钮: ${dropdownId}`);
            
            // 关闭其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${dropdownId}`) {
                    menu.classList.remove('show');
                    menu.removeAttribute('data-positioned');
                }
            });
            
            // 移除其他按钮的激活状态
            document.querySelectorAll('.test-button').forEach(btn => {
                if (btn !== button) {
                    btn.classList.remove('active');
                }
            });
            
            const dropdown = document.getElementById(`dropdown-${dropdownId}`);
            const isShowing = dropdown.classList.contains('show');
            
            if (isShowing) {
                // 隐藏
                dropdown.classList.remove('show');
                button.classList.remove('active');
                dropdown.removeAttribute('data-positioned');
                currentDropdown = null;
                updateDebugInfo(`隐藏菜单: ${dropdownId}`);
            } else {
                // 显示
                calculatePosition(dropdown, button);
                dropdown.classList.add('show');
                button.classList.add('active');
                currentDropdown = dropdownId;
                updateDebugInfo(`显示菜单: ${dropdownId}`);
            }
        }
        
        function calculatePosition(dropdown, button) {
            // 防止重复计算
            if (dropdown.hasAttribute('data-positioned')) {
                console.log('已定位，跳过');
                return;
            }
            
            positionCalculations++;
            console.log('开始位置计算 #', positionCalculations);
            
            // 获取按钮位置
            const buttonRect = button.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            // 菜单尺寸
            const menuWidth = 600;
            const menuHeight = 300;
            
            // 默认位置：按钮下方左对齐
            let left = buttonRect.left;
            let top = buttonRect.bottom + 10;
            
            console.log('初始位置:', { left, top });
            console.log('按钮位置:', buttonRect);
            console.log('窗口尺寸:', { windowWidth, windowHeight });
            
            // 检查右边界
            if (left + menuWidth > windowWidth - 20) {
                left = buttonRect.right - menuWidth;
                console.log('调整右边界，新left:', left);
            }
            
            // 检查左边界
            if (left < 20) {
                left = 20;
                console.log('调整左边界，新left:', left);
            }
            
            // 检查下边界
            if (top + menuHeight > windowHeight - 20) {
                top = buttonRect.top - menuHeight - 10;
                console.log('调整下边界，新top:', top);
            }
            
            // 检查上边界
            if (top < 20) {
                top = 20;
                console.log('调整上边界，新top:', top);
            }
            
            console.log('最终位置:', { left, top });
            
            // 设置位置
            dropdown.style.left = `${left}px`;
            dropdown.style.top = `${top}px`;
            dropdown.setAttribute('data-positioned', 'true');
            
            updateDebugInfo(`位置计算完成: (${Math.round(left)}, ${Math.round(top)})`);
        }
        
        // 点击外部关闭
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.test-button') && !event.target.closest('.dropdown-menu')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.removeAttribute('data-positioned');
                });
                document.querySelectorAll('.test-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                currentDropdown = null;
                updateDebugInfo('点击外部，关闭所有菜单');
            }
        });
        
        // 鼠标移动监控（仅用于调试）
        let mouseMoveCount = 0;
        document.addEventListener('mousemove', (event) => {
            mouseMoveCount++;
            // 每100次更新一次，避免频繁更新
            if (mouseMoveCount % 100 === 0) {
                console.log('鼠标移动:', event.clientX, event.clientY);
                // 重要：这里不调用任何位置计算函数
            }
        });
        
        updateDebugInfo('页面加载完成');
    </script>
</body>
</html>
