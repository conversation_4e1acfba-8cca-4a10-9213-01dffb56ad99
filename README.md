# 端口映射工具

这个项目包含两个端口映射工具，用于将内网服务（如视频流）映射到公网端口。

## 前提条件

- Python 3.6+
- 支持UPnP或NAT-PMP的路由器
- 网络运营商允许公网访问（不使用运营商级NAT）

## 安装依赖

```bash
# 安装miniupnpc库（用于UPnP）
pip install miniupnpc

# 安装py-natpmp库（用于NAT-PMP）
pip install py-natpmp
```

## 使用方法

### UPnP端口映射

UPnP（通用即插即用）是最常见的端口映射协议，大多数现代路由器都支持。

```bash
python upnp_port_mapper.py
```

### NAT-PMP端口映射

NAT-PMP（NAT端口映射协议）是Apple开发的一种协议，一些路由器支持此协议。

```bash
python natpmp_port_mapper.py
```

## 配置

两个脚本中都有一些可以修改的配置参数：

- `internal_port`：内网服务端口（默认为80）
- `external_port`：希望映射到的外网端口（默认为16800）

您可以根据需要修改这些参数。

## 故障排除

如果端口映射失败，可能有以下原因：

1. 路由器不支持UPnP或NAT-PMP，或这些功能已被禁用
2. 网络运营商使用了运营商级NAT (CGNAT)，没有为您分配公网IP
3. 网络运营商限制了入站连接

### 替代解决方案

如果端口映射不起作用，您可以考虑以下替代方案：

1. 使用内网穿透服务如Ngrok、Frp或Cloudflare Tunnel
2. 使用VPN解决方案
3. 联系您的网络运营商，询问是否可以获取公网IP

## 视频流访问

如果端口映射成功，您可以通过以下URL访问视频流：

```
http://<外网IP>:<映射端口>/0.mp4
```

例如：`http://************:16800/0.mp4`
