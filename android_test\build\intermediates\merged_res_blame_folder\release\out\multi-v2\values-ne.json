{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-ne\\values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,753,837,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1939,2052,2153,2249,2362,2472,2589,2756,2867", "endColumns": "110,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "211,322,430,521,628,748,832,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1934,2047,2148,2244,2357,2467,2584,2751,2862,2942"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "267,378,489,597,688,795,915,999,1078,1169,1262,1357,1451,1551,1644,1739,1833,1924,2015,2101,2214,2315,2411,2524,2634,2751,2918,7436", "endColumns": "110,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "373,484,592,683,790,910,994,1073,1164,1257,1352,1446,1546,1639,1734,1828,1919,2010,2096,2209,2310,2406,2519,2629,2746,2913,3024,7511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7516", "endColumns": "100", "endOffsets": "7612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,299,401,511,598,664,760,826,887,992,1064,1122,1196,1258,1312,1425,1485,1546,1605,1683,1807,1888,1973,2079,2160,2243,2326,2393,2459,2536,2615,2703,2772,2848,2929,2997,3088,3166,3259,3356,3430,3509,3607,3667,3733,3821,3909,3971,4039,4102,4207,4325,4420,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "212,294,396,506,593,659,755,821,882,987,1059,1117,1191,1253,1307,1420,1480,1541,1600,1678,1802,1883,1968,2074,2155,2238,2321,2388,2454,2531,2610,2698,2767,2843,2924,2992,3083,3161,3254,3351,3425,3504,3602,3662,3728,3816,3904,3966,4034,4097,4202,4320,4415,4535,4619"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3029,3111,3213,3323,3410,3476,3572,3638,3699,3804,3876,3934,4008,4070,4124,4237,4297,4358,4417,4495,4619,4700,4785,4891,4972,5055,5138,5205,5271,5348,5427,5515,5584,5660,5741,5809,5900,5978,6071,6168,6242,6321,6419,6479,6545,6633,6721,6783,6851,6914,7019,7137,7232,7352", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "262,3106,3208,3318,3405,3471,3567,3633,3694,3799,3871,3929,4003,4065,4119,4232,4292,4353,4412,4490,4614,4695,4780,4886,4967,5050,5133,5200,5266,5343,5422,5510,5579,5655,5736,5804,5895,5973,6066,6163,6237,6316,6414,6474,6540,6628,6716,6778,6846,6909,7014,7132,7227,7347,7431"}}]}]}