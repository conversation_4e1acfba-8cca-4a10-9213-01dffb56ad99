package com.ironnet.http_live_game.streaming

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.os.Binder
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import com.ironnet.http_live_game.MainActivity
import com.ironnet.http_live_game.R
import fi.iki.elonen.NanoHTTPD
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.Collections
import java.util.concurrent.atomic.AtomicBoolean

/**
 * MediaRecorder流媒体服务
 * 使用MediaRecorder捕获屏幕并提供HTTP流
 */
class MediaRecorderStreamingService : Service() {
    companion object {
        private const val TAG = "MediaRecorderService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "MediaRecorderStreamingChannel"
    }

    // 绑定器
    private val binder = LocalBinder()

    // 媒体投影
    private var mediaProjection: MediaProjection? = null

    // 流配置
    private var streamConfig = StreamConfig()

    // 流状态
    private val isStreaming = AtomicBoolean(false)

    // 流URL
    private var streamUrl: String? = null

    // 虚拟显示
    private var virtualDisplay: VirtualDisplay? = null

    // 媒体录制器
    private var mediaRecorder: MediaRecorder? = null

    // HTTP服务器
    private var httpServer: VideoHttpServer? = null

    // 视频文件
    private var videoFile: File? = null

    // 处理器
    private val handler = Handler(Looper.getMainLooper())

    /**
     * 本地绑定器
     */
    inner class LocalBinder : Binder() {
        fun getService(): MediaRecorderStreamingService = this@MediaRecorderStreamingService
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "MediaRecorderStreamingService created")

        // 创建通知渠道
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "MediaRecorderStreamingService started")

        // 显示前台服务通知
        startForeground(NOTIFICATION_ID, createNotification())

        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onDestroy() {
        Log.d(TAG, "MediaRecorderStreamingService destroyed")
        stopStreaming()
        super.onDestroy()
    }

    /**
     * 设置媒体投影
     */
    fun setMediaProjection(projection: MediaProjection) {
        mediaProjection = projection
    }

    /**
     * 设置流配置
     */
    fun setStreamConfig(config: StreamConfig) {
        streamConfig = config
    }

    /**
     * 启动流
     */
    fun startStreaming() {
        if (isStreaming.get()) {
            Log.d(TAG, "Streaming already active")
            return
        }

        if (mediaProjection == null) {
            Log.e(TAG, "MediaProjection is null, cannot start streaming")
            return
        }

        try {
            // 准备视频文件
            prepareVideoFile()

            // 准备媒体录制器
            prepareMediaRecorder()

            // 创建虚拟显示
            createVirtualDisplay()

            // 启动录制
            mediaRecorder?.start()

            // 启动HTTP服务器
            startHttpServer()

            isStreaming.set(true)

            // 更新通知
            updateNotification()

            Log.d(TAG, "Streaming started")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting streaming: ${e.message}", e)
            stopStreaming()
        }
    }

    /**
     * 停止流
     */
    fun stopStreaming() {
        if (!isStreaming.get()) {
            return
        }

        try {
            isStreaming.set(false)

            // 停止录制
            try {
                mediaRecorder?.stop()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping mediaRecorder: ${e.message}", e)
            }

            // 释放资源
            releaseMediaRecorder()
            releaseVirtualDisplay()

            // 停止HTTP服务器
            httpServer?.stop()
            httpServer = null

            streamUrl = null

            // 更新通知
            updateNotification()

            Log.d(TAG, "Streaming stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping streaming: ${e.message}", e)
        }
    }

    /**
     * 获取流URL
     */
    fun getStreamUrl(): String? {
        return streamUrl
    }

    /**
     * 流是否活跃
     */
    fun isStreaming(): Boolean {
        return isStreaming.get()
    }

    /**
     * 准备视频文件
     */
    private fun prepareVideoFile() {
        try {
            val outputDir = getExternalFilesDir(Environment.DIRECTORY_MOVIES)
            if (outputDir?.exists() != true) {
                outputDir?.mkdirs()
            }

            videoFile = File(outputDir, "screen_capture.mp4")
            if (videoFile?.exists() == true) {
                videoFile?.delete()
            }

            Log.d(TAG, "Video file prepared: ${videoFile?.absolutePath}")
        } catch (e: Exception) {
            Log.e(TAG, "Error preparing video file: ${e.message}", e)
            throw e
        }
    }

    /**
     * 准备媒体录制器
     */
    private fun prepareMediaRecorder() {
        try {
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(this)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }

            // 使用更兼容的编码参数
            mediaRecorder?.apply {
                setVideoSource(MediaRecorder.VideoSource.SURFACE)

                // 使用更兼容的MPEG_4格式
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)

                // 使用H264编码器，几乎所有设备都支持
                setVideoEncoder(MediaRecorder.VideoEncoder.H264)

                // 设置分辨率（使用较低的分辨率以提高兼容性）
                val width = streamConfig.width
                val height = streamConfig.height
                setVideoSize(width, height)

                // 设置较低的帧率以提高兼容性
                val frameRate = streamConfig.frameRate.coerceAtMost(30)
                setVideoFrameRate(frameRate)

                // 设置适中的比特率
                val bitRate = streamConfig.bitRate.coerceAtMost(4000000) // 最大4Mbps
                setVideoEncodingBitRate(bitRate)

                // 设置无限制的时长和文件大小
                setMaxDuration(0)
                setMaxFileSize(0)

                // 设置关键帧间隔为1秒，使视频更容易被播放器解析
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // Android 8.0及以上可以直接设置I帧间隔
                    setVideoEncodingBitRate(bitRate)
                    setVideoFrameRate(frameRate)
                    val iFrameInterval = 1 // 1秒一个I帧
                    javaClass.getMethod("setVideoEncodingProfileLevel", Int::class.java, Int::class.java)
                        .invoke(this, 1, 64) // 使用Baseline profile (AVCProfileBaseline = 1)

                    // 设置I帧间隔
                    try {
                        javaClass.getMethod("setVideoEncodingIFrameInterval", Int::class.java)
                            .invoke(this, iFrameInterval)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error setting I-frame interval: ${e.message}")
                    }
                }

                // 设置输出文件
                setOutputFile(videoFile?.absolutePath)

                // 准备录制
                prepare()
            }

            Log.d(TAG, "MediaRecorder prepared with: width=${streamConfig.width}, height=${streamConfig.height}, " +
                    "frameRate=${streamConfig.frameRate}, bitRate=${streamConfig.bitRate}")
        } catch (e: Exception) {
            Log.e(TAG, "Error preparing MediaRecorder: ${e.message}", e)
            throw e
        }
    }

    /**
     * 创建虚拟显示
     */
    private fun createVirtualDisplay() {
        try {
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                "ScreenCapture",
                streamConfig.width,
                streamConfig.height,
                resources.displayMetrics.densityDpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                mediaRecorder?.surface,
                null,
                null
            )

            Log.d(TAG, "Virtual display created")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating virtual display: ${e.message}", e)
            throw e
        }
    }

    /**
     * 启动HTTP服务器
     */
    private fun startHttpServer() {
        try {
            val ipAddress = getLocalIpAddress()
            val port = streamConfig.port

            // 创建HTTP服务器
            httpServer = VideoHttpServer(port, videoFile!!)
            httpServer?.start()

            // 设置流URL
            streamUrl = "http://$ipAddress:$port/"

            Log.d(TAG, "HTTP server started at $streamUrl")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting HTTP server: ${e.message}", e)
            throw e
        }
    }

    /**
     * 释放媒体录制器
     */
    private fun releaseMediaRecorder() {
        try {
            mediaRecorder?.release()
            mediaRecorder = null

            Log.d(TAG, "MediaRecorder released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing MediaRecorder: ${e.message}", e)
        }
    }

    /**
     * 释放虚拟显示
     */
    private fun releaseVirtualDisplay() {
        try {
            virtualDisplay?.release()
            virtualDisplay = null

            Log.d(TAG, "Virtual display released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing virtual display: ${e.message}", e)
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "MediaRecorder Streaming Service"
            val descriptionText = "MediaRecorder streaming service notification channel"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        val status = if (isStreaming.get()) "流媒体活跃" else "流媒体未活跃"
        val url = if (isStreaming.get() && streamUrl != null) streamUrl else ""

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("MediaRecorder视频流")
            .setContentText("$status\n$url")
            .setSmallIcon(android.R.drawable.ic_media_play)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, createNotification())
    }

    /**
     * 获取本地IP地址
     */
    private fun getLocalIpAddress(): String {
        try {
            val interfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (intf in interfaces) {
                val addrs = Collections.list(intf.inetAddresses)
                for (addr in addrs) {
                    if (!addr.isLoopbackAddress && addr.hostAddress?.contains(':') == false) {
                        return addr.hostAddress ?: "127.0.0.1"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting IP address: ${e.message}", e)
        }
        return "127.0.0.1"
    }

    /**
     * 视频HTTP服务器
     */
    private class VideoHttpServer(port: Int, private val videoFile: File) : NanoHTTPD(port) {
        companion object {
            private const val TAG = "VideoHttpServer"
        }

        override fun serve(session: IHTTPSession): Response {
            val uri = session.uri
            val method = session.method
            val headers = session.headers

            Log.d(TAG, "HTTP request: $method $uri")
            Log.d(TAG, "Headers: $headers")

            // 处理CORS预检请求
            if (method == Method.OPTIONS) {
                val response = newFixedLengthResponse(Response.Status.OK, MIME_PLAINTEXT, "")
                response.addHeader("Access-Control-Allow-Origin", "*")
                response.addHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
                response.addHeader("Access-Control-Allow-Headers", "Content-Type, Range")
                response.addHeader("Access-Control-Max-Age", "3600")
                return response
            }

            // 处理Range请求
            val rangeHeader = session.headers["range"]
            if (rangeHeader != null && uri == "/video.mp4") {
                return serveVideoRange(rangeHeader)
            }

            return when {
                uri == "/" -> serveHtml()
                uri == "/video.mp4" -> serveVideo()
                uri == "/stream.html" -> serveHtml()
                else -> newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "Not found")
            }
        }

        /**
         * 处理Range请求
         */
        private fun serveVideoRange(rangeHeader: String): Response {
            if (!videoFile.exists()) {
                Log.e(TAG, "Video file not found for range request: ${videoFile.absolutePath}")
                return newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "Video file not found")
            }

            try {
                // 获取文件大小
                val fileLength = videoFile.length()

                // 检查文件大小是否合理
                if (fileLength < 1024) { // 小于1KB的文件可能不完整
                    Log.w(TAG, "Video file is too small for range request (${fileLength} bytes), may be incomplete")
                    return newFixedLengthResponse(
                        Response.Status.SERVICE_UNAVAILABLE,
                        MIME_PLAINTEXT,
                        "Video file is being prepared, please try again in a moment"
                    )
                }

                // 创建一个临时文件的副本，避免在读取时文件被修改
                val tempFile = File(videoFile.parent, "temp_range_${System.currentTimeMillis()}.mp4")
                videoFile.copyTo(tempFile, overwrite = true)
                val tempFileLength = tempFile.length()

                // 解析Range头
                // 格式: "bytes=0-1023"
                var start = 0L
                var end = tempFileLength - 1

                if (rangeHeader.startsWith("bytes=")) {
                    val rangeValue = rangeHeader.substring("bytes=".length)
                    val parts = rangeValue.split("-")

                    if (parts.size >= 1 && parts[0].isNotEmpty()) {
                        start = parts[0].toLong()
                    }

                    if (parts.size >= 2 && parts[1].isNotEmpty()) {
                        end = parts[1].toLong().coerceAtMost(tempFileLength - 1)
                    }
                }

                // 计算内容长度
                val contentLength = end - start + 1

                Log.d(TAG, "Range request: bytes=$start-$end/$tempFileLength")

                // 打开文件并跳到起始位置
                val fileInputStream = FileInputStream(tempFile)
                fileInputStream.skip(start)

                // 创建响应
                val response = newFixedLengthResponse(
                    Response.Status.PARTIAL_CONTENT,
                    "video/mp4",
                    fileInputStream,
                    contentLength
                )

                // 添加必要的HTTP头
                response.addHeader("Content-Type", "video/mp4")
                response.addHeader("Content-Length", contentLength.toString())
                response.addHeader("Content-Range", "bytes $start-$end/$tempFileLength")
                response.addHeader("Accept-Ranges", "bytes")
                response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
                response.addHeader("Pragma", "no-cache")
                response.addHeader("Expires", "0")
                response.addHeader("Connection", "keep-alive")

                // 设置响应关闭监听器，删除临时文件
                val onCloseListener = Runnable {
                    try {
                        if (tempFile.exists()) {
                            tempFile.delete()
                            Log.d(TAG, "Temporary range video file deleted: ${tempFile.absolutePath}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting temporary range file: ${e.message}")
                    }
                }

                // 添加关闭监听器
                Thread(onCloseListener).start()

                return response
            } catch (e: Exception) {
                Log.e(TAG, "Error serving video range: ${e.message}", e)
                return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, MIME_PLAINTEXT, "Error serving video range: ${e.message}")
            }
        }

        /**
         * 提供视频文件
         */
        private fun serveVideo(): Response {
            if (!videoFile.exists()) {
                Log.e(TAG, "Video file not found: ${videoFile.absolutePath}")
                return newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "Video file not found")
            }

            try {
                // 获取文件大小
                val fileLength = videoFile.length()

                // 检查文件大小是否合理
                if (fileLength < 1024) { // 小于1KB的文件可能不完整
                    Log.w(TAG, "Video file is too small (${fileLength} bytes), may be incomplete")
                    return newFixedLengthResponse(
                        Response.Status.SERVICE_UNAVAILABLE,
                        MIME_PLAINTEXT,
                        "Video file is being prepared, please try again in a moment"
                    )
                }

                Log.d(TAG, "Serving video file: ${videoFile.absolutePath}, size: $fileLength bytes")

                // 创建一个临时文件的副本，避免在读取时文件被修改
                val tempFile = File(videoFile.parent, "temp_${System.currentTimeMillis()}.mp4")
                videoFile.copyTo(tempFile, overwrite = true)

                // 从临时文件读取
                val fileInputStream = FileInputStream(tempFile)

                // 创建响应
                val response = newFixedLengthResponse(
                    Response.Status.OK,
                    "video/mp4",
                    fileInputStream,
                    tempFile.length()
                )

                // 添加必要的HTTP头
                response.addHeader("Content-Type", "video/mp4")
                response.addHeader("Content-Length", tempFile.length().toString())
                response.addHeader("Content-Disposition", "inline; filename=\"video.mp4\"")
                response.addHeader("Accept-Ranges", "bytes")
                response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
                response.addHeader("Pragma", "no-cache")
                response.addHeader("Expires", "0")
                response.addHeader("Connection", "keep-alive")
                response.addHeader("X-Content-Type-Options", "nosniff")

                // 设置响应关闭监听器，删除临时文件
                val onCloseListener = Runnable {
                    try {
                        if (tempFile.exists()) {
                            tempFile.delete()
                            Log.d(TAG, "Temporary video file deleted: ${tempFile.absolutePath}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting temporary file: ${e.message}")
                    }
                }

                // 添加关闭监听器
                Thread(onCloseListener).start()

                return response
            } catch (e: Exception) {
                Log.e(TAG, "Error serving video: ${e.message}", e)
                return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, MIME_PLAINTEXT, "Error serving video: ${e.message}")
            }
        }

        /**
         * 提供HTML页面
         */
        private fun serveHtml(): Response {
            val html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>MediaRecorder视频流</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            text-align: center;
                            background-color: #f5f5f5;
                        }
                        h1 {
                            color: #333;
                        }
                        .video-container {
                            max-width: 800px;
                            margin: 0 auto;
                            border: 1px solid #ccc;
                            padding: 10px;
                            border-radius: 5px;
                            background-color: white;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        }
                        video {
                            max-width: 100%;
                            height: auto;
                            border-radius: 3px;
                        }
                        .controls {
                            margin-top: 10px;
                            display: flex;
                            justify-content: center;
                            gap: 10px;
                        }
                        button {
                            margin: 5px;
                            padding: 8px 16px;
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            transition: background-color 0.3s;
                        }
                        button:hover {
                            background-color: #45a049;
                        }
                        .status {
                            margin-top: 10px;
                            font-size: 14px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <h1>MediaRecorder视频流</h1>
                    <div class="video-container">
                        <video id="video" controls autoplay></video>
                        <div class="controls">
                            <button onclick="refreshVideo()">刷新视频</button>
                            <button onclick="togglePlayback()">播放/暂停</button>
                        </div>
                        <div class="status" id="status">状态: 加载中...</div>
                    </div>

                    <script>
                        // 视频元素
                        var video = document.getElementById('video');
                        var statusElement = document.getElementById('status');

                        // 视频事件监听
                        video.addEventListener('playing', function() {
                            updateStatus('播放中');
                        });

                        video.addEventListener('pause', function() {
                            updateStatus('已暂停');
                        });

                        video.addEventListener('waiting', function() {
                            updateStatus('缓冲中...');
                        });

                        video.addEventListener('error', function(e) {
                            console.error('Video error:', e);
                            updateStatus('错误: ' + (video.error ? video.error.message : '未知错误'));
                            // 5秒后自动重试
                            setTimeout(refreshVideo, 5000);
                        });

                        // 页面加载完成后初始化
                        document.addEventListener('DOMContentLoaded', function() {
                            loadVideo();

                            // 每30秒自动刷新一次视频
                            setInterval(function() {
                                if (!video.paused) {
                                    refreshVideo();
                                }
                            }, 30000);
                        });

                        // 加载视频
                        function loadVideo() {
                            updateStatus('加载中...');
                            var timestamp = new Date().getTime();

                            // 使用Range请求，从头开始请求视频
                            fetch('video.mp4?t=' + timestamp, {
                                headers: {
                                    'Range': 'bytes=0-'
                                }
                            })
                            .then(response => {
                                if (response.ok || response.status === 206) {
                                    return response.blob();
                                }
                                throw new Error('Network response was not ok: ' + response.status);
                            })
                            .then(blob => {
                                var videoUrl = URL.createObjectURL(blob);
                                video.src = videoUrl;
                                video.load();
                                var playPromise = video.play();

                                if (playPromise !== undefined) {
                                    playPromise.catch(error => {
                                        console.error('Auto-play was prevented:', error);
                                        updateStatus('自动播放被阻止，请点击播放按钮');
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('Error loading video:', error);
                                updateStatus('加载失败: ' + error.message);
                                // 5秒后自动重试
                                setTimeout(refreshVideo, 5000);
                            });
                        }

                        // 刷新视频
                        function refreshVideo() {
                            // 记住当前播放状态
                            var wasPlaying = !video.paused;

                            // 释放之前的Blob URL
                            if (video.src && video.src.startsWith('blob:')) {
                                URL.revokeObjectURL(video.src);
                            }

                            loadVideo();

                            // 如果之前是暂停状态，加载后也暂停
                            if (!wasPlaying) {
                                video.pause();
                            }
                        }

                        // 切换播放/暂停
                        function togglePlayback() {
                            if (video.paused) {
                                video.play();
                            } else {
                                video.pause();
                            }
                        }

                        // 更新状态显示
                        function updateStatus(message) {
                            statusElement.textContent = '状态: ' + message;
                        }
                    </script>
                </body>
                </html>
            """.trimIndent()

            val response = newFixedLengthResponse(Response.Status.OK, "text/html", html)
            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
            response.addHeader("Pragma", "no-cache")
            response.addHeader("Expires", "0")
            return response
        }
    }
}
