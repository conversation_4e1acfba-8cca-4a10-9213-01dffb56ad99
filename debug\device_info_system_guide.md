# 发送端设备信息上报系统

## 🎯 系统概述

这是一个完整的发送端设备信息收集、上报和存储系统，用于在信令服务器中记录和管理所有发送端设备的详细信息。

## 📊 系统架构

```
发送端设备 → 信令服务器 → MySQL数据库 → Web管理界面
    ↓           ↓            ↓           ↓
设备信息收集  WebSocket接收  数据存储    信息展示
```

## 🗄️ 数据库设计

### 主表：sender_device_info
存储发送端设备的完整信息：

**设备标识信息**:
- `sender_id` - 发送端ID（唯一标识）
- `cpu_unique_id` - CPU唯一ID
- `wechat_sn` - 微信SN
- `motherboard_model` - 主板型号

**系统信息**:
- `android_version` - 安卓系统版本
- `system_version` - 系统版本

**硬件信息**:
- `available_storage` / `total_storage` - 存储空间（MB）
- `total_memory` / `available_memory` - 内存信息（MB）
- `cpu_temperature` - CPU温度（摄氏度）

**网络信息**:
- `public_ip` / `public_ipv6` - 公网IP地址
- `local_ip` - 内网IP地址
- `network_type` - 网络类型（wired/wifi/mobile）
- `mac_address` - 网卡MAC地址

**地理位置信息**（从ipinfo.io获取）:
- `city` / `region` / `country` - 城市/省份/国家
- `location` - 经纬度坐标
- `isp_org` - ISP组织
- `timezone` - 时区

**显示信息**:
- `screen_resolution` - 屏幕分辨率
- `screen_orientation` - 屏幕方向

**时间信息**:
- `system_time` - 设备系统时间
- `first_online_time` - 首次上线时间
- `last_online_time` - 最后上线时间
- `last_offline_time` - 最后离线时间
- `last_update_time` - 信息最后更新时间

### 历史表：sender_status_history
记录设备状态变化历史（可选）。

## 🔧 实现组件

### 1. 数据库初始化
**文件**: `database/sender_device_info.sql`
- 创建数据表结构
- 设置索引和约束
- 包含示例数据

### 2. 信令服务器扩展
**文件**: `enhanced_signaling_server.py`

**新增功能**:
- 数据库连接池管理
- 设备信息上报处理
- 公网IP信息获取
- 设备离线状态标记
- API接口扩展

**关键函数**:
```python
async def update_device_info(sender_id, device_info)  # 更新设备信息
async def mark_device_offline(sender_id)              # 标记设备离线
async def get_public_ip_info()                        # 获取公网IP信息
```

### 3. 发送端上报客户端
**文件**: `android/device_info_reporter.py`

**功能**:
- 收集设备硬件信息
- 连接信令服务器
- 定期上报设备信息
- 自动重连机制

**使用方法**:
```python
reporter = DeviceInfoReporter("gamev-001", "ws://localhost:8765")
await reporter.connect_to_signaling_server()
await reporter.start_periodic_reporting(300)  # 每5分钟上报一次
```

### 4. Web管理界面
**文件**: `web/admin.html`

**新增API**:
- `GET /api/senders` - 获取所有发送端信息（包含数据库详细信息）
- 设备信息在管理界面中展示

## 📡 通信协议

### 设备信息上报消息格式
```json
{
    "type": "device_info",
    "sender_id": "gamev-001",
    "device_info": {
        "cpu_unique_id": "CPU_123456789",
        "wechat_sn": "WX_SN_001",
        "motherboard_model": "Qualcomm SM8350",
        "android_version": "Android 13",
        "system_version": "MIUI 14.0.1",
        "available_storage": 15360,
        "total_storage": 65536,
        "total_memory": 8192,
        "available_memory": 4096,
        "cpu_temperature": 45.5,
        "local_ip": "*************",
        "network_type": "wifi",
        "mac_address": "00:11:22:33:44:55",
        "screen_resolution": "1920x1080",
        "screen_orientation": "landscape",
        "system_time": "2024-01-01T12:00:00"
    },
    "timestamp": 1704096000
}
```

### 服务器确认响应
```json
{
    "type": "device_info_ack",
    "success": true,
    "timestamp": 1704096000,
    "message": "设备信息已更新"
}
```

## 🚀 部署步骤

### 1. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE signaling_server;

-- 导入表结构
mysql -u root -p signaling_server < database/sender_device_info.sql
```

### 2. 安装Python依赖
```bash
pip install mysql-connector-python aiohttp psutil
```

### 3. 配置数据库连接
在 `enhanced_signaling_server.py` 中修改数据库配置：
```python
DB_CONFIG = {
    'host': 'localhost',
    'database': 'signaling_server',
    'user': 'your_username',
    'password': 'your_password',
    'charset': 'utf8mb4'
}
```

### 4. 启动信令服务器
```bash
python enhanced_signaling_server.py --ws-port 8765 --http-port 28080
```

### 5. 发送端集成
在Android应用中集成设备信息上报功能：
```python
# 在连接成功后立即上报设备信息
await reporter.report_device_info()

# 启动定期上报
await reporter.start_periodic_reporting(300)
```

## 📊 API接口

### 获取发送端列表
```
GET /api/senders
```

**响应格式**:
```json
{
    "senders": {
        "gamev-001": {
            "id": "gamev-001",
            "name": "Device gamev-001",
            "description": "Qualcomm SM8350 - Android 13",
            "online": true,
            "last_heartbeat": 1704096000,
            "device_info": {
                "cpu_unique_id": "CPU_123456789",
                "android_version": "Android 13",
                "total_memory": 8192,
                "cpu_temperature": 45.5,
                "public_ip": "*************",
                "city": "Shenzhen",
                "country": "CN"
                // ... 更多设备信息
            },
            "status": {
                "first_online_time": "2024-01-01T10:00:00",
                "last_online_time": "2024-01-01T12:00:00",
                "last_update_time": "2024-01-01T12:00:00",
                "heartbeat_count": 150
            }
        }
    }
}
```

## 🔍 监控和维护

### 1. 数据库监控
```sql
-- 查看在线设备数量
SELECT COUNT(*) FROM sender_device_info WHERE is_online = TRUE;

-- 查看最近上线的设备
SELECT sender_id, last_online_time FROM sender_device_info 
ORDER BY last_online_time DESC LIMIT 10;

-- 查看设备地理分布
SELECT country, region, city, COUNT(*) as device_count 
FROM sender_device_info 
GROUP BY country, region, city 
ORDER BY device_count DESC;
```

### 2. 日志监控
信令服务器会记录以下关键日志：
- `📱 收到设备信息上报: {sender_id}`
- `✅ 设备信息已更新: {sender_id}`
- `📱 设备已标记为离线: {sender_id}`

### 3. 性能优化
- 数据库连接池大小调整
- 设备信息上报频率控制
- 历史数据清理策略

## 🎯 使用场景

1. **设备管理**: 实时查看所有发送端设备状态
2. **性能监控**: 监控设备CPU温度、内存使用等
3. **地理分析**: 分析设备地理分布
4. **故障诊断**: 根据设备信息快速定位问题
5. **资源规划**: 基于设备硬件信息进行资源分配

## ⚠️ 注意事项

1. **隐私保护**: 确保设备信息的安全传输和存储
2. **数据清理**: 定期清理过期的历史数据
3. **网络优化**: 控制上报频率，避免网络拥塞
4. **错误处理**: 完善的异常处理和重试机制
5. **权限控制**: 限制设备信息的访问权限

现在系统已经完整实现，可以收集、存储和展示发送端设备的详细信息！
