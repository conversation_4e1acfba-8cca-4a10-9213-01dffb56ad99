# APK下载版本号命名和自动删除功能

## 功能说明

实现了APK下载时使用版本号命名，并在安装完成后自动删除安装包的功能。

## 主要改进

### 1. 版本号命名
- **原文件名**: `update.apk`
- **新文件名**: `update_v{version}.apk`
- **示例**: `update_v1.0.5.apk`

### 2. 自动删除安装包
- **时机**: 检测到应用更新成功后
- **方式**: 多次尝试删除，失败时记录日志
- **清理**: 下次升级时也会清理旧的版本APK文件

## 技术实现

### 1. 下载时命名
```kotlin
// HTTP下载
val apkFileName = "update_v${version}.apk"
request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, apkFileName)

// FTP下载
val apkFileName = "update_v${version}.apk"
val localFile = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkFileName)
```

### 2. 文件路径更新
```kotlin
// 安装时使用版本号文件名
val apkFileName = "update_v${version}.apk"
val apkFile = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), apkFileName)
```

### 3. 安装成功后删除
```kotlin
// 检测到应用更新成功后
if (targetVersion != null && currentVersionCode.toString() >= targetVersion!!) {
    Logger.i(TAG, "✅ 检测到应用已更新，删除安装包并重启应用")
    
    // 删除APK安装包
    deleteApkFile(apkFile)
    
    // 重启应用
    restartApplication()
}
```

### 4. 删除APK文件函数
```kotlin
private fun deleteApkFile(apkFile: File) {
    try {
        if (apkFile.exists()) {
            Logger.i(TAG, "🗑️ 开始删除APK安装包: ${apkFile.name}")
            
            // 尝试多次删除
            var deleted = false
            for (attempt in 1..3) {
                if (apkFile.delete()) {
                    deleted = true
                    Logger.i(TAG, "✅ APK安装包删除成功: ${apkFile.name}")
                    break
                } else {
                    Thread.sleep(500) // 等待重试
                }
            }
            
            if (!deleted) {
                Logger.w(TAG, "⚠️ 无法删除APK安装包: ${apkFile.name}")
            }
        }
    } catch (e: Exception) {
        Logger.e(TAG, "❌ 删除APK安装包时出错: ${e.message}")
    }
}
```

### 5. 清理旧版本APK文件
```kotlin
// 查找所有带版本号的APK文件
downloadDir.listFiles()?.forEach { file ->
    if (file.name.matches(Regex("update_v.*\\.apk"))) {
        apkFiles.add(file)
        Logger.d(TAG, "🔍 发现版本APK文件: ${file.name}")
    }
}
```

## 工作流程

### 1. 下载阶段
```
开始下载 → 使用版本号命名 → 下载到 update_v1.0.5.apk
```

### 2. 安装阶段
```
验证APK → 调用安装程序 → 监控安装状态
```

### 3. 更新检测
```
检查版本 → 发现更新成功 → 删除安装包 → 重启应用
```

### 4. 清理阶段
```
下次升级 → 清理所有旧版本APK → 下载新版本
```

## 日志输出

### 下载时
```
📥 开始下载APK: http://example.com/app.apk
🌐 使用HTTP下载
📦 APK下载完成
```

### 安装时
```
📦 开始安装APK
✅ APK验证通过，开始调用ZtlManager安装
🔄 启动应用重启监控机制
```

### 删除时
```
✅ 检测到应用已更新，删除安装包并重启应用
🗑️ 开始删除APK安装包: update_v1.0.5.apk
✅ APK安装包删除成功: update_v1.0.5.apk (第1次尝试)
```

### 清理时
```
🧹 开始清理旧APK文件...
🔍 发现版本APK文件: update_v1.0.4.apk
🧹 清理文件成功: update_v1.0.4.apk (第1次尝试)
```

## 优势

### 1. 版本管理
- **清晰标识**: 文件名包含版本号，便于识别
- **避免冲突**: 不同版本使用不同文件名
- **便于调试**: 可以保留多个版本进行对比

### 2. 存储管理
- **自动清理**: 安装成功后自动删除安装包
- **节省空间**: 避免APK文件堆积占用存储空间
- **定期清理**: 每次升级都会清理旧文件

### 3. 可靠性
- **多次重试**: 删除失败时会重试3次
- **容错处理**: 删除失败不影响应用正常运行
- **日志记录**: 详细记录删除过程便于排查问题

## 文件命名示例

### 不同版本的APK文件名
```
update_v1.0.0.apk
update_v1.0.1.apk
update_v1.0.2.apk
update_v1.0.3.apk
update_v1.0.4.apk
update_v1.0.5.apk
```

### 清理规则
- **保留当前**: 正在安装的版本APK
- **删除旧版**: 所有 `update_v*.apk` 格式的旧文件
- **删除通用**: `update.apk`, `app-debug.apk` 等通用名称文件

## 版本信息

### Android应用
- **版本**: 1.0.4 → 1.0.5
- **版本代码**: 104 → 105
- **更新内容**: APK下载版本号命名和自动删除功能

### 主要改进
1. **版本号命名**: APK文件名包含版本号
2. **自动删除**: 安装成功后自动删除安装包
3. **智能清理**: 清理所有版本号格式的旧APK文件
4. **多次重试**: 删除失败时重试机制
5. **详细日志**: 完整的操作日志记录

## 注意事项

### 1. 存储权限
- 需要外部存储读写权限
- 确保Downloads目录可访问

### 2. 删除时机
- 只在检测到应用更新成功后删除
- 删除失败不影响应用功能

### 3. 文件管理
- 定期清理避免文件堆积
- 支持手动删除旧APK文件

### 4. 兼容性
- 支持HTTP和FTP下载方式
- 兼容不同Android版本的文件操作

现在APK下载会使用版本号命名，安装完成后会自动删除安装包，有效管理存储空间！
