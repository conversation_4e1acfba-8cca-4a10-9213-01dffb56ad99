# 服务器群配置指南

## 当前配置

所有服务器接口都使用 **POST** 请求方式：

```python
SERVER_GROUPS = [
    {
        'domain': 'http://testva2.91jdcd.com',
        'method': 'POST',
        'name': '测试服务器V2'
    },
    {
        'domain': 'http://bsth5.yinmengkj.cn',
        'method': 'POST',
        'name': '银梦科技服务器'
    },
    {
        'domain': 'http://yx.yhdyc.com',
        'method': 'POST',
        'name': '游戏服务器'
    }
]
```

## 接口详情

- **接口路径**: `/api/game/list_dcl_ast4`
- **请求方式**: POST
- **请求头**:
  ```
  Content-Type: application/json
  Accept: application/json, text/plain, */*
  User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  ```
- **请求体**: `{}` (空JSON对象)

## 响应格式

```json
{
    "code": 1,
    "msg": "成功获取",
    "time": "1756355794",
    "data": [
        {
            "id": 2,
            "heiqiplayer_sender_id": "gamev-b246c42d",
            "name": "水浒传_DF14",
            "c_id": 3,
            "sort": 0
        }
    ]
}
```

## 字段说明

- `id`: 房间ID
- `heiqiplayer_sender_id`: 关联的发送端ID
- `name`: 房间名称
- `c_id`: 分类ID (0=未分类, 1=街机游戏, 2=休闲游戏, 3=棋牌游戏, 等)
- `sort`: 排序权重

## 添加新服务器

在 `SERVER_GROUPS` 数组中添加新的配置对象：

```python
{
    'domain': 'http://your-server.com',
    'method': 'POST',
    'name': '你的服务器名称'
}
```

## 测试服务器连接

运行测试脚本：

```bash
python debug/test_server_requests.py
```

## 版本信息

- **当前版本**: 1.0.6
- **更新内容**: 统一所有服务器使用POST请求方式
- **更新时间**: 2025-08-28

## 注意事项

1. 所有服务器必须支持POST请求
2. 请求体为空JSON对象 `{}`
3. 响应必须是有效的JSON格式
4. `code: 1` 表示成功，其他值表示错误
5. 房间数据在 `data` 字段中
