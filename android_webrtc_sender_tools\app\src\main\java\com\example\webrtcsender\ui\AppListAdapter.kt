package com.example.webrtcsender.ui

import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.webrtcsender.R

class AppListAdapter(
    private val apps: List<ApplicationInfo>,
    private val packageManager: PackageManager,
    private val onAppClick: (ApplicationInfo) -> Unit
) : RecyclerView.Adapter<AppListAdapter.AppViewHolder>() {

    class AppViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val appIcon: ImageView = itemView.findViewById(R.id.appIcon)
        val appName: TextView = itemView.findViewById(R.id.appName)
        val packageName: TextView = itemView.findViewById(R.id.packageName)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app, parent, false)
        return AppViewHolder(view)
    }

    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        val app = apps[position]
        
        try {
            // 设置应用图标
            holder.appIcon.setImageDrawable(packageManager.getApplicationIcon(app))
            
            // 设置应用名称
            holder.appName.text = packageManager.getApplicationLabel(app)
            
            // 设置包名
            holder.packageName.text = app.packageName
            
            // 设置点击事件
            holder.itemView.setOnClickListener {
                onAppClick(app)
            }
            
        } catch (e: Exception) {
            // 如果获取应用信息失败，显示默认信息
            holder.appName.text = app.packageName
            holder.packageName.text = app.packageName
        }
    }

    override fun getItemCount(): Int = apps.size
}
