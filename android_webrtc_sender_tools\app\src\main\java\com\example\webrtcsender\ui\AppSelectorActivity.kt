package com.example.webrtcsender.ui

import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.webrtcsender.R
import com.example.webrtcsender.utils.Logger

class AppSelectorActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "AppSelectorActivity"
        const val EXTRA_SELECTED_PACKAGE = "selected_package"
        const val EXTRA_SELECTED_APP_NAME = "selected_app_name"
    }

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: AppListAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_app_selector)

        // 设置标题和返回按钮
        title = "选择游戏应用"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 初始化RecyclerView
        recyclerView = findViewById(R.id.recyclerView)
        recyclerView.layoutManager = LinearLayoutManager(this)

        // 加载应用列表
        loadAppList()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun loadAppList() {
        try {
            val packageManager = packageManager

            // 使用不同的标志获取更多应用
            // 方法1: 通过LAUNCHER Intent获取应用
            val launcherIntent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
            }
            val launcherApps = packageManager.queryIntentActivities(launcherIntent,
                PackageManager.MATCH_ALL or
                PackageManager.MATCH_DISABLED_COMPONENTS or
                PackageManager.MATCH_UNINSTALLED_PACKAGES)

            Logger.i(TAG, "通过LAUNCHER Intent找到 ${launcherApps.size} 个应用")

            // 方法2: 获取所有已安装的应用包
            val allPackages = try {
                packageManager.getInstalledPackages(PackageManager.GET_META_DATA)
            } catch (e: Exception) {
                Logger.w(TAG, "无法获取已安装包列表: ${e.message}")
                emptyList()
            }

            Logger.i(TAG, "通过getInstalledPackages找到 ${allPackages.size} 个包")

            // 合并两种方法的结果
            val appMap = mutableMapOf<String, ApplicationInfo>()

            // 添加LAUNCHER应用
            launcherApps.forEach { resolveInfo ->
                val packageName = resolveInfo.activityInfo.packageName
                if (packageName != this.packageName) {
                    try {
                        val appInfo = packageManager.getApplicationInfo(packageName, 0)
                        appMap[packageName] = appInfo
                    } catch (e: Exception) {
                        Logger.w(TAG, "无法获取应用信息: $packageName")
                    }
                }
            }

            // 添加所有已安装的包
            allPackages.forEach { packageInfo ->
                val packageName = packageInfo.packageName
                if (packageName != this.packageName && !appMap.containsKey(packageName)) {
                    try {
                        val appInfo = packageManager.getApplicationInfo(packageName, 0)
                        appMap[packageName] = appInfo
                    } catch (e: Exception) {
                        Logger.w(TAG, "无法获取应用信息: $packageName")
                    }
                }
            }

            // 特别检查Ocean3应用，使用多种方法
            val ocean3PackageName = "com.ironnet.ocean3"
            if (!appMap.containsKey(ocean3PackageName)) {
                try {
                    // 方法1: 直接获取应用信息
                    val ocean3Info = packageManager.getApplicationInfo(ocean3PackageName, 0)
                    appMap[ocean3PackageName] = ocean3Info
                    Logger.i(TAG, "方法1成功添加了Ocean3应用")
                } catch (e: Exception) {
                    try {
                        // 方法2: 使用扩展标志
                        val ocean3Info = packageManager.getApplicationInfo(ocean3PackageName,
                            PackageManager.MATCH_UNINSTALLED_PACKAGES or PackageManager.MATCH_DISABLED_COMPONENTS)
                        appMap[ocean3PackageName] = ocean3Info
                        Logger.i(TAG, "方法2成功添加了Ocean3应用")
                    } catch (e2: Exception) {
                        // 方法3: 从包列表中查找
                        val ocean3Package = allPackages.find { it.packageName == ocean3PackageName }
                        if (ocean3Package != null) {
                            try {
                                val ocean3Info = packageManager.getApplicationInfo(ocean3PackageName, 0)
                                appMap[ocean3PackageName] = ocean3Info
                                Logger.i(TAG, "方法3成功添加了Ocean3应用")
                            } catch (e3: Exception) {
                                Logger.w(TAG, "Ocean3应用在包列表中但无法获取信息: ${e3.message}")
                            }
                        } else {
                            Logger.w(TAG, "Ocean3应用完全不存在或无法访问: ${e.message}")
                        }
                    }
                }
            } else {
                Logger.i(TAG, "Ocean3应用已经在列表中")
            }

            val allApps = appMap.values.toList()

            Logger.i(TAG, "合并后总共 ${allApps.size} 个应用")

            // 打印所有应用的包名用于调试
            allApps.forEach { app ->
                val appName = try {
                    packageManager.getApplicationLabel(app).toString()
                } catch (e: Exception) {
                    app.packageName
                }
                Logger.d(TAG, "发现应用: $appName (${app.packageName})")
            }

            // 按安装包大小排序（从大到小）
            val sortedApps = allApps.sortedByDescending { app ->
                try {
                    val apkPath = app.sourceDir
                    val file = java.io.File(apkPath)
                    if (file.exists()) {
                        file.length()
                    } else {
                        0L
                    }
                } catch (e: Exception) {
                    Logger.w(TAG, "无法获取应用大小: ${app.packageName}")
                    0L
                }
            }

            Logger.i(TAG, "最终显示 ${sortedApps.size} 个应用")

            adapter = AppListAdapter(sortedApps, packageManager) { selectedApp ->
                onAppSelected(selectedApp)
            }
            recyclerView.adapter = adapter

        } catch (e: Exception) {
            Logger.e(TAG, "加载应用列表失败: ${e.message}")
            Toast.makeText(this, "加载应用列表失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun onAppSelected(app: ApplicationInfo) {
        try {
            val packageName = app.packageName
            val appName = packageManager.getApplicationLabel(app).toString()

            Logger.i(TAG, "选择了应用: $appName ($packageName)")

            // 返回选择的应用信息
            val resultIntent = Intent().apply {
                putExtra(EXTRA_SELECTED_PACKAGE, packageName)
                putExtra(EXTRA_SELECTED_APP_NAME, appName)
            }
            setResult(RESULT_OK, resultIntent)
            finish()

        } catch (e: Exception) {
            Logger.e(TAG, "选择应用失败", e)
            Toast.makeText(this, "选择应用失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}
