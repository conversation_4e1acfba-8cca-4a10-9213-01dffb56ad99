<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }
        
        .modal-overlay.show {
            display: flex;
        }
        
        .modal-dialog {
            background: white;
            border-radius: 12px;
            padding: 25px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .modal-footer {
            margin-top: 20px;
            text-align: right;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 管理控制台修复测试</h1>
    
    <div class="test-section">
        <h2>1. STUN/TURN配置功能测试</h2>
        <p>测试STUN/TURN配置模态框是否能正常打开和关闭</p>
        <button class="btn" onclick="testShowStunTurnModal()">🌐 打开STUN/TURN配置</button>
        <div id="stunTurnStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>2. JavaScript函数测试</h2>
        <p>测试各个JavaScript函数是否正确定义</p>
        <button class="btn" onclick="testFunctions()">🧪 测试函数定义</button>
        <div id="functionStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 预设配置测试</h2>
        <p>测试STUN/TURN预设配置是否正常工作</p>
        <button class="btn" onclick="testPresets()">⚙️ 测试预设配置</button>
        <div id="presetStatus"></div>
    </div>

    <!-- STUN/TURN配置模态框 -->
    <div class="modal-overlay" id="stunTurnModal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">🌐 STUN/TURN服务器配置</h3>
                <button class="modal-close" onclick="closeStunTurnModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>常用STUN服务器配置</label>
                    <select id="stunPresets" onchange="applyStunPreset()">
                        <option value="">选择预设配置</option>
                        <option value="google">Google STUN服务器</option>
                        <option value="mozilla">Mozilla STUN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>STUN服务器 (每行一个)</label>
                    <textarea id="modalStunServers" rows="4" placeholder="stun:stun.l.google.com:19302&#10;stun:stun1.l.google.com:19302"></textarea>
                </div>

                <div class="form-group">
                    <label>常用TURN服务器配置</label>
                    <select id="turnPresets" onchange="applyTurnPreset()">
                        <option value="">选择预设配置</option>
                        <option value="numb">Numb TURN服务器</option>
                        <option value="xirsys">Xirsys TURN服务器</option>
                        <option value="custom">自定义配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>TURN服务器配置 (JSON格式)</label>
                    <textarea id="modalTurnServers" rows="6" placeholder='[{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}]'></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeStunTurnModal()">取消</button>
                <button class="btn" onclick="saveStunTurnConfig()">保存配置</button>
                <button class="btn" onclick="saveAndBroadcastStunTurn()">保存并广播</button>
            </div>
        </div>
    </div>

    <!-- 隐藏的配置输入框（模拟主配置区域） -->
    <textarea id="stunServers" style="display: none;">stun:stun.l.google.com:19302</textarea>
    <textarea id="turnServers" style="display: none;">[]</textarea>

    <script>
        // 模拟admin对象
        const admin = {
            updateConfig: function() {
                console.log('配置已更新');
                return true;
            },
            updateConfigAndBroadcast: function() {
                console.log('配置已更新并广播');
                return true;
            },
            sendQuickCommand: function(deviceId, command, params) {
                console.log('发送命令:', deviceId, command, params);
                return true;
            }
        };

        // STUN/TURN配置相关方法
        function showStunTurnModal() {
            const modal = document.getElementById('stunTurnModal');
            const stunServers = document.getElementById('modalStunServers');
            const turnServers = document.getElementById('modalTurnServers');
            
            // 加载当前配置
            stunServers.value = document.getElementById('stunServers').value;
            turnServers.value = document.getElementById('turnServers').value;
            
            modal.classList.add('show');
        }

        function closeStunTurnModal() {
            const modal = document.getElementById('stunTurnModal');
            modal.classList.remove('show');
        }

        function applyStunPreset() {
            const preset = document.getElementById('stunPresets').value;
            const stunServers = document.getElementById('modalStunServers');
            
            switch(preset) {
                case 'google':
                    stunServers.value = 'stun:stun.l.google.com:19302\nstun:stun1.l.google.com:19302\nstun:stun2.l.google.com:19302';
                    break;
                case 'mozilla':
                    stunServers.value = 'stun:stun.services.mozilla.com';
                    break;
                case 'custom':
                    stunServers.value = '';
                    break;
            }
        }

        function applyTurnPreset() {
            const preset = document.getElementById('turnPresets').value;
            const turnServers = document.getElementById('modalTurnServers');
            
            switch(preset) {
                case 'numb':
                    turnServers.value = JSON.stringify([{
                        "urls": "turn:numb.viagenie.ca",
                        "username": "<EMAIL>",
                        "credential": "muazkh"
                    }], null, 2);
                    break;
                case 'xirsys':
                    turnServers.value = JSON.stringify([{
                        "urls": "turn:xirsys.com",
                        "username": "your_username",
                        "credential": "your_credential"
                    }], null, 2);
                    break;
                case 'custom':
                    turnServers.value = '';
                    break;
            }
        }

        function saveStunTurnConfig() {
            const stunServers = document.getElementById('modalStunServers').value;
            const turnServers = document.getElementById('modalTurnServers').value;
            
            // 更新主配置区域
            document.getElementById('stunServers').value = stunServers;
            document.getElementById('turnServers').value = turnServers;
            
            // 保存配置
            admin.updateConfig();
            closeStunTurnModal();
            
            showStatus('stunTurnStatus', '配置保存成功！', 'success');
        }

        function saveAndBroadcastStunTurn() {
            const stunServers = document.getElementById('modalStunServers').value;
            const turnServers = document.getElementById('modalTurnServers').value;
            
            // 更新主配置区域
            document.getElementById('stunServers').value = stunServers;
            document.getElementById('turnServers').value = turnServers;
            
            // 保存并广播配置
            admin.updateConfigAndBroadcast();
            closeStunTurnModal();
            
            showStatus('stunTurnStatus', '配置保存并广播成功！', 'success');
        }

        // 测试函数
        function testShowStunTurnModal() {
            try {
                showStunTurnModal();
                showStatus('stunTurnStatus', 'STUN/TURN配置模态框打开成功！', 'success');
            } catch (error) {
                showStatus('stunTurnStatus', '错误: ' + error.message, 'error');
            }
        }

        function testFunctions() {
            const functions = [
                'showStunTurnModal',
                'closeStunTurnModal', 
                'applyStunPreset',
                'applyTurnPreset',
                'saveStunTurnConfig',
                'saveAndBroadcastStunTurn'
            ];
            
            let results = [];
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`✅ ${funcName}: 已定义`);
                } else {
                    results.push(`❌ ${funcName}: 未定义`);
                }
            });
            
            showStatus('functionStatus', results.join('<br>'), 'success');
        }

        function testPresets() {
            try {
                // 测试STUN预设
                document.getElementById('stunPresets').value = 'google';
                applyStunPreset();
                
                // 测试TURN预设
                document.getElementById('turnPresets').value = 'numb';
                applyTurnPreset();
                
                showStatus('presetStatus', '预设配置测试成功！STUN和TURN预设都能正常工作。', 'success');
            } catch (error) {
                showStatus('presetStatus', '预设配置测试失败: ' + error.message, 'error');
            }
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 点击外部关闭模态框
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal-overlay')) {
                closeStunTurnModal();
            }
        });
    </script>
</body>
</html>
