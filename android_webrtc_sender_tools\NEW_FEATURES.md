# 发送端新增功能说明

## 新增命令

### 1. 设置自动启动游戏 (set_auto_start_game)
- **功能**: 远程设置是否自动启动游戏及指定游戏包名
- **参数**:
  - `enabled`: boolean - 是否启用自动启动游戏
  - `package_name`: string - 游戏包名（可选）
- **示例**:
  ```json
  {
    "type": "control_command",
    "command": "set_auto_start_game",
    "params": {
      "enabled": true,
      "package_name": "com.example.birdgame"
    }
  }
  ```

### 2. 开关日志输出 (toggle_log_display)
- **功能**: 远程控制日志显示的开关状态
- **特性**:
  - 设置会永久保存，重启应用或设备后仍然有效
  - 一旦开启，将持续开启直到手动关闭
  - 默认状态为关闭
- **参数**:
  - `enabled`: boolean - 是否显示日志
- **示例**:
  ```json
  {
    "type": "control_command",
    "command": "toggle_log_display",
    "params": {
      "enabled": false
    }
  }
  ```

### 3. 下载最近日志 (download_logs)
- **功能**: 收集最近5MB左右的日志文件，自动上传到指定接口
- **参数**:
  - `method`: string - 上传方式，"ftp" 或 "http"（默认ftp）
- **文件命名**: `{设备ID}_{时间戳}.log`
- **上传方式**: 
  - FTP: `******************************************`
  - HTTP: `https://testva2.91jdcd.com/api/common/upload_log`
- **示例**:
  ```json
  {
    "type": "control_command",
    "command": "download_logs",
    "params": {
      "method": "ftp"
    }
  }
  ```

### 4. 截屏 (take_screenshot)
- **功能**: 从视频流抽取当前一帧屏幕画面，自动上传并返回URL
- **参数**:
  - `request_id`: string - 请求ID（必需）
- **返回**: 通过信令服务器发送截屏结果消息
- **示例**:
  ```json
  {
    "type": "control_command",
    "command": "take_screenshot",
    "params": {
      "request_id": "screenshot_123456"
    }
  }
  ```
- **返回消息格式**:
  ```json
  {
    "type": "screenshot_result",
    "request_id": "screenshot_123456",
    "success": true,
    "full_url": "https://example.com/screenshot.jpg",
    "message": "截屏成功",
    "timestamp": 1640995200
  }
  ```

## 首次安装默认配置

### 默认参数设置
- **分辨率**: 576p
- **比特率**: 3000 kbps
- **帧率**: 60 fps
- **音频来源**: 远程混音 (remote_submix)
- **视频来源**: 摄像头 (camera)
- **日志显示**: 关闭

### 自动游戏检测
应用首次安装时会自动扫描已安装的应用，如果发现包名包含以下关键字的游戏（不分大小写），会自动设置为默认启动游戏：
- `bird`
- `ocean`
- `demo`

## 技术实现

### 新增文件
1. **LogManager.kt** - 日志管理器
   - 日志文件收集和合并
   - FTP和HTTP上传功能
   - 日志文件清理

2. **ScreenshotManager.kt** - 截屏管理器
   - MediaProjection截屏功能
   - 图像处理和上传
   - 异步处理机制

3. **ExtendedCommandHandler.kt** - 扩展命令处理器
   - 处理新增的四个命令
   - 异步任务管理
   - 结果回调处理

4. **FirstInstallConfigManager.kt** - 首次安装配置管理器
   - 默认配置设置
   - 游戏自动检测
   - 配置初始化

### 修改文件
1. **Constants.kt** - 添加新的配置常量
2. **Logger.kt** - 添加日志显示开关功能
3. **CommandDispatcher.kt** - 集成扩展命令处理器
4. **SignalingClient.kt** - 添加公共消息发送方法
5. **WebRTCManager.kt** - 添加MediaProjection访问方法
6. **WebRTCSenderApp.kt** - 集成首次安装配置
7. **build.gradle** - 添加OkHttp和Apache Commons Net依赖

### 依赖项
- **OkHttp 3.14.9** - HTTP客户端，用于文件上传
- **Apache Commons Net 3.6** - FTP客户端，用于FTP上传

## 配置接口

### 上传日志接口
- **URL**: `https://testva2.91jdcd.com/api/common/upload_log`
- **方法**: POST
- **参数**: `file` (文件流)
- **返回格式**:
  ```json
  {
    "code": "1",
    "msg": "返回成功",
    "data": {
      "fullurl": "文件地址"
    }
  }
  ```

### 截屏上传接口
- **URL**: `https://testva2.91jdcd.com/api/common/upload_log`
- **方法**: POST
- **参数**: `file` (图片文件)
- **返回格式**: 同上

### FTP服务器
- **地址**: `8.134.131.24:21`
- **用户名**: `gdevice`
- **密码**: `DtNNXThtp7W2`

## 使用说明

1. **首次安装**: 应用会自动设置默认配置，包括检测内置游戏
2. **日志控制**:
   - 默认关闭日志显示
   - 可通过命令远程开启/关闭
   - **重要**: 日志显示状态会永久保存，重启应用或设备后仍然有效
   - 一旦开启日志显示，将持续开启直到手动关闭
3. **日志上传**: 支持FTP和HTTP两种方式上传最近5MB日志
4. **截屏功能**: 实时从视频流截取画面并上传，返回图片URL
5. **游戏设置**: 可远程设置自动启动游戏的开关和具体游戏

所有新功能都通过信令服务器的控制命令进行远程操作，支持命令验证和结果反馈。

### 日志显示持久化说明

- **默认状态**: 首次安装时日志显示关闭
- **持久化机制**: 设置保存在SharedPreferences中
- **生效范围**: 影响控制台日志输出，不影响日志文件写入
- **重启保持**: 应用重启、设备重启后都会保持上次设置的状态
