{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-sv\\values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,302,400,522,601,664,756,820,880,972,1035,1097,1164,1228,1282,1387,1446,1507,1561,1630,1749,1832,1916,2022,2101,2185,2271,2338,2404,2473,2547,2636,2708,2785,2856,2930,3021,3100,3187,3275,3347,3421,3506,3557,3624,3705,3789,3851,3915,3978,4085,4192,4291,4399", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "216,297,395,517,596,659,751,815,875,967,1030,1092,1159,1223,1277,1382,1441,1502,1556,1625,1744,1827,1911,2017,2096,2180,2266,2333,2399,2468,2542,2631,2703,2780,2851,2925,3016,3095,3182,3270,3342,3416,3501,3552,3619,3700,3784,3846,3910,3973,4080,4187,4286,4394,4472"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2944,3025,3123,3245,3324,3387,3479,3543,3603,3695,3758,3820,3887,3951,4005,4110,4169,4230,4284,4353,4472,4555,4639,4745,4824,4908,4994,5061,5127,5196,5270,5359,5431,5508,5579,5653,5744,5823,5910,5998,6070,6144,6229,6280,6347,6428,6512,6574,6638,6701,6808,6915,7014,7122", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "266,3020,3118,3240,3319,3382,3474,3538,3598,3690,3753,3815,3882,3946,4000,4105,4164,4225,4279,4348,4467,4550,4634,4740,4819,4903,4989,5056,5122,5191,5265,5354,5426,5503,5574,5648,5739,5818,5905,5993,6065,6139,6224,6275,6342,6423,6507,6569,6633,6696,6803,6910,7009,7117,7195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7280", "endColumns": "100", "endOffsets": "7376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,374,477,588,672,774,887,964,1039,1132,1227,1322,1416,1518,1613,1710,1808,1904,1997,2077,2183,2282,2378,2483,2586,2688,2842,7200", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "369,472,583,667,769,882,959,1034,1127,1222,1317,1411,1513,1608,1705,1803,1899,1992,2072,2178,2277,2373,2478,2581,2683,2837,2939,7275"}}]}]}