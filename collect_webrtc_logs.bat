@echo off
REM 设置命令行代码页为UTF-8
chcp 65001
REM 线上线下服务台日志收集脚本
REM 此脚本用于收集线上线下服务台应用的错误日志

REM 清除之前的logcat日志
echo 清除之前的logcat日志...
adb logcat -c

REM 启动应用
echo 启动线上线下服务台应用...
adb shell am start -n com.example.webrtcsender/.ui.MainActivity

REM 提示用户手动操作
echo 请在设备上手动执行以下操作：
echo 1. 点击'启动服务'按钮
echo 2. 点击'屏幕录制'按钮
echo 3. 在权限对话框中点击'立即开始'
echo 4. 等待应用运行一段时间
echo 完成后，按任意键继续收集日志...
pause > nul

REM 收集logcat日志
echo 收集logcat日志...
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c%%a%%b)
for /f "tokens=1-2 delims=/:" %%a in ('time /t') do (set mytime=%%a%%b)
set timestamp=%mydate%_%mytime%
set logFile=webrtc_sender_log_%timestamp%.txt

REM 收集WebRTCSender相关标签的所有级别日志，其他标签只收集错误级别
echo 收集WebRTCSender相关标签的所有级别日志，其他标签只收集错误级别...
adb logcat -d "WebRTCSender:V" "WebRTCClient:V" "SignalingClient:V" "WebRTCManager:V" "*:E" > %logFile%

REM 同时创建一个只包含错误的日志文件
set errorLogFile=webrtc_sender_errors_%timestamp%.txt
echo 同时创建一个只包含错误的日志文件: %errorLogFile%
adb logcat -d "*:E" > %errorLogFile%

echo 日志收集完成！日志已保存到 %logFile%
echo 您可以使用以下命令继续查看实时日志：
echo adb logcat '*:E'
pause
