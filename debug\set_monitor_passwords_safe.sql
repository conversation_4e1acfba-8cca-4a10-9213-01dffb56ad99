-- 安全版本：设置监控密码
-- 适用于所有MySQL版本

-- 检查字段是否存在的安全方法
SELECT COUNT(*) as field_exists 
FROM information_schema.COLUMNS 
WHERE table_schema = DATABASE() 
AND table_name = 'fa_sender_device_info' 
AND column_name = 'monitor_password';

-- 如果上面查询结果为0，说明字段不存在，需要添加
-- 请根据查询结果决定是否执行下面的ALTER语句

-- 添加monitor_password字段（仅在字段不存在时执行）
-- ALTER TABLE fa_sender_device_info 
-- ADD COLUMN monitor_password VARCHAR(100) DEFAULT '' COMMENT '监控密码，用于权限控制';

-- 为测试服务器V2的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'test6743' 
WHERE room_server_domain = 'http://testva2.91jdcd.com';

-- 为银梦科技服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'yinmeng3623' 
WHERE room_server_domain = 'http://bsth5.yinmengkj.cn';

-- 为游戏服务器的设备设置密码
UPDATE fa_sender_device_info 
SET monitor_password = 'game8754' 
WHERE room_server_domain = 'http://yx.yhdyc.com';

-- 查看设置结果
SELECT 
    room_server_domain,
    monitor_password,
    COUNT(*) as device_count
FROM fa_sender_device_info 
WHERE room_server_domain != '' 
GROUP BY room_server_domain, monitor_password
ORDER BY room_server_domain;

-- 查看所有设备的监控密码设置
SELECT 
    sender_id,
    room_name,
    room_server_domain,
    monitor_password,
    is_online,
    last_online_time
FROM fa_sender_device_info 
WHERE room_server_domain != ''
ORDER BY room_server_domain, room_name
LIMIT 20;

-- 测试密码权限查询
SELECT 'test6743密码权限' as test_name, GROUP_CONCAT(DISTINCT room_server_domain) as allowed_domains
FROM fa_sender_device_info 
WHERE monitor_password = 'test6743' AND room_server_domain != ''

UNION ALL

SELECT 'yinmeng3623密码权限' as test_name, GROUP_CONCAT(DISTINCT room_server_domain) as allowed_domains
FROM fa_sender_device_info 
WHERE monitor_password = 'yinmeng3623' AND room_server_domain != ''

UNION ALL

SELECT 'game8754密码权限' as test_name, GROUP_CONCAT(DISTINCT room_server_domain) as allowed_domains
FROM fa_sender_device_info 
WHERE monitor_password = 'game8754' AND room_server_domain != '';

-- 统计各密码的设备数量
SELECT 
    CASE 
        WHEN monitor_password = '' THEN '未设置密码'
        ELSE CONCAT('密码: ', monitor_password)
    END as password_status,
    COUNT(*) as device_count,
    GROUP_CONCAT(DISTINCT room_server_domain) as server_domains
FROM fa_sender_device_info 
WHERE room_server_domain != ''
GROUP BY monitor_password
ORDER BY device_count DESC;
