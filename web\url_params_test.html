<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL参数测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .example h3 {
            margin-top: 0;
            color: #495057;
        }
        .url {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .param-table th,
        .param-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .param-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .test-link {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px 0;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC接收端 URL参数测试</h1>
        
        <h2>支持的URL参数</h2>
        <table class="param-table">
            <thead>
                <tr>
                    <th>参数名</th>
                    <th>别名</th>
                    <th>说明</th>
                    <th>示例值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>senderId</td>
                    <td>sender_id, id</td>
                    <td>发送端ID</td>
                    <td>gamev-8cd7c032</td>
                </tr>
                <tr>
                    <td>signalingUrl</td>
                    <td>signaling_url, ws</td>
                    <td>信令服务器WebSocket URL</td>
                    <td>wss://sling.91jdcd.com/ws/</td>
                </tr>
                <tr>
                    <td>autoConnect</td>
                    <td>auto_connect, auto</td>
                    <td>是否自动连接 (true/1)</td>
                    <td>true</td>
                </tr>
            </tbody>
        </table>

        <h2>使用示例</h2>

        <div class="example">
            <h3>示例1：只设置发送端ID</h3>
            <div class="url">mobile_receiver.html?senderId=gamev-8cd7c032</div>
            <a href="mobile_receiver.html?senderId=gamev-8cd7c032" class="test-link" target="_blank">测试此链接</a>
        </div>

        <div class="example">
            <h3>示例2：设置发送端ID和自动连接</h3>
            <div class="url">mobile_receiver.html?senderId=gamev-8cd7c032&autoConnect=true</div>
            <a href="mobile_receiver.html?senderId=gamev-8cd7c032&autoConnect=true" class="test-link" target="_blank">测试此链接</a>
        </div>

        <div class="example">
            <h3>示例3：设置所有参数</h3>
            <div class="url">mobile_receiver.html?senderId=gamev-8cd7c032&signalingUrl=wss://sling.91jdcd.com/ws/&autoConnect=true</div>
            <a href="mobile_receiver.html?senderId=gamev-8cd7c032&signalingUrl=wss://sling.91jdcd.com/ws/&autoConnect=true" class="test-link" target="_blank">测试此链接</a>
        </div>

        <div class="example">
            <h3>示例4：使用别名参数</h3>
            <div class="url">mobile_receiver.html?id=gamev-8cd7c032&ws=wss://sling.91jdcd.com/ws/&auto=1</div>
            <a href="mobile_receiver.html?id=gamev-8cd7c032&ws=wss://sling.91jdcd.com/ws/&auto=1" class="test-link" target="_blank">测试此链接</a>
        </div>

        <h2>功能说明</h2>
        <ul>
            <li><strong>senderId</strong>：如果提供，会自动填入发送端ID输入框</li>
            <li><strong>signalingUrl</strong>：如果提供，会自动填入信令服务器URL输入框</li>
            <li><strong>autoConnect</strong>：如果设置为 true 或 1，页面加载后会自动连接到信令服务器</li>
            <li>参数支持多种命名方式（驼峰式、下划线式、简写）以提高兼容性</li>
            <li>页面会在控制台和日志中显示从URL参数读取的设置</li>
        </ul>

        <h2>注意事项</h2>
        <ul>
            <li>URL参数会覆盖页面的默认设置</li>
            <li>自动连接功能会在页面加载1秒后触发</li>
            <li>如果URL中没有提供参数，会使用页面的默认值</li>
            <li>参数值需要进行URL编码（如果包含特殊字符）</li>
        </ul>
    </div>
</body>
</html>
