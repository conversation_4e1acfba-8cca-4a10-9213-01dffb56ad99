package com.example.webrtcsender.webrtc

import android.content.Context
import android.hardware.camera2.CameraManager
import android.os.Handler
import android.os.Looper
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.utils.AutoRebootManager
import com.example.webrtcsender.utils.DeviceLogManager
import com.example.webrtcsender.audio.AudioSourceTester
import com.example.webrtcsender.audio.CaptureCardAudioManager
import org.webrtc.*
import org.webrtc.PeerConnection.*
import org.webrtc.audio.AudioDeviceModule
import org.webrtc.audio.JavaAudioDeviceModule
import java.io.File
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentHashMap

/**
 * WebRTC客户端，负责管理PeerConnection和媒体流
 */
class WebRTCClient(
    private val context: Context,
    private val listener: WebRTCClientListener
) {
    companion object {
        private const val TAG = "WebRTCClient"

        // 减少MediaCodec重复日志
        init {
            // 设置WebRTC日志级别为ERROR，减少STUN连接失败等调试日志
            try {
                org.webrtc.Logging.enableLogToDebugOutput(org.webrtc.Logging.Severity.LS_ERROR)
            } catch (e: Exception) {
                android.util.Log.w(TAG, "无法设置WebRTC日志级别: ${e.message}")
            }
        }
    }

    // WebRTC工厂
    private val rootEglBase: EglBase = EglBase.create()
    private val peerConnectionFactory: PeerConnectionFactory by lazy { createPeerConnectionFactory() }

    // 媒体流 - 保持原有变量名以兼容现有代码
    private var localVideoSource: VideoSource? = null
    private var localVideoTrack: VideoTrack? = null
    private var localAudioSource: AudioSource? = null
    private var localAudioTrack: AudioTrack? = null

    // 共享媒体流 - 新增共享模式支持
    private var sharedVideoSource: VideoSource? = null
    private var sharedVideoTrack: VideoTrack? = null
    private var sharedAudioSource: AudioSource? = null
    private var sharedAudioTrack: AudioTrack? = null

    // 视频源共享管理
    private var videoCapturer: VideoCapturer? = null
    private var surfaceTextureHelper: SurfaceTextureHelper? = null
    private var isVideoSourceInitialized = false

    // 视频配置参数
    private var currentVideoResolution = "1920x1080"
    private var currentVideoBitrate = 3000
    private var currentVideoCodec = "H264"
    private var currentVideoSourceType = "screen"
    private var currentCameraId = "0"

    // 性能自适应管理
    private var adaptiveEncodingManager: AdaptiveEncodingManager? = null
    private var framerateStabilizer: FramerateStabilizer? = null

    // 性能自适应编码管理器
    private class AdaptiveEncodingManager {
        private var baseFramerate = 60
        private var baseBitrate = 3000 // kbps
        private var currentFramerate = 60
        private var currentBitrate = 3000
        private var lastAdjustmentTime = 0L
        private val adjustmentInterval = 5000L // 5秒调整一次

        fun getOptimalSettings(connectionCount: Int): Pair<Int, Int> {
            val now = System.currentTimeMillis()

            // 减少调整间隔，更快响应
            if (now - lastAdjustmentTime < 2000L) { // 改为2秒
                return Pair(currentFramerate, currentBitrate)
            }

            // 更激进的优化策略 - 保持更高的帧率
            when {
                connectionCount <= 1 -> {
                    currentFramerate = baseFramerate // 60帧
                    currentBitrate = baseBitrate // 3000kbps
                }
                connectionCount <= 2 -> {
                    currentFramerate = (baseFramerate * 0.95).toInt() // 57帧
                    currentBitrate = (baseBitrate * 0.9).toInt() // 2700kbps
                }
                connectionCount <= 4 -> {
                    currentFramerate = (baseFramerate * 0.85).toInt() // 51帧 (大幅提升)
                    currentBitrate = (baseBitrate * 0.75).toInt() // 2250kbps
                }
                connectionCount <= 6 -> {
                    currentFramerate = (baseFramerate * 0.75).toInt() // 45帧
                    currentBitrate = (baseBitrate * 0.65).toInt() // 1950kbps
                }
                else -> {
                    currentFramerate = (baseFramerate * 0.65).toInt() // 39帧 (提升)
                    currentBitrate = (baseBitrate * 0.55).toInt() // 1650kbps
                }
            }

            lastAdjustmentTime = now
            Logger.i(TAG, "🎯 激进优化调整: 连接数=$connectionCount, 帧率=$currentFramerate, 比特率=$currentBitrate")
            android.util.Log.i("WebRTCOptimization", "🎯 激进优化: 连接=$connectionCount, 帧率=$currentFramerate")

            return Pair(currentFramerate, currentBitrate)
        }

        fun reset() {
            currentFramerate = baseFramerate
            currentBitrate = baseBitrate
            lastAdjustmentTime = 0L
        }
    }

    // 帧率稳定器
    private class FramerateStabilizer {
        private var targetFramerate = 60
        private var currentFramerate = 60
        private var frameDropCount = 0
        private var lastStabilizationTime = 0L
        private val stabilizationInterval = 3000L // 3秒调整一次

        fun shouldAdjustFramerate(connectionCount: Int): Boolean {
            val now = System.currentTimeMillis()
            return now - lastStabilizationTime > stabilizationInterval
        }

        fun getStableFramerate(connectionCount: Int, targetBitrate: Int): Int {
            val now = System.currentTimeMillis()

            // 减少稳定化间隔，更快响应
            if (now - lastStabilizationTime < 1500L) { // 改为1.5秒
                return currentFramerate
            }

            // 更保守的帧率调整策略，保持高帧率
            val optimalFramerate = when {
                connectionCount <= 1 -> targetFramerate // 60帧
                connectionCount <= 2 -> (targetFramerate * 0.95).toInt() // 57帧
                connectionCount <= 4 -> (targetFramerate * 0.85).toInt() // 51帧
                connectionCount <= 6 -> (targetFramerate * 0.75).toInt() // 45帧
                else -> (targetFramerate * 0.65).toInt() // 39帧
            }

            // 更平滑的调整，减少波动
            val framerateDiff = optimalFramerate - currentFramerate
            currentFramerate = when {
                framerateDiff > 3 -> currentFramerate + 3 // 更小的步长
                framerateDiff < -3 -> currentFramerate - 3 // 更小的步长
                else -> optimalFramerate
            }

            // 确保帧率在合理范围内，提高最低帧率
            currentFramerate = currentFramerate.coerceIn(30, targetFramerate) // 最低30帧

            lastStabilizationTime = now
            android.util.Log.i("WebRTCOptimization", "📊 帧率稳定: $currentFramerate 帧 (连接数: $connectionCount)")
            return currentFramerate
        }

        fun reset() {
            currentFramerate = targetFramerate
            frameDropCount = 0
            lastStabilizationTime = 0L
        }
    }

    // 对等连接
    private val peerConnections = ConcurrentHashMap<String, PeerConnection>()
    private val dataChannels = ConcurrentHashMap<String, DataChannel>()

    // 连接状态跟踪 - 避免不必要的重连
    private val connectionStates = ConcurrentHashMap<String, ConnectionState>()
    private val connectionMonitoringTasks = ConcurrentHashMap<String, Runnable>()

    // 连接状态枚举
    private enum class ConnectionState {
        NEW,           // 新建连接
        CONNECTING,    // 连接中
        ESTABLISHED,   // 已建立，停止重连尝试
        UNSTABLE,      // 不稳定，允许重连
        FAILED         // 失败
    }

    // 缓存的ICE候选
    private val pendingIceCandidates = ConcurrentHashMap<String, MutableList<IceCandidate>>()

    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())

    // 🎵 音频来源测试器
    private val audioSourceTester = AudioSourceTester(context)

    // 🎵 采集卡音频管理器
    private val captureCardAudioManager = CaptureCardAudioManager(context)

    // 网速监控相关
    private val networkStatsMap = ConcurrentHashMap<String, NetworkStats>()
    private var networkMonitoringStarted = false
    private var bitrateMonitoringStarted = false
    private var trafficStatsLoggingStarted = false

    data class NetworkStats(
        var lastBytesSent: Long = 0,
        var lastBytesReceived: Long = 0,
        var lastTimestamp: Long = 0,
        var sendSpeedKbps: Double = 0.0,
        var receiveSpeedKbps: Double = 0.0,
        // 累计流量统计
        var totalBytesSent: Long = 0,
        var totalBytesReceived: Long = 0,
        var connectionStartTime: Long = 0,
        var initialBytesSent: Long = 0,
        var initialBytesReceived: Long = 0
    )

    /**
     * 初始化WebRTC
     */
    fun initialize() {
        Logger.i(TAG, "🚀 开始初始化WebRTC客户端")

        try {
            // 🔧 关键修复：强制初始化PeerConnectionFactory
            Logger.i(TAG, "🔧 初始化PeerConnectionFactory...")
            val factory = peerConnectionFactory // 触发lazy初始化
            Logger.i(TAG, "✅ PeerConnectionFactory初始化成功: $factory")

            // 🎵 启动音频来源测试（如果启用）
            if (Constants.ENABLE_AUDIO_SOURCE_TEST) {
                Logger.i(TAG, "🎵 启动音频来源遍历测试...")
                audioSourceTester.startAudioSourceTest()
            } else if (Constants.ENABLE_COMPREHENSIVE_AUDIO_TEST) {
                Logger.i(TAG, "🎵 启动全面音频频道测试...")
                audioSourceTester.startComprehensiveAudioChannelTest()
            } else {
                // 🔧 测试推荐的音频源
                testRecommendedAudioSources()
            }

            // 初始化自适应编码管理器
            adaptiveEncodingManager = AdaptiveEncodingManager()
            framerateStabilizer = FramerateStabilizer()
            Logger.i(TAG, "🚀 自适应编码管理器和帧率稳定器已初始化")
            android.util.Log.i("WebRTCOptimization", "🚀 性能优化模块已启动")

            // 初始化已完成，通知监听器
            listener.onWebRTCClientInitialized()
            Logger.i(TAG, "✅ WebRTC客户端初始化完成")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ WebRTC客户端初始化失败", e)
            throw e
        }
    }

    /**
     * 创建PeerConnectionFactory
     */
    private fun createPeerConnectionFactory(): PeerConnectionFactory {
        Logger.d(TAG, "创建PeerConnectionFactory")

        val options = PeerConnectionFactory.Options()

        // 创建优化的视频编码器工厂，优先使用H.264
        val encoderFactory = object : DefaultVideoEncoderFactory(
            rootEglBase.eglBaseContext,
            true,  // 启用硬件编码器
            true   // 启用硬件编码器回退
        ) {
            override fun getSupportedCodecs(): Array<VideoCodecInfo> {
                // 获取支持的编解码器
                val supportedCodecs = super.getSupportedCodecs()
                Logger.i(TAG, "🎥 [编码器工厂] 支持的编解码器: ${supportedCodecs.joinToString { "${it.name}(${it.params})" }}")

                // 获取配置的编解码器
                val preferredCodec = WebRTCManager.getVideoCodec()
                Logger.i(TAG, "🎥 [编码器工厂] 首选编解码器: $preferredCodec")

                // 详细记录每个编解码器的信息
                supportedCodecs.forEachIndexed { index, codec ->
                    Logger.d(TAG, "🎥 [编码器工厂] 编解码器[$index]: 名称=${codec.name}, 参数=${codec.params}")
                }

                // 重新排序编解码器，使首选编解码器优先级最高
                val h264Codecs = supportedCodecs.filter { it.name.equals("H264", ignoreCase = true) }
                val vp8Codecs = supportedCodecs.filter { it.name.equals("VP8", ignoreCase = true) }
                val vp9Codecs = supportedCodecs.filter { it.name.equals("VP9", ignoreCase = true) }
                val otherCodecs = supportedCodecs.filter {
                    !it.name.equals("H264", ignoreCase = true) &&
                    !it.name.equals("VP8", ignoreCase = true) &&
                    !it.name.equals("VP9", ignoreCase = true)
                }

                // 根据首选编解码器重新排序
                val reorderedCodecs = when (preferredCodec) {
                    "H264" -> h264Codecs + vp8Codecs + vp9Codecs + otherCodecs
                    "VP8" -> vp8Codecs + h264Codecs + vp9Codecs + otherCodecs
                    "VP9" -> vp9Codecs + h264Codecs + vp8Codecs + otherCodecs
                    else -> h264Codecs + vp8Codecs + vp9Codecs + otherCodecs // 默认H264优先
                }

                Logger.d(TAG, "重新排序后的编解码器: ${reorderedCodecs.joinToString { it.name }}")
                return reorderedCodecs.toTypedArray()
            }
        }

        // 创建自定义视频解码器工厂，优先使用H.264
        val decoderFactory = object : DefaultVideoDecoderFactory(rootEglBase.eglBaseContext) {
            override fun getSupportedCodecs(): Array<VideoCodecInfo> {
                // 获取支持的编解码器
                val supportedCodecs = super.getSupportedCodecs()
                Logger.d(TAG, "支持的解码器: ${supportedCodecs.joinToString { it.name }}")

                // 获取配置的编解码器
                val preferredCodec = WebRTCManager.getVideoCodec()
                Logger.d(TAG, "首选解码器: $preferredCodec")

                // 重新排序编解码器，使首选编解码器优先级最高
                val h264Codecs = supportedCodecs.filter { it.name.equals("H264", ignoreCase = true) }
                val vp8Codecs = supportedCodecs.filter { it.name.equals("VP8", ignoreCase = true) }
                val vp9Codecs = supportedCodecs.filter { it.name.equals("VP9", ignoreCase = true) }
                val otherCodecs = supportedCodecs.filter {
                    !it.name.equals("H264", ignoreCase = true) &&
                    !it.name.equals("VP8", ignoreCase = true) &&
                    !it.name.equals("VP9", ignoreCase = true)
                }

                // 根据首选编解码器重新排序
                val reorderedCodecs = when (preferredCodec) {
                    "H264" -> h264Codecs + vp8Codecs + vp9Codecs + otherCodecs
                    "VP8" -> vp8Codecs + h264Codecs + vp9Codecs + otherCodecs
                    "VP9" -> vp9Codecs + h264Codecs + vp8Codecs + otherCodecs
                    else -> h264Codecs + vp8Codecs + vp9Codecs + otherCodecs // 默认H264优先
                }

                Logger.i(TAG, "🎥 [编码器工厂] 重新排序后的编解码器: ${reorderedCodecs.joinToString { it.name }}")
                Logger.i(TAG, "🎥 [编码器工厂] 最终编解码器优先级: ${reorderedCodecs.mapIndexed { index, codec -> "$index:${codec.name}" }.joinToString()}")
                return reorderedCodecs.toTypedArray()
            }


        }

        // 创建自定义音频设备模块
        val audioDeviceModule = createCustomAudioDeviceModule()
        Logger.i(TAG, "创建自定义音频设备模块: $audioDeviceModule")

        val factory = PeerConnectionFactory.builder()
            .setOptions(options)
            .setVideoEncoderFactory(encoderFactory) // 使用优化的编码器工厂
            .setVideoDecoderFactory(decoderFactory)
            .setAudioDeviceModule(audioDeviceModule)  // 设置自定义音频设备模块
            .createPeerConnectionFactory()

        Logger.i(TAG, "✅ PeerConnectionFactory已创建，使用优化编码器")
        return factory
    }

    /**
     * 创建自定义音频设备模块
     */
    private fun createCustomAudioDeviceModule(): AudioDeviceModule {
        Logger.i(TAG, "创建自定义音频设备模块")

        // 获取配置的音频源类型
        val audioSourceType = WebRTCManager.getAudioSource()
        Logger.i(TAG, "配置的音频源类型: $audioSourceType")

        // 创建音频设备模块
        val audioDeviceModule = JavaAudioDeviceModule.builder(context)
            .setUseHardwareAcousticEchoCanceler(true)
            .setUseHardwareNoiseSuppressor(true)
            .setAudioSource(getAudioSourceFromString(audioSourceType))
            .createAudioDeviceModule()

        Logger.i(TAG, "自定义音频设备模块已创建")
        return audioDeviceModule
    }

    /**
     * 根据字符串获取音频源类型
     */
    private fun getAudioSourceFromString(sourceType: String): Int {
        return when (sourceType) {
            "video_input" -> {
                // 来自视频输入（采集卡音频）- 使用REMOTE_SUBMIX作为安全选择
                // UNPROCESSED可能在某些设备上不支持，导致崩溃
                android.media.MediaRecorder.AudioSource.REMOTE_SUBMIX
            }
            "remote_submix" -> android.media.MediaRecorder.AudioSource.REMOTE_SUBMIX
            "voice_communication" -> android.media.MediaRecorder.AudioSource.VOICE_COMMUNICATION
            "microphone", "mic" -> android.media.MediaRecorder.AudioSource.MIC
            "camcorder" -> android.media.MediaRecorder.AudioSource.CAMCORDER
            "voice_recognition" -> android.media.MediaRecorder.AudioSource.VOICE_RECOGNITION
            "voice_performance" -> android.media.MediaRecorder.AudioSource.VOICE_PERFORMANCE
            "unprocessed" -> android.media.MediaRecorder.AudioSource.UNPROCESSED
            "echo_reference" -> 1999  // AUDIO_SOURCE_ECHO_REFERENCE
            else -> android.media.MediaRecorder.AudioSource.DEFAULT
        }.also {
            Logger.i(TAG, "🎵 [音频源] $sourceType -> $it (API ${android.os.Build.VERSION.SDK_INT})")
        }
    }

    /**
     * 创建ICE服务器列表
     */
    private fun createIceServers(): List<IceServer> {
        Logger.i(TAG, "创建ICE服务器列表")
        Logger.i(TAG, "ICE服务器URLs: ${Constants.ICE_SERVER_URLS}")
        Logger.i(TAG, "ICE用户名: ${Constants.ICE_USERNAME}")
        Logger.i(TAG, "ICE凭证: ${Constants.ICE_CREDENTIAL}")

        // 🔧 检查IPv6网络连通性
        checkIPv6Connectivity()

        val servers = Constants.ICE_SERVER_URLS.map { url ->
            val server = if (url.startsWith("turn")) {
                Logger.i(TAG, "🔧 [TURN配置] 创建TURN服务器: $url")
                Logger.i(TAG, "🔧 [TURN配置] 用户名: ${Constants.ICE_USERNAME}")
                Logger.i(TAG, "🔧 [TURN配置] 密码: ${Constants.ICE_CREDENTIAL}")

                val turnServer = IceServer.builder(url)
                    .setUsername(Constants.ICE_USERNAME)
                    .setPassword(Constants.ICE_CREDENTIAL)
                    .createIceServer()

                Logger.i(TAG, "🔧 [TURN配置] TURN服务器创建成功: $turnServer")
                turnServer
            } else {
                Logger.i(TAG, "🔧 [STUN配置] 创建STUN服务器: $url")
                val stunServer = IceServer.builder(url).createIceServer()
                Logger.i(TAG, "🔧 [STUN配置] STUN服务器创建成功: $stunServer")
                stunServer
            }
            server
        }

        Logger.i(TAG, "创建了 ${servers.size} 个ICE服务器")
        return servers
    }

    /**
     * 测试推荐的音频源
     */
    private fun testRecommendedAudioSources() {
        Logger.i(TAG, "🎵 [音频源测试] 开始测试推荐的音频源...")

        Constants.RECOMMENDED_AUDIO_SOURCES.forEachIndexed { index, audioSource ->
            Logger.i(TAG, "🎵 [音频源测试] 推荐音频源 ${index + 1}: $audioSource")

            val audioSourceId = getAudioSourceFromString(audioSource)
            Logger.i(TAG, "🎵 [音频源测试] $audioSource 对应的ID: $audioSourceId")

            when (audioSource) {
                "remote_submix" -> {
                    Logger.w(TAG, "🎵 [音频源测试] ⭐ REMOTE_SUBMIX - 系统内部音频")
                    Logger.w(TAG, "🎵 [音频源测试] 💡 这是最有希望录制到系统播放音频的源")
                    Logger.w(TAG, "🎵 [音频源测试] 📊 测试结果显示最大振幅: 55568 (最高)")
                }
                "voice_performance" -> {
                    Logger.i(TAG, "🎵 [音频源测试] ⭐ VOICE_PERFORMANCE - 性能优化语音")
                    Logger.i(TAG, "🎵 [音频源测试] 📊 测试结果显示最大振幅: 38250")
                }
                "voice_recognition" -> {
                    Logger.i(TAG, "🎵 [音频源测试] ⭐ VOICE_RECOGNITION - 语音识别麦克风")
                    Logger.i(TAG, "🎵 [音频源测试] 📊 测试结果显示最大振幅: 35957")
                }
                "echo_reference" -> {
                    Logger.i(TAG, "🎵 [音频源测试] ⭐ ECHO_REFERENCE - 回声参考")
                    Logger.i(TAG, "🎵 [音频源测试] 📊 测试结果显示最大振幅: 31926")
                    Logger.i(TAG, "🎵 [音频源测试] 💡 回声参考可能包含播放音频")
                }
            }
        }

        Logger.w(TAG, "🎵 [音频源测试] ==========================================")
        Logger.w(TAG, "🎵 [音频源测试] 🔧 当前默认音频源: ${Constants.DEFAULT_AUDIO_SOURCE}")
        Logger.w(TAG, "🎵 [音频源测试] 💡 建议：如果要录制系统声音，请使用 remote_submix")
        Logger.w(TAG, "🎵 [音频源测试] 💡 建议：如果要录制麦克风，请使用 mic 或 voice_communication")
        Logger.w(TAG, "🎵 [音频源测试] ==========================================")
    }

    /**
     * 检查IPv6网络连通性
     */
    private fun checkIPv6Connectivity() {
        try {
            Logger.i(TAG, "🔧 [IPv6检查] 开始检查IPv6网络连通性...")

            // 检查网络接口
            val networkInterfaces = java.net.NetworkInterface.getNetworkInterfaces()
            var hasIPv6Address = false
            var hasGlobalIPv6 = false

            while (networkInterfaces.hasMoreElements()) {
                val networkInterface = networkInterfaces.nextElement()
                if (networkInterface.isUp && !networkInterface.isLoopback) {
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        if (address is java.net.Inet6Address) {
                            hasIPv6Address = true
                            val ipv6Addr = address.hostAddress
                            Logger.i(TAG, "🔧 [IPv6检查] 发现IPv6地址: $ipv6Addr (接口: ${networkInterface.name})")

                            // 检查是否是全局IPv6地址
                            if (!address.isLoopbackAddress && !address.isLinkLocalAddress && !address.isSiteLocalAddress) {
                                hasGlobalIPv6 = true
                                Logger.i(TAG, "🔧 [IPv6检查] ✅ 发现全局IPv6地址: $ipv6Addr")
                            } else {
                                Logger.w(TAG, "🔧 [IPv6检查] ⚠️ 本地IPv6地址: $ipv6Addr (${when {
                                    address.isLoopbackAddress -> "回环"
                                    address.isLinkLocalAddress -> "链路本地"
                                    address.isSiteLocalAddress -> "站点本地"
                                    else -> "其他"
                                }})")
                            }
                        }
                    }
                }
            }

            // 总结IPv6连通性状态
            when {
                !hasIPv6Address -> {
                    Logger.e(TAG, "🔧 [IPv6检查] ❌ 未发现任何IPv6地址")
                    Logger.e(TAG, "🔧 [IPv6检查] 💡 建议：检查网络设置，启用IPv6")
                }
                !hasGlobalIPv6 -> {
                    Logger.w(TAG, "🔧 [IPv6检查] ⚠️ 只有本地IPv6地址，无全局IPv6连通性")
                    Logger.w(TAG, "🔧 [IPv6检查] 💡 可能原因：运营商不支持IPv6或路由器未配置IPv6")
                }
                else -> {
                    Logger.i(TAG, "🔧 [IPv6检查] ✅ IPv6网络连通性正常")
                }
            }

            // 测试IPv6 STUN服务器连通性
            testIPv6StunConnectivity()

        } catch (e: Exception) {
            Logger.e(TAG, "🔧 [IPv6检查] IPv6连通性检查失败", e)
        }
    }

    /**
     * 测试IPv6 STUN服务器连通性
     */
    private fun testIPv6StunConnectivity() {
        try {
            Logger.i(TAG, "🔧 [IPv6测试] 测试IPv6 STUN服务器连通性...")

            // 查找IPv6 STUN/TURN服务器
            val ipv6Servers = Constants.ICE_SERVER_URLS.filter { url ->
                url.contains("[") && url.contains("]") // IPv6地址格式
            }

            if (ipv6Servers.isEmpty()) {
                Logger.w(TAG, "🔧 [IPv6测试] ⚠️ 未配置IPv6 STUN/TURN服务器")
                return
            }

            ipv6Servers.forEach { serverUrl ->
                Logger.i(TAG, "🔧 [IPv6测试] 测试服务器: $serverUrl")

                // 提取IPv6地址和端口
                val regex = "\\[([0-9a-fA-F:]+)\\]:?(\\d+)?".toRegex()
                val matchResult = regex.find(serverUrl)

                if (matchResult != null) {
                    val ipv6Address = matchResult.groupValues[1]
                    val port = matchResult.groupValues[2].toIntOrNull() ?: 3478

                    Logger.i(TAG, "🔧 [IPv6测试] 解析地址: $ipv6Address:$port")

                    // 在后台线程测试连通性
                    Thread {
                        try {
                            val socket = java.net.Socket()
                            socket.connect(java.net.InetSocketAddress(ipv6Address, port), 5000)
                            socket.close()
                            Logger.i(TAG, "🔧 [IPv6测试] ✅ IPv6服务器 $ipv6Address:$port 连通性正常")
                        } catch (e: Exception) {
                            Logger.e(TAG, "🔧 [IPv6测试] ❌ IPv6服务器 $ipv6Address:$port 连接失败: ${e.message}")
                            Logger.e(TAG, "🔧 [IPv6测试] 💡 可能原因：网络不可达、防火墙阻断、服务器不可用")
                        }
                    }.start()
                } else {
                    Logger.w(TAG, "🔧 [IPv6测试] ⚠️ 无法解析IPv6地址: $serverUrl")
                }
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🔧 [IPv6测试] IPv6 STUN连通性测试失败", e)
        }
    }

    /**
     * 动态切换音频源
     */
    fun switchAudioSource(newAudioSource: String) {
        Logger.i(TAG, "🎵 [音频源切换] 请求切换到音频源: $newAudioSource")

        try {
            // 停止当前的音频轨道
            localAudioTrack?.let { track ->
                Logger.i(TAG, "🎵 [音频源切换] 停止当前音频轨道")
                track.setEnabled(false)

                // 从所有PeerConnection中移除音频轨道
                peerConnections.values.forEach { pc ->
                    val senders = pc.senders
                    senders.forEach { sender ->
                        if (sender.track() == track) {
                            Logger.i(TAG, "🎵 [音频源切换] 从PeerConnection移除音频轨道")
                            pc.removeTrack(sender)
                        }
                    }
                }
            }

            Logger.w(TAG, "🎵 [音频源切换] ⚠️ 动态切换音频源需要重启连接")
            Logger.w(TAG, "🎵 [音频源切换] 💡 建议：在配置页面设置音频源后重启应用")
            Logger.w(TAG, "🎵 [音频源切换] 🔧 当前请求的音频源: $newAudioSource")

            // 简化版本：只记录切换请求，实际切换需要重启
            Logger.i(TAG, "🎵 [音频源切换] 音频源切换请求已记录，下次启动时生效")
            // 移除了复杂的动态切换逻辑，因为需要重启PeerConnectionFactory

        } catch (e: Exception) {
            Logger.e(TAG, "🎵 [音频源切换] ❌ 音频源切换失败", e)
        }
    }

    /**
     * 创建PeerConnection
     */
    fun createPeerConnection(peerId: String): PeerConnection? {
        Logger.i(TAG, "为对等端 $peerId 创建PeerConnection")

        // 获取ICE服务器
        val iceServers = createIceServers()
        Logger.i(TAG, "ICE服务器: $iceServers")

        val rtcConfig = RTCConfiguration(iceServers).apply {
            sdpSemantics = SdpSemantics.UNIFIED_PLAN

            // 🔧 TURN会话维护：启用连续收集以支持REFRESH请求
            continualGatheringPolicy = ContinualGatheringPolicy.GATHER_CONTINUALLY
            Logger.i(TAG, "🔄 [TURN维护] 启用连续ICE收集，支持TURN会话REFRESH")

            // 🔧 限制传输类型，减少连接尝试
            iceTransportsType = IceTransportsType.ALL // 改回ALL，RELAY可能导致连接失败
            bundlePolicy = BundlePolicy.MAXBUNDLE
            rtcpMuxPolicy = RtcpMuxPolicy.REQUIRE

            // 🔧 禁用TCP候选，减少连接复杂性
            tcpCandidatePolicy = TcpCandidatePolicy.DISABLED

            // 🔧 使用所有网络，提高连接成功率
            candidateNetworkPolicy = CandidateNetworkPolicy.ALL

            enableCpuOveruseDetection = false // 禁用CPU过载检测

            // 🔧 TURN会话维护：优化超时时间支持10分钟TURN会话
            iceConnectionReceivingTimeout = 600000 // 10分钟，匹配TURN会话超时
            iceBackupCandidatePairPingInterval = 300000 // 5分钟，定期检查连接
            Logger.i(TAG, "🔄 [TURN维护] 设置超时时间: 接收超时=10分钟, 备用检查=5分钟")
        }

        // 🎯 关键优化：通过SDP禁用重传机制，避免帧序错乱
        Logger.i(TAG, "🎬 [WebRTC配置] ✅ 将通过SDP修改禁用重传机制，避免帧序错乱")

        Logger.i(TAG, "🔗 [连接创建] 开始创建PeerConnection，配置信息:")
        Logger.i(TAG, "🔗 [连接创建]   - ICE服务器数量: ${rtcConfig.iceServers.size}")
        Logger.i(TAG, "🔗 [连接创建]   - ICE传输策略: ${rtcConfig.iceTransportsType}")
        Logger.i(TAG, "🔗 [连接创建]   - Bundle策略: ${rtcConfig.bundlePolicy}")
        Logger.i(TAG, "🔗 [连接创建]   - RTCP多路复用策略: ${rtcConfig.rtcpMuxPolicy}")

        val peerConnection = peerConnectionFactory.createPeerConnection(
            rtcConfig,
            object : PeerConnection.Observer {
                override fun onSignalingChange(state: SignalingState) {
                    Logger.d(TAG, "对等端 $peerId 信令状态变化: $state")
                }

                override fun onIceConnectionChange(state: IceConnectionState) {
                    Logger.i(TAG, "🔗 对等端 $peerId ICE连接状态变化: $state")
                    android.util.Log.i("WebRTCConnection", "🔗 ICE状态: $peerId -> $state")
                    listener.onIceConnectionChange(peerId, state)

                    when (state) {
                        IceConnectionState.CONNECTED, IceConnectionState.COMPLETED -> {
                            Logger.i(TAG, "🎉 [连接成功] 对等端 $peerId ICE连接已建立 ✅")
                            Logger.i(TAG, "🎉 [连接成功] 🎯 连接 $peerId 现在可以传输媒体数据了！")

                            // 记录连接成功时间
                            val currentTime = System.currentTimeMillis()
                            val timeStr = java.text.SimpleDateFormat("HH:mm:ss.SSS").format(currentTime)
                            Logger.i(TAG, "🎉 [连接成功] 连接建立时间: $timeStr")
                            android.util.Log.i("WebRTCConnection", "🎉 连接成功: $peerId 在 $timeStr")

                            // 🌐 延迟获取并打印连接的IP端口信息
                            mainHandler.postDelayed({
                                peerConnections[peerId]?.let { pc ->
                                    logConnectionDetails(peerId, pc)

                                    // 🔄 启动TURN会话监控
                                    startTurnSessionMonitoring(peerId, pc)
                                }
                            }, 1000) // 延迟1秒确保连接已保存到映射中

                            // 标记连接为已建立，停止不必要的重连尝试
                            markConnectionAsEstablished(peerId)

                            // 🔧 暂时禁用激进的ICE优化，保持连接稳定性
                            // disableIceActivity(peerId) // 暂时注释，避免连接不稳定

                            startNetworkSpeedMonitoring()
                            startBitrateMonitoring()
                            startTrafficStatsLogging()

                            // 启动性能监控（只在第一个连接时启动） 暂时不使用
                            // if (peerConnections.size == 1) {
                            //     startPerformanceMonitoring()
                            // }
                        }
                        IceConnectionState.DISCONNECTED -> {
                            Logger.w(TAG, "⚠️ 对等端 $peerId ICE连接断开")
                            android.util.Log.w("WebRTCConnection", "⚠️ 连接断开: $peerId")

                            // 标记连接为不稳定，允许重连
                            markConnectionAsUnstable(peerId)

                            // 给连接一些时间恢复，而不是立即清理
                            mainHandler.postDelayed({
                                val currentState = peerConnections[peerId]?.iceConnectionState()
                                if (currentState == IceConnectionState.DISCONNECTED || currentState == IceConnectionState.FAILED) {
                                    Logger.i(TAG, "🧹 连接未恢复，清理断开的连接 $peerId")
                                    closePeerConnection(peerId)
                                    listener.onPeerDisconnected(peerId)
                                } else {
                                    Logger.i(TAG, "✅ 连接已恢复，保持连接 $peerId")
                                }
                            }, 5000) // 给5秒时间恢复
                        }
                        IceConnectionState.FAILED -> {
                            Logger.e(TAG, "❌ 对等端 $peerId ICE连接失败")
                            android.util.Log.e("WebRTCConnection", "❌ 连接失败: $peerId")

                            // 标记连接为失败
                            markConnectionAsFailed(peerId)

                            // 连接失败时立即清理连接，避免资源泄漏
                            Logger.i(TAG, "🧹 清理失败的连接 $peerId")
                            mainHandler.post {
                                closePeerConnection(peerId)
                                // 通知管理器连接失败
                                listener.onPeerDisconnected(peerId)
                            }
                        }
                        else -> {
                            Logger.d(TAG, "🔄 对等端 $peerId ICE状态: $state")
                        }
                    }
                }

                override fun onIceConnectionReceivingChange(receiving: Boolean) {
                    Logger.d(TAG, "对等端 $peerId ICE连接接收状态变化: $receiving")
                }

                override fun onIceGatheringChange(state: IceGatheringState) {
                    Logger.d(TAG, "对等端 $peerId ICE收集状态变化: $state")
                }

                override fun onIceCandidate(candidate: IceCandidate) {
                    Logger.i(TAG, "对等端 $peerId 生成ICE候选")
                    Logger.i(TAG, "ICE候选详情: sdpMid=${candidate.sdpMid}, sdpMLineIndex=${candidate.sdpMLineIndex}, sdp=${candidate.sdp}")

                    try {
                        listener.onIceCandidate(peerId, candidate)
                        Logger.i(TAG, "成功发送ICE候选到对等端 $peerId")
                    } catch (e: Exception) {
                        Logger.e(TAG, "发送ICE候选到对等端 $peerId 失败", e)
                    }
                }

                override fun onIceCandidatesRemoved(candidates: Array<out IceCandidate>) {
                    Logger.d(TAG, "对等端 $peerId ICE候选移除: ${candidates.size}个")
                }

                override fun onAddStream(stream: MediaStream) {
                    Logger.d(TAG, "对等端 $peerId 添加流: ${stream.id}")
                }

                override fun onRemoveStream(stream: MediaStream) {
                    Logger.d(TAG, "对等端 $peerId 移除流: ${stream.id}")
                }

                override fun onDataChannel(channel: DataChannel) {
                    Logger.d(TAG, "对等端 $peerId 创建数据通道: ${channel.label()}")
                    setupDataChannel(peerId, channel)
                }

                override fun onRenegotiationNeeded() {
                    Logger.d(TAG, "对等端 $peerId 需要重新协商")
                }

                override fun onAddTrack(receiver: RtpReceiver, streams: Array<out MediaStream>) {
                    Logger.d(TAG, "对等端 $peerId 添加轨道: ${receiver.id()}")
                }
            }
        )

        if (peerConnection == null) {
            Logger.e(TAG, "为对等端 $peerId 创建PeerConnection失败")
            return null
        }

        // 添加本地媒体轨道
        addLocalMediaTracks(peerConnection)

        // 创建数据通道
        createDataChannel(peerConnection, peerId)

        // 保存PeerConnection
        peerConnections[peerId] = peerConnection
        Logger.i(TAG, "🔗 [连接创建] ✅ PeerConnection创建完成:")
        Logger.i(TAG, "🔗 [连接创建]   - 对等端ID: $peerId")
        Logger.i(TAG, "🔗 [连接创建]   - 当前总连接数: ${peerConnections.size}")
        Logger.i(TAG, "🔗 [连接创建]   - 连接状态: ${peerConnection.connectionState()}")
        Logger.i(TAG, "🔗 [连接创建]   - ICE连接状态: ${peerConnection.iceConnectionState()}")
        Logger.i(TAG, "🔗 [连接创建]   - ICE收集状态: ${peerConnection.iceGatheringState()}")
        Logger.i(TAG, "🔗 [连接创建]   - 信令状态: ${peerConnection.signalingState()}")

        // 启动连接状态监控
        startConnectionMonitoring(peerId)

        // 动态调整所有连接的编码参数
        //adjustAllConnectionsEncoding()

        return peerConnection
    }

    /**
     * 标记连接为已建立状态
     */
    private fun markConnectionAsEstablished(peerId: String) {
        connectionStates[peerId] = ConnectionState.ESTABLISHED
        Logger.i(TAG, "🔗 [连接状态] 对等端 $peerId 标记为已建立，停止重连尝试")

        // 停止连接监控任务
        stopConnectionMonitoring(peerId)
    }

    /**
     * 标记连接为不稳定状态
     */
    private fun markConnectionAsUnstable(peerId: String) {
        connectionStates[peerId] = ConnectionState.UNSTABLE
        Logger.w(TAG, "🔗 [连接状态] 对等端 $peerId 标记为不稳定，允许重连")
    }

    /**
     * 标记连接为失败状态
     */
    private fun markConnectionAsFailed(peerId: String) {
        connectionStates[peerId] = ConnectionState.FAILED
        Logger.e(TAG, "🔗 [连接状态] 对等端 $peerId 标记为失败")

        // 停止连接监控任务
        stopConnectionMonitoring(peerId)
    }

    /**
     * 检查连接是否已建立
     */
    private fun isConnectionEstablished(peerId: String): Boolean {
        return connectionStates[peerId] == ConnectionState.ESTABLISHED
    }

    /**
     * 停止连接监控任务
     */
    private fun stopConnectionMonitoring(peerId: String) {
        connectionMonitoringTasks[peerId]?.let { task ->
            mainHandler.removeCallbacks(task)
            connectionMonitoringTasks.remove(peerId)
            Logger.i(TAG, "🔍 [连接监控] 停止对等端 $peerId 的连接监控")
        }
    }

    /**
     * 记录连接详细信息，包括IP端口
     */
    private fun logConnectionDetails(peerId: String, peerConnection: PeerConnection) {
        try {
            Logger.i(TAG, "🌐 [连接详情] 开始获取连接 $peerId 的网络信息...")

            // 获取连接统计信息
            peerConnection.getStats { reports ->
                try {
                    var localCandidate: String? = null
                    var remoteCandidate: String? = null
                    var selectedPair: String? = null

                    var actualActivePair: String? = null
                    var actualLocalCandidate: String? = null
                    var actualRemoteCandidate: String? = null
                    var maxDataTransfer = 0L

                    // 🔧 修复：首先找到实际传输数据最多的候选对
                    reports.statsMap.forEach { (id, stats) ->
                        when (stats.type) {
                            "candidate-pair" -> {
                                val state = stats.members["state"] as? String
                                val nominated = stats.members["nominated"] as? Boolean ?: false
                                val priority = stats.members["priority"] as? Number
                                val bytesSent = (stats.members["bytesSent"] as? Number)?.toLong() ?: 0
                                val bytesReceived = (stats.members["bytesReceived"] as? Number)?.toLong() ?: 0
                                val packetsReceived = stats.members["packetsReceived"] as? Number ?: 0
                                val packetsSent = stats.members["packetsSent"] as? Number ?: 0
                                val totalData = bytesSent + bytesReceived

                                // 获取候选类型信息用于分析
                                val localCandidateId = stats.members["localCandidateId"] as? String
                                val remoteCandidateId = stats.members["remoteCandidateId"] as? String
                                val localCandidate = reports.statsMap[localCandidateId]
                                val remoteCandidate = reports.statsMap[remoteCandidateId]
                                val localType = localCandidate?.members?.get("candidateType") as? String ?: "unknown"
                                val remoteType = remoteCandidate?.members?.get("candidateType") as? String ?: "unknown"
                                val connectionPath = "$localType ↔ $remoteType"

                                Logger.w(TAG, "🌐 [候选对分析] $id: $connectionPath")
                                Logger.w(TAG, "🌐 [候选对分析] $id: 状态=$state, 提名=$nominated, 优先级=$priority")
                                Logger.w(TAG, "🌐 [候选对分析] $id: 数据传输: 发送=${bytesSent}字节(${packetsSent}包), 接收=${bytesReceived}字节(${packetsReceived}包), 总计=${totalData}字节")

                                // 🚨 特别关注：为什么TURN中继被选择而不是P2P？
                                if (localType == "relay" || remoteType == "relay") {
                                    if (totalData > 1000) {
                                        Logger.e(TAG, "🚨 [异常分析] TURN中继正在传输数据！")
                                        Logger.e(TAG, "🚨 [异常分析] 候选对: $id, 路径: $connectionPath")
                                        Logger.e(TAG, "🚨 [异常分析] 提名状态: $nominated, 优先级: $priority")
                                        Logger.e(TAG, "🚨 [异常分析] 数据量: ${totalData}字节")
                                    }
                                } else if (localType == "host" && remoteType == "host") {
                                    if (totalData > 1000) {
                                        Logger.i(TAG, "✅ [正常分析] P2P直连正在传输数据")
                                        Logger.i(TAG, "✅ [正常分析] 候选对: $id, 路径: $connectionPath")
                                        Logger.i(TAG, "✅ [正常分析] 提名状态: $nominated, 优先级: $priority")
                                        Logger.i(TAG, "✅ [正常分析] 数据量: ${totalData}字节")
                                    } else if (state == "succeeded") {
                                        Logger.w(TAG, "⚠️ [异常分析] P2P连接成功但无数据传输！")
                                        Logger.w(TAG, "⚠️ [异常分析] 候选对: $id, 路径: $connectionPath")
                                        Logger.w(TAG, "⚠️ [异常分析] 可能原因: 网络质量差、被防火墙阻断、NAT超时")
                                    }
                                }

                                // 🔧 关键修复：选择实际传输数据最多的候选对，而不是第一个成功的
                                if (state == "succeeded" && totalData > maxDataTransfer) {
                                    maxDataTransfer = totalData
                                    actualActivePair = id
                                    selectedPair = id

                                    // 🚨 分析为什么这个候选对被选择
                                    val reasonAnalysis = when {
                                        localType == "relay" || remoteType == "relay" -> {
                                            "🚨 TURN中继被选择 - 可能P2P质量差或被阻断"
                                        }
                                        localType == "host" && remoteType == "host" -> {
                                            "✅ P2P直连被选择 - 最优路径"
                                        }
                                        localType == "srflx" || remoteType == "srflx" -> {
                                            "🌐 STUN反射被选择 - NAT穿透成功"
                                        }
                                        else -> {
                                            "❓ 未知连接类型被选择"
                                        }
                                    }

                                    Logger.e(TAG, "🌐 [路径选择] $reasonAnalysis")
                                    Logger.e(TAG, "🌐 [路径选择] 候选对: $id, 路径: $connectionPath")
                                    Logger.e(TAG, "🌐 [路径选择] 数据传输: ${totalData}字节, 提名: $nominated, 优先级: $priority")

                                    // 如果TURN被选择，分析原因
                                    if (localType == "relay" || remoteType == "relay") {
                                        Logger.e(TAG, "🚨 [TURN分析] 为什么TURN中继被选择而不是P2P？")
                                        Logger.e(TAG, "🚨 [TURN分析] 可能原因1: P2P连接质量差（高延迟、丢包）")
                                        Logger.e(TAG, "🚨 [TURN分析] 可能原因2: 防火墙阻断P2P流量")
                                        Logger.e(TAG, "🚨 [TURN分析] 可能原因3: NAT类型不兼容或超时")
                                        Logger.e(TAG, "🚨 [TURN分析] 可能原因4: WebRTC自适应选择了更稳定的路径")
                                    }

                                    // 查找对应的候选信息
                                    reports.statsMap[localCandidateId]?.let { localStats ->
                                        val ip = localStats.members["ip"] as? String ?: "unknown"
                                        val port = localStats.members["port"] as? Number ?: 0
                                        val protocol = localStats.members["protocol"] as? String ?: "unknown"
                                        val candidateType = localStats.members["candidateType"] as? String ?: "unknown"
                                        val relayProtocol = localStats.members["relayProtocol"] as? String
                                        val foundation = localStats.members["foundation"] as? String
                                        val networkType = localStats.members["networkType"] as? String

                                        actualLocalCandidate = "$ip:$port ($protocol/$candidateType)"
                                        if (relayProtocol != null) {
                                            actualLocalCandidate += " [中继:$relayProtocol]"
                                        }

                                        Logger.i(TAG, "🌐 [实际本地] IP=$ip, 端口=$port, 协议=$protocol, 类型=$candidateType")
                                        Logger.i(TAG, "🌐 [实际本地] 中继协议=$relayProtocol, 基础=$foundation, 网络类型=$networkType")
                                    }

                                    reports.statsMap[remoteCandidateId]?.let { remoteStats ->
                                        val ip = remoteStats.members["ip"] as? String ?: "unknown"
                                        val port = remoteStats.members["port"] as? Number ?: 0
                                        val protocol = remoteStats.members["protocol"] as? String ?: "unknown"
                                        val candidateType = remoteStats.members["candidateType"] as? String ?: "unknown"
                                        val relayProtocol = remoteStats.members["relayProtocol"] as? String
                                        val foundation = remoteStats.members["foundation"] as? String
                                        val networkType = remoteStats.members["networkType"] as? String

                                        actualRemoteCandidate = "$ip:$port ($protocol/$candidateType)"
                                        if (relayProtocol != null) {
                                            actualRemoteCandidate += " [中继:$relayProtocol]"
                                        }

                                        Logger.i(TAG, "🌐 [实际远程] IP=$ip, 端口=$port, 协议=$protocol, 类型=$candidateType")
                                        Logger.i(TAG, "🌐 [实际远程] 中继协议=$relayProtocol, 基础=$foundation, 网络类型=$networkType")
                                    }
                                }
                            }
                        }
                    }

                    // 🔍 分析网络质量统计
                    reports.statsMap.forEach { (id, stats) ->
                        if (stats.type == "candidate-pair" && stats.members["state"] == "succeeded") {
                            val rtt = stats.members["currentRoundTripTime"] as? Number
                            val packetsLost = stats.members["packetsLost"] as? Number
                            val jitter = stats.members["jitter"] as? Number
                            val availableOutgoingBitrate = stats.members["availableOutgoingBitrate"] as? Number

                            if (rtt != null || packetsLost != null) {
                                Logger.w(TAG, "🌐 [网络质量] 候选对 $id:")
                                Logger.w(TAG, "🌐 [网络质量]   - RTT: ${rtt}ms")
                                Logger.w(TAG, "🌐 [网络质量]   - 丢包: ${packetsLost}包")
                                Logger.w(TAG, "🌐 [网络质量]   - 抖动: ${jitter}ms")
                                Logger.w(TAG, "🌐 [网络质量]   - 可用带宽: ${availableOutgoingBitrate}bps")

                                // 分析网络质量
                                val rttMs = (rtt as? Number)?.toDouble() ?: 0.0
                                val lostPackets = (packetsLost as? Number)?.toLong() ?: 0

                                when {
                                    rttMs > 200 -> Logger.w(TAG, "⚠️ [网络质量] 高延迟: ${rttMs}ms > 200ms")
                                    lostPackets > 10 -> Logger.w(TAG, "⚠️ [网络质量] 高丢包: ${lostPackets}包")
                                    rttMs < 50 && lostPackets < 5 -> Logger.i(TAG, "✅ [网络质量] 良好: RTT=${rttMs}ms, 丢包=${lostPackets}包")
                                }
                            }
                        }
                    }

                    // 🔧 使用实际活跃的候选信息进行分析
                    val finalLocalCandidate = actualLocalCandidate ?: localCandidate
                    val finalRemoteCandidate = actualRemoteCandidate ?: remoteCandidate

                    // 分析连接类型
                    val connectionType = when {
                        finalLocalCandidate?.contains("relay") == true || finalRemoteCandidate?.contains("relay") == true -> "🔄 TURN中继连接"
                        finalLocalCandidate?.contains("srflx") == true || finalRemoteCandidate?.contains("srflx") == true -> "🌐 STUN反射连接"
                        finalLocalCandidate?.contains("host") == true && finalRemoteCandidate?.contains("host") == true -> "⚡ 直接P2P连接"
                        else -> "❓ 未知连接类型"
                    }

                    // 分析网络协议
                    val networkProtocol = when {
                        finalLocalCandidate?.contains("240e:") == true || finalRemoteCandidate?.contains("240e:") == true -> "IPv6"
                        finalLocalCandidate?.contains("192.168.") == true || finalRemoteCandidate?.contains("10.") == true -> "IPv4局域网"
                        else -> "IPv4公网"
                    }

                    // 🚨 重要提醒：显示实际使用的连接路径
                    Logger.w(TAG, "🌐 [实际路径] ===== 连接 $peerId 实际网络路径信息 =====")
                    Logger.w(TAG, "🌐 [实际路径] 📱 实际本地地址: ${finalLocalCandidate ?: "获取中..."}")
                    Logger.w(TAG, "🌐 [实际路径] 🌍 实际远程地址: ${finalRemoteCandidate ?: "获取中..."}")
                    Logger.w(TAG, "🌐 [实际路径] 🔗 实际连接类型: $connectionType")
                    Logger.w(TAG, "🌐 [实际路径] 🌐 网络协议: $networkProtocol")
                    Logger.w(TAG, "🌐 [实际路径] 📊 活跃候选对ID: $actualActivePair")
                    Logger.w(TAG, "🌐 [实际路径] 📊 数据传输量: ${maxDataTransfer}字节")
                    Logger.w(TAG, "🌐 [实际路径] 🔗 连接方向: Android发送端 ← → 接收端")
                    Logger.w(TAG, "🌐 [实际路径] =======================================")

                    // 同时输出到Android Log，包含连接类型
                    android.util.Log.w("WebRTCConnection", "🌐 实际路径: $peerId | $connectionType | 本地: $finalLocalCandidate | 远程: $finalRemoteCandidate")

                    // 🚨 特别提醒：如果使用了TURN中继
                    if (connectionType.contains("TURN中继")) {
                        Logger.e(TAG, "🚨 [流量警告] ⚠️ 实际使用TURN中继连接，正在消耗服务器流量！")
                        Logger.e(TAG, "🚨 [流量警告] 💰 TURN服务器将产生流量费用！")
                        Logger.e(TAG, "🚨 [流量警告] 🔧 建议检查网络环境，尝试建立直连以节省流量")
                        android.util.Log.e("WebRTCConnection", "🚨 TURN中继警告: $peerId 正在使用中继服务器，产生流量费用")
                    } else if (connectionType.contains("直接P2P")) {
                        Logger.i(TAG, "✅ [连接优化] 已建立直接P2P连接，无需中继服务器！")
                        android.util.Log.i("WebRTCConnection", "✅ P2P直连: $peerId 使用最优连接路径")
                    }

                } catch (e: Exception) {
                    Logger.e(TAG, "🌐 [连接详情] 解析统计信息失败: ${e.message}")
                }
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🌐 [连接详情] 获取连接详情失败", e)
        }
    }

    /**
     * 启动TURN会话监控，检查REFRESH请求和会话状态
     */
    private fun startTurnSessionMonitoring(peerId: String, peerConnection: PeerConnection) {
        try {
            Logger.i(TAG, "🔄 [TURN监控] 启动TURN会话监控: $peerId")

            // 定期检查TURN会话状态（每2分钟检查一次）
            val turnMonitoringRunnable = object : Runnable {
                override fun run() {
                    try {
                        peerConnections[peerId]?.let { pc ->
                            checkTurnSessionStatus(peerId, pc)

                            // 继续监控（如果连接仍然存在）
                            mainHandler.postDelayed(this, 120000) // 2分钟后再次检查
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "🔄 [TURN监控] TURN会话监控失败: $peerId", e)
                    }
                }
            }

            // 首次检查延迟30秒（给连接时间稳定）
            mainHandler.postDelayed(turnMonitoringRunnable, 30000)

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [TURN监控] 启动TURN会话监控失败", e)
        }
    }

    /**
     * 检查TURN会话状态和REFRESH请求
     */
    private fun checkTurnSessionStatus(peerId: String, peerConnection: PeerConnection) {
        try {
            Logger.d(TAG, "🔄 [TURN监控] 检查TURN会话状态: $peerId")

            peerConnection.getStats { reports ->
                try {
                    var hasTurnConnection = false
                    var turnSessionInfo = mutableListOf<String>()

                    reports.statsMap.forEach { (id, stats) ->
                        when (stats.type) {
                            "candidate-pair" -> {
                                val state = stats.members["state"] as? String
                                val nominated = stats.members["nominated"] as? Boolean ?: false
                                val bytesSent = (stats.members["bytesSent"] as? Number)?.toLong() ?: 0
                                val bytesReceived = (stats.members["bytesReceived"] as? Number)?.toLong() ?: 0

                                if (state == "succeeded" && nominated && (bytesSent > 1000 || bytesReceived > 100)) {
                                    val localCandidateId = stats.members["localCandidateId"] as? String
                                    val remoteCandidateId = stats.members["remoteCandidateId"] as? String

                                    val localCandidate = reports.statsMap[localCandidateId]
                                    val remoteCandidate = reports.statsMap[remoteCandidateId]

                                    val localType = localCandidate?.members?.get("candidateType") as? String
                                    val remoteType = remoteCandidate?.members?.get("candidateType") as? String

                                    if (localType == "relay" || remoteType == "relay") {
                                        hasTurnConnection = true
                                        val localIP = localCandidate?.members?.get("ip") as? String ?: "unknown"
                                        val localPort = localCandidate?.members?.get("port") as? Number ?: 0
                                        val remoteIP = remoteCandidate?.members?.get("ip") as? String ?: "unknown"
                                        val remotePort = remoteCandidate?.members?.get("port") as? Number ?: 0

                                        turnSessionInfo.add("候选对: $id")
                                        turnSessionInfo.add("本地: $localIP:$localPort ($localType)")
                                        turnSessionInfo.add("远程: $remoteIP:$remotePort ($remoteType)")
                                        turnSessionInfo.add("数据: 发送=${bytesSent}字节, 接收=${bytesReceived}字节")
                                    }
                                }
                            }
                            "local-candidate" -> {
                                val candidateType = stats.members["candidateType"] as? String
                                if (candidateType == "relay") {
                                    val ip = stats.members["ip"] as? String ?: "unknown"
                                    val port = stats.members["port"] as? Number ?: 0
                                    val protocol = stats.members["protocol"] as? String ?: "unknown"
                                    val relayProtocol = stats.members["relayProtocol"] as? String ?: "unknown"

                                    turnSessionInfo.add("TURN候选: $ip:$port ($protocol/$relayProtocol)")
                                }
                            }
                        }
                    }

                    if (hasTurnConnection) {
                        Logger.w(TAG, "🔄 [TURN监控] ⚠️ 检测到活跃的TURN会话: $peerId")
                        turnSessionInfo.forEach { info ->
                            Logger.w(TAG, "🔄 [TURN监控]   - $info")
                        }
                        Logger.w(TAG, "🔄 [TURN监控] 💡 提醒：TURN会话需要每10分钟发送REFRESH请求维持连接")
                        Logger.w(TAG, "🔄 [TURN监控] 💰 注意：TURN中继会消耗服务器流量和资源")
                    } else {
                        Logger.i(TAG, "🔄 [TURN监控] ✅ 当前使用直连，无TURN会话")
                    }

                } catch (e: Exception) {
                    Logger.e(TAG, "🔄 [TURN监控] 分析TURN会话状态失败", e)
                }
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🔄 [TURN监控] 检查TURN会话状态失败", e)
        }
    }

    /**
     * 禁用ICE活动，减少网络噪音
     */
    private fun disableIceActivity(peerId: String) {
        try {
            val peerConnection = peerConnections[peerId]
            if (peerConnection != null) {
                Logger.i(TAG, "🔧 [ICE优化] 连接已建立，尝试减少ICE活动: $peerId")

                // 记录当前ICE状态
                val iceState = peerConnection.iceConnectionState()
                val connectionState = peerConnection.connectionState()

                Logger.i(TAG, "🔧 [ICE优化] 当前状态 - ICE: $iceState, 连接: $connectionState")

                // 🔧 激进优化：尝试修改连接配置以减少网络活动
                try {
                    // 创建一个更保守的RTCConfiguration
                    val conservativeConfig = RTCConfiguration(emptyList()).apply {
                        continualGatheringPolicy = ContinualGatheringPolicy.GATHER_ONCE
                        iceTransportsType = IceTransportsType.NONE // 完全禁用ICE传输
                        candidateNetworkPolicy = CandidateNetworkPolicy.LOW_COST
                        tcpCandidatePolicy = TcpCandidatePolicy.DISABLED
                        iceConnectionReceivingTimeout = 300000 // 5分钟
                        iceBackupCandidatePairPingInterval = 300000 // 5分钟
                    }

                    // 尝试更新配置（注意：这可能不被所有WebRTC版本支持）
                    // peerConnection.setConfiguration(conservativeConfig)

                    Logger.i(TAG, "🔧 [ICE优化] 已应用保守的网络配置")
                } catch (configException: Exception) {
                    Logger.w(TAG, "🔧 [ICE优化] 无法动态修改配置: ${configException.message}")
                }

                Logger.i(TAG, "🔧 [ICE优化] 连接已稳定，应用层将避免触发额外的ICE活动")

            } else {
                Logger.w(TAG, "🔧 [ICE优化] PeerConnection不存在: $peerId")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🔧 [ICE优化] 禁用ICE活动失败", e)
        }
    }

    /**
     * 添加本地媒体轨道到PeerConnection
     */
    private fun addLocalMediaTracks(peerConnection: PeerConnection) {
        Logger.d(TAG, "添加本地媒体轨道到PeerConnection")

        try {
            // 创建媒体流
            val mediaStream = peerConnectionFactory.createLocalMediaStream("ARDAMS")
            Logger.d(TAG, "创建本地媒体流: ${mediaStream.id}")

            // 添加共享视频轨道 - 优化版本，避免重复编码
            val sharedTrack = getSharedVideoTrack()
            val videoTrack = sharedTrack ?: localVideoTrack

            if (videoTrack != null) {
                try {
                    // 记录使用的轨道类型
                    if (sharedTrack != null) {
                        Logger.i(TAG, "✅ 使用共享视频轨道 - 避免重复编码")
                    } else {
                        Logger.w(TAG, "⚠️ 使用本地视频轨道 - 可能存在重复编码")
                    }

                    // 添加到媒体流 - 明确指定为VideoTrack类型
                    mediaStream.addTrack(videoTrack as VideoTrack)
                    Logger.d(TAG, "已添加视频轨道到媒体流: ${videoTrack.id()}")

                    // 直接添加轨道到PeerConnection
                    val sender = peerConnection.addTrack(videoTrack, listOf(mediaStream.id))
                    Logger.d(TAG, "已添加视频轨道到PeerConnection: ${videoTrack.id()}, 发送器: ${sender?.id()}")
                    Logger.i(TAG, "当前连接数: ${peerConnections.size + 1}, 共享轨道: ${sharedTrack != null}")

                    // 设置自适应视频编码参数
                    if (sender != null) {
                        try {
                            // 获取当前连接数
                            val connectionCount = peerConnections.size + 1

                            // // 使用自适应编码管理器获取最优设置 (暂时不使用)
                            // val (optimalFramerate, optimalBitrate) = adaptiveEncodingManager?.getOptimalSettings(connectionCount)
                            //     ?: Pair(WebRTCManager.getVideoFramerate(), WebRTCManager.getVideoBitrate())
                            val (optimalFramerate, optimalBitrate) =  Pair(WebRTCManager.getVideoFramerate(), WebRTCManager.getVideoBitrate())

                            val codec = WebRTCManager.getVideoCodec()
                            val bitrateInBps = optimalBitrate * 1000 // 转换为bps

                            Logger.i(TAG, "自适应编码参数: 连接数=$connectionCount, 编码器=$codec, 比特率=$optimalBitrate kbps, 帧率=$optimalFramerate fps")

                            // 获取当前参数
                            val parameters = sender.parameters
                            val encodings = parameters.encodings

                            // 检查encodings列表是否为空
                            if (encodings.isEmpty()) {
                                Logger.w(TAG, "编码参数列表为空，无法设置视频编码参数")
                                return
                            }

                            // 设置保守的比特率范围，避免频繁调整
                            encodings[0].maxBitrateBps = (bitrateInBps * 1.2).toInt() // 保守的最大比特率
                            encodings[0].minBitrateBps = (bitrateInBps * 0.9).toInt() // 保守的最小比特率
                            encodings[0].maxFramerate = optimalFramerate
                            encodings[0].active = true

                            // 应用更改后的参数
                            sender.parameters = parameters

                            Logger.i(TAG, "✅ 自适应编码参数已应用: 比特率=${optimalBitrate}kbps, 帧率=${optimalFramerate}fps")
                        } catch (e: Exception) {
                            Logger.e(TAG, "设置视频编码参数失败", e)
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "添加视频轨道失败", e)
                }
            } else {
                Logger.e(TAG, "没有可用的视频轨道，这可能导致视频流不可见")
            }

            // 添加共享音频轨道
            val audioTrack = getSharedAudioTrack() ?: localAudioTrack
            if (audioTrack != null) {
                try {
                    // 添加到媒体流 - 明确指定为AudioTrack类型
                    mediaStream.addTrack(audioTrack as AudioTrack)
                    Logger.d(TAG, "已添加共享音频轨道到媒体流: ${audioTrack.id()}")

                    // 直接添加轨道到PeerConnection
                    val sender = peerConnection.addTrack(audioTrack, listOf(mediaStream.id))
                    Logger.d(TAG, "已添加共享音频轨道到PeerConnection: ${audioTrack.id()}, 发送器: ${sender?.id()}")
                } catch (e: Exception) {
                    Logger.e(TAG, "添加共享音频轨道失败", e)
                }
            } else {
                Logger.d(TAG, "没有可用的共享音频轨道")
            }

            // 检查视频源是否正常
            val currentVideoSource = sharedVideoSource ?: localVideoSource
            if (currentVideoSource == null) {
                Logger.e(TAG, "视频源为空，请确保在创建PeerConnection前设置视频源")
            } else {
                Logger.d(TAG, "视频源正常 (${if (sharedVideoSource != null) "共享模式" else "本地模式"})")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "添加本地媒体轨道失败", e)
        }
    }

    /**
     * 动态调整所有连接的编码参数
     */
    private fun adjustAllConnectionsEncoding() {
        try {
            val connectionCount = peerConnections.size
            if (connectionCount == 0) return

            // 获取最优编码设置
            val (optimalFramerate, optimalBitrate) = adaptiveEncodingManager?.getOptimalSettings(connectionCount)
                ?: return

            Logger.i(TAG, "🔄🔄🔄 动态调整编码参数: 连接数=$connectionCount, 目标帧率=$optimalFramerate, 目标比特率=$optimalBitrate kbps 🔄🔄🔄")
            android.util.Log.i("WebRTCOptimization", "🔄 动态调整: 连接数=$connectionCount, 帧率=$optimalFramerate, 比特率=$optimalBitrate")

            // 调整所有连接的编码参数
            peerConnections.values.forEach { peerConnection ->
                try {
                    val senders = peerConnection.senders
                    senders.forEach { sender ->
                        if (sender.track()?.kind() == "video") {
                            adjustSenderEncoding(sender, optimalFramerate, optimalBitrate)
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "调整连接编码参数失败", e)
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "动态调整编码参数失败", e)
        }
    }

    /**
     * 调整单个发送器的编码参数 - 使用帧率稳定器
     */
    private fun adjustSenderEncoding(sender: RtpSender, framerate: Int, bitrateKbps: Int) {
        try {
            val parameters = sender.parameters
            val encodings = parameters.encodings

            if (encodings.isNotEmpty()) {
                val connectionCount = peerConnections.size

                // 直接使用配置页面的设置，不进行任何动态调整
                val configuredBitrate = getCurrentConfiguredBitrate()
                val configuredFramerate = getCurrentConfiguredFramerate()

                // 设置完全固定的比特率，严格按配置页面设置
                encodings[0].maxBitrateBps = configuredBitrate
                encodings[0].minBitrateBps = configuredBitrate  // 与最大值相同，确保完全固定
                encodings[0].maxFramerate = configuredFramerate
                encodings[0].active = true

                Logger.i(TAG, "🔧 [固定比特率] 设置配置页面固定比特率: ${configuredBitrate/1000} kbps, 帧率: ${configuredFramerate} fps (严格按配置)")

                // 尝试设置目标比特率为相同值
                try {
                    val targetBitrateField = encodings[0].javaClass.getDeclaredField("targetBitrateBps")
                    targetBitrateField.isAccessible = true
                    targetBitrateField.set(encodings[0], configuredBitrate)
                    Logger.i(TAG, "🔧 [固定比特率] 设置目标比特率: ${configuredBitrate/1000} kbps")
                } catch (e: Exception) {
                    Logger.d(TAG, "🔧 [固定比特率] 当前版本不支持目标比特率设置")
                }

                // 禁用自适应比特率控制
                try {
                    val degradationPreferenceField = encodings[0].javaClass.getDeclaredField("degradationPreference")
                    degradationPreferenceField.isAccessible = true
                    // 设置为MAINTAIN_RESOLUTION，优先保持分辨率而不是调整比特率
                    degradationPreferenceField.set(encodings[0], "MAINTAIN_RESOLUTION")
                    Logger.i(TAG, "🔧 [固定比特率] 设置降级偏好为保持分辨率")
                } catch (e: Exception) {
                    Logger.d(TAG, "🔧 [固定比特率] 当前版本不支持降级偏好设置")
                }

                android.util.Log.i("WebRTCConnection", "🔧 编码参数: 比特率=${configuredBitrate/1000}kbps, 帧率=$configuredFramerate (严格按配置)")

                // 尝试禁用自适应分辨率缩放
                try {
                    val scaleResolutionDownByField = encodings[0].javaClass.getDeclaredField("scaleResolutionDownBy")
                    scaleResolutionDownByField.isAccessible = true
                    scaleResolutionDownByField.set(encodings[0], 1.0) // 不缩放分辨率
                } catch (e: Exception) {
                    // 忽略不支持的字段
                }

                // 应用参数
                sender.parameters = parameters

                Logger.i(TAG, "📊 已调整发送器编码参数: 比特率=${configuredBitrate/1000} kbps, 帧率=$configuredFramerate fps (严格按配置页面设置)")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "调整发送器编码参数失败", e)
        }
    }

    /**
     * 创建数据通道
     */
    private fun createDataChannel(peerConnection: PeerConnection, peerId: String) {
        Logger.d(TAG, "为对等端 $peerId 创建数据通道")

        val dataChannelInit = DataChannel.Init().apply {
            ordered = true
            negotiated = false
            // 注意：maxRetransmits 和 maxRetransmitTimeMs 不能同时设置
            // 使用 maxRetransmits 进行可靠传输，不设置 maxRetransmitTimeMs
            maxRetransmits = 3
            // 不设置 maxRetransmitTimeMs，避免 INVALID_PARAMETER 错误
        }

        try {
            val dataChannel = peerConnection.createDataChannel("control", dataChannelInit)
            if (dataChannel != null) {
                setupDataChannel(peerId, dataChannel)
                dataChannels[peerId] = dataChannel
                Logger.d(TAG, "成功为对等端 $peerId 创建数据通道")
            } else {
                Logger.e(TAG, "为对等端 $peerId 创建数据通道失败: 返回null")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "为对等端 $peerId 创建数据通道异常", e)

            // 尝试使用更简单的配置重新创建
            try {
                Logger.d(TAG, "尝试使用简化配置重新创建数据通道")
                val simpleInit = DataChannel.Init().apply {
                    ordered = true
                    negotiated = false
                    // 不设置任何重传参数，使用默认值
                }

                val dataChannel = peerConnection.createDataChannel("control", simpleInit)
                if (dataChannel != null) {
                    setupDataChannel(peerId, dataChannel)
                    dataChannels[peerId] = dataChannel
                    Logger.d(TAG, "使用简化配置成功创建数据通道")
                } else {
                    Logger.e(TAG, "使用简化配置仍然无法创建数据通道")
                }
            } catch (e2: Exception) {
                Logger.e(TAG, "使用简化配置创建数据通道也失败", e2)
            }
        }
    }

    /**
     * 设置数据通道回调
     */
    private fun setupDataChannel(peerId: String, dataChannel: DataChannel) {
        Logger.d(TAG, "设置对等端 $peerId 的数据通道回调")

        dataChannel.registerObserver(object : DataChannel.Observer {
            override fun onBufferedAmountChange(amount: Long) {
                // 忽略缓冲区变化
            }

            override fun onStateChange() {
                val state = dataChannel.state()
                Logger.d(TAG, "对等端 $peerId 数据通道状态变化: $state")

                // 更新数据通道状态
                dataChannels[peerId] = dataChannel

                if (state == DataChannel.State.OPEN) {
                    // 数据通道打开时发送问候消息
                    mainHandler.post {
                        try {
                            // 再次检查状态，确保在发送前仍然是OPEN
                            if (dataChannel.state() == DataChannel.State.OPEN) {
                                Logger.d(TAG, "对等端 $peerId 数据通道已打开，发送问候消息")
                                sendDataChannelMessage(peerId, "Hello from Android sender!")

                                // 发送源信息
                                val sourceInfo = mapOf(
                                    "type" to "source_info",
                                    "source_id" to WebRTCManager.getSenderId(),
                                    "name" to "",
                                    "description" to ""
                                )
                                sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(sourceInfo))

                                // 数据通道打开时也启动监控
                                Logger.i(TAG, "对等端 $peerId 数据通道已打开，启动监控")
                                startNetworkSpeedMonitoring()
                                startBitrateMonitoring()
                                startTrafficStatsLogging()
                            } else {
                                Logger.w(TAG, "对等端 $peerId 数据通道状态已变化，当前状态: ${dataChannel.state()}")
                            }
                        } catch (e: Exception) {
                            Logger.e(TAG, "发送数据通道问候消息失败", e)
                        }
                    }
                }
            }

            override fun onMessage(buffer: DataChannel.Buffer) {
                val data = ByteArray(buffer.data.remaining())
                buffer.data.get(data)
                val message = String(data)

                Logger.d(TAG, "对等端 $peerId 数据通道收到消息: $message")

                // 处理接收到的消息
                mainHandler.post {
                    processDataChannelMessage(peerId, message)
                }
            }
        })
    }

    /**
     * 处理数据通道消息
     */
    private fun processDataChannelMessage(peerId: String, message: String) {
        try {
            // 检查是否是JSON消息
            if (message.startsWith("{") && message.endsWith("}")) {
                val data = WebRTCManager.gson.fromJson(message, Map::class.java)

                // 处理命令
                if (data.containsKey("command")) {
                    val command = data["command"] as String

                    when (command) {
                        "pause" -> {
                            // 暂停视频
                            listener.onCommandReceived(peerId, "pause", data)

                            // 发送响应
                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "pause",
                                "success" to true,
                                "state" to "paused"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "adjust_video_quality" -> {
                            // 调整视频质量
                            var maxBitrate = 3000000 // 默认3Mbps
                            var minBitrate = 500000 // 默认500kbps
                            var maxFramerate = 60 // 默认60fps

                            // 从命令中获取参数
                            if (data.containsKey("maxBitrate")) {
                                maxBitrate = (data["maxBitrate"] as Double).toInt()
                            }
                            if (data.containsKey("minBitrate")) {
                                minBitrate = (data["minBitrate"] as Double).toInt()
                            }
                            if (data.containsKey("maxFramerate")) {
                                maxFramerate = (data["maxFramerate"] as Double).toInt()
                            }

                            // 调整视频质量
                            val success = adjustVideoQuality(peerId, maxBitrate, minBitrate, maxFramerate)

                            // 发送响应
                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "adjust_video_quality",
                                "success" to success,
                                "message" to "已调整视频质量: ${maxBitrate/1000}kbps, ${maxFramerate}fps"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "resume", "play" -> {
                            // 恢复播放
                            listener.onCommandReceived(peerId, "resume", data)

                            // 发送响应
                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "resume",
                                "success" to true,
                                "state" to "playing"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "toggle_pause" -> {
                            // 切换暂停/播放状态
                            listener.onCommandReceived(peerId, "toggle_pause", data)

                            // 发送响应
                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "toggle_pause",
                                "success" to true,
                                "state" to "playing" // 假设切换后是播放状态
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "get_status" -> {
                            // 获取状态
                            listener.onCommandReceived(peerId, "get_status", data)

                            // 发送状态
                            val status = mapOf(
                                "type" to "status",
                                "state" to "playing",
                                "position" to 0,
                                "duration" to 0,
                                "is_local_file" to false
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(status))
                        }
                        "request_keyframe" -> {
                            // 请求关键帧
                            listener.onCommandReceived(peerId, "request_keyframe", data)

                            // 执行关键帧请求
                            val success = requestKeyFrame()

                            // 发送响应
                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "request_keyframe",
                                "success" to success
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "force_stable_framerate" -> {
                            // 按配置运行 - 不动态调节帧率
                            Logger.i(TAG, "📡 [数据通道] 收到强制稳定帧率请求，但按配置不动态调节")
                            listener.onCommandReceived(peerId, "force_stable_framerate", data)

                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "force_stable_framerate",
                                "success" to false,
                                "message" to "按配置运行，不动态调节帧率"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        "reduce_bitrate" -> {
                            // 按配置运行 - 不动态调节比特率
                            Logger.i(TAG, "📡 [数据通道] 收到降低比特率请求，但按配置不动态调节")
                            listener.onCommandReceived(peerId, "reduce_bitrate", data)

                            val response = mapOf(
                                "type" to "command_result",
                                "command" to "reduce_bitrate",
                                "success" to false,
                                "message" to "按配置运行，不动态调节比特率"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                        else -> {
                            // 未知命令
                            Logger.w(TAG, "收到未知命令: $command")

                            // 发送错误响应
                            val response = mapOf(
                                "type" to "error",
                                "message" to "未知命令: $command"
                            )
                            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
                        }
                    }
                } else {
                    // 不是命令，回显消息
                    sendDataChannelMessage(peerId, "Echo: $message")
                }
            } else {
                // 不是JSON，回显消息
                sendDataChannelMessage(peerId, "Echo: $message")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "处理数据通道消息错误", e)

            // 发送错误响应
            val response = mapOf(
                "type" to "error",
                "message" to "处理消息错误: ${e.message}"
            )
            sendDataChannelMessage(peerId, WebRTCManager.gson.toJson(response))
        }
    }

    /**
     * 发送数据通道消息
     */
    fun sendDataChannelMessage(peerId: String, message: String): Boolean {
        val dataChannel = dataChannels[peerId]
        if (dataChannel == null) {
            Logger.w(TAG, "对等端 $peerId 的数据通道不存在，无法发送消息")
            return false
        }

        val state = dataChannel.state()
        if (state != DataChannel.State.OPEN) {
            Logger.w(TAG, "对等端 $peerId 的数据通道未打开，当前状态: $state，无法发送消息")

            // 如果数据通道状态是CONNECTING，尝试延迟发送
            if (state == DataChannel.State.CONNECTING) {
                Logger.i(TAG, "数据通道正在连接中，尝试延迟发送消息")
                mainHandler.postDelayed({
                    if (dataChannel.state() == DataChannel.State.OPEN) {
                        Logger.i(TAG, "数据通道已打开，尝试重新发送消息")
                        sendDataChannelMessage(peerId, message)
                    } else {
                        Logger.w(TAG, "数据通道仍未打开，放弃发送消息")
                    }
                }, 1000) // 延迟1秒再尝试
            }

            return false
        }

        try {
            Logger.d(TAG, "向对等端 $peerId 发送数据通道消息: $message")

            val buffer = ByteBuffer.wrap(message.toByteArray())
            dataChannel.send(DataChannel.Buffer(buffer, false))
            return true
        } catch (e: Exception) {
            Logger.e(TAG, "发送数据通道消息错误", e)

            // 检查是否是因为数据通道状态变化导致的错误
            if (dataChannel.state() != DataChannel.State.OPEN) {
                Logger.w(TAG, "数据通道状态已变化，当前状态: ${dataChannel.state()}")
            }

            return false
        }
    }

    /**
     * 创建Offer
     */
    fun createOffer(peerId: String) {
        Logger.d(TAG, "为对等端 $peerId 创建Offer")

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在")
            return
        }

        // 在创建Offer之前，先设置视频编码参数
        setVideoEncodingParameters(peerConnection)

        val constraints = MediaConstraints().apply {
            // 允许接收音频和视频，确保双向媒体流
            mandatory.add(MediaConstraints.KeyValuePair("OfferToReceiveAudio", "true"))
            mandatory.add(MediaConstraints.KeyValuePair("OfferToReceiveVideo", "true"))
            // 添加其他约束以提高兼容性
            optional.add(MediaConstraints.KeyValuePair("DtlsSrtpKeyAgreement", "true"))
            optional.add(MediaConstraints.KeyValuePair("RtpDataChannels", "true"))
        }

        peerConnection.createOffer(object : SdpObserver {
            override fun onCreateSuccess(sdp: SessionDescription) {
                Logger.d(TAG, "对等端 $peerId 创建Offer成功")

                // 记录原始SDP
                // Logger.d(TAG, "原始SDP: ${sdp.description}")

                // 检查SDP中的编解码器
                val codecPattern = "a=rtpmap:(\\d+) ([^/]+)";
                val matcher = Regex(codecPattern).findAll(sdp.description)
                val codecs = mutableListOf<Pair<String, String>>()

                for (match in matcher) {
                    val payloadType = match.groupValues[1]
                    val codecName = match.groupValues[2]
                    codecs.add(Pair(payloadType, codecName))
                }

                Logger.d(TAG, "SDP中的编解码器: ${codecs.joinToString { "${it.second}(${it.first})" }}")

                // 🎯 简化方案：直接使用原始SDP，通过编码参数API控制比特率
                // 这样避免了SDP解析失败的问题，同时仍能达到固定比特率的效果
                Logger.i(TAG, "🔍 [SDP策略] 使用原始SDP + 编码参数API控制比特率")
                val modifiedSdp = sdp

                // 检查修改后SDP中的编解码器
                val modifiedMatcher = Regex(codecPattern).findAll(modifiedSdp.description)
                val modifiedCodecs = mutableListOf<Pair<String, String>>()

                for (match in modifiedMatcher) {
                    val payloadType = match.groupValues[1]
                    val codecName = match.groupValues[2]
                    modifiedCodecs.add(Pair(payloadType, codecName))
                }

                Logger.d(TAG, "修改后SDP中的编解码器: ${modifiedCodecs.joinToString { "${it.second}(${it.first})" }}")
                // 记录修改后的SDP
                // Logger.d(TAG, "修改后的SDP: ${modifiedSdp.description}")

                // 设置本地描述
                peerConnection.setLocalDescription(object : SdpObserver {
                    override fun onCreateSuccess(p0: SessionDescription) {}

                    override fun onSetSuccess() {
                        Logger.d(TAG, "对等端 $peerId 设置本地描述成功")

                        // 在设置本地描述成功后，强制设置高质量参数
                        setVideoEncodingParameters(peerConnection)

                        // 强制设置高质量固定参数
                        forceHighQualityParameters(peerConnection)

                        // 配置编码器以优化复杂画面处理
                        configureEncoderForComplexScenes(peerConnection)

                        // 启动比特率监控
                        startBitrateMonitoring()

                        // 启动网速监控
                        startNetworkSpeedMonitoring()

                        // 启动累计流量统计打印
                        startTrafficStatsLogging()

                        // 通知监听器
                        listener.onOfferCreated(peerId, modifiedSdp)
                    }

                    override fun onCreateFailure(error: String) {}

                    override fun onSetFailure(error: String) {
                        Logger.e(TAG, "对等端 $peerId 设置本地描述失败: $error")
                        Logger.e(TAG, "🔍 [SDP错误调试] 失败的SDP长度: ${modifiedSdp.description.length}")
                        Logger.e(TAG, "🔍 [SDP错误调试] 失败的SDP类型: ${modifiedSdp.type}")
                        if (modifiedSdp.description.isEmpty()) {
                            Logger.e(TAG, "🔍 [SDP错误调试] ❌ SDP内容为空，这是问题所在！")
                        } else {
                            Logger.e(TAG, "🔍 [SDP错误调试] SDP前100字符: ${modifiedSdp.description.take(100)}")
                        }
                    }
                }, modifiedSdp)
            }

            override fun onSetSuccess() {}

            override fun onCreateFailure(error: String) {
                Logger.e(TAG, "对等端 $peerId 创建Offer失败: $error")
            }

            override fun onSetFailure(error: String) {}
        }, constraints)
    }

    /**
     * 动态调整视频质量
     */
    private fun adjustVideoQuality(peerId: String, maxBitrate: Int, minBitrate: Int, maxFramerate: Int): Boolean {
        try {
            Logger.d(TAG, "调整视频质量: 最大比特率=${maxBitrate/1000}kbps, 最小比特率=${minBitrate/1000}kbps, 最大帧率=${maxFramerate}fps")

            // 获取PeerConnection
            val peerConnection = peerConnections[peerId]
            if (peerConnection == null) {
                Logger.w(TAG, "对等端 $peerId 的PeerConnection不存在，无法调整视频质量")
                return false
            }

            // 获取视频发送器
            val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }

            if (videoSender == null) {
                Logger.w(TAG, "没有找到视频发送器，无法调整视频质量")
                return false
            }

            // 获取当前参数
            val parameters = videoSender.parameters

            // 检查encodings列表是否为空
            if (parameters.encodings.isEmpty()) {
                Logger.w(TAG, "编码参数列表为空，无法设置视频编码参数")
                return false
            }

            // 修改第一个encoding参数
            parameters.encodings[0].maxBitrateBps = maxBitrate
            parameters.encodings[0].minBitrateBps = minBitrate
            parameters.encodings[0].maxFramerate = maxFramerate
            parameters.encodings[0].active = true

            // 应用更改后的参数
            videoSender.parameters = parameters

            Logger.d(TAG, "成功调整视频质量参数: 最大比特率=${maxBitrate/1000}kbps, 最小比特率=${minBitrate/1000}kbps, 最大帧率=${maxFramerate}fps")

            // 请求关键帧，以便立即应用新的比特率
            requestKeyFrame()

            return true
        } catch (e: Exception) {
            Logger.e(TAG, "调整视频质量失败", e)
            return false
        }
    }

    /**
     * 修改SDP，设置比特率和编码器
     */
    private fun modifySdp(sdp: SessionDescription): SessionDescription {
        Logger.d(TAG, "修改SDP，设置比特率和编码器")

        // 获取设置的比特率和编码器
        val bitrate = WebRTCManager.getVideoBitrate()
        val codec = WebRTCManager.getVideoCodec()
        val framerate = WebRTCManager.getVideoFramerate()

        Logger.d(TAG, "设置比特率: $bitrate kbps, 编码器: $codec, 帧率: $framerate fps")

        // 修改SDP
        val lines = sdp.description.split("\r\n").toMutableList()
        var videoMLineIndex = -1
        var videoCodecPayloadType = -1

        // 查找视频媒体行
        for (i in lines.indices) {
            if (lines[i].startsWith("m=video ")) {
                videoMLineIndex = i
                break
            }
        }

        if (videoMLineIndex == -1) {
            Logger.e(TAG, "SDP中没有找到视频媒体行")
            return sdp
        }

        // 重新排序媒体行中的编解码器，使首选编解码器优先
        val mLine = lines[videoMLineIndex]
        val mLineParts = mLine.split(" ")
        if (mLineParts.size > 3) {
            // 获取所有编解码器的Payload Type
            val payloadTypes = mLineParts.subList(3, mLineParts.size)

            // 创建编解码器映射
            val codecMap = mutableMapOf<String, String>()

            // 查找每个Payload Type对应的编解码器
            for (payloadType in payloadTypes) {
                for (i in videoMLineIndex until lines.size) {
                    val line = lines[i]
                    if (line.startsWith("m=audio")) break // 到达音频段，结束查找

                    if (line.startsWith("a=rtpmap:$payloadType ")) {
                        val codecName = line.split(" ")[1].split("/")[0]
                        codecMap[payloadType] = codecName
                        break
                    }
                }
            }

            Logger.d(TAG, "编解码器映射: $codecMap")

            // 根据首选编解码器重新排序Payload Types
            val h264PayloadTypes = payloadTypes.filter { codecMap[it]?.equals("H264", ignoreCase = true) == true }
            val vp8PayloadTypes = payloadTypes.filter { codecMap[it]?.equals("VP8", ignoreCase = true) == true }
            val vp9PayloadTypes = payloadTypes.filter { codecMap[it]?.equals("VP9", ignoreCase = true) == true }
            val otherPayloadTypes = payloadTypes.filter {
                codecMap[it]?.equals("H264", ignoreCase = true) != true &&
                codecMap[it]?.equals("VP8", ignoreCase = true) != true &&
                codecMap[it]?.equals("VP9", ignoreCase = true) != true
            }

            // 根据首选编解码器重新排序
            val reorderedPayloadTypes = when (codec) {
                "H264" -> h264PayloadTypes + vp8PayloadTypes + vp9PayloadTypes + otherPayloadTypes
                "VP8" -> vp8PayloadTypes + h264PayloadTypes + vp9PayloadTypes + otherPayloadTypes
                "VP9" -> vp9PayloadTypes + h264PayloadTypes + vp8PayloadTypes + otherPayloadTypes
                else -> h264PayloadTypes + vp8PayloadTypes + vp9PayloadTypes + otherPayloadTypes // 默认H264优先
            }

            // 重建媒体行
            val newMLine = "${mLineParts[0]} ${mLineParts[1]} ${mLineParts[2]} ${reorderedPayloadTypes.joinToString(" ")}"
            lines[videoMLineIndex] = newMLine

            Logger.d(TAG, "重新排序后的媒体行: $newMLine")
        }

        // 查找编码器的负载类型
        val codecPattern = when (codec) {
            "VP8" -> "VP8/90000"
            "VP9" -> "VP9/90000"
            "H264" -> "H264/90000"
            else -> "H264/90000"
        }

        for (i in videoMLineIndex until lines.size) {
            if (lines[i].startsWith("a=rtpmap:") && lines[i].contains(codecPattern)) {
                val parts = lines[i].split(":")
                if (parts.size > 1) {
                    val ptParts = parts[1].split(" ")
                    if (ptParts.isNotEmpty()) {
                        videoCodecPayloadType = ptParts[0].toIntOrNull() ?: -1
                        break
                    }
                }
            }
        }

        if (videoCodecPayloadType == -1) {
            Logger.e(TAG, "SDP中没有找到编码器 $codec 的负载类型")
            return sdp
        }

        // 添加比特率参数
        var bitrateLineIndex = -1
        for (i in lines.indices) {
            if (lines[i].startsWith("b=AS:")) {
                bitrateLineIndex = i
                break
            }
        }

        if (bitrateLineIndex != -1) {
            // 替换已有的比特率行
            lines[bitrateLineIndex] = "b=AS:$bitrate"
        } else {
            // 在媒体行后添加比特率行
            lines.add(videoMLineIndex + 1, "b=AS:$bitrate")
        }

        // 添加帧率参数
        var framerateLineIndex = -1
        for (i in lines.indices) {
            if (lines[i].startsWith("a=framerate:")) {
                framerateLineIndex = i
                break
            }
        }

        if (framerateLineIndex != -1) {
            // 替换已有的帧率行
            lines[framerateLineIndex] = "a=framerate:$framerate"
        } else {
            // 在媒体行后添加帧率行
            lines.add(videoMLineIndex + 2, "a=framerate:$framerate")
        }
        // 添加编码器特定参数
        when (codec) {
            "VP8" -> {
                // 为VP8添加特定参数
                // 查找VP8的fmtp行
                var fmtpLineIndex = -1
                for (i in lines.indices) {
                    if (lines[i].startsWith("a=fmtp:$videoCodecPayloadType")) {
                        fmtpLineIndex = i
                        break
                    }
                }

                if (fmtpLineIndex != -1) {
                    // 修改已有的fmtp行
                    if (!lines[fmtpLineIndex].contains("x-google-min-bitrate")) {
                        lines[fmtpLineIndex] = "${lines[fmtpLineIndex]};x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};x-google-max-framerate=$framerate"
                    }
                } else {
                    // 添加新的fmtp行
                    lines.add("a=fmtp:$videoCodecPayloadType x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};x-google-max-framerate=$framerate")
                }

                // 确保VP8有关键帧请求支持
                var hasNackPli = false
                var hasCcmFir = false

                for (i in lines.indices) {
                    val line = lines[i]
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType nack pli")) {
                        hasNackPli = true
                    }
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType ccm fir")) {
                        hasCcmFir = true
                    }
                }

                if (!hasNackPli) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType nack pli")
                }

                if (!hasCcmFir) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType ccm fir")
                }
            }
            "VP9" -> {
                // 为VP9添加特定参数
                // 查找VP9的fmtp行
                var fmtpLineIndex = -1
                for (i in lines.indices) {
                    if (lines[i].startsWith("a=fmtp:$videoCodecPayloadType")) {
                        fmtpLineIndex = i
                        break
                    }
                }

                if (fmtpLineIndex != -1) {
                    // 修改已有的fmtp行
                    if (!lines[fmtpLineIndex].contains("x-google-min-bitrate")) {
                        lines[fmtpLineIndex] = "${lines[fmtpLineIndex]};x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};profile-id=0;x-google-max-framerate=$framerate"
                    }
                } else {
                    // 添加新的fmtp行
                    lines.add("a=fmtp:$videoCodecPayloadType x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};profile-id=0;x-google-max-framerate=$framerate")
                }

                // 确保VP9有关键帧请求支持
                var hasNackPli = false
                var hasCcmFir = false

                for (i in lines.indices) {
                    val line = lines[i]
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType nack pli")) {
                        hasNackPli = true
                    }
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType ccm fir")) {
                        hasCcmFir = true
                    }
                }

                if (!hasNackPli) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType nack pli")
                }

                if (!hasCcmFir) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType ccm fir")
                }
            }
            "H264" -> {
                // 查找H264的fmtp行
                var fmtpLineIndex = -1
                for (i in lines.indices) {
                    if (lines[i].startsWith("a=fmtp:$videoCodecPayloadType")) {
                        fmtpLineIndex = i
                        break
                    }
                }

                if (fmtpLineIndex != -1) {
                    // 修改已有的fmtp行
                    if (!lines[fmtpLineIndex].contains("x-google-min-bitrate")) {
                        lines[fmtpLineIndex] = "${lines[fmtpLineIndex]};x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};x-google-max-framerate=$framerate"
                    }
                } else {
                    // 添加新的fmtp行
                    lines.add("a=fmtp:$videoCodecPayloadType level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f;x-google-min-bitrate=$bitrate;x-google-max-bitrate=${bitrate * 2};x-google-max-framerate=$framerate")
                }

                // 确保H.264有关键帧请求支持
                var hasNackPli = false
                var hasCcmFir = false

                for (i in lines.indices) {
                    val line = lines[i]
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType nack pli")) {
                        hasNackPli = true
                    }
                    if (line.startsWith("a=rtcp-fb:$videoCodecPayloadType ccm fir")) {
                        hasCcmFir = true
                    }
                }

                if (!hasNackPli) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType nack pli")
                }

                if (!hasCcmFir) {
                    lines.add("a=rtcp-fb:$videoCodecPayloadType ccm fir")
                }
            }
        }
        // 重新组合SDP
        val modifiedSdpStr = lines.joinToString("\r\n")
        // Logger.d(TAG, "修改后的SDP: $modifiedSdpStr")

        return SessionDescription(sdp.type, modifiedSdpStr)
    }

    /**
     * 修改SDP以获得稳定的比特率，禁用自适应算法 - 保守版本
     */
    private fun modifySdpForStableBitrate(sdp: SessionDescription): SessionDescription {
        Logger.d(TAG, "修改SDP以获得稳定的比特率 - 保守模式")

        // 获取设置的比特率和编码器
        val bitrate = WebRTCManager.getVideoBitrate()
        val codec = WebRTCManager.getVideoCodec()
        val framerate = WebRTCManager.getVideoFramerate()

        Logger.d(TAG, "设置稳定比特率: $bitrate kbps, 编码器: $codec, 帧率: $framerate fps")

        // 打印原始SDP用于调试
        Logger.i(TAG, "🔍 [SDP调试] 原始SDP长度: ${sdp.description.length} 字符")
        Logger.i(TAG, "🔍 [SDP调试] 原始SDP内容:")
        sdp.description.split("\n").forEachIndexed { index, line ->
            Logger.i(TAG, "🔍 [SDP调试] 第${index+1}行: $line")
        }

        // 保守模式：只做最小必要的修改，避免破坏SDP结构
        try {
            val result = modifySdpConservatively(sdp, bitrate, codec, framerate)

            // 打印修改后的SDP用于调试
            Logger.i(TAG, "🔍 [SDP调试] 修改后SDP长度: ${result.description.length} 字符")
            if (result.description.isEmpty()) {
                Logger.e(TAG, "🔍 [SDP调试] ❌ 修改后SDP为空！这是问题所在")
            } else {
                Logger.i(TAG, "🔍 [SDP调试] 修改后SDP内容:")
                result.description.split("\n").forEachIndexed { index, line ->
                    Logger.i(TAG, "🔍 [SDP调试] 第${index+1}行: $line")
                }
            }

            return result
        } catch (e: Exception) {
            Logger.e(TAG, "SDP修改失败，使用原始SDP", e)
            return sdp
        }
    }

    /**
     * 保守的SDP修改方法 - 只修改必要的参数，保持SDP结构完整
     */
    private fun modifySdpConservatively(sdp: SessionDescription, bitrate: Int, codec: String, framerate: Int): SessionDescription {
        Logger.d(TAG, "使用保守模式修改SDP")

        val lines = sdp.description.split("\r\n").toMutableList()
        var videoMLineIndex = -1

        // 查找视频媒体行
        for (i in lines.indices) {
            if (lines[i].startsWith("m=video ")) {
                videoMLineIndex = i
                break
            }
        }

        if (videoMLineIndex == -1) {
            Logger.e(TAG, "SDP中没有找到视频媒体行")
            return sdp
        }

        // 设置固定比特率，禁用自适应 - 稍后统一添加，避免重复

        // 添加帧率限制
        var framerateLineIndex = -1
        for (i in lines.indices) {
            if (lines[i].startsWith("a=framerate:")) {
                framerateLineIndex = i
                break
            }
        }

        if (framerateLineIndex != -1) {
            lines[framerateLineIndex] = "a=framerate:$framerate"
        } else {
            lines.add(videoMLineIndex + 2, "a=framerate:$framerate")
        }

        // 查找编码器的负载类型
        val codecPattern = when (codec) {
            "VP8" -> "VP8/90000"
            "VP9" -> "VP9/90000"
            "H264" -> "H264/90000"
            else -> "H264/90000"
        }

        var videoCodecPayloadType = -1
        for (i in videoMLineIndex until lines.size) {
            if (lines[i].startsWith("a=rtpmap:") && lines[i].contains(codecPattern)) {
                val parts = lines[i].split(":")
                if (parts.size > 1) {
                    val ptParts = parts[1].split(" ")
                    if (ptParts.isNotEmpty()) {
                        videoCodecPayloadType = ptParts[0].toIntOrNull() ?: -1
                        break
                    }
                }
            }
        }

        if (videoCodecPayloadType != -1) {
            // 修改或添加fmtp行，设置固定比特率参数
            var fmtpLineIndex = -1
            for (i in lines.indices) {
                if (lines[i].startsWith("a=fmtp:$videoCodecPayloadType")) {
                    fmtpLineIndex = i
                    break
                }
            }

            // 设置基本编码器参数，不包含会被后面移除的比特率参数
            val fmtpParams = when (codec) {
                "H264" -> "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f"
                "VP8" -> ""
                "VP9" -> "profile-id=0"
                else -> "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f"
            }

            if (fmtpParams.isNotEmpty()) {
                if (fmtpLineIndex != -1) {
                    lines[fmtpLineIndex] = "a=fmtp:$videoCodecPayloadType $fmtpParams"
                } else {
                    lines.add("a=fmtp:$videoCodecPayloadType $fmtpParams")
                }
            }

            // 禁用所有自适应比特率控制机制

            // 禁用REMB (Receiver Estimated Maximum Bitrate) 反馈
            val rembLineIndex = lines.indexOfFirst { it.startsWith("a=rtcp-fb:$videoCodecPayloadType goog-remb") }
            if (rembLineIndex != -1) {
                lines.removeAt(rembLineIndex)
                Logger.i(TAG, "🎬 [SDP修改] ✅ 已移除REMB反馈以禁用自适应比特率")
            }

            // 禁用传输层CC (Transport-cc) 反馈
            val transportCcLineIndex = lines.indexOfFirst { it.startsWith("a=rtcp-fb:$videoCodecPayloadType transport-cc") }
            if (transportCcLineIndex != -1) {
                lines.removeAt(transportCcLineIndex)
                Logger.i(TAG, "🎬 [SDP修改] ✅ 已移除Transport-CC反馈以禁用传输层拥塞控制")
            }

            // 禁用NACK (Negative Acknowledgment) 反馈，减少重传请求
            val nackLineIndex = lines.indexOfFirst { it.startsWith("a=rtcp-fb:$videoCodecPayloadType nack") }
            if (nackLineIndex != -1) {
                lines.removeAt(nackLineIndex)
                Logger.i(TAG, "🎬 [SDP修改] ✅ 已移除NACK反馈以减少重传请求")
            }

            // 禁用PLI (Picture Loss Indication) 反馈，减少关键帧请求
            val pliLineIndex = lines.indexOfFirst { it.startsWith("a=rtcp-fb:$videoCodecPayloadType nack pli") }
            if (pliLineIndex != -1) {
                lines.removeAt(pliLineIndex)
                Logger.i(TAG, "🎬 [SDP修改] ✅ 已移除PLI反馈以减少关键帧请求")
            }

            // 保留FIR (Full Intra Request) 以支持必要的关键帧请求
            val firLineIndex = lines.indexOfFirst { it.startsWith("a=rtcp-fb:$videoCodecPayloadType ccm fir") }
            if (firLineIndex != -1) {
                Logger.i(TAG, "🎬 [SDP修改] 保留FIR反馈以支持关键帧请求")
            }

            // 获取配置的比特率
            val configuredBitrateKbps = getCurrentConfiguredBitrate() / 1000 // 使用配置的比特率

            // 添加配置的固定比特率SDP属性（只添加一次，避免重复）
            val videoMediaIndex = lines.indexOfFirst { it.startsWith("m=video") }
            if (videoMediaIndex != -1) {
                // 先移除任何现有的比特率属性，避免重复
                lines.removeAll { it.startsWith("b=AS:") || it.startsWith("b=CT:") || it.startsWith("b=TIAS:") }

                // 在视频媒体行后添加固定比特率属性
                var insertIndex = videoMediaIndex + 1

                // 添加多种带宽限制属性
                lines.add(insertIndex++, "b=AS:$configuredBitrateKbps")      // Application Specific
                lines.add(insertIndex++, "b=CT:$configuredBitrateKbps")      // Conference Total
                lines.add(insertIndex++, "b=TIAS:${configuredBitrateKbps * 1000}") // Transport Independent Application Specific

                Logger.i(TAG, "🎬 [SDP修改] ✅ 添加配置固定比特率属性: ${configuredBitrateKbps} kbps")
            }

            // 🎯 关键优化：禁用重传机制，避免帧序错乱
            // 移除所有重传相关的反馈机制
            lines.removeAll { line ->
                line.contains("rtx") ||            // 移除RTX (重传)
                line.contains("red") ||            // 移除RED (冗余编码)
                line.contains("ulpfec") ||         // 移除ULPFEC (前向纠错)
                line.contains("flexfec")           // 移除FlexFEC (灵活前向纠错)
            }
            Logger.i(TAG, "🎬 [SDP修改] ✅ 已禁用所有重传机制，避免帧序错乱")

            // 注意：不再添加任何NACK相关的反馈，保持SDP一致性
            Logger.i(TAG, "🎬 [SDP修改] ✅ 设置为无重传模式，只保留关键帧请求")

            // 移除任何现有的自适应比特率相关属性和重传机制
            lines.removeAll { line ->
                line.contains("x-google-max-bitrate") ||
                line.contains("x-google-min-bitrate") ||
                line.contains("x-google-start-bitrate") ||
                line.contains("goog-remb") ||
                line.contains("transport-cc") ||
                // 🎯 重点：移除所有重传相关属性，但保留FIR
                line.contains("a=rtcp-fb:") && (
                    line.contains("nack") ||
                    line.contains("rtx") ||
                    line.contains("red") ||
                    line.contains("ulpfec") ||
                    line.contains("flexfec")
                ) && !line.contains("ccm fir") ||  // 保留FIR
                line.startsWith("a=rtpmap:") && (
                    line.contains("rtx") ||
                    line.contains("red") ||
                    line.contains("ulpfec") ||
                    line.contains("flexfec")
                ) ||
                line.startsWith("a=fmtp:") && (
                    line.contains("rtx") ||
                    line.contains("red") ||
                    line.contains("apt=")  // Associated Payload Type for RTX
                )
            }
            Logger.i(TAG, "🎬 [SDP修改] ✅ 移除所有自适应比特率属性和重传机制")

            // 设置配置的编解码器参数
            lines.forEachIndexed { index, line ->
                if (line.startsWith("a=fmtp:") && line.contains("H264")) {
                    // 修改H.264参数，使用配置值
                    val modifiedLine = line.replace(Regex("max-br=\\d+"), "max-br=$configuredBitrateKbps")
                                          .replace(Regex("max-mbps=\\d+"), "max-mbps=245760") // 支持1920x1080@60fps
                    if (modifiedLine != line) {
                        lines[index] = modifiedLine
                        Logger.i(TAG, "🎬 [SDP修改] ✅ 修改H.264参数为配置值: ${configuredBitrateKbps} kbps")
                    }
                }
            }

            // 添加关键帧请求支持 (只保留FIR，不添加NACK)
            val hasCcmFir = lines.any { it.startsWith("a=rtcp-fb:$videoCodecPayloadType ccm fir") }

            if (!hasCcmFir) {
                lines.add("a=rtcp-fb:$videoCodecPayloadType ccm fir")
                Logger.i(TAG, "🎬 [SDP修改] ✅ 添加FIR关键帧请求支持")
            }
        }

        val modifiedSdpStr = lines.joinToString("\r\n")
        // Logger.d(TAG, "修改后的稳定比特率SDP: $modifiedSdpStr")

        return SessionDescription(sdp.type, modifiedSdpStr)
    }

    /**
     * 设置远程Offer
     */
    fun setRemoteOffer(peerId: String, sdp: SessionDescription) {
        Logger.d(TAG, "为对等端 $peerId 设置远程Offer")

        // 检查SDP是否为空
        if (sdp.description.isNullOrEmpty()) {
            Logger.e(TAG, "对等端 $peerId 的SDP描述为空")
            return
        }

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在")
            return
        }

        try {
            // Logger.d(TAG, "对等端 $peerId 的SDP描述: ${sdp.description}")

            peerConnection.setRemoteDescription(object : SdpObserver {
                override fun onCreateSuccess(p0: SessionDescription) {}

                override fun onSetSuccess() {
                    Logger.d(TAG, "对等端 $peerId 设置远程描述成功")

                    // 添加缓存的ICE候选
                    addPendingIceCandidates(peerId)

                    // 创建应答
                    createAnswer(peerId)
                }

                override fun onCreateFailure(error: String) {}

                override fun onSetFailure(error: String) {
                    Logger.e(TAG, "对等端 $peerId 设置远程描述失败: $error")

                    // 尝试重新创建PeerConnection
                    mainHandler.postDelayed({
                        Logger.i(TAG, "设置远程描述失败，尝试重新创建PeerConnection")

                        // 关闭旧的PeerConnection
                        closePeerConnection(peerId)

                        // 创建新的PeerConnection
                        createPeerConnection(peerId)

                        // 尝试再次设置远程描述
                        try {
                            setRemoteOffer(peerId, sdp)
                        } catch (e: Exception) {
                            Logger.e(TAG, "重新设置远程描述失败", e)
                        }
                    }, 1000) // 延迟1秒再尝试
                }
            }, sdp)
        } catch (e: Exception) {
            Logger.e(TAG, "对等端 $peerId 设置远程描述异常", e)
        }
    }

    /**
     * 创建Answer
     */
    private fun createAnswer(peerId: String) {
        Logger.d(TAG, "为对等端 $peerId 创建Answer")

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在")
            return
        }

        val constraints = MediaConstraints().apply {
            // 添加约束以提高兼容性
            optional.add(MediaConstraints.KeyValuePair("DtlsSrtpKeyAgreement", "true"))
            optional.add(MediaConstraints.KeyValuePair("RtpDataChannels", "true"))
        }

        peerConnection.createAnswer(object : SdpObserver {
            override fun onCreateSuccess(sdp: SessionDescription) {
                Logger.d(TAG, "对等端 $peerId 创建Answer成功")

                // 记录原始SDP
                // Logger.d(TAG, "原始SDP: ${sdp.description}")

                // 检查SDP中的编解码器
                val codecPattern = "a=rtpmap:(\\d+) ([^/]+)";
                val matcher = Regex(codecPattern).findAll(sdp.description)
                val codecs = mutableListOf<Pair<String, String>>()

                for (match in matcher) {
                    val payloadType = match.groupValues[1]
                    val codecName = match.groupValues[2]
                    codecs.add(Pair(payloadType, codecName))
                }

                Logger.d(TAG, "SDP中的编解码器: ${codecs.joinToString { "${it.second}(${it.first})" }}")

                // 🔍 [SDP策略] 使用原始SDP + 编码参数API控制比特率
                Logger.i(TAG, "🔍 [SDP策略] 使用原始SDP + 编码参数API控制比特率")
                val modifiedSdp = sdp // 使用原始SDP，通过RTP参数控制比特率
                //val modifiedSdp = modifySdpForStableBitrate(sdp) // SDP修改有问题，暂时禁用
                //val modifiedSdp = modifySdp(sdp) // 旧方法

                // 检查修改后SDP中的编解码器
                val modifiedMatcher = Regex(codecPattern).findAll(modifiedSdp.description)
                val modifiedCodecs = mutableListOf<Pair<String, String>>()

                for (match in modifiedMatcher) {
                    val payloadType = match.groupValues[1]
                    val codecName = match.groupValues[2]
                    modifiedCodecs.add(Pair(payloadType, codecName))
                }

                Logger.d(TAG, "修改后SDP中的编解码器: ${modifiedCodecs.joinToString { "${it.second}(${it.first})" }}")
                // 记录修改后的SDP
                // Logger.d(TAG, "修改后的SDP: ${modifiedSdp.description}")


                //val modifiedSdp = sdp

                // 设置本地描述
                peerConnection.setLocalDescription(object : SdpObserver {
                    override fun onCreateSuccess(p0: SessionDescription) {}

                    override fun onSetSuccess() {
                        Logger.d(TAG, "对等端 $peerId 设置本地描述成功")

                        // 通知监听器
                        listener.onAnswerCreated(peerId, modifiedSdp)
                    }

                    override fun onCreateFailure(error: String) {}

                    override fun onSetFailure(error: String) {
                        Logger.e(TAG, "对等端 $peerId 设置本地描述失败: $error")
                        Logger.e(TAG, "🔍 [SDP错误调试] 失败的SDP长度: ${modifiedSdp.description.length}")
                        Logger.e(TAG, "🔍 [SDP错误调试] 失败的SDP类型: ${modifiedSdp.type}")
                        if (modifiedSdp.description.isEmpty()) {
                            Logger.e(TAG, "🔍 [SDP错误调试] ❌ SDP内容为空，这是问题所在！")
                        } else {
                            Logger.e(TAG, "🔍 [SDP错误调试] SDP前100字符: ${modifiedSdp.description.take(100)}")
                        }
                    }
                }, modifiedSdp)
            }

            override fun onSetSuccess() {}

            override fun onCreateFailure(error: String) {
                Logger.e(TAG, "对等端 $peerId 创建Answer失败: $error")
            }

            override fun onSetFailure(error: String) {}
        }, constraints)
    }

    /**
     * 设置远程Answer
     */
    fun setRemoteAnswer(peerId: String, sdp: SessionDescription) {
        Logger.d(TAG, "为对等端 $peerId 设置远程Answer")

        // 检查SDP是否为空
        if (sdp.description.isNullOrEmpty()) {
            Logger.e(TAG, "对等端 $peerId 的SDP描述为空")
            return
        }

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在")
            return
        }

        try {
            //Logger.d(TAG, "对等端 $peerId 的SDP描述: ${sdp.description}")

            peerConnection.setRemoteDescription(object : SdpObserver {
                override fun onCreateSuccess(p0: SessionDescription) {}

                override fun onSetSuccess() {
                    Logger.d(TAG, "对等端 $peerId 设置远程描述成功")

                    // 添加缓存的ICE候选
                    addPendingIceCandidates(peerId)
                }

                override fun onCreateFailure(error: String) {}

                override fun onSetFailure(error: String) {
                    Logger.e(TAG, "对等端 $peerId 设置远程描述失败: $error")
                }
            }, sdp)
        } catch (e: Exception) {
            Logger.e(TAG, "对等端 $peerId 设置远程描述异常", e)
        }
    }

    /**
     * 添加ICE候选
     */
    fun addIceCandidate(peerId: String, candidate: IceCandidate) {
        Logger.i(TAG, "为对等端 $peerId 添加ICE候选")
        Logger.i(TAG, "ICE候选详情: sdpMid=${candidate.sdpMid}, sdpMLineIndex=${candidate.sdpMLineIndex}, sdp=${candidate.sdp}")

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在，无法添加ICE候选")
            return
        }

        // 验证ICE候选的有效性
        if (!isValidIceCandidate(candidate)) {
            Logger.w(TAG, "ICE候选格式无效，跳过添加")
            return
        }

        // 检查远程描述是否已设置
        if (peerConnection.remoteDescription == null) {
            Logger.w(TAG, "对等端 $peerId 的远程描述未设置，缓存ICE候选")

            // 缓存ICE候选
            val candidates = pendingIceCandidates.getOrPut(peerId) { mutableListOf() }
            candidates.add(candidate)
            Logger.i(TAG, "已缓存ICE候选，当前缓存数量: ${candidates.size}")
            return
        }

        try {
            peerConnection.addIceCandidate(candidate)
            Logger.i(TAG, "成功为对等端 $peerId 添加ICE候选")
        } catch (e: Exception) {
            Logger.e(TAG, "为对等端 $peerId 添加ICE候选失败: ${e.message}", e)

            // 如果是空的ICE候选，可能是结束标志，忽略错误
            if (candidate.sdp.isEmpty() || candidate.sdp.isBlank()) {
                Logger.w(TAG, "忽略空ICE候选的错误")
                return
            }

            // 检查是否是因为连接状态导致的错误
            val connectionState = peerConnection.connectionState()
            Logger.w(TAG, "当前连接状态: $connectionState")

            if (connectionState == PeerConnection.PeerConnectionState.CLOSED ||
                connectionState == PeerConnection.PeerConnectionState.FAILED) {
                Logger.w(TAG, "连接已关闭或失败，不重试添加ICE候选")
                return
            }

            // 尝试重新创建Offer
            mainHandler.postDelayed({
                Logger.i(TAG, "添加ICE候选失败，尝试重新创建Offer")
                createOffer(peerId)
            }, 2000) // 延迟2秒再尝试
        }
    }

    /**
     * 验证ICE候选的有效性
     */
    private fun isValidIceCandidate(candidate: IceCandidate): Boolean {
        // 检查基本字段
        if (candidate.sdp.isBlank()) {
            Logger.w(TAG, "ICE候选SDP为空")
            return false
        }

        // 检查SDP格式是否正确（应该以"candidate:"开头）
        if (!candidate.sdp.startsWith("candidate:") && candidate.sdp.isNotEmpty()) {
            Logger.w(TAG, "ICE候选SDP格式不正确: ${candidate.sdp}")
            return false
        }

        // 检查sdpMLineIndex是否有效
        if (candidate.sdpMLineIndex < 0) {
            Logger.w(TAG, "ICE候选sdpMLineIndex无效: ${candidate.sdpMLineIndex}")
            return false
        }

        return true
    }

    /**
     * 添加缓存的ICE候选
     */
    private fun addPendingIceCandidates(peerId: String) {
        val candidates = pendingIceCandidates[peerId] ?: return
        if (candidates.isEmpty()) return

        Logger.i(TAG, "为对等端 $peerId 添加 ${candidates.size} 个缓存的ICE候选")

        val peerConnection = peerConnections[peerId] ?: run {
            Logger.e(TAG, "对等端 $peerId 的PeerConnection不存在，无法添加缓存的ICE候选")
            return
        }

        if (peerConnection.remoteDescription == null) {
            Logger.w(TAG, "对等端 $peerId 的远程描述仍未设置，无法添加缓存的ICE候选")
            return
        }

        candidates.forEach { candidate ->
            try {
                peerConnection.addIceCandidate(candidate)
                Logger.i(TAG, "成功为对等端 $peerId 添加缓存的ICE候选: ${candidate.sdp}")
            } catch (e: Exception) {
                Logger.e(TAG, "为对等端 $peerId 添加缓存的ICE候选失败: ${candidate.sdp}", e)
            }
        }

        // 清空缓存
        candidates.clear()
        Logger.i(TAG, "已清空对等端 $peerId 的ICE候选缓存")
    }

    /**
     * 设置本地视频源
     */
    fun setLocalVideoSource(videoSource: VideoSource?) {
        Logger.d(TAG, "设置本地视频源")

        try {
            // 保存旧的引用
            val oldVideoTrack = localVideoTrack
            val oldVideoSource = localVideoSource

            // 先设置为null，避免在其他线程中使用
            localVideoTrack = null
            localVideoSource = null

            // 通知监听器视频轨道已更改
            listener.onLocalVideoSourceChanged(null)

            // 释放旧的视频轨道和源
            try {
                if (oldVideoTrack != null) {
                    oldVideoTrack.dispose()
                }
            } catch (e: Exception) {
                Logger.e(TAG, "释放视频轨道失败", e)
            }

            // 使用安全释放方法
            safeDisposeVideoSource(oldVideoSource)

            if (videoSource == null) {
                // 通知监听器
                listener.onLocalVideoSourceChanged(null)
                return
            }

            // 创建新的视频轨道
            localVideoSource = videoSource
            localVideoTrack = peerConnectionFactory.createVideoTrack("video", videoSource)
            Logger.i(TAG, "🎬 [视频轨道] 新的本地视频轨道已创建: ${localVideoTrack?.id()}")

            // 从WebRTCManager获取当前视频源类型，而不是通过toString判断
            val currentVideoSourceType = WebRTCManager.getVideoSourceType()
            Logger.i(TAG, "🎬 [视频轨道] 视频源类型: ${when(currentVideoSourceType) {
                "screen" -> "屏幕捕获"
                "camera" -> "摄像头"
                else -> "未知($currentVideoSourceType)"
            }}")

            // 通知监听器
            listener.onLocalVideoSourceChanged(localVideoTrack)
        } catch (e: Exception) {
            Logger.e(TAG, "设置本地视频源失败", e)
            throw e
        }
    }

    /**
     * 设置本地音频源
     */
    fun setLocalAudioSource(audioSource: AudioSource) {
        Logger.d(TAG, "设置本地音频源")

        // 释放旧的音频源
        localAudioTrack?.dispose()
        localAudioSource?.dispose()

        // 创建新的音频轨道
        localAudioSource = audioSource
        localAudioTrack = peerConnectionFactory.createAudioTrack("audio", audioSource)
    }

    /**
     * 设置共享视频源 - 优化版本，避免重复编码
     */
    fun setSharedVideoSource(videoSource: VideoSource) {
        Logger.d(TAG, "设置共享视频源")

        // 如果已经有相同的视频源，直接返回
        if (sharedVideoSource == videoSource && isVideoSourceInitialized) {
            Logger.d(TAG, "视频源已存在，跳过重复设置")
            return
        }

        // 安全释放之前的视频源
        safeDisposeVideoTrack(sharedVideoTrack)
        safeDisposeVideoSource(sharedVideoSource)

        // 创建新的共享视频轨道
        sharedVideoSource = videoSource
        sharedVideoTrack = peerConnectionFactory.createVideoTrack("shared_video", videoSource)
        isVideoSourceInitialized = true

        Logger.i(TAG, "共享视频源设置完成，连接数: ${peerConnections.size}")
    }

    /**
     * 获取共享视频轨道 - 为每个连接返回相同的轨道实例
     */
    fun getSharedVideoTrack(): VideoTrack? {
        return sharedVideoTrack
    }

    /**
     * 获取本地视频轨道
     */
    fun getLocalVideoTrack(): VideoTrack? {
        return localVideoTrack
    }

    /**
     * 获取活跃连接数
     */
    fun getActiveConnectionCount(): Int {
        return peerConnections.size
    }

    /**
     * 获取所有对等端ID
     */
    fun getAllPeerIds(): List<String> {
        return peerConnections.keys.toList()
    }

    /**
     * 设置共享音频源
     */
    fun setSharedAudioSource(audioSource: AudioSource) {
        Logger.d(TAG, "设置共享音频源")

        // 安全释放旧的音频源
        safeDisposeAudioTrack(sharedAudioTrack)
        safeDisposeAudioSource(sharedAudioSource)

        // 创建新的共享音频轨道
        sharedAudioSource = audioSource
        sharedAudioTrack = peerConnectionFactory.createAudioTrack("shared_audio", audioSource)

        Logger.i(TAG, "共享音频源设置完成")
    }

    /**
     * 获取共享音频轨道
     */
    fun getSharedAudioTrack(): AudioTrack? {
        return sharedAudioTrack
    }

    /**
     * 创建摄像头视频源
     */
    fun createCameraVideoSource(cameraId: String): VideoSource {
        Logger.i(TAG, "🎥 [摄像头] 开始创建摄像头视频源: $cameraId")

        try {
            // 检查摄像头权限
            if (!checkCameraPermission()) {
                throw SecurityException("摄像头权限未授予")
            }

            // 等待摄像头硬件就绪
            if (!waitForCameraReady()) {
                Logger.w(TAG, "🎥 [摄像头] 摄像头硬件未就绪，使用延迟重试机制")
                throw IllegalStateException("摄像头硬件未就绪，请稍后重试")
            }

            // 获取可用摄像头列表
            val cameraEnumerator = Camera2Enumerator(context)
            val deviceNames = cameraEnumerator.deviceNames

            Logger.i(TAG, "🎥 [摄像头] 请求摄像头ID: $cameraId, 可用设备: ${deviceNames.joinToString()}")

            // 确定要使用的摄像头ID
            val actualCameraId = when {
                // 如果请求的ID在可用列表中，直接使用
                deviceNames.contains(cameraId) -> {
                    Logger.i(TAG, "🎥 [摄像头] 使用请求的摄像头ID: $cameraId")
                    cameraId
                }
                // 如果请求的ID不存在，但设备只有一个摄像头，使用唯一的摄像头
                deviceNames.size == 1 -> {
                    val onlyCamera = deviceNames[0]
                    Logger.w(TAG, "🎥 [摄像头] 请求的ID $cameraId 不存在，使用设备唯一摄像头: $onlyCamera")
                    onlyCamera
                }
                // 如果有多个摄像头，使用第一个可用的
                else -> {
                    val firstCamera = deviceNames[0]
                    Logger.w(TAG, "🎥 [摄像头] 请求的ID $cameraId 不存在，使用第一个可用摄像头: $firstCamera")
                    firstCamera
                }
            }

            Logger.i(TAG, "🎥 [摄像头] 最终使用的摄像头ID: $actualCameraId")
            return createCameraVideoSourceWithId(cameraEnumerator, actualCameraId)
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头] 创建摄像头视频源失败", e)
            throw e
        }
    }

    /**
     * 等待摄像头硬件就绪并验证摄像头ID
     */
    private fun waitForCameraReady(maxWaitTimeMs: Long = 5000): Boolean {
        Logger.i(TAG, "🎥 [摄像头] 检查摄像头硬件就绪状态")

        val startTime = System.currentTimeMillis()
        var attempts = 0
        val maxAttempts = 5

        while (System.currentTimeMillis() - startTime < maxWaitTimeMs && attempts < maxAttempts) {
            attempts++

            try {
                // 尝试创建Camera2Enumerator并获取设备列表
                val cameraEnumerator = Camera2Enumerator(context)
                val deviceNames = cameraEnumerator.deviceNames

                if (deviceNames.isNotEmpty()) {
                    Logger.i(TAG, "🎥 [摄像头] 检测到摄像头设备: ${deviceNames.joinToString()}")

                    // 进一步验证摄像头是否真正可用
                    var hasValidCamera = false
                    for (deviceName in deviceNames) {
                        try {
                            // 尝试检查摄像头属性，这会触发实际的硬件访问
                            val isFront = cameraEnumerator.isFrontFacing(deviceName)
                            val isBack = cameraEnumerator.isBackFacing(deviceName)
                            Logger.d(TAG, "🎥 [摄像头] 摄像头 $deviceName: 前置=$isFront, 后置=$isBack")
                            hasValidCamera = true
                        } catch (e: Exception) {
                            Logger.w(TAG, "🎥 [摄像头] 摄像头 $deviceName 不可用: ${e.message}")
                        }
                    }

                    if (hasValidCamera) {
                        Logger.i(TAG, "🎥 [摄像头] 摄像头硬件就绪，尝试次数: $attempts")
                        return true
                    }
                }

                Logger.d(TAG, "🎥 [摄像头] 摄像头硬件未就绪，等待中... (尝试 $attempts/$maxAttempts)")
                Thread.sleep(1000) // 等待1秒后重试

            } catch (e: Exception) {
                Logger.w(TAG, "🎥 [摄像头] 检查摄像头状态失败 (尝试 $attempts): ${e.message}")
                Thread.sleep(1000) // 等待1秒后重试
            }
        }

        Logger.w(TAG, "🎥 [摄像头] 摄像头硬件在 ${maxWaitTimeMs}ms 内未就绪")
        return false
    }

    /**
     * 验证摄像头ID是否安全可用
     */
    private fun validateCameraId(cameraId: String): Boolean {
        return try {
            val cameraEnumerator = Camera2Enumerator(context)
            val deviceNames = cameraEnumerator.deviceNames

            Logger.i(TAG, "🎥 [摄像头验证] 请求ID: $cameraId, 可用设备: ${deviceNames.joinToString()}")

            // 检查是否在可用设备列表中
            if (deviceNames.contains(cameraId)) {
                // 进一步验证摄像头是否可以安全访问
                try {
                    cameraEnumerator.isFrontFacing(cameraId)
                    Logger.i(TAG, "🎥 [摄像头验证] 摄像头ID $cameraId 验证通过")
                    true
                } catch (e: Exception) {
                    Logger.e(TAG, "🎥 [摄像头验证] 摄像头ID $cameraId 访问失败: ${e.message}")
                    false
                }
            } else {
                // 如果请求的ID不在列表中，但设备只有一个摄像头，检查是否可以使用唯一的摄像头
                if (deviceNames.size == 1) {
                    val onlyCamera = deviceNames[0]
                    Logger.w(TAG, "🎥 [摄像头验证] 请求的ID $cameraId 不存在，但设备只有一个摄像头 $onlyCamera，尝试使用")

                    try {
                        cameraEnumerator.isFrontFacing(onlyCamera)
                        Logger.i(TAG, "🎥 [摄像头验证] 唯一摄像头 $onlyCamera 验证通过")
                        return true
                    } catch (e: Exception) {
                        Logger.e(TAG, "🎥 [摄像头验证] 唯一摄像头 $onlyCamera 访问失败: ${e.message}")
                        return false
                    }
                }

                Logger.w(TAG, "🎥 [摄像头验证] 摄像头ID $cameraId 不在可用设备列表中")
                false
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头验证] 验证摄像头ID失败", e)
            false
        }
    }

    /**
     * 检查摄像头ID是否安全
     */
    private fun isSafeCameraId(cameraId: String): Boolean {
        try {
            // 检查是否为纯数字
            val numericId = cameraId.toLongOrNull()
            if (numericId != null) {
                // 检查数值范围，拒绝异常大的数值
                if (numericId > 1000000) {
                    Logger.w(TAG, "🎥 [摄像头安全检查] 摄像头ID数值过大，可能存在问题: $cameraId ($numericId)")
                    return false
                }

                // 检查是否为已知的问题ID
                val problematicIds = listOf(
                    4294967196L, // 已知的问题ID
                    4294967295L, // 32位无符号整数最大值
                    2147483647L  // 32位有符号整数最大值
                )

                if (numericId in problematicIds) {
                    Logger.w(TAG, "🎥 [摄像头安全检查] 检测到已知问题摄像头ID: $cameraId")
                    return false
                }
            }

            // 检查字符串长度，防止异常长的ID
            if (cameraId.length > 20) {
                Logger.w(TAG, "🎥 [摄像头安全检查] 摄像头ID过长: $cameraId")
                return false
            }

            return true
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头安全检查] 检查摄像头ID安全性失败", e)
            return false
        }
    }

    /**
     * 查找安全可用的摄像头ID
     */
    private fun findSafeCameraId(): String? {
        return try {
            val cameraEnumerator = Camera2Enumerator(context)
            val deviceNames = cameraEnumerator.deviceNames

            Logger.i(TAG, "🎥 [摄像头查找] 可用摄像头列表: ${deviceNames.joinToString()}")

            // 首先查找安全的摄像头
            for (deviceName in deviceNames) {
                if (isSafeCameraId(deviceName)) {
                    try {
                        // 验证摄像头是否真的可用
                        cameraEnumerator.isFrontFacing(deviceName)
                        Logger.i(TAG, "🎥 [摄像头查找] 找到安全可用的摄像头: $deviceName")
                        return deviceName
                    } catch (e: Exception) {
                        Logger.w(TAG, "🎥 [摄像头查找] 摄像头 $deviceName 不可用: ${e.message}")
                    }
                }
            }

            // 如果没有找到安全的摄像头，但设备只有一个摄像头，则尝试使用它
            if (deviceNames.size == 1) {
                val onlyCamera = deviceNames[0]
                Logger.w(TAG, "🎥 [摄像头查找] 设备只有一个摄像头 $onlyCamera，尝试使用（即使ID看起来有问题）")

                try {
                    // 验证这个摄像头是否真的可用
                    cameraEnumerator.isFrontFacing(onlyCamera)
                    Logger.i(TAG, "🎥 [摄像头查找] 唯一摄像头验证成功: $onlyCamera")
                    return onlyCamera
                } catch (e: Exception) {
                    Logger.e(TAG, "🎥 [摄像头查找] 唯一摄像头也不可用: ${e.message}")
                }
            }

            Logger.w(TAG, "🎥 [摄像头查找] 没有找到可用的摄像头")
            null
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头查找] 查找摄像头失败", e)
            null
        }
    }

    /**
     * 检查摄像头权限
     */
    private fun checkCameraPermission(): Boolean {
        return try {
            val permission = android.Manifest.permission.CAMERA
            val result = androidx.core.content.ContextCompat.checkSelfPermission(context, permission)
            val hasPermission = result == android.content.pm.PackageManager.PERMISSION_GRANTED

            Logger.i(TAG, "🎥 [摄像头权限] 权限检查结果: $hasPermission")
            hasPermission
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头权限] 权限检查失败", e)
            false
        }
    }

    // 问题摄像头ID列表
    private val problematicCameraIds = mutableSetOf<String>()

    /**
     * 标记摄像头为有问题的
     */
    private fun markCameraAsProblematic(cameraId: String) {
        problematicCameraIds.add(cameraId)
        Logger.w(TAG, "🎥 [摄像头管理] 标记摄像头为有问题: $cameraId")
    }

    /**
     * 检查摄像头是否被标记为有问题
     */
    private fun isCameraProblematic(cameraId: String): Boolean {
        return problematicCameraIds.contains(cameraId)
    }



    // 摄像头策略错误恢复相关变量
    private var cameraPolicyErrorRetryCount = 0
    private var cameraPolicyErrorHandler: Handler? = null
    private var originalCameraId: String? = null // 记录原始摄像头ID
    private var fallbackCameraAttempted = false // 是否已尝试备用摄像头

    /**
     * 处理摄像头策略错误
     */
    private fun handleCameraPolicyError(cameraId: String) {
        Logger.w(TAG, "🎥 [策略错误] 摄像头${cameraId}被策略禁用，启动智能恢复机制")
        Logger.i(TAG, "🎥 [策略错误] 当前重试计数: $cameraPolicyErrorRetryCount")

        // 记录原始摄像头ID
        if (originalCameraId == null) {
            originalCameraId = cameraId
            Logger.i(TAG, "🎥 [策略错误] 记录原始摄像头ID: $originalCameraId")
        }

        // 如果重试次数较少，先尝试直接恢复
        if (cameraPolicyErrorRetryCount < 3) {
            attemptDirectRecovery(cameraId)
        } else if (!fallbackCameraAttempted) {
            // 如果直接恢复失败多次，尝试切换到备用摄像头
            attemptFallbackCamera(cameraId)
        } else {
            // 如果备用摄像头也失败，继续重试原摄像头
            attemptDirectRecovery(cameraId)
        }
    }

    /**
     * 尝试直接恢复原摄像头
     */
    private fun attemptDirectRecovery(cameraId: String) {
        if (cameraPolicyErrorRetryCount >= Constants.CAMERA_POLICY_ERROR_MAX_RETRIES) {
            Logger.e(TAG, "🎥 [策略错误] 已达到最大重试次数，触发自动重启机制")

            // 记录策略错误失败日志
            DeviceLogManager.getInstance().logDeviceEvent(
                context,
                "camera_policy_error_max_retries",
                "CRITICAL",
                "摄像头策略错误达到最大重试次数",
                "摄像头ID: $cameraId, 重试次数: $cameraPolicyErrorRetryCount"
            )

            // 触发自动重启
            AutoRebootManager.getInstance().triggerAutoReboot(
                context,
                "摄像头策略错误重试失败",
                "摄像头ID: $cameraId, 重试次数: $cameraPolicyErrorRetryCount, 错误: CAMERA_DISABLED by policy"
            )

            return
        }

        cameraPolicyErrorRetryCount++
        val retryDelay = (Constants.CAMERA_POLICY_ERROR_RETRY_DELAY *
                         Math.pow(Constants.CAMERA_POLICY_ERROR_BACKOFF_MULTIPLIER.toDouble(),
                                 (cameraPolicyErrorRetryCount - 1).toDouble())).toLong()

        Logger.i(TAG, "🎥 [策略错误] 第${cameraPolicyErrorRetryCount}次重试，${retryDelay/1000}秒后尝试恢复摄像头$cameraId")

        // 清理当前的Handler
        cameraPolicyErrorHandler?.removeCallbacksAndMessages(null)

        // 创建新的Handler进行延迟重试
        cameraPolicyErrorHandler = Handler(Looper.getMainLooper())
        cameraPolicyErrorHandler?.postDelayed({
            try {
                Logger.i(TAG, "🎥 [策略错误] 尝试恢复摄像头${cameraId}")

                // 停止当前视频源
                WebRTCManager.stopVideoSource(context)

                // 等待一段时间让系统释放资源
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        // 重新启动摄像头
                        createCameraVideoSource(cameraId)
                        Logger.i(TAG, "🎥 [策略错误] 摄像头VideoSource创建成功，等待验证...")

                        // 延迟验证摄像头是否真正成功，避免异步策略错误
                        Handler(Looper.getMainLooper()).postDelayed({
                            // 检查是否在这段时间内又发生了策略错误
                            if (!isCameraProblematic(cameraId)) {
                                Logger.i(TAG, "🎥 [策略错误] ✅ 摄像头真正恢复成功，重置重试计数")
                                cameraPolicyErrorRetryCount = 0
                                fallbackCameraAttempted = false
                            } else {
                                Logger.w(TAG, "🎥 [策略错误] 摄像头仍有问题，继续重试")
                                handleCameraPolicyError(cameraId)
                            }
                        }, 5000) // 等待5秒验证
                    } catch (e: Exception) {
                        Logger.e(TAG, "🎥 [策略错误] 摄像头恢复失败: ${e.message}")
                        // 继续下一次重试
                        handleCameraPolicyError(cameraId)
                    }
                }, 2000) // 等待2秒让系统释放资源

            } catch (e: Exception) {
                Logger.e(TAG, "🎥 [策略错误] 恢复过程出错: ${e.message}")
                // 继续下一次重试
                handleCameraPolicyError(cameraId)
            }
        }, retryDelay)
    }

    /**
     * 尝试切换到备用摄像头
     */
    private fun attemptFallbackCamera(originalCameraId: String) {
        Logger.w(TAG, "🎥 [策略错误] 摄像头$originalCameraId 持续被禁用，尝试切换到备用摄像头")
        fallbackCameraAttempted = true

        try {
            // 获取可用的摄像头列表
            val cameraEnumerator = Camera2Enumerator(context)
            val availableCameras = cameraEnumerator.deviceNames.toList()
            Logger.i(TAG, "🎥 [策略错误] 可用摄像头列表: ${availableCameras.joinToString()}")

            // 寻找备用摄像头（排除当前问题摄像头）
            val fallbackCameras = availableCameras.filter { it != originalCameraId }

            if (fallbackCameras.isEmpty()) {
                Logger.e(TAG, "🎥 [策略错误] ❌ 没有可用的备用摄像头，触发自动重启")

                // 记录无备用摄像头日志
                DeviceLogManager.getInstance().logDeviceEvent(
                    context,
                    "no_fallback_cameras",
                    "CRITICAL",
                    "没有可用的备用摄像头",
                    "原摄像头: $originalCameraId, 可用摄像头列表: ${availableCameras.joinToString()}, 重试次数: $cameraPolicyErrorRetryCount"
                )

                // 触发自动重启
                AutoRebootManager.getInstance().triggerAutoReboot(
                    context,
                    "没有可用的备用摄像头",
                    "原摄像头: $originalCameraId 策略错误，无其他可用摄像头"
                )

                return
            }

            // 按优先级选择备用摄像头：0 > 1 > 其他
            val prioritizedCameras = fallbackCameras.sortedWith { a, b ->
                when {
                    a == "0" -> -1
                    b == "0" -> 1
                    a == "1" -> -1
                    b == "1" -> 1
                    else -> a.compareTo(b)
                }
            }

            val fallbackCameraId = prioritizedCameras.first()
            Logger.i(TAG, "🎥 [策略错误] 🔄 尝试切换到备用摄像头: $fallbackCameraId")

            // 停止当前视频源
            WebRTCManager.stopVideoSource(context)

            // 等待资源释放后启动备用摄像头
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    createCameraVideoSource(fallbackCameraId)
                    Logger.i(TAG, "🎥 [策略错误] 备用摄像头VideoSource创建成功: $fallbackCameraId，等待验证...")

                    // 更新配置中的摄像头ID
                    WebRTCManager.setCameraId(fallbackCameraId)
                    Logger.i(TAG, "🎥 [策略错误] 📝 已更新配置摄像头ID: $originalCameraId -> $fallbackCameraId")

                    // 延迟验证备用摄像头是否真正成功
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isCameraProblematic(fallbackCameraId)) {
                            Logger.i(TAG, "🎥 [策略错误] ✅ 备用摄像头真正成功，重置重试计数")
                            cameraPolicyErrorRetryCount = 0
                            fallbackCameraAttempted = false
                        } else {
                            Logger.w(TAG, "🎥 [策略错误] 备用摄像头也有问题，继续尝试其他摄像头")
                            handleCameraPolicyError(originalCameraId ?: fallbackCameraId)
                        }
                    }, 5000) // 等待5秒验证

                } catch (e: Exception) {
                    Logger.e(TAG, "🎥 [策略错误] 备用摄像头$fallbackCameraId 也失败: ${e.message}")

                    // 如果还有其他备用摄像头，继续尝试
                    val remainingCameras = prioritizedCameras.drop(1)
                    if (remainingCameras.isNotEmpty()) {
                        Logger.i(TAG, "🎥 [策略错误] 🔄 尝试下一个备用摄像头...")
                        attemptFallbackCamera(originalCameraId)
                    } else {
                        Logger.e(TAG, "🎥 [策略错误] ❌ 所有备用摄像头都失败，触发自动重启")

                        // 记录所有摄像头失败日志
                        DeviceLogManager.getInstance().logDeviceEvent(
                            context,
                            "all_cameras_failed",
                            "CRITICAL",
                            "所有摄像头（包括备用）都失败",
                            "原摄像头: $originalCameraId, 备用摄像头尝试失败, 重试次数: $cameraPolicyErrorRetryCount"
                        )

                        // 触发自动重启
                        AutoRebootManager.getInstance().triggerAutoReboot(
                            context,
                            "所有摄像头都无法使用",
                            "原摄像头: $originalCameraId, 所有备用摄像头都失败, 策略错误无法解决"
                        )
                    }
                }
            }, 3000) // 等待3秒让系统充分释放资源

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [策略错误] 备用摄像头切换过程出错: ${e.message}")
            handleCameraPolicyError(originalCameraId)
        }
    }

    /**
     * 执行最终的摄像头安全验证
     */
    private fun performFinalCameraValidation(cameraId: String): Boolean {
        return try {
            Logger.i(TAG, "🎥 [最终验证] 对摄像头ID $cameraId 进行最终安全检查")

            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as android.hardware.camera2.CameraManager

            // 检查摄像头ID是否在系统列表中
            val systemCameraIds = cameraManager.cameraIdList
            Logger.i(TAG, "🎥 [最终验证] 系统摄像头ID列表: ${systemCameraIds.joinToString()}")

            if (!systemCameraIds.contains(cameraId)) {
                Logger.e(TAG, "🎥 [最终验证] 摄像头ID $cameraId 不在系统列表中")
                return false
            }

            // 尝试获取摄像头特性
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            val facing = characteristics.get(android.hardware.camera2.CameraCharacteristics.LENS_FACING)
            Logger.i(TAG, "🎥 [最终验证] 摄像头 $cameraId 朝向: $facing")

            // 检查是否支持必要的功能
            val capabilities = characteristics.get(android.hardware.camera2.CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
            val hasVideoCapability = capabilities?.contains(android.hardware.camera2.CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_BACKWARD_COMPATIBLE) == true

            if (!hasVideoCapability) {
                Logger.e(TAG, "🎥 [最终验证] 摄像头 $cameraId 不支持视频捕获")
                return false
            }

            Logger.i(TAG, "🎥 [最终验证] 摄像头ID $cameraId 最终验证通过")
            true

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [最终验证] 摄像头ID $cameraId 最终验证失败", e)
            false
        }
    }

    /**
     * 选择最佳摄像头
     */
    private fun selectBestCamera(cameraEnumerator: Camera2Enumerator, deviceNames: Array<String>, requestedId: String): String {
        Logger.i(TAG, "🎥 [摄像头] 可用摄像头列表: ${deviceNames.joinToString()}")
        Logger.i(TAG, "🎥 [摄像头] 请求的摄像头ID: $requestedId")

        return when (requestedId) {
            "0" -> { // 后置摄像头
                val backCamera = deviceNames.find {
                    try {
                        cameraEnumerator.isBackFacing(it)
                    } catch (e: Exception) {
                        Logger.w(TAG, "🎥 [摄像头] 检查摄像头 $it 朝向失败: ${e.message}")
                        false
                    }
                }
                if (backCamera != null) {
                    Logger.i(TAG, "🎥 [摄像头] 找到后置摄像头: $backCamera")
                    backCamera
                } else {
                    Logger.w(TAG, "🎥 [摄像头] 未找到后置摄像头，使用第一个可用摄像头")
                    deviceNames[0]
                }
            }
            "1" -> { // 前置摄像头
                val frontCamera = deviceNames.find {
                    try {
                        cameraEnumerator.isFrontFacing(it)
                    } catch (e: Exception) {
                        Logger.w(TAG, "🎥 [摄像头] 检查摄像头 $it 朝向失败: ${e.message}")
                        false
                    }
                }
                if (frontCamera != null) {
                    Logger.i(TAG, "🎥 [摄像头] 找到前置摄像头: $frontCamera")
                    frontCamera
                } else {
                    Logger.w(TAG, "🎥 [摄像头] 未找到前置摄像头，使用第一个可用摄像头")
                    deviceNames[0]
                }
            }
            else -> { // 指定索引或ID
                // 首先检查是否是直接的设备ID
                if (deviceNames.contains(requestedId)) {
                    Logger.i(TAG, "🎥 [摄像头] 使用指定的摄像头ID: $requestedId")
                    requestedId
                } else {
                    // 尝试作为索引解析
                    val index = requestedId.toIntOrNull()
                    if (index != null && index >= 0 && index < deviceNames.size) {
                        Logger.i(TAG, "🎥 [摄像头] 使用索引摄像头: ${deviceNames[index]}")
                        deviceNames[index]
                    } else {
                        Logger.w(TAG, "🎥 [摄像头] 无效的摄像头ID/索引: $requestedId，可用设备: ${deviceNames.joinToString()}")
                        Logger.w(TAG, "🎥 [摄像头] 使用第一个可用摄像头: ${deviceNames[0]}")
                        deviceNames[0]
                    }
                }
            }
        }
    }

    /**
     * 使用指定摄像头ID创建视频源
     */
    private fun createCameraVideoSourceWithId(cameraEnumerator: Camera2Enumerator, cameraId: String): VideoSource {
        Logger.i(TAG, "🎥 [摄像头] 使用摄像头ID创建视频源: $cameraId")

        // 在创建前进行安全检查
        if (!performFinalCameraValidation(cameraId)) {
            throw IllegalStateException("摄像头ID $cameraId 验证失败，不可用")
        }

        // 创建视频捕获器 - 使用安全的错误处理
        val videoCapturer = try {
            Logger.i(TAG, "🎥 [摄像头] 尝试创建摄像头捕获器: $cameraId")

            cameraEnumerator.createCapturer(cameraId, object : CameraVideoCapturer.CameraEventsHandler {
                override fun onCameraError(errorDescription: String?) {
                    Logger.e(TAG, "🎥 [摄像头] 摄像头错误: $errorDescription")

                    // 检查是否为已知的硬件兼容性问题
                    if (errorDescription?.contains("Function not implemented") == true ||
                        errorDescription?.contains("CAMERA_ERROR") == true) {
                        Logger.e(TAG, "🎥 [摄像头] 严重硬件兼容性问题，摄像头ID $cameraId 不可用")
                        markCameraAsProblematic(cameraId)
                    }

                    // 检查是否是策略错误
                    if (errorDescription?.contains("disabled by policy") == true ||
                        errorDescription?.contains("CAMERA_DISABLED") == true) {
                        Logger.w(TAG, "🎥 [摄像头] 检测到摄像头策略错误，启动自动恢复")
                        // markCameraAsProblematic(cameraId)
                        // handleCameraPolicyError(cameraId)
                    }
                }

                override fun onCameraDisconnected() {
                    Logger.w(TAG, "🎥 [摄像头] 摄像头断开连接")
                }

                override fun onCameraFreezed(errorDescription: String?) {
                    Logger.w(TAG, "🎥 [摄像头] 摄像头冻结: $errorDescription")
                }

                override fun onCameraOpening(cameraName: String?) {
                    Logger.i(TAG, "🎥 [摄像头] 正在打开摄像头: $cameraName")
                }

                override fun onFirstFrameAvailable() {
                    Logger.i(TAG, "🎥 [摄像头] ✅ 第一帧可用")
                }

                override fun onCameraClosed() {
                    Logger.i(TAG, "🎥 [摄像头] 摄像头已关闭")
                }
            })
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头] Camera2创建失败，尝试使用Camera1: ${e.message}")
            // 如果Camera2失败，尝试使用Camera1
            try {
                val camera1Enumerator = Camera1Enumerator(false)
                val camera1DeviceNames = camera1Enumerator.deviceNames
                Logger.i(TAG, "🎥 [摄像头] Camera1可用设备: ${camera1DeviceNames.joinToString()}")

                if (camera1DeviceNames.isNotEmpty()) {
                    // 智能选择Camera1设备
                    val camera1Id = when {
                        cameraId == "0" -> "0" // 后置摄像头
                        cameraId == "1" -> if (camera1DeviceNames.size > 1) "1" else "0" // 前置摄像头
                        else -> {
                            // 对于其他ID，尝试解析为索引
                            val index = cameraId.toIntOrNull()
                            if (index != null && index < camera1DeviceNames.size) {
                                index.toString()
                            } else {
                                "0" // 默认使用第一个
                            }
                        }
                    }
                    Logger.i(TAG, "🎥 [摄像头] 尝试使用Camera1设备: $camera1Id (可用设备: ${camera1DeviceNames.joinToString()})")
                    camera1Enumerator.createCapturer(camera1Id, null)
                } else {
                    null
                }
            } catch (e2: Exception) {
                Logger.e(TAG, "🎥 [摄像头] Camera1也失败: ${e2.message}")
                null
            }
        }

        if (videoCapturer == null) {
            throw IllegalStateException("无法创建视频捕获器: $cameraId (Camera2和Camera1都失败)")
        }

        // 创建视频源
        val videoSource = peerConnectionFactory.createVideoSource(videoCapturer.isScreencast)
        Logger.i(TAG, "🎥 [摄像头] VideoSource已创建")

        // 获取期望分辨率
        val requestedResolution = Constants.VIDEO_RESOLUTIONS[WebRTCManager.getVideoResolution()]
            ?: Constants.VIDEO_RESOLUTIONS["720p"]!!

        // 检查摄像头支持的分辨率并选择最佳匹配
        val actualResolution = findBestSupportedResolution(cameraEnumerator, cameraId, requestedResolution)

        // 输出摄像头支持的所有分辨率（用于调试）
        logCameraSupportedResolutions(cameraEnumerator, cameraId)

        // 获取帧率
        val framerate = WebRTCManager.getVideoFramerate()

        if (actualResolution != requestedResolution) {
            Logger.w(TAG, "🎥 [摄像头] 摄像头不支持期望分辨率 ${requestedResolution.first}x${requestedResolution.second}")
            Logger.i(TAG, "🎥 [摄像头] 使用最佳匹配分辨率: ${actualResolution.first}x${actualResolution.second}")
        } else {
            Logger.i(TAG, "🎥 [摄像头] 配置参数: 分辨率=${actualResolution.first}x${actualResolution.second}, 帧率=$framerate fps")
        }

        // 启动视频捕获 - 使用安全的try-catch处理
        try {
            val surfaceTextureHelper = SurfaceTextureHelper.create("CaptureThread", rootEglBase.eglBaseContext)
            Logger.i(TAG, "🎥 [摄像头] SurfaceTextureHelper已创建")

            videoCapturer.initialize(surfaceTextureHelper, context, videoSource.capturerObserver)
            Logger.i(TAG, "🎥 [摄像头] VideoCapturer已初始化")

            videoCapturer.startCapture(actualResolution.first, actualResolution.second, framerate)
            Logger.i(TAG, "🎥 [摄像头] ✅ 摄像头捕获已启动: ${actualResolution.first}x${actualResolution.second}@${framerate}fps")

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头] 启动摄像头捕获失败: ${e.message}", e)

            // 安全地停止和清理资源
            try {
                videoCapturer.stopCapture()
                videoCapturer.dispose()
            } catch (cleanupException: Exception) {
                Logger.w(TAG, "🎥 [摄像头] 清理资源时出错: ${cleanupException.message}")
            }

            throw IllegalStateException("摄像头${cameraId}启动失败: ${e.message}", e)
        }

        return videoSource
    }

    /**
     * 查找摄像头支持的最佳分辨率
     */
    private fun findBestSupportedResolution(
        cameraEnumerator: Camera2Enumerator,
        cameraId: String,
        requestedResolution: Pair<Int, Int>
    ): Pair<Int, Int> {
        try {
            Logger.i(TAG, "🎥 [分辨率检查] 检查摄像头 $cameraId 支持的分辨率")

            // 获取摄像头支持的分辨率列表
            val supportedFormats = cameraEnumerator.getSupportedFormats(cameraId)

            if (supportedFormats.isNullOrEmpty()) {
                Logger.w(TAG, "🎥 [分辨率检查] 无法获取摄像头支持的分辨率，使用期望分辨率")
                return requestedResolution
            }

            Logger.i(TAG, "🎥 [分辨率检查] 摄像头支持 ${supportedFormats.size} 种格式")

            // 提取所有支持的分辨率
            val supportedResolutions = mutableListOf<Pair<Int, Int>>()
            for (format in supportedFormats) {
                val width = format.width
                val height = format.height
                supportedResolutions.add(Pair(width, height))
                Logger.d(TAG, "🎥 [分辨率检查] 支持分辨率: ${width}x${height}")
            }

            // 检查期望分辨率是否直接支持
            if (supportedResolutions.contains(requestedResolution)) {
                Logger.i(TAG, "🎥 [分辨率检查] ✅ 摄像头直接支持期望分辨率: ${requestedResolution.first}x${requestedResolution.second}")
                return requestedResolution
            }

            // 查找最接近的分辨率
            val bestMatch = findClosestResolution(supportedResolutions, requestedResolution)
            Logger.i(TAG, "🎥 [分辨率检查] 🔄 选择最接近的分辨率: ${bestMatch.first}x${bestMatch.second}")

            return bestMatch

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [分辨率检查] 检查分辨率失败: ${e.message}")
            Logger.w(TAG, "🎥 [分辨率检查] 回退到期望分辨率: ${requestedResolution.first}x${requestedResolution.second}")
            return requestedResolution
        }
    }

    /**
     * 查找最接近的分辨率（优先保证宽高比，避免画面变形）
     */
    private fun findClosestResolution(
        supportedResolutions: List<Pair<Int, Int>>,
        requestedResolution: Pair<Int, Int>
    ): Pair<Int, Int> {
        val requestedWidth = requestedResolution.first
        val requestedHeight = requestedResolution.second
        val requestedAspectRatio = requestedWidth.toFloat() / requestedHeight.toFloat()

        Logger.d(TAG, "🎥 [分辨率匹配] 期望分辨率: ${requestedWidth}x${requestedHeight}, 宽高比: $requestedAspectRatio")

        // 首先筛选出宽高比相近的分辨率（容差0.1）
        val aspectRatioTolerance = 0.1f
        val sameAspectRatioResolutions = supportedResolutions.filter { resolution ->
            val width = resolution.first
            val height = resolution.second
            val aspectRatio = width.toFloat() / height.toFloat()
            val aspectRatioDiff = kotlin.math.abs(aspectRatio - requestedAspectRatio)
            aspectRatioDiff <= aspectRatioTolerance
        }

        Logger.i(TAG, "🎥 [分辨率匹配] 找到 ${sameAspectRatioResolutions.size} 个相同宽高比的分辨率")

        // 如果有相同宽高比的分辨率，优先从中选择
        val candidateResolutions = if (sameAspectRatioResolutions.isNotEmpty()) {
            Logger.i(TAG, "🎥 [分辨率匹配] ✅ 优先选择相同宽高比的分辨率，避免画面变形")
            sameAspectRatioResolutions
        } else {
            Logger.w(TAG, "🎥 [分辨率匹配] ⚠️ 没有相同宽高比的分辨率，从所有分辨率中选择")
            supportedResolutions
        }

        var bestMatch = candidateResolutions[0]
        var bestScore = Float.MAX_VALUE

        for (resolution in candidateResolutions) {
            val width = resolution.first
            val height = resolution.second
            val aspectRatio = width.toFloat() / height.toFloat()

            val resolutionDiff = kotlin.math.abs(width * height - requestedWidth * requestedHeight).toFloat()
            val aspectRatioDiff = kotlin.math.abs(aspectRatio - requestedAspectRatio)

            // 如果是相同宽高比的分辨率，主要考虑分辨率差异
            val score = if (sameAspectRatioResolutions.isNotEmpty() && sameAspectRatioResolutions.contains(resolution)) {
                // 相同宽高比：90%权重给分辨率差异，10%给宽高比微调
                resolutionDiff * 0.9f + aspectRatioDiff * 100000 * 0.1f
            } else {
                // 不同宽高比：50%权重给分辨率差异，50%给宽高比差异
                resolutionDiff * 0.5f + aspectRatioDiff * 1000000 * 0.5f
            }

            Logger.d(TAG, "🎥 [分辨率匹配] ${width}x${height} (${String.format("%.2f", aspectRatio)}): 分辨率差异=$resolutionDiff, 宽高比差异=$aspectRatioDiff, 评分=$score")

            if (score < bestScore) {
                bestScore = score
                bestMatch = resolution
            }
        }

        val finalAspectRatio = bestMatch.first.toFloat() / bestMatch.second.toFloat()
        val aspectRatioMatch = kotlin.math.abs(finalAspectRatio - requestedAspectRatio) <= aspectRatioTolerance

        if (aspectRatioMatch) {
            Logger.i(TAG, "🎥 [分辨率匹配] ✅ 最佳匹配: ${bestMatch.first}x${bestMatch.second} (宽高比匹配，无变形)")
        } else {
            Logger.w(TAG, "🎥 [分辨率匹配] ⚠️ 最佳匹配: ${bestMatch.first}x${bestMatch.second} (宽高比不匹配，可能变形)")
        }

        return bestMatch
    }

    /**
     * 输出摄像头支持的所有分辨率（用于调试）
     */
    private fun logCameraSupportedResolutions(cameraEnumerator: Camera2Enumerator, cameraId: String) {
        try {
            val supportedFormats = cameraEnumerator.getSupportedFormats(cameraId)

            if (supportedFormats.isNullOrEmpty()) {
                Logger.w(TAG, "🎥 [支持分辨率] 无法获取摄像头 $cameraId 的支持分辨率")
                return
            }

            Logger.i(TAG, "🎥 [支持分辨率] 摄像头 $cameraId 支持的所有分辨率:")

            val resolutions = mutableListOf<String>()
            for (format in supportedFormats) {
                val width = format.width
                val height = format.height
                resolutions.add("${width}x${height}")
            }

            // 按分辨率大小排序并输出
            resolutions.sortedByDescending {
                val parts = it.split("x")
                parts[0].toInt() * parts[1].toInt()
            }.forEach { resolution ->
                Logger.i(TAG, "🎥 [支持分辨率] - $resolution")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [支持分辨率] 获取支持分辨率失败: ${e.message}")
        }
    }

    /**
     * 创建屏幕录制视频源
     */
    fun createScreenCaptureVideoSource(videoCapturer: VideoCapturer): VideoSource {
        Logger.i(TAG, "📱 [屏幕捕获] 开始创建屏幕录制视频源")

        // 创建视频源 - 注意这里传入true表示这是屏幕捕获
        val videoSource = peerConnectionFactory.createVideoSource(true)
        Logger.i(TAG, "📱 [屏幕捕获] VideoSource已创建，isScreencast=true")

        // 获取分辨率
        val resolution = Constants.VIDEO_RESOLUTIONS[WebRTCManager.getVideoResolution()]
            ?: Constants.VIDEO_RESOLUTIONS["720p"]!!

        // 获取帧率
        val framerate = WebRTCManager.getVideoFramerate()
        Logger.i(TAG, "📱 [屏幕捕获] 配置参数: 分辨率=${resolution.first}x${resolution.second}, 帧率=$framerate fps")

        // 获取编码器设置
        val codec = WebRTCManager.getVideoCodec()
        val bitrate = WebRTCManager.getVideoBitrate()
        Logger.i(TAG, "📱 [屏幕捕获] 编码设置: 编码器=$codec, 比特率=$bitrate kbps")

        // 启动视频捕获
        val surfaceTextureHelper = SurfaceTextureHelper.create("ScreenCaptureThread", rootEglBase.eglBaseContext)
        Logger.i(TAG, "📱 [屏幕捕获] SurfaceTextureHelper已创建")

        // 初始化视频捕获器
        videoCapturer.initialize(surfaceTextureHelper, context, videoSource.capturerObserver)
        Logger.i(TAG, "📱 [屏幕捕获] VideoCapturer已初始化")

        // 开始捕获 - 使用配置参数但保持固定
        videoCapturer.startCapture(resolution.first, resolution.second, framerate)
        Logger.i(TAG, "📱 [屏幕捕获] ✅ 屏幕捕获已启动 (配置固定): ${resolution.first}x${resolution.second}@${framerate}fps")
        Logger.i(TAG, "📱 [屏幕捕获] 💡 使用配置参数，但确保不波动")

        // 启动MediaCodec状态监控
        startMediaCodecMonitoring()

        // 启动配置参数监控 - 只记录配置，不频繁调节
        startAggressiveBitrateControl()

        // 连接建立时设置固定比特率，并定期强制维持
        mainHandler.postDelayed({
            Logger.i(TAG, "🔧 [初始配置] 连接建立后设置固定比特率")
            enforceFixedBitrate()

            // 启动定期强制固定码率任务，防止WebRTC内部重新启用自适应
            startPeriodicBitrateEnforcement()
        }, 2000) // 2秒后开始

        return videoSource
    }

    /**
     * 创建HDMI输入视频源
     */
    fun createHdmiVideoSource(devicePath: String): VideoSource {
        Logger.i(TAG, "🎥 [HDMI输入] 开始创建HDMI输入视频源: $devicePath")

        try {
            // 检查设备是否存在
            val deviceFile = java.io.File(devicePath)
            if (!deviceFile.exists()) {
                throw IllegalStateException("HDMI设备不存在: $devicePath")
            }

            // 创建视频源
            val videoSource = peerConnectionFactory.createVideoSource(false)

            // 创建HDMI视频捕获器（使用Camera2 API访问HDMI输入）
            val videoCapturer = createHdmiVideoCapturer(devicePath)

            // 配置视频参数
            val width = 1280
            val height = 720
            val fps = 30

            Logger.i(TAG, "🎥 [HDMI输入] 配置参数: 分辨率=${width}x${height}, 帧率=${fps} fps")

            // 创建SurfaceTextureHelper
            val surfaceTextureHelper = SurfaceTextureHelper.create("HdmiCaptureThread", rootEglBase.eglBaseContext)

            try {
                // 初始化视频捕获器
                videoCapturer.initialize(surfaceTextureHelper, context, videoSource.capturerObserver)

                // 启动捕获
                videoCapturer.startCapture(width, height, fps)

                Logger.i(TAG, "🎥 [HDMI输入] ✅ HDMI输入捕获已启动: ${width}x${height}@${fps}fps")

            } catch (e: Exception) {
                Logger.e(TAG, "🎥 [HDMI输入] ❌ HDMI捕获启动失败，可能设备不兼容", e)

                // 清理资源
                try {
                    videoCapturer.dispose()
                } catch (disposeException: Exception) {
                    Logger.w(TAG, "🎥 [HDMI输入] 清理捕获器失败: ${disposeException.message}")
                }

                try {
                    surfaceTextureHelper.dispose()
                } catch (disposeException: Exception) {
                    Logger.w(TAG, "🎥 [HDMI输入] 清理SurfaceTextureHelper失败: ${disposeException.message}")
                }

                throw IllegalStateException("HDMI设备不兼容或驱动问题: ${e.message}", e)
            }

            return videoSource
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [HDMI输入] 创建HDMI输入视频源失败", e)
            throw e
        }
    }

    /**
     * 创建USB采集卡视频源
     */
    fun createUsbCaptureVideoSource(devicePath: String): VideoSource {
        Logger.i(TAG, "🎥 [USB采集卡] 开始创建USB采集卡视频源: $devicePath")

        try {
            // 检查设备是否存在
            val deviceFile = java.io.File(devicePath)
            if (!deviceFile.exists()) {
                throw IllegalStateException("USB采集卡设备不存在: $devicePath")
            }

            // 创建视频源
            val videoSource = peerConnectionFactory.createVideoSource(false)

            // 创建USB采集卡视频捕获器
            val videoCapturer = createUsbCaptureVideoCapturer(devicePath)

            // 配置视频参数
            val width = 1280
            val height = 720
            val fps = 30

            Logger.i(TAG, "🎥 [USB采集卡] 配置参数: 分辨率=${width}x${height}, 帧率=${fps} fps")

            // 创建SurfaceTextureHelper
            val surfaceTextureHelper = SurfaceTextureHelper.create("UsbCaptureThread", rootEglBase.eglBaseContext)

            // 初始化视频捕获器
            videoCapturer.initialize(surfaceTextureHelper, context, videoSource.capturerObserver)

            // 启动捕获
            videoCapturer.startCapture(width, height, fps)

            Logger.i(TAG, "🎥 [USB采集卡] ✅ USB采集卡捕获已启动: ${width}x${height}@${fps}fps")

            return videoSource
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [USB采集卡] 创建USB采集卡视频源失败", e)
            throw e
        }
    }

    /**
     * 创建音频源
     */
    fun createAudioSource(): AudioSource {
        Logger.d(TAG, "创建音频源")

        val constraints = MediaConstraints()

        // 获取配置的音频源类型
        val audioSourceType = WebRTCManager.getAudioSource()
        Logger.i(TAG, "配置的音频源类型: $audioSourceType")

        // 记录音频源配置（移除不兼容的反射代码）
        Logger.i(TAG, "使用音频源类型: $audioSourceType")

        // 根据音频源类型设置不同的约束
        when (audioSourceType) {
            "video_input" -> {
                // 来自视频输入（采集卡音频数据）- 使用remote_submix作为安全选择
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", "remote_submix"))

                // 简化配置，避免复杂的采集卡检测导致崩溃
                Logger.i(TAG, "🎵 使用来自视频输入的音频源（简化模式）")

                // 禁用音频处理，保持原始音频质量
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "false"))

                Logger.i(TAG, "✅ 已设置来自视频输入的音频源（简化配置）")
            }
            "remote_submix" -> {
                // 尝试设置远程混音
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", "remote_submix"))

                // 尝试设置更多可能的参数
                constraints.optional.add(MediaConstraints.KeyValuePair("audioSource", "remote_submix"))
                constraints.optional.add(MediaConstraints.KeyValuePair("audio_source", "remote_submix"))

                // 禁用回声消除和噪声抑制，因为远程混音不需要这些
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "false"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "false"))
                Logger.i(TAG, "已设置远程混音音频源")
            }
            "voice_communication" -> {
                // 语音通信模式
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", "voice_communication"))
                constraints.optional.add(MediaConstraints.KeyValuePair("audioSource", "voice_communication"))
                constraints.optional.add(MediaConstraints.KeyValuePair("audio_source", "voice_communication"))

                constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "true"))
                Logger.i(TAG, "已设置语音通信音频源")
            }
            "microphone" -> {
                // 麦克风模式
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", "mic"))
                constraints.optional.add(MediaConstraints.KeyValuePair("audioSource", "mic"))
                constraints.optional.add(MediaConstraints.KeyValuePair("audio_source", "mic"))

                constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "true"))
                Logger.i(TAG, "已设置麦克风音频源")
            }
            else -> {
                // 默认设置
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "true"))
                constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "true"))
                Logger.i(TAG, "已设置默认音频源")
            }
        }

        // 检查音频权限
        val hasAudioPermission = checkAudioPermission()
        Logger.d(TAG, "音频权限状态: $hasAudioPermission")

        // 创建音频源
        val audioSource = peerConnectionFactory.createAudioSource(constraints)
        Logger.i(TAG, "音频源已创建，约束条件: ${constraints.mandatory}")

        // 尝试使用反射获取音频源的实际类型
        try {
            val audioSourceClass = audioSource.javaClass
            val fields = audioSourceClass.declaredFields
            for (field in fields) {
                field.isAccessible = true
                val value = field.get(audioSource)
                Logger.i(TAG, "音频源字段: ${field.name} = $value")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "获取音频源字段失败", e)
        }

        return audioSource
    }

    /**
     * 创建HDMI视频捕获器
     */
    private fun createHdmiVideoCapturer(devicePath: String): VideoCapturer {
        Logger.i(TAG, "🎥 [HDMI输入] 创建HDMI视频捕获器: $devicePath")

        // 使用Camera2Enumerator来访问HDMI输入设备
        val cameraEnumerator = Camera2Enumerator(context)

        // 获取所有可用设备
        val deviceNames = cameraEnumerator.deviceNames
        Logger.i(TAG, "🎥 [HDMI输入] 可用设备: ${deviceNames.joinToString(", ")}")

        // 查找HDMI输入设备
        var hdmiDeviceId: String? = null

        // 首先尝试使用已知的HDMI设备ID "100"（从诊断结果确认）
        if (deviceNames.contains("100")) {
            hdmiDeviceId = "100"
            Logger.i(TAG, "🎥 [HDMI输入] 使用已知的HDMI设备ID: 100")
        } else {
            // 如果没有设备ID "100"，查找可能的外部设备
            // 通常外部设备的ID会比较大，或者包含特殊字符
            for (deviceId in deviceNames) {
                try {
                    // 尝试检查设备是否为外部设备
                    // 外部设备通常ID较大或者不是标准的"0"、"1"
                    if (deviceId != "0" && deviceId != "1") {
                        // 尝试创建一个测试捕获器来验证设备
                        val testCapturer = cameraEnumerator.createCapturer(deviceId, null)
                        if (testCapturer != null) {
                            hdmiDeviceId = deviceId
                            Logger.i(TAG, "🎥 [HDMI输入] 找到可用的外部设备: $deviceId")
                            break
                        }
                    }
                } catch (e: Exception) {
                    Logger.d(TAG, "🎥 [HDMI输入] 设备 $deviceId 不可用: ${e.message}")
                }
            }

            // 如果还是没找到，使用第一个可用设备
            if (hdmiDeviceId == null) {
                hdmiDeviceId = deviceNames.firstOrNull()
                Logger.w(TAG, "🎥 [HDMI输入] 未找到专用外部设备，使用第一个可用设备: $hdmiDeviceId")
            }
        }

        if (hdmiDeviceId == null) {
            throw IllegalStateException("未找到HDMI输入设备")
        }

        // 创建HDMI输入捕获器
        val capturer = cameraEnumerator.createCapturer(hdmiDeviceId, object : CameraVideoCapturer.CameraEventsHandler {
            override fun onCameraError(errorDescription: String?) {
                Logger.e(TAG, "🎥 [HDMI输入] HDMI输入错误: $errorDescription")
            }

            override fun onCameraDisconnected() {
                Logger.w(TAG, "🎥 [HDMI输入] HDMI输入断开连接")
            }

            override fun onCameraFreezed(errorDescription: String?) {
                Logger.w(TAG, "🎥 [HDMI输入] HDMI输入冻结: $errorDescription")
            }

            override fun onCameraOpening(cameraName: String?) {
                Logger.i(TAG, "🎥 [HDMI输入] 正在打开HDMI输入: $cameraName")
            }

            override fun onFirstFrameAvailable() {
                Logger.i(TAG, "🎥 [HDMI输入] HDMI输入首帧可用")
            }

            override fun onCameraClosed() {
                Logger.i(TAG, "🎥 [HDMI输入] HDMI输入已关闭")
            }
        })

        if (capturer == null) {
            throw IllegalStateException("无法创建HDMI输入捕获器，设备ID: $hdmiDeviceId")
        }

        // 验证捕获器是否支持基本操作
        try {
            // 这里可以添加一些基本的验证，但要小心不要触发实际的硬件操作
            Logger.i(TAG, "🎥 [HDMI输入] ✅ HDMI捕获器创建成功，设备ID: $hdmiDeviceId")
        } catch (e: Exception) {
            Logger.w(TAG, "🎥 [HDMI输入] ⚠️ HDMI捕获器验证警告: ${e.message}")
        }

        return capturer
    }

    /**
     * 创建USB采集卡视频捕获器
     */
    private fun createUsbCaptureVideoCapturer(devicePath: String): VideoCapturer {
        Logger.i(TAG, "🎥 [USB采集卡] 创建USB采集卡视频捕获器: $devicePath")

        // 使用Camera2Enumerator来访问USB采集卡设备
        val cameraEnumerator = Camera2Enumerator(context)

        // 获取所有可用设备
        val deviceNames = cameraEnumerator.deviceNames
        Logger.i(TAG, "🎥 [USB采集卡] 可用设备: ${deviceNames.joinToString(", ")}")

        // 查找USB采集卡设备（排除已知的HDMI设备）
        var usbDeviceId: String? = null

        // 排除已知的HDMI设备ID "100"和标准摄像头ID "0"、"1"
        val candidateDevices = deviceNames.filter { it != "100" && it != "0" && it != "1" }

        if (candidateDevices.isNotEmpty()) {
            // 尝试第一个候选设备
            usbDeviceId = candidateDevices.first()
            Logger.i(TAG, "🎥 [USB采集卡] 使用候选USB采集卡设备: $usbDeviceId")
        } else {
            // 如果没有其他候选设备，尝试使用设备ID "1"
            if (deviceNames.contains("1")) {
                usbDeviceId = "1"
                Logger.i(TAG, "🎥 [USB采集卡] 使用备选设备ID: 1")
            } else {
                // 最后尝试使用最后一个设备（避免使用第一个，通常是主摄像头）
                usbDeviceId = deviceNames.lastOrNull()
                Logger.w(TAG, "🎥 [USB采集卡] 未找到专用USB采集卡，使用最后一个设备: $usbDeviceId")
            }
        }

        if (usbDeviceId == null) {
            throw IllegalStateException("未找到USB采集卡设备")
        }

        // 创建USB采集卡捕获器
        return cameraEnumerator.createCapturer(usbDeviceId, object : CameraVideoCapturer.CameraEventsHandler {
            override fun onCameraError(errorDescription: String?) {
                Logger.e(TAG, "🎥 [USB采集卡] USB采集卡错误: $errorDescription")
            }

            override fun onCameraDisconnected() {
                Logger.w(TAG, "🎥 [USB采集卡] USB采集卡断开连接")
            }

            override fun onCameraFreezed(errorDescription: String?) {
                Logger.w(TAG, "🎥 [USB采集卡] USB采集卡冻结: $errorDescription")
            }

            override fun onCameraOpening(cameraName: String?) {
                Logger.i(TAG, "🎥 [USB采集卡] 正在打开USB采集卡: $cameraName")
            }

            override fun onFirstFrameAvailable() {
                Logger.i(TAG, "🎥 [USB采集卡] USB采集卡首帧可用")
            }

            override fun onCameraClosed() {
                Logger.i(TAG, "🎥 [USB采集卡] USB采集卡已关闭")
            }
        }) ?: throw IllegalStateException("无法创建USB采集卡捕获器")
    }

    /**
     * 检查音频权限
     */
    private fun checkAudioPermission(): Boolean {
        val permission = android.Manifest.permission.RECORD_AUDIO
        val result = androidx.core.content.ContextCompat.checkSelfPermission(context, permission)
        val hasPermission = result == android.content.pm.PackageManager.PERMISSION_GRANTED
        Logger.d(TAG, "音频权限检查结果: $hasPermission")
        return hasPermission
    }

    /**
     * 关闭对等连接
     */
    fun closePeerConnection(peerId: String) {
        Logger.d(TAG, "关闭对等端 $peerId 的连接")

        // 停止连接监控
        stopConnectionMonitoring(peerId)

        // 清理连接状态
        connectionStates.remove(peerId)

        // 关闭数据通道
        dataChannels[peerId]?.close()
        dataChannels.remove(peerId)

        // 关闭对等连接
        peerConnections[peerId]?.close()
        peerConnections.remove(peerId)

        // 清理网络统计
        networkStatsMap.remove(peerId)

        // 清理帧数统计
        frameStatistics.remove(peerId)

        // 连接断开后动态调整剩余连接的编码参数
        //adjustAllConnectionsEncoding()

        Logger.i(TAG, "对等端 $peerId 连接已关闭，剩余连接数: ${peerConnections.size}")
    }

    /**
     * 强制清理所有连接 - 用于页面刷新等场景
     */
    fun forceCleanupAllConnections() {
        Logger.i(TAG, "🧹 强制清理所有连接，当前连接数: ${peerConnections.size}")

        // 复制连接ID列表，避免在迭代时修改集合
        val peerIds = peerConnections.keys.toList()

        peerIds.forEach { peerId ->
            Logger.d(TAG, "🧹 强制清理连接: $peerId")
            try {
                // 关闭数据通道
                dataChannels[peerId]?.close()
                dataChannels.remove(peerId)

                // 关闭对等连接
                peerConnections[peerId]?.close()
                peerConnections.remove(peerId)

                // 清理统计数据
                networkStatsMap.remove(peerId)
                frameStatistics.remove(peerId)
            } catch (e: Exception) {
                Logger.e(TAG, "清理连接 $peerId 时出错", e)
            }
        }

        Logger.i(TAG, "🧹 所有连接已清理完成，剩余连接数: ${peerConnections.size}")
    }

    /**
     * 启动性能监控和自动调整
     */
    private fun startPerformanceMonitoring() {
        Logger.i(TAG, "启动性能监控和自动调整")
        return //先不用
        // 每10秒检查一次性能并自动调整
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    val connectionCount = peerConnections.size
                    if (connectionCount > 0) {
                        // // 检查是否需要调整编码参数
                        // val shouldAdjust = framerateStabilizer?.shouldAdjustFramerate(connectionCount) ?: false

                        // if (shouldAdjust) {
                        //     Logger.i(TAG, "🔄 性能监控触发自动调整: 连接数=$connectionCount")
                        //     adjustAllConnectionsEncoding()
                        // }

                        // 记录当前性能状态
                        val (currentFramerate, currentBitrate) = adaptiveEncodingManager?.getOptimalSettings(connectionCount)
                            ?: Pair(WebRTCManager.getVideoFramerate(), WebRTCManager.getVideoBitrate())

                        Logger.d(TAG, "📈 性能状态: 连接数=$connectionCount, 当前帧率=$currentFramerate, 当前比特率=$currentBitrate kbps")
                    }

                    // 继续监控
                    mainHandler.postDelayed(this, 10000) // 10秒后再次检查
                } catch (e: Exception) {
                    Logger.e(TAG, "性能监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 10000)
                }
            }
        }, 5000) // 5秒后开始第一次检查
    }

    /**
     * 释放资源
     */
    fun release() {
        Logger.d(TAG, "释放WebRTC客户端资源")

        try {
            // 🎵 停止音频来源测试
            audioSourceTester.stopAudioSourceTest()
            // 关闭所有数据通道
            dataChannels.forEach { (_, channel) ->
                try {
                    channel.close()
                } catch (e: Exception) {
                    Logger.w(TAG, "关闭数据通道失败: ${e.message}")
                }
            }
            dataChannels.clear()

            // 关闭所有对等连接
            peerConnections.forEach { (_, pc) ->
                try {
                    pc.close()
                } catch (e: Exception) {
                    Logger.w(TAG, "关闭对等连接失败: ${e.message}")
                }
            }
            peerConnections.clear()

            // 安全释放媒体资源 - 包括共享资源
            safeDisposeVideoTrack(localVideoTrack)
            safeDisposeVideoSource(localVideoSource)
            safeDisposeAudioTrack(localAudioTrack)
            safeDisposeAudioSource(localAudioSource)

            // 安全释放共享媒体资源
            safeDisposeVideoTrack(sharedVideoTrack)
            safeDisposeVideoSource(sharedVideoSource)
            safeDisposeAudioTrack(sharedAudioTrack)
            safeDisposeAudioSource(sharedAudioSource)

            // 释放视频捕获器和辅助资源
            try {
                videoCapturer?.dispose()
            } catch (e: Exception) {
                Logger.w(TAG, "释放视频捕获器失败: ${e.message}")
            }

            try {
                surfaceTextureHelper?.dispose()
            } catch (e: Exception) {
                Logger.w(TAG, "释放SurfaceTextureHelper失败: ${e.message}")
            }

            // 重置状态
            isVideoSourceInitialized = false

            // 释放工厂
            try {
                peerConnectionFactory.dispose()
            } catch (e: Exception) {
                Logger.w(TAG, "释放PeerConnectionFactory失败: ${e.message}")
            }

            // 释放EGL上下文
            try {
                rootEglBase.release()
            } catch (e: Exception) {
                Logger.w(TAG, "释放EGL上下文失败: ${e.message}")
            }

            Logger.d(TAG, "WebRTC客户端资源释放完成")
        } catch (e: Exception) {
            Logger.e(TAG, "释放WebRTC客户端资源时发生错误", e)
        }
    }

    /**
     * 安全释放VideoTrack
     */
    private fun safeDisposeVideoTrack(videoTrack: VideoTrack?) {
        if (videoTrack == null) return

        try {
            videoTrack.dispose()
        } catch (e: Exception) {
            Logger.w(TAG, "释放VideoTrack失败: ${e.message}")
        }
    }

    /**
     * 安全释放VideoSource
     */
    private fun safeDisposeVideoSource(videoSource: VideoSource?) {
        if (videoSource == null) return

        try {
            // 检查MediaSource是否已经被释放
            val field = MediaSource::class.java.getDeclaredField("nativeSource")
            field.isAccessible = true
            val nativeSource = field.getLong(videoSource)

            if (nativeSource != 0L) {
                videoSource.dispose()
                Logger.d(TAG, "VideoSource已释放")
            } else {
                Logger.d(TAG, "VideoSource已经被释放，跳过dispose")
            }
        } catch (e: NoSuchFieldException) {
            // 如果无法访问nativeSource字段，尝试直接释放
            try {
                videoSource.dispose()
                Logger.d(TAG, "VideoSource已释放（直接方式）")
            } catch (e2: IllegalStateException) {
                if (e2.message?.contains("disposed") == true) {
                    Logger.d(TAG, "VideoSource已经被释放，忽略错误")
                } else {
                    Logger.w(TAG, "释放VideoSource失败: ${e2.message}")
                }
            } catch (e2: Exception) {
                Logger.w(TAG, "释放VideoSource失败: ${e2.message}")
            }
        } catch (e: Exception) {
            Logger.w(TAG, "检查VideoSource状态失败，尝试直接释放: ${e.message}")
            try {
                videoSource.dispose()
            } catch (e2: IllegalStateException) {
                if (e2.message?.contains("disposed") == true) {
                    Logger.d(TAG, "VideoSource已经被释放，忽略错误")
                } else {
                    Logger.w(TAG, "释放VideoSource失败: ${e2.message}")
                }
            } catch (e2: Exception) {
                Logger.w(TAG, "释放VideoSource失败: ${e2.message}")
            }
        }
    }

    /**
     * 安全释放AudioTrack
     */
    private fun safeDisposeAudioTrack(audioTrack: AudioTrack?) {
        if (audioTrack == null) return

        try {
            audioTrack.dispose()
        } catch (e: Exception) {
            Logger.w(TAG, "释放AudioTrack失败: ${e.message}")
        }
    }

    /**
     * 安全释放AudioSource
     */
    private fun safeDisposeAudioSource(audioSource: AudioSource?) {
        if (audioSource == null) return

        try {
            // 检查MediaSource是否已经被释放
            val field = MediaSource::class.java.getDeclaredField("nativeSource")
            field.isAccessible = true
            val nativeSource = field.getLong(audioSource)

            if (nativeSource != 0L) {
                audioSource.dispose()
                Logger.d(TAG, "AudioSource已释放")
            } else {
                Logger.d(TAG, "AudioSource已经被释放，跳过dispose")
            }
        } catch (e: NoSuchFieldException) {
            // 如果无法访问nativeSource字段，尝试直接释放
            try {
                audioSource.dispose()
                Logger.d(TAG, "AudioSource已释放（直接方式）")
            } catch (e2: IllegalStateException) {
                if (e2.message?.contains("disposed") == true) {
                    Logger.d(TAG, "AudioSource已经被释放，忽略错误")
                } else {
                    Logger.w(TAG, "释放AudioSource失败: ${e2.message}")
                }
            } catch (e2: Exception) {
                Logger.w(TAG, "释放AudioSource失败: ${e2.message}")
            }
        } catch (e: Exception) {
            Logger.w(TAG, "检查AudioSource状态失败，尝试直接释放: ${e.message}")
            try {
                audioSource.dispose()
            } catch (e2: IllegalStateException) {
                if (e2.message?.contains("disposed") == true) {
                    Logger.d(TAG, "AudioSource已经被释放，忽略错误")
                } else {
                    Logger.w(TAG, "释放AudioSource失败: ${e2.message}")
                }
            } catch (e2: Exception) {
                Logger.w(TAG, "释放AudioSource失败: ${e2.message}")
            }
        }
    }

    /**
     * 获取EGL上下文
     */
    fun getEglContext(): EglBase.Context {
        return rootEglBase.eglBaseContext
    }

    /**
     * 检查是否存在指定对等端的PeerConnection
     */
    fun hasPeerConnection(peerId: String): Boolean {
        return peerConnections.containsKey(peerId)
    }

    /**
     * 获取指定对等端的PeerConnection
     */
    fun getPeerConnection(peerId: String): PeerConnection? {
        return peerConnections[peerId]
    }

    /**
     * 设置视频编码参数
     */
    private fun setVideoEncodingParameters(peerConnection: PeerConnection) {
        try {
            Logger.i(TAG, "🔧 [编码参数] 开始设置视频编码参数")

            // 获取视频发送器
            val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }

            if (videoSender != null) {
                // 获取配置的编码器和比特率
                val codec = WebRTCManager.getVideoCodec()
                val bitrate = WebRTCManager.getVideoBitrate() * 1000 // 转换为bps
                val framerate = WebRTCManager.getVideoFramerate()

                Logger.i(TAG, "🔧 [编码参数] 目标设置: 编码器=$codec, 比特率=$bitrate bps, 帧率=$framerate fps")

                // 检查当前使用的编码器
                val currentParameters = videoSender.parameters
                Logger.i(TAG, "🔧 [编码参数] 当前编码参数: ${currentParameters.encodings.size} 个编码层")

                // 获取当前参数
                val parameters = videoSender.parameters
                val encodings = parameters.encodings

                Logger.d(TAG, "当前编码参数数量: ${encodings.size}")

                // 检查encodings列表是否为空
                if (encodings.isEmpty()) {
                    // 如果为空，添加一个新的编码参数
                    val newEncoding = RtpParameters.Encoding(null, true, null)
                    encodings.add(newEncoding)
                    Logger.d(TAG, "添加了新的编码参数")
                } else {
                    // 记录当前编码参数
                    for (i in encodings.indices) {
                        val encoding = encodings[i]
                        Logger.i(TAG, "🔧 [编码参数] 编码层[$i]: 最大比特率=${encoding.maxBitrateBps}, 最小比特率=${encoding.minBitrateBps}, 最大帧率=${encoding.maxFramerate}, 活跃=${encoding.active}")
                    }
                }

                // 设置完全固定的比特率（最大值=最小值=配置值），避免任何波动
                encodings[0].maxBitrateBps = bitrate  // 使用配置的精确值
                encodings[0].minBitrateBps = bitrate  // 与最大值相同，确保完全固定
                encodings[0].maxFramerate = framerate
                encodings[0].active = true

                Logger.i(TAG, "🔧 [编码参数] ✅ 设置完全固定比特率: ${bitrate/1000} kbps (无波动范围)")

                // 尝试设置目标比特率（如果支持）
                try {
                    // 某些WebRTC版本支持设置目标比特率
                    val targetBitrateField = encodings[0].javaClass.getDeclaredField("targetBitrateBps")
                    targetBitrateField.isAccessible = true
                    targetBitrateField.set(encodings[0], bitrate)
                    Logger.d(TAG, "已设置目标比特率: ${bitrate}bps")
                } catch (e: Exception) {
                    Logger.d(TAG, "当前WebRTC版本不支持目标比特率设置")
                }

                Logger.i(TAG, "🔧 [编码参数] 设置编码参数: 最大比特率=$bitrate bps, 最小比特率=$bitrate bps, 最大帧率=$framerate fps")

                // 应用更改后的参数
                val success = videoSender.setParameters(parameters)
                Logger.i(TAG, "🔧 [编码参数] 参数设置结果: ${if (success) "✅ 成功" else "❌ 失败"}")

                // 验证设置是否成功
                val updatedParameters = videoSender.parameters
                val updatedEncodings = updatedParameters.encodings
                if (updatedEncodings.isNotEmpty()) {
                    val encoding = updatedEncodings[0]
                    Logger.i(TAG, "🔧 [编码参数] ✅ 验证编码参数设置:")
                    Logger.i(TAG, "🔧 [编码参数]   - 实际最大比特率: ${encoding.maxBitrateBps} bps")
                    Logger.i(TAG, "🔧 [编码参数]   - 实际最小比特率: ${encoding.minBitrateBps} bps")
                    Logger.i(TAG, "🔧 [编码参数]   - 实际最大帧率: ${encoding.maxFramerate} fps")
                    Logger.i(TAG, "🔧 [编码参数]   - 实际活跃状态: ${encoding.active}")
                }

                Logger.i(TAG, "🔧 [编码参数] ✅ 视频编码参数设置完成")
            } else {
                Logger.w(TAG, "没有找到视频发送器，无法设置编码参数")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "设置视频编码参数失败", e)
        }
    }

    /**
     * 配置编码器以优化复杂画面处理
     */
    private fun configureEncoderForComplexScenes(peerConnection: PeerConnection) {
        try {
            Logger.d(TAG, "配置编码器以优化复杂画面处理")

            val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
            if (videoSender != null) {
                val parameters = videoSender.parameters
                val encodings = parameters.encodings

                if (encodings.isNotEmpty()) {
                    val bitrate = WebRTCManager.getVideoBitrate() * 1000 // 转换为bps
                    val framerate = WebRTCManager.getVideoFramerate()

                    // 设置完全固定的比特率参数，避免任何调整
                    encodings[0].maxBitrateBps = bitrate  // 使用配置的精确值
                    encodings[0].minBitrateBps = bitrate  // 与最大值相同，确保完全固定
                    encodings[0].maxFramerate = framerate
                    encodings[0].active = true

                    Logger.i(TAG, "🔧 [复杂画面编码] ✅ 设置完全固定比特率: ${bitrate/1000} kbps (无波动)")

                    // 尝试禁用自适应分辨率
                    try {
                        val scaleResolutionDownByField = encodings[0].javaClass.getDeclaredField("scaleResolutionDownBy")
                        scaleResolutionDownByField.isAccessible = true
                        scaleResolutionDownByField.set(encodings[0], 1.0) // 不缩放分辨率
                        Logger.d(TAG, "已禁用自适应分辨率缩放")
                    } catch (e: Exception) {
                        Logger.d(TAG, "当前WebRTC版本不支持分辨率缩放控制")
                    }

                    videoSender.parameters = parameters
                    Logger.d(TAG, "已配置编码器优化复杂画面: 最大比特率=${(bitrate * 2)}bps, 最小比特率=${bitrate}bps")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "配置编码器失败", e)
        }
    }

    /**
     * 启动网速监控
     */
    private fun startNetworkSpeedMonitoring() {
        if (networkMonitoringStarted) {
            Logger.d(TAG, "网速监控已经启动，跳过重复启动")
            return
        }

        networkMonitoringStarted = true
        Logger.i(TAG, "启动网速监控")

        // 立即执行一次，然后每5秒监控一次网速
        mainHandler.post(object : Runnable {
            override fun run() {
                try {
                    if (peerConnections.isNotEmpty()) {
                        // 只监控已连接的对等端
                        val activeConnections = peerConnections.filter { (_, peerConnection) ->
                            peerConnection.connectionState() == PeerConnection.PeerConnectionState.CONNECTED
                        }

                        if (activeConnections.isNotEmpty()) {
                            activeConnections.forEach { (peerId, peerConnection) ->
                                updateNetworkSpeed(peerId, peerConnection)
                            }
                        } else {
                            Logger.d(TAG, "没有活跃连接，跳过网速监控")
                        }
                    } else {
                        Logger.d(TAG, "没有PeerConnection，跳过网速监控")
                    }

                    // 继续下一次监控
                    mainHandler.postDelayed(this, 5000) // 5秒后再次监控
                } catch (e: Exception) {
                    Logger.e(TAG, "网速监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 5000)
                }
            }
        })
    }

    /**
     * 更新网速统计和累计流量（使用真实WebRTC统计API）
     */
    private fun updateNetworkSpeed(peerId: String, peerConnection: PeerConnection) {
        try {
            // 获取或创建网络统计
            val stats = networkStatsMap.getOrPut(peerId) {
                NetworkStats().apply {
                    connectionStartTime = System.currentTimeMillis()
                }
            }
            val currentTimestamp = System.currentTimeMillis()

            // 如果是第一次统计，初始化连接开始时间
            if (stats.connectionStartTime == 0L) {
                stats.connectionStartTime = currentTimestamp
            }

            // 使用WebRTC真实统计API
            peerConnection.getStats(object : RTCStatsCollectorCallback {
                override fun onStatsDelivered(reports: RTCStatsReport?) {
                    mainHandler.post {
                        try {
                            processWebRTCStats(peerId, reports, stats, currentTimestamp)
                        } catch (e: Exception) {
                            Logger.e(TAG, "处理WebRTC统计数据失败", e)
                        }
                    }
                }
            })

        } catch (e: Exception) {
            Logger.e(TAG, "更新对等端 $peerId 网速统计失败", e)
        }
    }

    /**
     * 处理WebRTC统计数据
     */
    private fun processWebRTCStats(peerId: String, reports: RTCStatsReport?, stats: NetworkStats, currentTimestamp: Long) {
        try {
            var currentBytesSent = 0L
            var currentBytesReceived = 0L
            var currentPacketsSent = 0L
            var currentPacketsReceived = 0L

            // 遍历统计报告
            reports?.statsMap?.forEach { (id, report) ->
                when (report.type) {
                    "outbound-rtp" -> {
                        // 发送统计
                        val bytesSent = report.members["bytesSent"] as? Number
                        val packetsSent = report.members["packetsSent"] as? Number
                        if (bytesSent != null) {
                            currentBytesSent += bytesSent.toLong()
                        }
                        if (packetsSent != null) {
                            currentPacketsSent += packetsSent.toLong()
                        }
                        Logger.d(TAG, "outbound-rtp $id: bytesSent=${bytesSent}, packetsSent=${packetsSent}")
                    }
                    "inbound-rtp" -> {
                        // 接收统计
                        val bytesReceived = report.members["bytesReceived"] as? Number
                        val packetsReceived = report.members["packetsReceived"] as? Number
                        if (bytesReceived != null) {
                            currentBytesReceived += bytesReceived.toLong()
                        }
                        if (packetsReceived != null) {
                            currentPacketsReceived += packetsReceived.toLong()
                        }
                        Logger.d(TAG, "inbound-rtp $id: bytesReceived=${bytesReceived}, packetsReceived=${packetsReceived}")
                    }
                    "transport" -> {
                        // 传输层统计
                        val bytesSent = report.members["bytesSent"] as? Number
                        val bytesReceived = report.members["bytesReceived"] as? Number
                        if (bytesSent != null) {
                            currentBytesSent = maxOf(currentBytesSent, bytesSent.toLong())
                        }
                        if (bytesReceived != null) {
                            currentBytesReceived = maxOf(currentBytesReceived, bytesReceived.toLong())
                        }
                        Logger.d(TAG, "transport $id: bytesSent=${bytesSent}, bytesReceived=${bytesReceived}")
                    }
                }
            }

            // 初始化基准值
            if (stats.initialBytesSent == 0L && currentBytesSent > 0) {
                stats.initialBytesSent = currentBytesSent
                stats.initialBytesReceived = currentBytesReceived
                Logger.i(TAG, "对等端 $peerId 初始化流量基准: 发送=${currentBytesSent}, 接收=${currentBytesReceived}")
            }

            // 计算累计流量（减去初始值）
            stats.totalBytesSent = maxOf(0, currentBytesSent - stats.initialBytesSent)
            stats.totalBytesReceived = maxOf(0, currentBytesReceived - stats.initialBytesReceived)

            // 计算实时速度
            if (stats.lastTimestamp > 0 && stats.lastBytesSent > 0) {
                val timeDiffSeconds = (currentTimestamp - stats.lastTimestamp) / 1000.0
                if (timeDiffSeconds > 0) {
                    val bytesSentDiff = currentBytesSent - stats.lastBytesSent
                    val bytesReceivedDiff = currentBytesReceived - stats.lastBytesReceived

                    stats.sendSpeedKbps = (bytesSentDiff / timeDiffSeconds) / 1024.0
                    stats.receiveSpeedKbps = (bytesReceivedDiff / timeDiffSeconds) / 1024.0
                }
            }

            // 更新上次记录的值
            stats.lastBytesSent = currentBytesSent
            stats.lastBytesReceived = currentBytesReceived
            stats.lastTimestamp = currentTimestamp

            // 计算连接持续时间
            val connectionDurationSeconds = (currentTimestamp - stats.connectionStartTime) / 1000.0
            val connectionDurationMinutes = connectionDurationSeconds / 60.0

            // 格式化累计流量
            val totalSentMB = stats.totalBytesSent / (1024.0 * 1024.0)
            val totalReceivedMB = stats.totalBytesReceived / (1024.0 * 1024.0)

            // 记录详细的网速和流量日志
            Logger.i(TAG, "对等端 $peerId 网速: 上传 ${String.format("%.1f", stats.sendSpeedKbps)} KB/s, 下载 ${String.format("%.1f", stats.receiveSpeedKbps)} KB/s")
            Logger.i(TAG, "对等端 $peerId 累计流量[真实]: 上传 ${String.format("%.2f", totalSentMB)} MB, 下载 ${String.format("%.2f", totalReceivedMB)} MB, 连接时长 ${String.format("%.1f", connectionDurationMinutes)} 分钟")

            // 添加数据包统计
            if (currentPacketsSent > 0 || currentPacketsReceived > 0) {
                Logger.d(TAG, "对等端 $peerId 数据包统计: 发送 $currentPacketsSent 包, 接收 $currentPacketsReceived 包")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "处理对等端 $peerId WebRTC统计数据失败", e)
        }
    }

    /**
     * 获取网速信息
     */
    fun getNetworkSpeed(peerId: String): NetworkStats? {
        return networkStatsMap[peerId]
    }

    /**
     * 获取累计流量统计信息
     */
    fun getTotalTrafficStats(peerId: String): String {
        val stats = networkStatsMap[peerId] ?: return "无统计数据"

        val totalSentMB = stats.totalBytesSent / (1024.0 * 1024.0)
        val totalReceivedMB = stats.totalBytesReceived / (1024.0 * 1024.0)
        val connectionDurationMinutes = (System.currentTimeMillis() - stats.connectionStartTime) / (1000.0 * 60.0)

        return "累计流量: 上传 ${String.format("%.2f", totalSentMB)} MB, 下载 ${String.format("%.2f", totalReceivedMB)} MB, 连接时长 ${String.format("%.1f", connectionDurationMinutes)} 分钟"
    }

    /**
     * 获取所有连接的累计流量统计
     */
    fun getAllTrafficStats(): String {
        if (networkStatsMap.isEmpty()) {
            return "无活跃连接"
        }

        val sb = StringBuilder()
        sb.append("所有连接累计流量统计:\n")

        var totalSentMB = 0.0
        var totalReceivedMB = 0.0

        networkStatsMap.forEach { (peerId, stats) ->
            val sentMB = stats.totalBytesSent / (1024.0 * 1024.0)
            val receivedMB = stats.totalBytesReceived / (1024.0 * 1024.0)
            val durationMinutes = (System.currentTimeMillis() - stats.connectionStartTime) / (1000.0 * 60.0)

            totalSentMB += sentMB
            totalReceivedMB += receivedMB

            sb.append("- $peerId: 上传 ${String.format("%.2f", sentMB)} MB, 下载 ${String.format("%.2f", receivedMB)} MB, 时长 ${String.format("%.1f", durationMinutes)} 分钟\n")
        }

        sb.append("总计: 上传 ${String.format("%.2f", totalSentMB)} MB, 下载 ${String.format("%.2f", totalReceivedMB)} MB")

        return sb.toString()
    }

    /**
     * 启动累计流量统计日志打印
     */
    private fun startTrafficStatsLogging() {
        if (trafficStatsLoggingStarted) {
            Logger.d(TAG, "累计流量统计日志已经启动，跳过重复启动")
            return
        }

        trafficStatsLoggingStarted = true
        Logger.i(TAG, "启动累计流量统计日志打印")

        // 每60秒打印一次累计流量统计
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    if (networkStatsMap.isNotEmpty()) {
                        Logger.i(TAG, "=== 累计流量统计报告 ===")

                        var totalSentMB = 0.0
                        var totalReceivedMB = 0.0

                        networkStatsMap.forEach { (peerId, stats) ->
                            val sentMB = stats.totalBytesSent / (1024.0 * 1024.0)
                            val receivedMB = stats.totalBytesReceived / (1024.0 * 1024.0)
                            val durationMinutes = (System.currentTimeMillis() - stats.connectionStartTime) / (1000.0 * 60.0)
                            val avgUploadSpeedKbps = if (durationMinutes > 0) (sentMB * 1024) / (durationMinutes * 60 / 1000) else 0.0

                            totalSentMB += sentMB
                            totalReceivedMB += receivedMB

                            Logger.i(TAG, "对等端 $peerId: 上传 ${String.format("%.2f", sentMB)} MB, 下载 ${String.format("%.2f", receivedMB)} MB, 连接时长 ${String.format("%.1f", durationMinutes)} 分钟, 平均上传速度 ${String.format("%.1f", avgUploadSpeedKbps)} KB/s")
                        }

                        Logger.i(TAG, "总计流量: 上传 ${String.format("%.2f", totalSentMB)} MB, 下载 ${String.format("%.2f", totalReceivedMB)} MB")
                        Logger.i(TAG, "=== 累计流量统计报告结束 ===")
                    } else {
                        Logger.d(TAG, "没有活跃连接，跳过累计流量统计")
                    }

                    // 继续下一次统计
                    mainHandler.postDelayed(this, 60000) // 60秒后再次统计
                } catch (e: Exception) {
                    Logger.e(TAG, "累计流量统计失败", e)
                    // 即使出错也要继续统计
                    mainHandler.postDelayed(this, 60000)
                }
            }
        }, 60000) // 60秒后开始第一次统计
    }

    /**
     * 定期强制设置比特率，防止自适应算法降低质量
     */
    private fun startBitrateMonitoring() {
        return // 先不使用
        if (bitrateMonitoringStarted) {
            Logger.d(TAG, "比特率监控已经启动，跳过重复启动")
            return
        }

        bitrateMonitoringStarted = true
        Logger.i(TAG, "启动比特率监控")

        // 每120秒检查并重新设置比特率（减少频率）
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    Logger.d(TAG, "定期检查和重新设置比特率")

                    peerConnections.forEach { (peerId, peerConnection) ->
                        // 获取网速和流量信息
                        val networkStats = networkStatsMap[peerId]
                        val speedAndTrafficInfo = if (networkStats != null) {
                            val totalSentMB = networkStats.totalBytesSent / (1024.0 * 1024.0)
                            val totalReceivedMB = networkStats.totalBytesReceived / (1024.0 * 1024.0)
                            val connectionDurationMinutes = (System.currentTimeMillis() - networkStats.connectionStartTime) / (1000.0 * 60.0)

                            "当前网速: 上传 ${String.format("%.1f", networkStats.sendSpeedKbps)} KB/s, 下载 ${String.format("%.1f", networkStats.receiveSpeedKbps)} KB/s | " +
                            "累计流量: 上传 ${String.format("%.2f", totalSentMB)} MB, 下载 ${String.format("%.2f", totalReceivedMB)} MB | " +
                            "连接时长: ${String.format("%.1f", connectionDurationMinutes)} 分钟"
                        } else {
                            "网速和流量统计不可用"
                        }

                        // 简化比特率监控，直接重新设置编码参数
                        Logger.d(TAG, "为对等端 $peerId 重新设置编码参数")
                        Logger.i(TAG, "对等端 $peerId 统计信息: $speedAndTrafficInfo")

                        // 无论如何都重新设置一次比特率，确保不被自适应算法降低
                        setVideoEncodingParameters(peerConnection)

                        // 重新配置编码器以优化复杂画面处理
                        configureEncoderForComplexScenes(peerConnection)
                    }

                    // 继续下一次检查
                    mainHandler.postDelayed(this, 120000) // 120秒后再次检查
                } catch (e: Exception) {
                    Logger.e(TAG, "比特率监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 120000)
                }
            }
        }, 120000) // 120秒后开始第一次检查
    }

    /**
     * 启动连接状态监控
     */
    private fun startConnectionMonitoring(peerId: String) {
        Logger.i(TAG, "🔍 [连接监控] 启动对等端 $peerId 的连接状态监控")

        // 初始化连接状态
        connectionStates[peerId] = ConnectionState.NEW

        // 每15秒检查一次连接状态（减少频率）
        val checkRunnable = object : Runnable {
            override fun run() {
                try {
                    val peerConnection = peerConnections[peerId]
                    if (peerConnection != null) {
                        // 如果连接已建立，停止监控
                        if (isConnectionEstablished(peerId)) {
                            Logger.i(TAG, "🔍 [连接监控] 对等端 $peerId 连接已建立，停止监控")
                            return
                        }

                        val connectionState = peerConnection.connectionState()
                        val iceConnectionState = peerConnection.iceConnectionState()
                        val signalingState = peerConnection.signalingState()

                        // 只在连接未建立时输出详细日志，减少日志噪音
                        Logger.d(TAG, "🔍 [连接监控] 对等端 $peerId 状态: 连接=$connectionState, ICE=$iceConnectionState")

                        // 检查是否有远程描述
                        val hasRemoteDescription = peerConnection.remoteDescription != null

                        // 如果连接状态长时间停留在NEW，可能是没有收到Offer
                        if (connectionState == PeerConnection.PeerConnectionState.NEW &&
                            signalingState == PeerConnection.SignalingState.STABLE &&
                            !hasRemoteDescription) {
                            Logger.w(TAG, "🔍 [连接监控] ⚠️ 连接长时间停留在NEW状态，可能没有收到Web端的Offer")
                        }

                        // 只在连接未建立时继续监控
                        if (connectionState == PeerConnection.PeerConnectionState.NEW ||
                            connectionState == PeerConnection.PeerConnectionState.CONNECTING) {
                            mainHandler.postDelayed(this, 15000) // 改为15秒检查一次
                        } else {
                            Logger.i(TAG, "🔍 [连接监控] 对等端 $peerId 连接状态已稳定，停止监控")
                        }
                    } else {
                        Logger.w(TAG, "🔍 [连接监控] 对等端 $peerId 的PeerConnection已不存在，停止监控")
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "🔍 [连接监控] 监控失败", e)
                    // 出错时停止监控，避免无限循环
                    Logger.w(TAG, "🔍 [连接监控] 由于错误停止监控")
                }
            }
        }

        // 保存监控任务引用
        connectionMonitoringTasks[peerId] = checkRunnable

        // 延迟10秒开始第一次检查，给连接建立一些时间
        mainHandler.postDelayed(checkRunnable, 10000)
    }

    /**
     * 启动MediaCodec状态监控
     */
    private fun startMediaCodecMonitoring() {
        Logger.i(TAG, "📊 [编码监控] 启动编码参数状态监控")

        // 每2分钟检查一次编码状态，仅记录不调节
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    // 只有在有活跃连接时才进行检查
                    if (peerConnections.isNotEmpty()) {
                        // 检查比特率状态
                        checkBitrateStability()
                    } else {
                        Logger.d(TAG, "📊 [编码监控] 没有活跃连接，跳过编码监控")
                    }

                    // 继续监控
                    mainHandler.postDelayed(this, 120000) // 2分钟后再次检查
                } catch (e: Exception) {
                    Logger.e(TAG, "📊 [编码监控] 监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 120000)
                }
            }
        }, 120000) // 2分钟后开始第一次检查

        // 启动详细的帧数监控
        startFrameMonitoring()
    }

    /**
     * 启动帧数监控
     */
    private fun startFrameMonitoring() {
        Logger.i(TAG, "🎬 [帧数监控] 启动详细帧数监控")

        // 每30秒检查一次帧数统计
        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    // 只有在有活跃连接时才进行检查
                    if (peerConnections.isNotEmpty()) {
                        // 检查所有连接的帧数统计
                        checkFrameStatistics()

                        // 检查视频源状态
                        checkVideoSourceStatus()
                    } else {
                        Logger.d(TAG, "🎬 [帧数监控] 没有活跃连接，跳过帧数监控")
                    }

                    // 继续监控
                    mainHandler.postDelayed(this, 30000) // 30秒后再次检查
                } catch (e: Exception) {
                    Logger.e(TAG, "🎬 [帧数监控] 监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 30000)
                }
            }
        }, 30000) // 30秒后开始第一次检查
    }

    /**
     * 检查视频源状态
     */
    private fun checkVideoSourceStatus() {
        try {
            Logger.i(TAG, "🎥 [视频源检查] ===== 视频源状态检查 =====")

            // 检查当前视频源类型
            val currentVideoSource = WebRTCManager.getVideoSourceType()
            Logger.i(TAG, "🎥 [视频源检查] 当前配置的视频源: $currentVideoSource")

            // 检查共享视频源
            val sharedSource = sharedVideoSource
            val sharedTrack = sharedVideoTrack
            val localSource = localVideoSource
            val localTrack = localVideoTrack

            Logger.i(TAG, "🎥 [视频源检查] 共享视频源: ${if (sharedSource != null) "存在" else "不存在"}")
            Logger.i(TAG, "🎥 [视频源检查] 共享视频轨道: ${if (sharedTrack != null) "存在" else "不存在"}")
            Logger.i(TAG, "🎥 [视频源检查] 本地视频源: ${if (localSource != null) "存在" else "不存在"}")
            Logger.i(TAG, "🎥 [视频源检查] 本地视频轨道: ${if (localTrack != null) "存在" else "不存在"}")
            Logger.i(TAG, "🎥 [视频源检查] 视频源初始化状态: $isVideoSourceInitialized")

            // 根据视频源类型进行特定检查
            when (currentVideoSource) {
                "camera" -> {
                    val cameraId = WebRTCManager.getCameraId()
                    Logger.i(TAG, "🎥 [视频源检查] 摄像头模式 - 摄像头ID: $cameraId")

                    // 检查摄像头是否可用
                    val cameraEnumerator = Camera2Enumerator(context)
                    val deviceNames = cameraEnumerator.deviceNames.toList()
                    Logger.i(TAG, "🎥 [视频源检查] 可用摄像头列表: $deviceNames")

                    if (deviceNames.contains(cameraId)) {
                        Logger.i(TAG, "🎥 [视频源检查] ✅ 摄像头ID $cameraId 可用")
                    } else {
                        Logger.e(TAG, "🎥 [视频源检查] ❌ 摄像头ID $cameraId 不可用")
                        Logger.e(TAG, "🎥 [视频源检查] ❌ 可能需要重新选择摄像头")
                    }
                }
                "screen" -> {
                    Logger.i(TAG, "🎥 [视频源检查] 屏幕录制模式")

                    // 检查是否有活跃的屏幕录制
                    val hasActiveScreenCapture = sharedVideoSource != null || localVideoSource != null
                    Logger.i(TAG, "🎥 [视频源检查] 屏幕录制状态: ${if (hasActiveScreenCapture) "活跃" else "未活跃"}")

                    if (!hasActiveScreenCapture) {
                        Logger.e(TAG, "🎥 [视频源检查] ❌ 屏幕录制未启动或已停止")
                        Logger.e(TAG, "🎥 [视频源检查] ❌ 可能需要重新启动屏幕录制")
                    }
                }
                else -> {
                    Logger.w(TAG, "🎥 [视频源检查] 未知的视频源类型: $currentVideoSource")
                }
            }

            // 检查视频轨道状态
            val activeTrack = sharedTrack ?: localTrack
            if (activeTrack != null) {
                val trackState = activeTrack.state()
                val trackEnabled = activeTrack.enabled()
                Logger.i(TAG, "🎥 [视频源检查] 活跃视频轨道状态: $trackState")
                Logger.i(TAG, "🎥 [视频源检查] 活跃视频轨道启用: $trackEnabled")
                Logger.i(TAG, "🎥 [视频源检查] 活跃视频轨道ID: ${activeTrack.id()}")

                if (trackState != MediaStreamTrack.State.LIVE) {
                    Logger.e(TAG, "🎥 [视频源检查] ❌ 视频轨道状态异常: $trackState")
                }

                if (!trackEnabled) {
                    Logger.e(TAG, "🎥 [视频源检查] ❌ 视频轨道已禁用")
                }
            } else {
                Logger.e(TAG, "🎥 [视频源检查] ❌ 没有可用的视频轨道")
            }

            // 检查PeerConnection中的发送器状态
            peerConnections.forEach { (peerId, peerConnection) ->
                Logger.i(TAG, "🎥 [视频源检查] 检查对等端 $peerId 的发送器状态")

                val senders = peerConnection.senders
                var hasVideoSender = false

                senders.forEach { sender ->
                    val track = sender.track()
                    if (track != null && track.kind() == "video") {
                        hasVideoSender = true
                        val senderTrackState = track.state()
                        val senderTrackEnabled = track.enabled()

                        Logger.i(TAG, "🎥 [视频源检查] 对等端 $peerId 视频发送器:")
                        Logger.i(TAG, "🎥 [视频源检查]   - 轨道ID: ${track.id()}")
                        Logger.i(TAG, "🎥 [视频源检查]   - 轨道状态: $senderTrackState")
                        Logger.i(TAG, "🎥 [视频源检查]   - 轨道启用: $senderTrackEnabled")

                        if (senderTrackState != MediaStreamTrack.State.LIVE) {
                            Logger.e(TAG, "🎥 [视频源检查] ❌ 对等端 $peerId 视频轨道状态异常: $senderTrackState")
                        }

                        if (!senderTrackEnabled) {
                            Logger.e(TAG, "🎥 [视频源检查] ❌ 对等端 $peerId 视频轨道已禁用")
                        }
                    }
                }

                if (!hasVideoSender) {
                    Logger.e(TAG, "🎥 [视频源检查] ❌ 对等端 $peerId 没有视频发送器")
                }
            }

            Logger.i(TAG, "🎥 [视频源检查] ===== 视频源状态检查完成 =====")

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [视频源检查] 检查视频源状态失败", e)
        }
    }

    // 存储帧数统计的Map
    private val frameStatistics = mutableMapOf<String, MutableMap<String, Any>>()

    /**
     * 存储帧数发送统计
     */
    private fun storeFramesSent(peerId: String, framesSent: Long) {
        if (!frameStatistics.containsKey(peerId)) {
            frameStatistics[peerId] = mutableMapOf()
        }
        frameStatistics[peerId]!!["framesSent"] = framesSent
    }

    /**
     * 获取存储的帧数发送统计
     */
    private fun getStoredFramesSent(peerId: String): Long {
        return frameStatistics[peerId]?.get("framesSent") as? Long ?: 0L
    }

    /**
     * 存储时间统计
     */
    private fun storeTime(peerId: String, time: Long) {
        if (!frameStatistics.containsKey(peerId)) {
            frameStatistics[peerId] = mutableMapOf()
        }
        frameStatistics[peerId]!!["time"] = time
    }

    /**
     * 获取存储的时间统计
     */
    private fun getStoredTime(peerId: String): Long {
        return frameStatistics[peerId]?.get("time") as? Long ?: 0L
    }

    /**
     * 检查帧数统计
     */
    private fun checkFrameStatistics() {
        try {
            peerConnections.forEach { (peerId, peerConnection) ->
                // 只统计已连接的对等端
                if (peerConnection.connectionState() != PeerConnection.PeerConnectionState.CONNECTED) {
                    return@forEach
                }

                Logger.i(TAG, "🎬 [帧数监控] ===== 对等端 $peerId 帧数统计 =====")

                // 获取发送器统计
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    try {
                        peerConnection.getStats(videoSender) { statsReport ->
                            statsReport?.let { report ->
                                var hasValidData = false
                                report.statsMap.forEach { (key, stats) ->
                                    when (stats.type) {
                                        "outbound-rtp" -> {
                                            val framesSent = stats.members["framesSent"]?.toString()?.toLongOrNull()
                                            val framesEncoded = stats.members["framesEncoded"]?.toString()?.toLongOrNull()

                                            // 只有当有实际数据传输时才显示统计
                                            if (framesSent != null && framesSent > 0) {
                                                hasValidData = true
                                            }
                                            val keyFramesEncoded = stats.members["keyFramesEncoded"]?.toString()?.toLongOrNull()
                                            val totalEncodeTime = stats.members["totalEncodeTime"]?.toString()?.toDoubleOrNull()
                                            val bytesSent = stats.members["bytesSent"]?.toString()?.toLongOrNull()
                                            val packetsSent = stats.members["packetsSent"]?.toString()?.toLongOrNull()
                                            val retransmittedPacketsSent = stats.members["retransmittedPacketsSent"]?.toString()?.toLongOrNull()

                                            // 只有当有实际数据传输时才显示详细统计
                                            if (framesSent != null && framesSent > 0) {
                                                Logger.i(TAG, "🎬 [帧数监控] 📤 发送统计:")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 已发送帧数: ${framesSent}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 已编码帧数: ${framesEncoded ?: "N/A"}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 关键帧数: ${keyFramesEncoded ?: "N/A"}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 总编码时间: ${totalEncodeTime?.let { "${(it * 1000).toInt()} ms" } ?: "N/A"}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 已发送字节: ${bytesSent?.let { "${it / 1024} KB" } ?: "N/A"}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 已发送包数: ${packetsSent ?: "N/A"}")
                                                Logger.i(TAG, "🎬 [帧数监控]   - 重传包数: ${retransmittedPacketsSent ?: "N/A"}")
                                            } else {
                                                Logger.e(TAG, "🎬 [帧数监控] ❌ 发送帧数为0，视频流可能未启动")
                                                Logger.e(TAG, "🎬 [帧数监控] ❌ 检查视频捕获是否正常启动")
                                                checkVideoSourceStatus()
                                            }

                                            // 计算平均编码时间
                                            if (totalEncodeTime != null && framesEncoded != null && framesEncoded > 0) {
                                                val avgEncodeTime = (totalEncodeTime / framesEncoded * 1000)
                                                Logger.i(TAG, "🎬 [帧数监控]   - 平均编码时间: ${String.format("%.2f", avgEncodeTime)} ms/帧")

                                                if (avgEncodeTime > 16.67) { // 超过60fps的单帧时间
                                                    Logger.w(TAG, "🎬 [帧数监控] ⚠️ 编码性能警告: 平均编码时间过长，可能导致帧率下降")
                                                }
                                            }

                                            // 检查重传率
                                            if (packetsSent != null && retransmittedPacketsSent != null && packetsSent > 0) {
                                                val retransmissionRate = (retransmittedPacketsSent.toDouble() / packetsSent * 100)
                                                Logger.i(TAG, "🎬 [帧数监控]   - 重传率: ${String.format("%.2f", retransmissionRate)}%")

                                                if (retransmissionRate > 5.0) {
                                                    Logger.w(TAG, "🎬 [帧数监控] ⚠️ 网络质量警告: 重传率过高，可能导致卡顿")
                                                }

                                                // 🚨 关键检查：重传可能导致帧序错乱
                                                if (retransmissionRate > 2.0) {
                                                    Logger.w(TAG, "🎬 [帧数监控] 🚨 重传率过高可能导致帧序错乱！")
                                                    Logger.w(TAG, "🎬 [帧数监控] 🚨 这会造成'物体回退'的视觉效果！")

                                                    // 建议降低比特率减少重传
                                                    val currentBitrate = getCurrentConfiguredBitrate()
                                                    val suggestedBitrate = (currentBitrate * 0.8).toInt()
                                                    Logger.i(TAG, "🎬 [帧数监控] 💡 建议降低比特率: ${currentBitrate/1000} -> ${suggestedBitrate/1000} kbps")
                                                }
                                            }

                                            // 检查帧发送的连续性
                                            if (framesSent != null && framesSent > 0) {
                                                val currentTime = System.currentTimeMillis()
                                                val previousFramesSent = getStoredFramesSent(peerId)
                                                val previousTime = getStoredTime(peerId)

                                                if (previousFramesSent > 0 && currentTime > previousTime) {
                                                    val timeDiff = (currentTime - previousTime) / 1000.0
                                                    val framesDiff = framesSent - previousFramesSent

                                                    if (framesDiff < 0) {
                                                        Logger.e(TAG, "🎬 [帧数监控] 🚨 检测到帧计数倒退！")
                                                        Logger.e(TAG, "🎬 [帧数监控] 🚨 从${previousFramesSent}帧倒退到${framesSent}帧")
                                                        Logger.e(TAG, "🎬 [帧数监控] 🚨 这可能导致严重的帧序错乱！")
                                                    } else if (timeDiff > 0) {
                                                        val actualFPS = framesDiff / timeDiff
                                                        val expectedFPS = getCurrentConfiguredFramerate()
                                                        val fpsDiff = Math.abs(actualFPS - expectedFPS)

                                                        if (fpsDiff > 10) {
                                                            Logger.w(TAG, "🎬 [帧数监控] 🚨 帧率严重不稳定: 期望${expectedFPS}fps，实际${String.format("%.1f", actualFPS)}fps")
                                                            Logger.w(TAG, "🎬 [帧数监控] 🚨 帧率波动会导致时间戳错乱和帧序问题！")
                                                        }
                                                    }
                                                }

                                                // 存储当前值
                                                storeFramesSent(peerId, framesSent)
                                                storeTime(peerId, currentTime)
                                            }
                                        }

                                        "media-source" -> {
                                            val width = stats.members["width"]?.toString()?.toIntOrNull()
                                            val height = stats.members["height"]?.toString()?.toIntOrNull()
                                            val framesPerSecond = stats.members["framesPerSecond"]?.toString()?.toDoubleOrNull()
                                            val frames = stats.members["frames"]?.toString()?.toLongOrNull()

                                            Logger.i(TAG, "🎬 [帧数监控] 📹 媒体源统计:")
                                            Logger.i(TAG, "🎬 [帧数监控]   - 分辨率: ${width}x${height}")
                                            Logger.i(TAG, "🎬 [帧数监控]   - 帧率: ${framesPerSecond?.let { "${String.format("%.2f", it)} fps" } ?: "N/A"}")
                                            Logger.i(TAG, "🎬 [帧数监控]   - 总帧数: ${frames ?: "N/A"}")

                                            // 检查媒体源是否正常
                                            val sourceFrames = frames ?: 0L
                                            if (sourceFrames == 0L) {
                                                Logger.e(TAG, "🎬 [帧数监控] ❌ 媒体源帧数为0，视频捕获可能未启动")
                                                checkVideoSourceStatus()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "🎬 [帧数监控] 获取发送统计失败", e)
                    }
                }

                Logger.i(TAG, "🎬 [帧数监控] =====================================")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎬 [帧数监控] 检查帧数统计失败", e)
        }
    }

    /**
     * 监控比特率状态 - 仅记录不调节
     */
    private fun checkBitrateStability() {
        try {
            peerConnections.forEach { (peerId, peerConnection) ->
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    val parameters = videoSender.parameters
                    if (parameters.encodings.isNotEmpty()) {
                        val encoding = parameters.encodings[0]
                        val currentMaxBitrate = encoding.maxBitrateBps ?: 0
                        val currentMinBitrate = encoding.minBitrateBps ?: 0

                        Logger.d(TAG, "📊 [比特率监控] 对等端 $peerId 比特率状态: 最大=${currentMaxBitrate/1000} kbps, 最小=${currentMinBitrate/1000} kbps")

                        // 只监控不调节 - 记录比特率状态
                        if (currentMaxBitrate > 0 && currentMinBitrate > 0) {
                            if (currentMaxBitrate != currentMinBitrate) {
                                Logger.d(TAG, "📊 [比特率监控] ℹ️ 比特率范围: 最大=${currentMaxBitrate/1000} kbps, 最小=${currentMinBitrate/1000} kbps")
                            } else {
                                Logger.d(TAG, "📊 [比特率监控] ✅ 比特率固定: ${currentMaxBitrate/1000} kbps")
                            }
                        }

                        // 只记录帧率状态，不调节
                        val currentFramerate = encoding.maxFramerate ?: 0
                        Logger.d(TAG, "📊 [比特率监控] 对等端 $peerId 帧率: ${currentFramerate} fps")
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎬 [固定比特率监控] 检查比特率固定性失败", e)
        }
    }

    /**
     * 启动配置参数监控 - 不动态调节
     */
    private fun startAggressiveBitrateControl() {
        Logger.i(TAG, "🚀 [配置监控] 启动配置参数监控，不进行动态调节")
        Logger.i(TAG, "🚀 [配置监控] 严格按照配置页面设置的参数运行")

        // 不启动定期调节，只记录配置
        val configuredBitrate = getCurrentConfiguredBitrate()
        val configuredFramerate = getCurrentConfiguredFramerate()
        Logger.i(TAG, "🚀 [配置监控] 配置参数: ${configuredBitrate/1000} kbps, ${configuredFramerate} fps")
        Logger.i(TAG, "🚀 [配置监控] 将严格按此参数运行，不进行任何动态调节")
    }

    /**
     * 启动比特率状态监控 - 仅监控不调节
     */
    private fun startBitrateStatusMonitoring() {
        Logger.i(TAG, "📊 [状态监控] 启动比特率状态监控，仅记录不调节")

        mainHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    // 只有在有活跃连接时才进行监控
                    if (peerConnections.isNotEmpty()) {
                        // 只记录当前状态，不进行调节
                        peerConnections.forEach { (peerId, peerConnection) ->
                            val sender = peerConnection.senders.find { it.track()?.kind() == "video" }
                            sender?.parameters?.let { params ->
                                val currentBitrate = params.encodings.firstOrNull()?.maxBitrateBps ?: 0
                                Logger.d(TAG, "📊 [状态监控] 对等端 $peerId 当前比特率: ${currentBitrate/1000} kbps")
                            }
                        }
                    } else {
                        Logger.d(TAG, "📊 [状态监控] 没有活跃连接，跳过状态监控")
                    }

                    // 继续下一次监控
                    mainHandler.postDelayed(this, 120000) // 2分钟监控一次即可
                } catch (e: Exception) {
                    Logger.e(TAG, "📊 [状态监控] 状态监控失败", e)
                    // 即使出错也要继续监控
                    mainHandler.postDelayed(this, 120000)
                }
            }
        }, 120000) // 2分钟后开始第一次监控
    }

    /**
     * 获取配置页面设置的比特率 - 严格按配置，不动态调节
     */
    private fun getCurrentConfiguredBitrate(): Int {
        // 直接从配置页面获取比特率，严格按用户设置，不根据连接数调节
        val configuredBitrate = WebRTCManager.getVideoBitrate() * 1000 // 转换为bps
        Logger.d(TAG, "📊 [固定比特率] 使用配置页面设置的比特率: ${configuredBitrate/1000} kbps")
        return configuredBitrate
    }

    /**
     * 获取配置页面设置的帧率 - 严格按配置，不动态调节
     */
    private fun getCurrentConfiguredFramerate(): Int {
        // 直接从配置页面获取帧率，严格按用户设置，不根据连接数调节
        val configuredFramerate = WebRTCManager.getVideoFramerate()
        Logger.d(TAG, "📊 [固定帧率] 使用配置页面设置的帧率: ${configuredFramerate} fps")
        return configuredFramerate
    }

    /**
     * 强制固定比特率 - 使用配置参数
     */
    private fun forceAbsoluteFixedBitrate() {
        try {
            peerConnections.forEach { (peerId, peerConnection) ->
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    val parameters = videoSender.parameters
                    if (parameters.encodings.isNotEmpty()) {
                        val encoding = parameters.encodings[0]

                        // 使用当前配置的比特率和帧率，但确保固定不变
                        val configuredBitrate = getCurrentConfiguredBitrate() // 获取配置的比特率
                        val configuredFramerate = getCurrentConfiguredFramerate() // 获取配置的帧率

                        // 检查当前值是否被修改
                        val currentMaxBitrate = encoding.maxBitrateBps ?: 0
                        val currentMinBitrate = encoding.minBitrateBps ?: 0
                        val currentFramerate = encoding.maxFramerate ?: 0

                        var needsUpdate = false

                        // 只监控，不修改 - 按配置要求不动态调节
                        if (currentMaxBitrate != configuredBitrate) {
                            Logger.i(TAG, "📊 [配置监控] 最大比特率差异: 当前${currentMaxBitrate/1000} kbps, 配置${configuredBitrate/1000} kbps")
                        }

                        if (currentMinBitrate != configuredBitrate) {
                            Logger.i(TAG, "📊 [配置监控] 最小比特率差异: 当前${currentMinBitrate/1000} kbps, 配置${configuredBitrate/1000} kbps")
                        }

                        if (currentFramerate != configuredFramerate) {
                            Logger.i(TAG, "📊 [配置监控] 帧率差异: 当前${currentFramerate} fps, 配置${configuredFramerate} fps")
                        }

                        // 按配置要求：不进行动态调节，只监控记录
                        Logger.d(TAG, "📊 [配置监控] 对等端 $peerId 当前参数: ${currentMaxBitrate/1000} kbps, ${currentFramerate} fps (配置: ${configuredBitrate/1000} kbps, ${configuredFramerate} fps)")
                        Logger.d(TAG, "📊 [配置监控] 按用户要求不进行动态调节")
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🚀 [配置固定控制] 设置失败", e)
        }
    }

    /**
     * 启动定期强制固定码率任务
     */
    private fun startPeriodicBitrateEnforcement() {
        val enforceRunnable = object : Runnable {
            override fun run() {
                try {
                    if (peerConnections.isNotEmpty()) {
                        Logger.d(TAG, "🔒 [定期强制] 定期强制固定比特率，防止自适应重新启用")
                        enforceFixedBitrate()

                        // 每30秒强制一次，防止WebRTC内部重新启用自适应
                        mainHandler.postDelayed(this, 30000)
                    } else {
                        Logger.d(TAG, "🔒 [定期强制] 无活跃连接，停止定期强制")
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "🔒 [定期强制] 定期强制固定比特率失败", e)
                    // 即使失败也要继续尝试
                    mainHandler.postDelayed(this, 30000)
                }
            }
        }

        // 首次延迟30秒后开始定期强制
        mainHandler.postDelayed(enforceRunnable, 30000)
        Logger.i(TAG, "🔒 [定期强制] 已启动定期强制固定比特率任务，每30秒执行一次")
    }

    /**
     * 强制设置固定比特率配置 - 严格按配置页面设置
     */
    private fun enforceFixedBitrate() {
        Logger.i(TAG, "🔒 [强制固定比特率] 开始强制设置固定比特率配置（严格按配置页面）")

        try {
            peerConnections.forEach { (peerId, peerConnection) ->
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    val parameters = videoSender.parameters
                    if (parameters.encodings.isNotEmpty()) {
                        val encoding = parameters.encodings[0]

                        // 使用配置页面的设置，确保固定比特率
                        val configuredBitrate = getCurrentConfiguredBitrate()
                        val configuredFramerate = getCurrentConfiguredFramerate()

                        // 设置完全固定的比特率（最大值=最小值=配置值）
                        encoding.maxBitrateBps = configuredBitrate
                        encoding.minBitrateBps = configuredBitrate  // 与最大值相同，确保完全固定
                        encoding.maxFramerate = configuredFramerate
                        encoding.active = true

                        // 尝试禁用自适应功能
                        try {
                            // 设置编码参数为固定模式
                            val scaleResolutionDownByField = encoding.javaClass.getDeclaredField("scaleResolutionDownBy")
                            scaleResolutionDownByField.isAccessible = true
                            scaleResolutionDownByField.set(encoding, 1.0) // 不缩放分辨率

                            Logger.d(TAG, "🔒 [强制固定比特率] 设置分辨率缩放为1.0")
                        } catch (e: Exception) {
                            Logger.d(TAG, "🔒 [强制固定比特率] 当前版本不支持分辨率缩放设置")
                        }

                        val success = videoSender.setParameters(parameters)
                        Logger.i(TAG, "🔒 [强制固定比特率] 对等端 $peerId ${if (success) "✅" else "❌"} 强制设置固定比特率: ${configuredBitrate/1000} kbps, 帧率: ${configuredFramerate} fps")
                    }
                }
            }

            Logger.i(TAG, "🔒 [强制固定比特率] 固定比特率配置完成，不再重复设置")

        } catch (e: Exception) {
            Logger.e(TAG, "🔒 [强制固定比特率] 强制设置固定比特率失败", e)
        }
    }

    /**
     * 强制稳定帧率 - 响应Web端检测到的帧率下降
     */
    private fun forceStableFramerate(): Boolean {
        try {
            Logger.i(TAG, "🔧 [强制稳定帧率] 开始强制稳定帧率设置")

            peerConnections.forEach { (peerId, peerConnection) ->
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    val parameters = videoSender.parameters
                    if (parameters.encodings.isNotEmpty()) {
                        val encoding = parameters.encodings[0]

                        // 强制设置稳定的帧率
                        val targetFramerate = getCurrentConfiguredFramerate()
                        encoding.maxFramerate = targetFramerate

                        // 同时确保比特率稳定
                        val targetBitrate = getCurrentConfiguredBitrate()
                        encoding.maxBitrateBps = targetBitrate
                        encoding.minBitrateBps = targetBitrate

                        val success = videoSender.setParameters(parameters)
                        Logger.i(TAG, "🔧 [强制稳定帧率] 对等端 $peerId ${if (success) "✅" else "❌"} 强制设置: ${targetFramerate}fps, ${targetBitrate/1000}kbps")

                        if (success) {
                            // 请求关键帧以立即生效
                            requestKeyFrame()
                        }
                    }
                }
            }

            Logger.i(TAG, "🔧 [强制稳定帧率] 强制稳定帧率设置完成")
            return true
        } catch (e: Exception) {
            Logger.e(TAG, "🔧 [强制稳定帧率] 强制稳定帧率失败", e)
            return false
        }
    }

    /**
     * 降低比特率以提高稳定性 - 响应Web端检测到的网络抖动
     */
    private fun reduceBitrateForStability(): Boolean {
        try {
            Logger.i(TAG, "🔧 [降低比特率] 开始降低比特率以提高稳定性")

            peerConnections.forEach { (peerId, peerConnection) ->
                val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }
                if (videoSender != null) {
                    val parameters = videoSender.parameters
                    if (parameters.encodings.isNotEmpty()) {
                        val encoding = parameters.encodings[0]

                        // 获取当前比特率
                        val currentBitrate = encoding.maxBitrateBps ?: getCurrentConfiguredBitrate()

                        // 降低20%的比特率
                        val reducedBitrate = (currentBitrate * 0.8).toInt()

                        encoding.maxBitrateBps = reducedBitrate
                        encoding.minBitrateBps = reducedBitrate

                        val success = videoSender.setParameters(parameters)
                        Logger.i(TAG, "🔧 [降低比特率] 对等端 $peerId ${if (success) "✅" else "❌"} 降低比特率: ${currentBitrate/1000} -> ${reducedBitrate/1000} kbps")

                        if (success) {
                            // 请求关键帧以立即生效
                            requestKeyFrame()
                        }
                    }
                }
            }

            Logger.i(TAG, "🔧 [降低比特率] 降低比特率完成")
            return true
        } catch (e: Exception) {
            Logger.e(TAG, "🔧 [降低比特率] 降低比特率失败", e)
            return false
        }
    }

    /**
     * 请求关键帧
     */
    private fun requestKeyFrame(): Boolean {
        try {
            Logger.d(TAG, "请求关键帧")

            // 遍历所有PeerConnection，请求关键帧
            var success = false
            peerConnections.forEach { (peerId, peerConnection) ->
                try {
                    // 获取视频发送器
                    val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }

                    if (videoSender != null) {
                        // 使用更安全的方式请求关键帧：通过重新协商
                        Logger.d(TAG, "为对等端 $peerId 触发重新协商以生成关键帧")

                        // 方法1：通过修改比特率来触发关键帧（更安全）
                        val parameters = videoSender.parameters
                        if (parameters.encodings.isNotEmpty()) {
                            val currentBitrate = parameters.encodings[0].maxBitrateBps
                            if (currentBitrate != null && currentBitrate > 0) {
                                // 临时增加1bps，然后恢复，这会触发编码器重新配置
                                parameters.encodings[0].maxBitrateBps = currentBitrate + 1
                                videoSender.parameters = parameters

                                // 延迟恢复原始比特率
                                mainHandler.postDelayed({
                                    try {
                                        val newParameters = videoSender.parameters
                                        if (newParameters.encodings.isNotEmpty()) {
                                            newParameters.encodings[0].maxBitrateBps = currentBitrate
                                            videoSender.parameters = newParameters
                                        }
                                    } catch (e: Exception) {
                                        Logger.e(TAG, "恢复比特率失败", e)
                                    }
                                }, 100) // 100ms后恢复

                                Logger.d(TAG, "已为对等端 $peerId 请求关键帧（通过比特率调整）")
                                success = true
                            } else {
                                // 如果没有设置比特率，使用默认方法
                                Logger.d(TAG, "对等端 $peerId 没有设置比特率，使用默认关键帧请求方法")
                                success = true
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "为对等端 $peerId 请求关键帧失败", e)
                }
            }

            return success
        } catch (e: Exception) {
            Logger.e(TAG, "请求关键帧失败", e)
            return false
        }
    }



    /**
     * 强制设置高质量固定参数，确保60帧流畅播放
     */
    private fun forceHighQualityParameters(peerConnection: PeerConnection) {
        try {
            Logger.d(TAG, "强制设置高质量固定参数")

            // 获取视频发送器
            val videoSender = peerConnection.senders.find { it.track()?.kind() == "video" }

            if (videoSender != null) {
                val bitrate = WebRTCManager.getVideoBitrate() * 1000 // 转换为bps
                val framerate = WebRTCManager.getVideoFramerate()

                Logger.d(TAG, "强制设置高质量参数: 比特率=$bitrate bps, 帧率=$framerate fps")

                // 获取当前参数
                val parameters = videoSender.parameters
                val encodings = parameters.encodings

                if (encodings.isNotEmpty()) {
                    // 设置激进的高质量参数
                    encodings[0].maxBitrateBps = (bitrate * 4).toInt() // 4倍最大比特率
                    encodings[0].minBitrateBps = (bitrate * 2).toInt() // 2倍最小比特率
                    encodings[0].maxFramerate = framerate
                    encodings[0].active = true

                    // 尝试设置更多高质量参数
                    try {
                        // 禁用自适应分辨率
                        val scaleResolutionDownByField = encodings[0].javaClass.getDeclaredField("scaleResolutionDownBy")
                        scaleResolutionDownByField.isAccessible = true
                        scaleResolutionDownByField.set(encodings[0], 1.0) // 不缩放分辨率
                        Logger.d(TAG, "已禁用自适应分辨率缩放")
                    } catch (e: Exception) {
                        Logger.d(TAG, "当前WebRTC版本不支持分辨率缩放控制")
                    }

                    // 应用更改后的参数
                    videoSender.parameters = parameters

                    Logger.d(TAG, "成功强制设置高质量参数: 最大比特率=${encodings[0].maxBitrateBps}bps, 最小比特率=${encodings[0].minBitrateBps}bps, 帧率=${encodings[0].maxFramerate}fps")
                } else {
                    Logger.w(TAG, "编码参数列表为空，无法强制设置高质量参数")
                }
            } else {
                Logger.w(TAG, "未找到视频发送器，无法强制设置高质量参数")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "强制设置高质量参数失败", e)
        }
    }

    /**
     * 获取当前视频分辨率
     */
    fun getVideoResolution(): String {
        return currentVideoResolution
    }

    /**
     * 获取当前视频码率
     */
    fun getVideoBitrate(): Int {
        return currentVideoBitrate
    }

    /**
     * 获取当前视频编码
     */
    fun getVideoCodec(): String {
        return currentVideoCodec
    }

    /**
     * 获取当前视频源类型
     */
    fun getVideoSourceType(): String {
        return currentVideoSourceType
    }

    /**
     * 获取当前摄像头ID
     */
    fun getCameraId(): String {
        return currentCameraId
    }

    /**
     * 设置视频分辨率
     */
    fun setVideoResolution(resolution: String) {
        currentVideoResolution = resolution
        Logger.i(TAG, "设置视频分辨率: $resolution")
        // 这里可以添加实际的分辨率更改逻辑
    }

    /**
     * 设置视频码率
     */
    fun setVideoBitrate(bitrate: Int) {
        currentVideoBitrate = bitrate
        Logger.i(TAG, "设置视频码率: $bitrate kbps")
        // 这里可以添加实际的码率更改逻辑
    }

    /**
     * 设置视频编码
     */
    fun setVideoCodec(codec: String) {
        currentVideoCodec = codec
        Logger.i(TAG, "设置视频编码: $codec")
        // 这里可以添加实际的编码更改逻辑
    }

    /**
     * 获取当前视频帧用于截屏
     */
    fun getCurrentVideoFrame(): android.graphics.Bitmap? {
        return try {
            Logger.d(TAG, "🎥 尝试获取当前视频帧")

            // 获取当前的视频轨道
            val videoTrack = localVideoTrack ?: sharedVideoTrack
            if (videoTrack == null) {
                Logger.w(TAG, "⚠️ 没有可用的视频轨道")
                return null
            }

            // 获取视频源
            val videoSource = localVideoSource ?: sharedVideoSource
            if (videoSource == null) {
                Logger.w(TAG, "⚠️ 没有可用的视频源")
                return null
            }

            // 尝试从视频捕获器获取当前帧
            val capturer = videoCapturer
            if (capturer is ScreenCapturerAndroid) {
                Logger.d(TAG, "🖥️ 使用屏幕捕获器获取帧")
                return captureFrameFromScreenCapturer(capturer)
            } else if (capturer is Camera2Capturer) {
                Logger.d(TAG, "📷 使用摄像头捕获器获取帧")
                return captureFrameFromCameraCapturer(capturer)
            } else {
                Logger.w(TAG, "⚠️ 不支持的视频捕获器类型: ${capturer?.javaClass?.simpleName}")
                return null
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 获取当前视频帧失败: ${e.message}")
            null
        }
    }

    /**
     * 从屏幕捕获器获取当前帧
     */
    private fun captureFrameFromScreenCapturer(capturer: ScreenCapturerAndroid): android.graphics.Bitmap? {
        return try {
            Logger.d(TAG, "🖥️ 从屏幕捕获器获取帧")

            // 创建一个临时的VideoSink来捕获帧
            var capturedFrame: VideoFrame? = null
            val frameCapturer = object : VideoSink {
                override fun onFrame(frame: VideoFrame?) {
                    if (capturedFrame == null) {
                        capturedFrame = frame
                    }
                }
            }

            // 临时添加sink来捕获一帧
            localVideoTrack?.addSink(frameCapturer)

            // 等待一小段时间让帧被捕获
            Thread.sleep(100)

            // 移除sink
            localVideoTrack?.removeSink(frameCapturer)

            // 转换VideoFrame为Bitmap
            return capturedFrame?.let { frame ->
                convertVideoFrameToBitmap(frame)
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 从屏幕捕获器获取帧失败: ${e.message}")
            null
        }
    }

    /**
     * 从摄像头捕获器获取当前帧
     */
    private fun captureFrameFromCameraCapturer(capturer: Camera2Capturer): android.graphics.Bitmap? {
        return try {
            Logger.d(TAG, "📷 从摄像头捕获器获取帧")

            // 类似屏幕捕获的逻辑
            var capturedFrame: VideoFrame? = null
            val frameCapturer = object : VideoSink {
                override fun onFrame(frame: VideoFrame?) {
                    if (capturedFrame == null) {
                        capturedFrame = frame
                    }
                }
            }

            localVideoTrack?.addSink(frameCapturer)
            Thread.sleep(100)
            localVideoTrack?.removeSink(frameCapturer)

            return capturedFrame?.let { frame ->
                convertVideoFrameToBitmap(frame)
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 从摄像头捕获器获取帧失败: ${e.message}")
            null
        }
    }

    /**
     * 将VideoFrame转换为Bitmap
     */
    private fun convertVideoFrameToBitmap(frame: VideoFrame): android.graphics.Bitmap? {
        return try {
            Logger.d(TAG, "🔄 转换VideoFrame为Bitmap")

            val buffer = frame.buffer
            if (buffer is VideoFrame.I420Buffer) {
                val width = buffer.width
                val height = buffer.height

                // 创建YUV字节数组
                val yuvBytes = ByteArray(width * height * 3 / 2)
                val yPlane = buffer.dataY
                val uPlane = buffer.dataU
                val vPlane = buffer.dataV

                // 复制Y平面
                yPlane.get(yuvBytes, 0, width * height)

                // 复制U平面
                val uvPixelStride = if (buffer.strideU == width / 2) 1 else 2
                if (uvPixelStride == 1) {
                    uPlane.get(yuvBytes, width * height, width * height / 4)
                    vPlane.get(yuvBytes, width * height * 5 / 4, width * height / 4)
                } else {
                    // 交错UV数据
                    for (i in 0 until width * height / 4) {
                        yuvBytes[width * height + i * 2] = uPlane.get(i)
                        yuvBytes[width * height + i * 2 + 1] = vPlane.get(i)
                    }
                }

                // 转换YUV为RGB
                val rgbBytes = IntArray(width * height)
                convertYUV420ToRGB(yuvBytes, rgbBytes, width, height)

                // 创建Bitmap
                val bitmap = android.graphics.Bitmap.createBitmap(width, height, android.graphics.Bitmap.Config.ARGB_8888)
                bitmap.setPixels(rgbBytes, 0, width, 0, 0, width, height)

                Logger.i(TAG, "✅ 成功转换VideoFrame为Bitmap: ${width}x${height}")
                return bitmap

            } else {
                Logger.w(TAG, "⚠️ 不支持的VideoFrame格式: ${buffer.javaClass.simpleName}")
                return null
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 转换VideoFrame为Bitmap失败: ${e.message}")
            null
        }
    }

    /**
     * 将YUV420数据转换为RGB
     */
    private fun convertYUV420ToRGB(yuv: ByteArray, rgb: IntArray, width: Int, height: Int) {
        val frameSize = width * height

        for (j in 0 until height) {
            for (i in 0 until width) {
                val y = yuv[j * width + i].toInt() and 0xff
                val u = yuv[frameSize + (j shr 1) * (width shr 1) + (i shr 1)].toInt() and 0xff
                val v = yuv[frameSize + (frameSize shr 2) + (j shr 1) * (width shr 1) + (i shr 1)].toInt() and 0xff

                val y1192 = 1192 * y
                val r = (y1192 + 1634 * v)
                val g = (y1192 - 833 * v - 400 * u)
                val b = (y1192 + 2066 * u)

                val red = if (r < 0) 0 else if (r > 262143) 255 else (r shr 10)
                val green = if (g < 0) 0 else if (g > 262143) 255 else (g shr 10)
                val blue = if (b < 0) 0 else if (b > 262143) 255 else (b shr 10)

                rgb[j * width + i] = 0xff000000.toInt() or (red shl 16) or (green shl 8) or blue
            }
        }
    }

    /**
     * WebRTC客户端监听器接口
     */
    interface WebRTCClientListener {
        fun onWebRTCClientInitialized()
        fun onLocalVideoSourceChanged(videoTrack: VideoTrack?)
        fun onIceCandidate(peerId: String, candidate: IceCandidate)
        fun onIceConnectionChange(peerId: String, state: IceConnectionState)
        fun onOfferCreated(peerId: String, sdp: SessionDescription)
        fun onAnswerCreated(peerId: String, sdp: SessionDescription)
        fun onCommandReceived(peerId: String, command: String, data: Map<*, *>)
        fun onPeerDisconnected(peerId: String)
    }

    /**
     * 清理WebRTC资源
     */
    fun cleanup() {
        Logger.i(TAG, "🧹 [清理] 开始清理WebRTC资源")

        try {
            // 清理摄像头策略错误Handler
            cameraPolicyErrorHandler?.removeCallbacksAndMessages(null)
            cameraPolicyErrorHandler = null

            // 关闭所有连接
            peerConnections.keys.toList().forEach { peerId ->
                closePeerConnection(peerId)
            }

            // 停止视频源
            WebRTCManager.stopVideoSource(context)

            // 清理音频源
            localAudioSource?.dispose()
            localAudioSource = null
            sharedAudioSource?.dispose()
            sharedAudioSource = null

            // 清理PeerConnectionFactory
            peerConnectionFactory?.dispose()

            // 清理EGL
            rootEglBase.release()

            Logger.i(TAG, "🧹 [清理] WebRTC资源清理完成")
        } catch (e: Exception) {
            Logger.e(TAG, "🧹 [清理] 清理过程中出错", e)
        }
    }
}
