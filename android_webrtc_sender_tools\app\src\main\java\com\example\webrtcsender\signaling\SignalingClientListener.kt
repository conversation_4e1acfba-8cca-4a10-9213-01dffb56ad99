package com.example.webrtcsender.signaling

import org.webrtc.IceCandidate
import org.webrtc.SessionDescription

/**
 * 信令客户端监听器接口
 */
interface SignalingClientListener {
    /**
     * 当连接到信令服务器时调用
     */
    fun onSignalingConnected()
    
    /**
     * 当断开与信令服务器的连接时调用
     */
    fun onSignalingDisconnected()
    
    /**
     * 当信令服务器发生错误时调用
     */
    fun onSignalingError(error: String)
    
    /**
     * 当客户端加入时调用
     */
    fun onClientJoined(clientId: String)
    
    /**
     * 当客户端离开时调用
     */
    fun onClientLeft(clientId: String)
    
    /**
     * 当收到Offer时调用
     */
    fun onOfferReceived(clientId: String, sdp: SessionDescription)
    
    /**
     * 当收到Answer时调用
     */
    fun onAnswerReceived(clientId: String, sdp: SessionDescription)
    
    /**
     * 当收到ICE候选时调用
     */
    fun onIceCandidateReceived(clientId: String, candidate: IceCandidate)
}
