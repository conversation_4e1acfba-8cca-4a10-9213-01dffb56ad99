package com.example.webrtcsender.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志工具类，支持控制台输出和文件记录
 */
object Logger {
    private const val TAG = "WebRTCSender"
    private var logToFile = false
    private var logFile: File? = null
    private var displayEnabled = true // 日志显示开关
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    /**
     * 初始化日志系统
     * @param logDir 日志文件目录
     * @param enableFileLogging 是否启用文件日志
     */
    fun init(logDir: File, enableFileLogging: Boolean) {
        logToFile = enableFileLogging

        // 注意：displayEnabled的状态由FirstInstallConfigManager在应用启动时设置
        // 这里不重置displayEnabled，保持之前设置的状态
        
        if (logToFile) {
            try {
                if (!logDir.exists()) {
                    logDir.mkdirs()
                }
                
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                logFile = File(logDir, "webrtc_sender_$timestamp.log")
                
                // 写入日志头
                val header = "=== WebRTC Sender Log ===\n" +
                        "Started at: ${dateFormat.format(Date())}\n" +
                        "===========================\n\n"
                
                FileOutputStream(logFile, true).use { it.write(header.toByteArray()) }
                
                i("Logger", "日志文件已创建: ${logFile?.absolutePath}")
            } catch (e: Exception) {
                Log.e(TAG, "初始化日志文件失败", e)
                logToFile = false
            }
        }
    }
    
    /**
     * 设置日志显示开关
     */
    fun setDisplayEnabled(enabled: Boolean) {
        displayEnabled = enabled
        Log.i(TAG, "日志显示状态已设置为: $enabled")
    }

    /**
     * 获取日志显示状态
     */
    fun isDisplayEnabled(): Boolean {
        return displayEnabled
    }

    /**
     * 从SharedPreferences加载日志显示设置
     */
    fun loadDisplaySettingFromPreferences(context: Context) {
        try {
            val preferences = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
            val enabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
            setDisplayEnabled(enabled)
            Log.i(TAG, "从SharedPreferences加载日志显示设置: $enabled")
        } catch (e: Exception) {
            Log.e(TAG, "加载日志显示设置失败", e)
            // 如果加载失败，使用默认值
            setDisplayEnabled(Constants.DEFAULT_LOG_DISPLAY_ENABLED)
        }
    }

    /**
     * 记录调试日志
     */
    fun d(tag: String, message: String) {
        val formattedMessage = formatMessage(tag, "DEBUG", message)
        if (displayEnabled) {
            Log.d(TAG, formattedMessage)
        }
        writeToFile(formattedMessage)
    }

    /**
     * 记录信息日志
     */
    fun i(tag: String, message: String) {
        val formattedMessage = formatMessage(tag, "INFO", message)
        if (displayEnabled) {
            Log.i(TAG, formattedMessage)
        }
        writeToFile(formattedMessage)
    }

    /**
     * 记录警告日志
     */
    fun w(tag: String, message: String) {
        val formattedMessage = formatMessage(tag, "WARN", message)
        if (displayEnabled) {
            Log.w(TAG, formattedMessage)
        }
        writeToFile(formattedMessage)
    }

    /**
     * 记录错误日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        val formattedMessage = formatMessage(tag, "ERROR", message + (throwable?.let { "\n${it.stackTraceToString()}" } ?: ""))
        if (displayEnabled) {
            Log.e(TAG, formattedMessage)
        }
        writeToFile(formattedMessage)
    }
    
    /**
     * 格式化日志消息
     */
    private fun formatMessage(tag: String, level: String, message: String): String {
        return "[$tag] $level: $message"
    }
    
    /**
     * 写入日志到文件
     */
    private fun writeToFile(message: String) {
        if (logToFile && logFile != null) {
            try {
                val timestamp = dateFormat.format(Date())
                val logEntry = "$timestamp $message\n"
                
                FileOutputStream(logFile, true).use { it.write(logEntry.toByteArray()) }
            } catch (e: Exception) {
                Log.e(TAG, "写入日志文件失败", e)
            }
        }
    }
}
