# 重复连接问题修复

## 问题分析

从日志可以看出有两次连接和设备信息上报：

### 1. 第一次连接（18:00:04）
```
18:00:04.588  DeviceInfoReporter: 发送设备信息到信令服务器: gamev-b246c42d
18:00:04.601  DeviceInfoReporter: 重连后设备信息上报成功
```

### 2. 第二次连接（18:00:06-09）
```
18:00:06.907  WebSocket已打开
18:00:06.916  🔗 [信令连接] ✅ 已连接到信令服务器 (重连)
18:00:08.952  DeviceInfoReporter: 开始收集设备信息: gamev-b246c42d
18:00:09.233  DeviceInfoReporter: 发送设备信息到信令服务器: gamev-b246c42d
```

## 根本原因

### 1. 多个连接调用点

应用中有多个地方会调用 `connectToSignalingServer()`：

1. **MainActivity.startService()** - 服务启动时
2. **MainActivity.updateConnectionStatus()** - 连接健康检查时
3. **WebRTCSenderService.startWebRTC()** - 服务内部启动时
4. **WebRTCManager.forceReconnect()** - 强制重连时

### 2. 连接健康检查触发重复连接

`updateConnectionStatus()` 方法中的逻辑问题：

```kotlin
when {
    isConnected && !isHealthy -> {
        WebRTCManager.forceReconnect()  // 断开并重连
    }
    !isConnected -> {
        if (service.isRunning()) {
            WebRTCManager.connectToSignalingServer()  // 又一次连接
        }
    }
}
```

### 3. 重连状态误判

`hasBeenConnectedBefore` 变量永远不重置，导致正常连接被误判为重连。

## 修复方案

### 1. 添加重连冷却时间

**修复前的连接状态处理**：
```kotlin
when {
    isConnected && !isHealthy -> {
        WebRTCManager.forceReconnect()
    }
    !isConnected -> {
        WebRTCManager.connectToSignalingServer()
    }
}
```

**修复后的连接状态处理**：
```kotlin
val currentTime = System.currentTimeMillis()
val lastReconnectTime = getSharedPreferences("app_state", MODE_PRIVATE)
    .getLong("last_reconnect_time", 0)

when {
    isConnected && !isHealthy -> {
        // 连接异常时，添加30秒冷却时间
        if (currentTime - lastReconnectTime > 30000) {
            Logger.w(TAG, "检测到连接异常，尝试强制重连")
            saveLastReconnectTime(currentTime)
            WebRTCManager.forceReconnect()
        } else {
            Logger.d(TAG, "连接异常但在冷却期内，跳过重连")
        }
    }
    !isConnected -> {
        // 未连接时，添加15秒冷却时间
        if (currentTime - lastReconnectTime > 15000) {
            Logger.w(TAG, "检测到未连接状态，检查是否需要重连")
            if (service.isRunning()) {
                saveLastReconnectTime(currentTime)
                WebRTCManager.connectToSignalingServer()
            }
        } else {
            Logger.d(TAG, "未连接但在冷却期内，跳过重连")
        }
    }
}
```

### 2. 改进重连状态判断

**修复前的重连判断**：
```kotlin
val isReconnection = hasBeenConnectedBefore
```

**修复后的重连判断**：
```kotlin
val currentTime = System.currentTimeMillis()
val lastConnectionTime = context.getSharedPreferences("webrtc_state", Context.MODE_PRIVATE)
    .getLong("last_connection_time", 0)

// 如果距离上次连接超过5分钟，认为是新的连接会话
val isReconnection = hasBeenConnectedBefore && (currentTime - lastConnectionTime < 300000)

// 记录连接时间
context.getSharedPreferences("webrtc_state", Context.MODE_PRIVATE)
    .edit()
    .putLong("last_connection_time", currentTime)
    .apply()
```

## 技术实现

### 1. 冷却时间机制

**连接异常重连冷却**：30秒
- 避免频繁的强制重连
- 给连接足够时间稳定

**未连接状态重连冷却**：15秒
- 避免过于频繁的连接尝试
- 平衡响应速度和稳定性

### 2. 连接会话识别

**时间窗口**：5分钟
- 5分钟内的连接认为是同一会话的重连
- 超过5分钟认为是新的连接会话

**状态持久化**：
- 使用SharedPreferences保存连接时间
- 跨应用重启保持状态

### 3. 日志优化

**修复前的日志**：
```
🔗 [信令连接] ✅ 已连接到信令服务器 (重连)
🔗 [信令连接] ✅ 已连接到信令服务器 (重连)
```

**修复后的日志**：
```
🔗 [信令连接] ✅ 已连接到信令服务器 (新连接)
🔗 [信令连接] ✅ 已连接到信令服务器 (重连)
```

## 预期效果

### 1. 减少重复连接

**修复前**：
- 短时间内多次连接
- 重复的设备信息上报
- 资源浪费和日志混乱

**修复后**：
- 有效的冷却时间控制
- 避免不必要的重连
- 清晰的连接状态日志

### 2. 准确的重连识别

**修复前**：
- 所有连接都被标记为"重连"
- 无法区分真正的重连和新连接

**修复后**：
- 基于时间窗口的准确判断
- 区分新连接和重连
- 正确的设备信息上报策略

### 3. 改善用户体验

**连接稳定性**：
- 减少不必要的连接中断
- 避免频繁的重连尝试

**日志清晰度**：
- 明确的连接状态标识
- 便于问题诊断和调试

## 测试验证

### 1. 正常启动场景

**测试步骤**：
1. 启动应用
2. 观察连接日志
3. 验证只有一次连接

**预期结果**：
```
🔗 [信令连接] ✅ 已连接到信令服务器 (新连接)
DeviceInfoReporter: 连接建立后设备信息上报成功
```

### 2. 网络中断恢复场景

**测试步骤**：
1. 断开网络
2. 恢复网络
3. 观察重连行为

**预期结果**：
```
🔗 [信令连接] ✅ 已连接到信令服务器 (重连)
DeviceInfoReporter: 重连后设备信息上报成功
```

### 3. 频繁连接检查场景

**测试步骤**：
1. 触发多次连接状态检查
2. 观察是否有重复连接

**预期结果**：
```
🔍 连接异常但在冷却期内，跳过重连
🔍 未连接但在冷却期内，跳过重连
```

## 配置参数

### 1. 冷却时间配置

```kotlin
// 连接异常重连冷却时间（毫秒）
private const val FORCE_RECONNECT_COOLDOWN = 30000L  // 30秒

// 未连接状态重连冷却时间（毫秒）
private const val NORMAL_RECONNECT_COOLDOWN = 15000L  // 15秒
```

### 2. 连接会话窗口

```kotlin
// 连接会话时间窗口（毫秒）
private const val CONNECTION_SESSION_WINDOW = 300000L  // 5分钟
```

## 版本更新

当前版本：v1.35 → v1.36

主要修复：
- 添加重连冷却时间机制
- 改进重连状态判断逻辑
- 优化连接会话识别
- 减少重复连接和设备信息上报
- 提升连接稳定性和日志清晰度

## 总结

这次修复解决了重复连接的问题：

1. **问题根源**：多个连接调用点和缺乏冷却机制
2. **修复策略**：添加冷却时间和改进状态判断
3. **预期效果**：减少重复连接，提升稳定性
4. **用户体验**：更稳定的连接和清晰的状态反馈

现在应用会智能地管理连接状态，避免不必要的重复连接和设备信息上报。
