# MAC地址获取修复方案

## 🎯 问题分析

### 原问题
- 只能获取到`02:00:00:00:00:00`虚拟MAC地址
- 原代码只尝试获取WiFi MAC地址
- Android 6.0+系统出于隐私保护返回固定虚拟地址

### 根本原因
```kotlin
// 原代码问题
val wifiInfo = wifiManager.connectionInfo
wifiInfo.macAddress  // 在Android 6.0+返回 "02:00:00:00:00:00"
```

## 🔧 修复方案

### 1. 多层级MAC地址获取策略

```kotlin
private fun getMacAddress(): String {
    // 方法1: 从网络接口获取真实MAC地址（优先级最高）
    val realMacAddress = getRealMacAddress()
    if (realMacAddress != "02:00:00:00:00:00") return realMacAddress
    
    // 方法2: 从系统文件获取
    val systemMacAddress = getMacFromSystemFiles()
    if (systemMacAddress != "02:00:00:00:00:00") return systemMacAddress
    
    // 方法3: WiFi MAC地址（最后备选）
    val wifiMacAddress = getWifiMacAddress()
    return wifiMacAddress
}
```

### 2. 网络接口优先级策略

```kotlin
private fun getRealMacAddress(): String {
    // 按优先级分类网络接口
    val ethernetInterfaces = mutableListOf<Pair<String, String>>()  // 以太网
    val wifiInterfaces = mutableListOf<Pair<String, String>>()      // WiFi
    val otherInterfaces = mutableListOf<Pair<String, String>>()     // 其他
    
    // 接口名称匹配规则
    when {
        interfaceName.contains("eth") || interfaceName.contains("lan") -> 
            ethernetInterfaces.add(Pair(interfaceName, macAddress))
        interfaceName.contains("wlan") || interfaceName.contains("wifi") -> 
            wifiInterfaces.add(Pair(interfaceName, macAddress))
        else -> 
            otherInterfaces.add(Pair(interfaceName, macAddress))
    }
}
```

### 3. 系统文件备选方案

```kotlin
private fun getMacFromSystemFiles(): String {
    val macFilePaths = listOf(
        "/sys/class/net/eth0/address",    // 以太网0
        "/sys/class/net/eth1/address",    // 以太网1
        "/sys/class/net/wlan0/address",   // WiFi0
        "/sys/class/net/wlan1/address"    // WiFi1
    )
    
    for (filePath in macFilePaths) {
        val file = File(filePath)
        if (file.exists() && file.canRead()) {
            val macAddress = file.readText().trim()
            if (macAddress != "02:00:00:00:00:00") {
                return macAddress
            }
        }
    }
}
```

## 📊 接口类型识别

### 1. 以太网接口（优先级最高）
```
eth0, eth1, lan0, enp0s3, eno1
```

### 2. WiFi接口（优先级中等）
```
wlan0, wlan1, wifi0, wlp2s0
```

### 3. 其他接口（优先级最低）
```
rmnet, ccmni, dummy, lo
```

## 🛡️ 虚拟地址过滤

### 1. 跳过虚拟MAC地址
```kotlin
// 跳过这些虚拟MAC地址
if (macAddress == "02:00:00:00:00:00" || 
    macAddress.startsWith("00:00:00")) {
    continue  // 跳过虚拟地址
}
```

### 2. 验证MAC地址格式
```kotlin
// 验证MAC地址长度和格式
if (macBytes != null && macBytes.size == 6) {
    val macAddress = macBytes.joinToString(":") { "%02x".format(it) }
    // 进一步处理...
}
```

## 📝 详细日志记录

### 1. 接口发现日志
```
🌐 [MAC地址] 发现接口: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] 🔌 以太网接口: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] 📶 WiFi接口: wlan0 = 11:22:33:44:55:66
```

### 2. 选择结果日志
```
🌐 [MAC地址] ✅ 选择以太网MAC: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] ✅ 从网络接口获取到真实MAC: aa:bb:cc:dd:ee:ff
```

### 3. 错误处理日志
```
🌐 [MAC地址] ⚠️ 跳过虚拟MAC: dummy0 = 02:00:00:00:00:00
🌐 [MAC地址] ❌ 无法获取真实MAC地址，返回默认值
```

## 🎯 预期效果

### 修复前
```
🌐 [MAC地址] WiFi MAC: 02:00:00:00:00:00
```

### 修复后
```
🌐 [MAC地址] 开始获取设备MAC地址...
🌐 [MAC地址] 遍历网络接口...
🌐 [MAC地址] 发现接口: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] 🔌 以太网接口: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] ✅ 选择以太网MAC: eth0 = aa:bb:cc:dd:ee:ff
🌐 [MAC地址] ✅ 从网络接口获取到真实MAC: aa:bb:cc:dd:ee:ff
```

## 🚀 测试验证

### 1. 编译安装
```bash
./gradlew clean assembleDebug
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 2. 查看日志
```bash
adb logcat | grep "MAC地址"
```

### 3. 检查数据库
```sql
SELECT sender_id, mac_address, network_type, local_ip 
FROM fa_sender_device_info 
WHERE sender_id = 'your-sender-id';
```

## 📋 常见网络接口类型

### Android设备常见接口
| 接口名 | 类型 | 说明 |
|--------|------|------|
| eth0 | 以太网 | 有线网络接口 |
| wlan0 | WiFi | 无线网络接口 |
| rmnet0 | 移动网络 | 蜂窝数据接口 |
| dummy0 | 虚拟接口 | 系统虚拟接口 |
| lo | 回环接口 | 本地回环接口 |

### 优先级排序
1. **以太网接口** (eth*, lan*, enp*, eno*) - 最可靠
2. **WiFi接口** (wlan*, wifi*, wlp*) - 较可靠
3. **其他接口** - 备选方案

## ⚠️ 注意事项

### 1. 权限要求
- 不需要额外权限
- 使用系统公开的网络接口API
- 读取系统文件可能需要root权限

### 2. 兼容性
- 支持Android 6.0+系统
- 兼容不同厂商的设备
- 处理各种网络接口命名规则

### 3. 隐私保护
- 遵循Android隐私政策
- 只获取必要的网络标识信息
- 不获取敏感的设备指纹

## 🔧 故障排除

### 1. 仍然获取到虚拟地址
- 检查设备是否有真实的网络接口
- 确认设备网络连接状态
- 查看详细日志确定原因

### 2. 权限不足
- 确保应用有网络状态权限
- 检查系统文件读取权限
- 考虑使用其他获取方法

### 3. 接口名称不匹配
- 添加新的接口名称匹配规则
- 查看设备的实际网络接口名称
- 更新接口类型识别逻辑

现在MAC地址获取应该能够正确识别真实的以太网MAC地址，而不是虚拟的`02:00:00:00:00:00`！
