<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 快速功能测试</h1>
    
    <div class="test-card">
        <h2>JavaScript加载测试</h2>
        <button class="btn" onclick="testAdminObject()">测试admin对象</button>
        <div id="adminStatus"></div>
    </div>
    
    <div class="test-card">
        <h2>STUN/TURN函数测试</h2>
        <button class="btn" onclick="testStunTurnFunctions()">测试函数定义</button>
        <div id="functionStatus"></div>
    </div>
    
    <div class="test-card">
        <h2>模拟调用测试</h2>
        <button class="btn" onclick="testFunctionCall()">模拟调用showStunTurnModal</button>
        <div id="callStatus"></div>
    </div>

    <script>
        function testAdminObject() {
            const statusDiv = document.getElementById('adminStatus');
            
            if (typeof admin !== 'undefined') {
                statusDiv.innerHTML = '<div class="status success">✅ admin对象已定义</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ admin对象未定义</div>';
            }
        }
        
        function testStunTurnFunctions() {
            const statusDiv = document.getElementById('functionStatus');
            const functions = [
                'showStunTurnModal',
                'closeStunTurnModal',
                'applyStunPreset',
                'applyTurnPreset',
                'saveStunTurnConfig',
                'saveAndBroadcastStunTurn'
            ];
            
            let results = [];
            
            if (typeof admin === 'undefined') {
                results.push('❌ admin对象未定义，无法测试函数');
            } else {
                functions.forEach(funcName => {
                    if (typeof admin[funcName] === 'function') {
                        results.push(`✅ ${funcName}: 已定义`);
                    } else {
                        results.push(`❌ ${funcName}: 未定义`);
                    }
                });
            }
            
            const className = results.some(r => r.includes('❌')) ? 'error' : 'success';
            statusDiv.innerHTML = `<div class="status ${className}">${results.join('<br>')}</div>`;
        }
        
        function testFunctionCall() {
            const statusDiv = document.getElementById('callStatus');
            
            try {
                if (typeof admin === 'undefined') {
                    throw new Error('admin对象未定义');
                }
                
                if (typeof admin.showStunTurnModal !== 'function') {
                    throw new Error('showStunTurnModal函数未定义');
                }
                
                // 创建必要的DOM元素进行测试
                if (!document.getElementById('stunTurnModal')) {
                    const modal = document.createElement('div');
                    modal.id = 'stunTurnModal';
                    modal.style.display = 'none';
                    document.body.appendChild(modal);
                }
                
                if (!document.getElementById('modalStunServers')) {
                    const textarea = document.createElement('textarea');
                    textarea.id = 'modalStunServers';
                    textarea.style.display = 'none';
                    document.body.appendChild(textarea);
                }
                
                if (!document.getElementById('modalTurnServers')) {
                    const textarea = document.createElement('textarea');
                    textarea.id = 'modalTurnServers';
                    textarea.style.display = 'none';
                    document.body.appendChild(textarea);
                }
                
                if (!document.getElementById('stunServers')) {
                    const textarea = document.createElement('textarea');
                    textarea.id = 'stunServers';
                    textarea.value = 'stun:stun.l.google.com:19302';
                    textarea.style.display = 'none';
                    document.body.appendChild(textarea);
                }
                
                if (!document.getElementById('turnServers')) {
                    const textarea = document.createElement('textarea');
                    textarea.id = 'turnServers';
                    textarea.value = '[]';
                    textarea.style.display = 'none';
                    document.body.appendChild(textarea);
                }
                
                // 尝试调用函数
                admin.showStunTurnModal();
                
                statusDiv.innerHTML = '<div class="status success">✅ 函数调用成功！showStunTurnModal可以正常执行</div>';
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 函数调用失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testAdminObject();
                testStunTurnFunctions();
            }, 1000); // 等待1秒确保admin对象已创建
        });
    </script>
    
    <!-- 模拟加载admin.js -->
    <script>
        // 模拟admin对象和相关方法
        let admin;
        
        // 模拟DOMContentLoaded事件处理
        setTimeout(() => {
            // 模拟SignalingServerAdmin类
            class MockSignalingServerAdmin {
                updateConfig() { return true; }
                updateConfigAndBroadcast() { return true; }
                sendQuickCommand() { return true; }
            }
            
            admin = new MockSignalingServerAdmin();
            
            // 添加STUN/TURN配置相关方法
            admin.showStunTurnModal = function() {
                const modal = document.getElementById('stunTurnModal');
                const stunServers = document.getElementById('modalStunServers');
                const turnServers = document.getElementById('modalTurnServers');
                
                if (modal && stunServers && turnServers) {
                    stunServers.value = document.getElementById('stunServers').value;
                    turnServers.value = document.getElementById('turnServers').value;
                    modal.style.display = 'block';
                }
            };

            admin.closeStunTurnModal = function() {
                const modal = document.getElementById('stunTurnModal');
                if (modal) {
                    modal.style.display = 'none';
                }
            };

            admin.applyStunPreset = function() {
                return true;
            };

            admin.applyTurnPreset = function() {
                return true;
            };

            admin.saveStunTurnConfig = function() {
                admin.updateConfig();
                admin.closeStunTurnModal();
            };

            admin.saveAndBroadcastStunTurn = function() {
                admin.updateConfigAndBroadcast();
                admin.closeStunTurnModal();
            };
            
            console.log('模拟admin对象已创建');
        }, 500);
    </script>
</body>
</html>
