# 自动重启机制实现

## 功能概述

当视频推流无法正常启动时，系统会自动触发重启机制，包括设备日志记录、FTP日志上传和重启次数限制。

## 核心组件

### 1. DeviceLogManager - 设备日志管理器
- **功能**：记录设备重要日志并上报到信令服务器
- **位置**：`utils/DeviceLogManager.kt`
- **特性**：
  - 记录设备事件日志
  - 发送日志到信令服务器
  - 管理每日重启次数限制（最多10次）
  - 自动重置每日计数

### 2. AutoRebootManager - 自动重启管理器
- **功能**：执行自动重启逻辑
- **位置**：`utils/AutoRebootManager.kt`
- **特性**：
  - 检查重启权限（每日限制）
  - 重启前上传日志到FTP
  - 使用ZtlManager执行重启
  - 备用重启方法

### 3. 视频源失败检测
- **位置**：`WebRTCManager.kt`
- **触发条件**：视频源连续失败3次
- **检测间隔**：5分钟内的失败才计数

## 工作流程

### 1. 视频源启动失败
```
视频源启动失败 → handleVideoSourceFailure() → 增加失败计数 → 检查是否达到阈值(3次)
```

### 2. 触发自动重启
```
达到失败阈值 → 检查每日重启次数 → 记录重启日志 → 上传FTP日志 → 执行重启
```

### 3. 重启执行
```
ZtlManager.reboot(10) → 备用方法(Shell/PowerManager) → 10秒后重启
```

## 重启限制机制

### 每日重启次数限制
- **最大次数**：10次/天
- **重置时间**：每天00:00自动重置
- **存储方式**：SharedPreferences
- **超限处理**：记录日志，拒绝重启

### 失败计数重置
- **重置间隔**：5分钟
- **重置条件**：距离上次失败超过5分钟
- **目的**：避免偶发性失败触发重启

## 日志记录

### 设备日志事件类型
- `video_source_failure` - 视频源启动失败
- `auto_reboot_triggered` - 自动重启已触发
- `auto_reboot_rejected` - 自动重启被拒绝
- `pre_reboot_log_upload` - 重启前日志上传
- `device_reboot_start` - 设备重启开始

### 日志格式
```json
{
  "device_id": "设备ID",
  "event_type": "事件类型",
  "event_level": "日志级别",
  "message": "消息内容",
  "details": "详细信息",
  "timestamp": "时间戳",
  "app_version": "应用版本",
  "system_info": "系统信息"
}
```

## 信令服务器集成

### 消息类型
- **发送**：`device_log` - 设备日志消息
- **处理**：信令服务器接收并记录到日志表

### 数据库表结构（建议）
```sql
CREATE TABLE device_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    details TEXT,
    timestamp BIGINT NOT NULL,
    formatted_time DATETIME NOT NULL,
    app_version VARCHAR(50),
    system_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_id (device_id),
    INDEX idx_event_type (event_type),
    INDEX idx_timestamp (timestamp)
);
```

## 使用方法

### 手动触发重启
```kotlin
AutoRebootManager.getInstance().triggerAutoReboot(
    context,
    "手动触发重启",
    "测试重启机制"
)
```

### 记录设备日志
```kotlin
DeviceLogManager.getInstance().logDeviceEvent(
    context,
    "custom_event",
    "INFO",
    "自定义事件",
    "事件详情"
)
```

### 检查重启权限
```kotlin
val canReboot = DeviceLogManager.getInstance().canReboot(context)
if (canReboot) {
    // 可以执行重启
} else {
    // 今日重启次数已达上限
}
```

## 配置参数

### WebRTCManager
- `maxFailureCount = 3` - 最大失败次数
- `failureResetInterval = 300000L` - 失败重置间隔(5分钟)

### AutoRebootManager
- `REBOOT_DELAY_SECONDS = 10` - 重启延迟时间

### DeviceLogManager
- `MAX_DAILY_REBOOTS = 10` - 每日最大重启次数

## 版本信息

**当前版本**：v1.46
**新增功能**：
- ✅ 视频推流失败自动重启
- ✅ 设备日志管理和上报
- ✅ 每日重启次数限制（持久化保存）
- ✅ 重启前FTP日志上传
- ✅ ZtlManager重启集成
- ✅ 信令服务器日志记录
- ✅ 摄像头策略错误自动重启（3次重试后触发）
- ✅ 备用摄像头失败自动重启
- ✅ 重启次数统计和调试功能
- ✅ Lint错误修复和权限检查优化
- ✅ 音频录制权限安全检查
- ✅ 修复重复启动无限循环问题
- ✅ 修复信令服务器WebSocket连接错误
- ✅ 添加服务启动防重复调用机制
- ✅ 优化异步摄像头验证逻辑

## 重启触发条件

### 1. 视频源启动失败（WebRTCManager）
- **触发条件**：视频源连续失败3次
- **检测间隔**：5分钟内的失败才计数
- **触发位置**：`WebRTCManager.handleVideoSourceFailure()`

### 2. 摄像头策略错误（WebRTCClient）
- **触发条件**：摄像头策略错误重试3次失败
- **错误类型**：`CAMERA_DISABLED by policy`
- **触发位置**：`WebRTCClient.attemptDirectRecovery()`

### 3. 备用摄像头全部失败
- **触发条件**：原摄像头和所有备用摄像头都失败
- **触发位置**：`WebRTCClient.attemptFallbackCamera()`

### 4. 无可用备用摄像头
- **触发条件**：没有找到任何备用摄像头
- **触发位置**：`WebRTCClient.attemptFallbackCamera()`

## 测试建议

1. **模拟视频源失败**：断开摄像头连接，观察失败计数
2. **测试重启限制**：连续触发重启，验证每日限制
3. **验证日志上传**：检查FTP服务器日志文件
4. **信令服务器日志**：确认设备日志正确发送到服务器
5. **重启功能测试**：验证ZtlManager重启是否正常工作
