@echo off
echo ===================================
echo    WebRTC Test Sender
echo ===================================
echo.

set SENDER_ID=sender-test-%RANDOM%

echo 1. Gradient pattern
echo 2. Checkerboard pattern
echo 3. Color bars
set /p PATTERN_CHOICE="Choose pattern (1-3, default 1): "

if "%PATTERN_CHOICE%"=="2" (
    set PATTERN=checkerboard
) else if "%PATTERN_CHOICE%"=="3" (
    set PATTERN=color
) else (
    set PATTERN=gradient
)

echo.
echo Starting test sender...
echo.
echo Sender info:
echo - Signaling server: wss://sling.91jdcd.com/ws/
echo - Sender ID: %SENDER_ID%
echo - Pattern: %PATTERN%
echo.
echo Press Ctrl+C to stop
echo.

python start_test_sender.py --id "%SENDER_ID%" --name "Test Sender" --description "Test video sender" --pattern "%PATTERN%" --signaling wss://sling.91jdcd.com/ws/

pause
