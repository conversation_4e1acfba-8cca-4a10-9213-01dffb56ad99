# WebSocket 开机监控系统

## 功能概述

将开机监控页面从HTTP轮询改为WebSocket实时通信，并添加密码认证功能。

## 主要改进

### 1. WebSocket 实时通信

**原来**：HTTP轮询，每5秒请求一次
```javascript
setInterval(loadDevices, 5000); // HTTP GET请求
```

**现在**：WebSocket实时推送
```javascript
websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    handleWebSocketMessage(data);
};
```

### 2. 密码认证系统

**登录界面**：
- 密码输入框
- 自动保存到localStorage
- 下次访问自动填写

**认证流程**：
```
用户输入密码 → WebSocket连接 → 发送订阅请求 → 服务器验证 → 认证成功/失败
```

### 3. 连接状态管理

**状态指示器**：
- 🔴 未连接/断开
- 🟡 正在连接
- 🟢 已连接

**自动重连**：
- 指数退避算法
- 最多重连5次
- 最大延迟30秒

## 技术实现

### 1. 前端 WebSocket 客户端

**连接建立**：
```javascript
const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
const wsUrl = `${protocol}//${window.location.host}`;
websocket = new WebSocket(wsUrl);
```

**订阅请求**：
```javascript
const subscribeMessage = {
    type: 'subscribe_boot_monitor',
    password: password
};
websocket.send(JSON.stringify(subscribeMessage));
```

**消息处理**：
```javascript
switch (data.type) {
    case 'boot_devices_update':
        // 更新设备列表
        break;
    case 'auth_result':
        // 处理认证结果
        break;
    case 'device_boot':
        // 新设备开机通知
        break;
}
```

### 2. 服务器端 WebSocket 处理

**订阅处理**：
```python
if data.get('type') == 'subscribe_boot_monitor':
    password = data.get('password', '')
    
    if password == BOOT_MONITOR_PASSWORD:
        boot_monitor_subscribers.add(websocket)
        # 发送认证成功和当前设备列表
    else:
        # 认证失败，关闭连接
        await websocket.close(code=1008, reason='认证失败')
```

**实时推送**：
```python
async def send_boot_devices_update(websocket=None):
    # 构建设备列表
    message = {
        'type': 'boot_devices_update',
        'success': True,
        'devices': devices_list,
        'total': len(devices_list),
        'timestamp': current_time
    }
    
    # 发送给所有订阅者
    for subscriber in boot_monitor_subscribers:
        await subscriber.send(json.dumps(message))
```

### 3. 密码配置

**服务器端配置**：
```python
# 监控密码配置
BOOT_MONITOR_PASSWORD = "admin123"  # 可以从配置文件读取
```

**前端存储**：
```javascript
// 保存密码到本地存储
localStorage.setItem('monitor_password', password);

// 下次访问自动填写
const savedPassword = localStorage.getItem('monitor_password');
if (savedPassword) {
    document.getElementById('passwordInput').value = savedPassword;
    authenticate();
}
```

## 消息协议

### 1. 客户端到服务器

**订阅请求**：
```json
{
    "type": "subscribe_boot_monitor",
    "password": "admin123"
}
```

### 2. 服务器到客户端

**认证结果**：
```json
{
    "type": "auth_result",
    "success": true,
    "message": "认证成功"
}
```

**设备列表更新**：
```json
{
    "type": "boot_devices_update",
    "success": true,
    "devices": [
        {
            "cpu_unique_id": "CPU_12345678",
            "cpu_id_short": "CPU_1234...",
            "boot_time": 1692959000000,
            "uptime_seconds": 3600,
            "uptime_display": "1小时0分",
            "game_package": "com.example.game",
            "device_brand": "Samsung",
            "device_model": "SM-G973F",
            "android_version": "11",
            "app_version": "1.24",
            "local_ip": "*************",
            "public_ip": "***********"
        }
    ],
    "total": 1,
    "timestamp": 1692963600000
}
```

**新设备开机通知**：
```json
{
    "type": "device_boot",
    "device": {
        "cpu_unique_id": "CPU_87654321",
        "device_brand": "Xiaomi",
        "device_model": "Mi 11"
    }
}
```

## 安全特性

### 1. 密码认证
- 连接时必须提供正确密码
- 密码错误立即关闭连接
- 支持密码本地保存

### 2. 连接管理
- 自动清理断开的订阅者
- 防止内存泄漏
- 连接状态实时监控

### 3. 错误处理
- 网络异常自动重连
- 认证失败友好提示
- 连接状态可视化

## 使用方法

### 1. 启动服务器
```bash
python3 enhanced_signaling_server.py --log-dir ./logs
```

### 2. 访问监控页面
```
http://your-server:28080/boot_monitor.html
```

### 3. 输入密码
默认密码：`admin123`

### 4. 实时监控
- 页面会实时显示所有开机设备
- 运行时间每秒自动更新
- 新设备开机会立即显示

## 配置选项

### 1. 修改监控密码
编辑 `enhanced_signaling_server.py`：
```python
BOOT_MONITOR_PASSWORD = "your_password_here"
```

### 2. 调整重连参数
编辑 `boot_monitor.html`：
```javascript
let maxReconnectAttempts = 5;  // 最大重连次数
```

### 3. 修改WebSocket端口
WebSocket使用与HTTP相同的端口（默认28080）

## 性能优势

### 1. 实时性
- **HTTP轮询**：最多5秒延迟
- **WebSocket**：毫秒级实时推送

### 2. 资源消耗
- **HTTP轮询**：每5秒一次HTTP请求
- **WebSocket**：仅在数据变化时推送

### 3. 用户体验
- **HTTP轮询**：固定刷新间隔
- **WebSocket**：即时更新，连接状态可视

## 故障排除

### 1. 连接失败
- 检查服务器是否启动
- 确认端口是否开放
- 查看浏览器控制台错误

### 2. 认证失败
- 确认密码是否正确
- 清除浏览器localStorage
- 检查服务器日志

### 3. 自动重连失败
- 检查网络连接
- 查看重连次数是否达到上限
- 手动点击重新连接按钮

## 版本更新

当前版本：v1.24 → v1.25

主要改进：
- HTTP轮询改为WebSocket实时通信
- 添加密码认证系统
- 实现自动重连机制
- 改进连接状态管理
- 优化用户体验和性能
