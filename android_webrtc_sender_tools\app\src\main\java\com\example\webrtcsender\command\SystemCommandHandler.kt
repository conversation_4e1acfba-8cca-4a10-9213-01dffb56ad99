package com.example.webrtcsender.command

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import com.example.webrtcsender.utils.Logger
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 系统控制命令处理器
 * 处理 reboot_device 命令
 */
class SystemCommandHandler(
    private val context: Context,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "SystemCommandHandler"
    }
    
    override fun executeControlCommand(command: String, params: JSONObject): Boolean {
        return when (command) {
            "reboot_device" -> rebootDevice(params)
            else -> {
                Logger.w(TAG, "❓ 不支持的系统命令: $command")
                false
            }
        }
    }
    
    /**
     * 重启设备
     */
    private fun rebootDevice(params: JSONObject): Boolean {
        return try {
            Logger.i(TAG, "🔄 重启设备")
            statusCallback("system", "rebooting", 10, "正在重启设备...")

            // 尝试多种重启方法
            var success = false

            // 方法1: 检查是否有root权限
            if (hasRootPermission()) {
                Logger.i(TAG, "✅ 检测到root权限，尝试root重启")
                statusCallback("system", "rebooting", 30, "检测到root权限，尝试root重启...")
                success = tryRootReboot()
            }

            // 方法2: 如果root重启失败，尝试系统重启
            if (!success) {
                Logger.i(TAG, "🔄 尝试系统权限重启")
                statusCallback("system", "rebooting", 60, "尝试系统权限重启...")
                success = trySystemReboot()
            }

            // 方法3: 如果都失败，尝试强制重启
            if (!success) {
                Logger.i(TAG, "🔄 尝试强制重启")
                statusCallback("system", "rebooting", 80, "尝试强制重启...")
                success = tryForceReboot()
            }

            // 方法4: 如果都失败，尝试Intent重启
            if (!success) {
                Logger.i(TAG, "🔄 尝试Intent重启")
                statusCallback("system", "rebooting", 90, "尝试Intent重启...")
                success = tryIntentReboot()
            }

            if (success) {
                statusCallback("system", "rebooted", 100, "设备重启命令已执行")
                Logger.i(TAG, "✅ 重启命令执行成功")
            } else {
                statusCallback("system", "reboot_failed", 0, "所有重启方法都失败")
                Logger.e(TAG, "❌ 所有重启方法都失败")
            }

            success
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 重启设备失败: ${e.message}")
            statusCallback("system", "reboot_failed", 0, "重启设备失败: ${e.message}")
            false
        }
    }
    
    /**
     * 尝试root重启
     */
    private fun tryRootReboot(): Boolean {
        return try {
            Logger.i(TAG, "🔧 执行root重启命令")

            // 尝试多种root重启命令
            val commands = arrayOf(
                "su -c 'reboot'",
                "su -c 'reboot now'",
                "su -c '/system/bin/reboot'",
                "su -c 'echo 1 > /proc/sys/kernel/sysrq && echo b > /proc/sysrq-trigger'"
            )

            for (command in commands) {
                try {
                    Logger.d(TAG, "尝试命令: $command")
                    val process = Runtime.getRuntime().exec(command)

                    // 等待命令执行
                    val exitCode = process.waitFor()
                    Logger.d(TAG, "命令 '$command' 退出码: $exitCode")

                    if (exitCode == 0) {
                        Logger.i(TAG, "✅ Root重启命令执行成功: $command")
                        return true
                    }
                } catch (e: Exception) {
                    Logger.w(TAG, "命令 '$command' 执行失败: ${e.message}")
                }
            }

            Logger.w(TAG, "⚠️ 所有root重启命令都失败")
            false
        } catch (e: Exception) {
            Logger.e(TAG, "❌ Root重启失败: ${e.message}")
            false
        }
    }

    /**
     * 尝试系统重启
     */
    private fun trySystemReboot(): Boolean {
        return try {
            Logger.i(TAG, "🔧 执行系统重启")

            val pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager?
            if (pm != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                pm.reboot("Remote command")
                Logger.i(TAG, "✅ 系统重启命令执行成功")
                true
            } else {
                Logger.w(TAG, "⚠️ 系统重启不可用")
                false
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 系统重启失败: ${e.message}")
            false
        }
    }

    /**
     * 尝试强制重启
     */
    private fun tryForceReboot(): Boolean {
        return try {
            Logger.i(TAG, "🔧 执行强制重启")

            // 尝试直接执行系统命令
            val commands = arrayOf(
                "reboot",
                "/system/bin/reboot",
                "toolbox reboot",
                "busybox reboot"
            )

            for (command in commands) {
                try {
                    Logger.d(TAG, "尝试强制命令: $command")
                    Runtime.getRuntime().exec(command)
                    Logger.i(TAG, "✅ 强制重启命令已执行: $command")
                    return true
                } catch (e: Exception) {
                    Logger.w(TAG, "强制命令 '$command' 失败: ${e.message}")
                }
            }

            Logger.w(TAG, "⚠️ 所有强制重启命令都失败")
            false
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 强制重启失败: ${e.message}")
            false
        }
    }

    /**
     * 尝试Intent重启
     */
    private fun tryIntentReboot(): Boolean {
        return try {
            Logger.i(TAG, "🔧 执行Intent重启")

            // 尝试发送重启Intent
            val intent = Intent(Intent.ACTION_REBOOT)
            intent.putExtra("nowait", 1)
            intent.putExtra("interval", 1)
            intent.putExtra("window", 0)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            context.startActivity(intent)
            Logger.i(TAG, "✅ Intent重启命令已发送")
            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ Intent重启失败: ${e.message}")
            false
        }
    }

    /**
     * 检查是否有root权限
     */
    private fun hasRootPermission(): Boolean {
        return try {
            Logger.d(TAG, "🔍 检查root权限...")

            // 方法1: 检查su命令
            val process = Runtime.getRuntime().exec("su")
            process.outputStream.close()
            val exitCode = process.waitFor()

            if (exitCode == 0) {
                Logger.d(TAG, "✅ su命令检查通过")

                // 方法2: 尝试执行一个简单的root命令
                try {
                    val testProcess = Runtime.getRuntime().exec("su -c 'id'")
                    val reader = BufferedReader(InputStreamReader(testProcess.inputStream))
                    val output = reader.readLine()
                    testProcess.waitFor()
                    reader.close()

                    val hasRootAccess = output?.contains("uid=0") == true
                    Logger.d(TAG, "Root权限测试结果: $hasRootAccess (输出: $output)")
                    return hasRootAccess
                } catch (e: Exception) {
                    Logger.w(TAG, "Root权限测试失败: ${e.message}")
                }
            }

            Logger.d(TAG, "Root权限检查结果: false (退出码: $exitCode)")
            false
        } catch (e: Exception) {
            Logger.d(TAG, "检查root权限失败: ${e.message}")
            false
        }
    }
    
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        // 系统命令处理器不处理升级命令
        Logger.w(TAG, "⚠️ 系统命令处理器不处理升级命令")
    }
    
    override fun updateServerConfig(configData: JSONObject) {
        // 系统命令处理器不处理配置更新
        Logger.w(TAG, "⚠️ 系统命令处理器不处理配置更新")
    }
    
    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success)
    }
}
