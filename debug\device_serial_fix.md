# Android 设备序列号获取修复

## 修改说明

根据要求，修改了序列号获取逻辑，改为获取 Android 设备真正的序列号，而不是自己生成的标识符。

## 修改内容

### 1. 主要逻辑调整

**修改前**：生成各种自定义标识符
```kotlin
val candidates = listOf(
    "ANDROID_ID_${androidId.take(8)}",
    generateDeviceFingerprint(),
    "BUILD_${Build.BRAND}_${Build.MODEL}_${Build.DEVICE}"
)
```

**修改后**：优先获取真实设备序列号
```kotlin
// 尝试获取Android设备真实序列号
val deviceSerial = getDeviceSerial()

if (deviceSerial.isNotEmpty() && deviceSerial != "unknown") {
    "DEVICE_SN_$deviceSerial"
} else {
    // 备用方案：使用Android ID
    "ANDROID_ID_$androidId"
}
```

### 2. 设备序列号获取方法

**Android 版本兼容性处理**：
```kotlin
@SuppressLint("HardwareIds")
private fun getDeviceSerial(): String {
    return try {
        when {
            // Android 9 (API 28) 及以上版本
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> {
                try {
                    Build.getSerial()  // 需要READ_PHONE_STATE权限
                } catch (e: SecurityException) {
                    getSerialByReflection()  // 权限被拒绝时使用反射
                }
            }
            // Android 8 及以下版本
            else -> {
                @Suppress("DEPRECATION")
                val serial = Build.SERIAL
                if (serial.isNotEmpty() && serial != "unknown") {
                    serial
                } else {
                    getSerialByReflection()
                }
            }
        }
    } catch (e: Exception) {
        ""
    }
}
```

### 3. 反射获取序列号

**系统属性访问**：
```kotlin
private fun getSerialByReflection(): String {
    return try {
        val systemPropertiesClass = Class.forName("android.os.SystemProperties")
        val getMethod = systemPropertiesClass.getMethod("get", String::class.java, String::class.java)
        val serial = getMethod.invoke(null, "ro.serialno", "unknown") as String
        
        if (serial.isNotEmpty() && serial != "unknown") {
            serial
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }
}
```

## 获取策略

### 优先级顺序

1. **ZtlManager.getBuildSerial()** - 首选方案
2. **Build.getSerial()** - Android 9+ 官方API
3. **Build.SERIAL** - Android 8- 已弃用API
4. **SystemProperties.get("ro.serialno")** - 反射获取系统属性
5. **Android ID** - 备用方案
6. **时间戳** - 最终备用方案

### 版本兼容性

| Android 版本 | 主要方法 | 备用方法 |
|--------------|----------|----------|
| Android 9+ | `Build.getSerial()` | 反射获取 `ro.serialno` |
| Android 8- | `Build.SERIAL` | 反射获取 `ro.serialno` |
| 所有版本 | Android ID | 时间戳 |

## 返回值格式

### 可能的返回值

| 情况 | 返回格式 | 示例 |
|------|----------|------|
| ZtlManager成功 | `ZTL_SN_[序列号]` | `ZTL_SN_ABC123DEF456` |
| 设备序列号 | `DEVICE_SN_[序列号]` | `DEVICE_SN_R58M70ABCDE` |
| Android ID | `ANDROID_ID_[ID]` | `ANDROID_ID_a1b2c3d4e5f6g7h8` |
| 时间戳备用 | `FALLBACK_[时间戳]` | `FALLBACK_12345678` |
| 最终备用 | `UNKNOWN_[时间戳]` | `UNKNOWN_123456` |

### 序列号特征

**真实设备序列号通常特征**：
- 长度：8-20个字符
- 格式：字母数字组合
- 示例：`R58M70ABCDE`, `HT7B1234567`, `988ABCD123456`

## 权限要求

### 可能需要的权限

```xml
<!-- Android 9+ 获取序列号需要此权限，但通常不会被授予普通应用 -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
```

### 权限处理

- **有权限**：直接使用 `Build.getSerial()`
- **无权限**：自动降级到反射方法
- **反射失败**：使用 Android ID 备用方案

## 日志输出

### 详细的日志记录

```kotlin
Logger.d(TAG, "✅ ZtlManager序列号获取成功: ABC12345...")
Logger.w(TAG, "🔒 ZtlManager权限不足，使用备用方案")
Logger.d(TAG, "✅ 获取到设备序列号: R58M70AB...")
Logger.d(TAG, "Build.getSerial()权限被拒绝: SecurityException")
Logger.d(TAG, "通过反射获取序列号成功: R58M70AB...")
Logger.w(TAG, "⚠️ 无法获取设备序列号，使用Android ID")
```

### 日志级别

- **DEBUG**：成功获取序列号的详细信息
- **WARN**：权限问题或降级使用备用方案
- **ERROR**：所有方法都失败的情况

## 测试建议

### 1. 功能测试

```kotlin
val deviceInfo = DeviceInfoCollector.collectDeviceInfo(context)
val wechatSn = deviceInfo.getString("wechat_sn")

// 验证返回值格式
assert(wechatSn.isNotEmpty())
println("设备序列号: $wechatSn")

// 检查是否获取到真实序列号
if (wechatSn.startsWith("DEVICE_SN_")) {
    println("✅ 成功获取到设备真实序列号")
} else if (wechatSn.startsWith("ZTL_SN_")) {
    println("✅ 使用ZtlManager序列号")
} else {
    println("⚠️ 使用备用方案: $wechatSn")
}
```

### 2. 权限测试

```kotlin
// 测试不同权限状态下的行为
// 1. 有READ_PHONE_STATE权限
// 2. 无READ_PHONE_STATE权限
// 3. 反射被限制的情况
```

### 3. 版本兼容性测试

- **Android 9+**：测试 `Build.getSerial()` 和权限处理
- **Android 8-**：测试 `Build.SERIAL` 的使用
- **所有版本**：测试反射方法的可用性

## 安全考虑

### 1. 权限最小化

- 不强制要求 `READ_PHONE_STATE` 权限
- 权限被拒绝时自动降级
- 提供多层备用方案

### 2. 隐私保护

- 日志中只显示序列号前几位
- 不在日志中暴露完整序列号
- 遵循Android隐私政策

### 3. 兼容性

- 支持所有Android版本
- 处理厂商定制系统的差异
- 优雅处理API变更

## 版本更新

当前版本：v1.22 → v1.23

主要改进：
- 改为获取Android设备真实序列号
- 增强版本兼容性处理
- 添加反射获取系统属性方法
- 优化权限处理和降级策略
- 改进日志记录和错误处理
