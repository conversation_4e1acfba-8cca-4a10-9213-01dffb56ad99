# 控制台新功能说明

## 界面更新

### 发送端状态监控改进

原来的发送端状态卡片下方有三个独立按钮：
- 开始服务
- 停止服务  
- 重启设备

现在改为：
- **主操作按钮**: 根据设备状态显示"启动服务"或"重启服务"
- **下拉箭头按钮**: 点击展开完整的功能控制菜单

### 下拉菜单功能

点击箭头按钮后，会弹出包含以下分类的功能菜单：

#### 🚀 服务控制
- 启动服务
- 停止服务
- 重启服务

#### 🎥 视频控制
- 视频参数设置 (分辨率、码率、帧率)
- 立即截屏

#### 🎮 游戏控制
- 游戏设置 (自动启动游戏开关、游戏包名)

#### 📝 日志管理
- 开启日志显示
- 关闭日志显示
- 下载日志(FTP)
- 下载日志(HTTP)

#### 🔧 系统控制
- 重启设备 (需要确认)
- 升级应用

## 新增命令支持

控制台现在支持以下新命令：

### 1. 设置自动启动游戏 (set_auto_start_game)
```json
{
  "command": "set_auto_start_game",
  "params": {
    "enabled": true,
    "package_name": "com.example.game"
  }
}
```

### 2. 开关日志输出 (toggle_log_display)
```json
{
  "command": "toggle_log_display",
  "params": {
    "enabled": true
  }
}
```

### 3. 下载最近日志 (download_logs)
```json
{
  "command": "download_logs",
  "params": {
    "method": "ftp"
  }
}
```

### 4. 截屏 (take_screenshot)
```json
{
  "command": "take_screenshot",
  "params": {
    "request_id": "admin_1640995200123"
  }
}
```

## 交互改进

### 智能对话框
- **视频参数设置**: 依次提示输入分辨率、码率、帧率
- **游戏设置**: 确认是否启用，可选输入游戏包名
- **设备重启**: 需要用户确认，防止误操作
- **应用升级**: 提示输入APK地址、版本号、是否强制升级

### 用户体验
- 点击外部区域自动关闭下拉菜单
- 操作完成后自动关闭菜单
- 实时日志显示操作结果
- 支持批量操作选中的设备

## 信令服务器更新

### 新增消息类型处理

#### 截屏结果消息 (screenshot_result)
```json
{
  "type": "screenshot_result",
  "request_id": "admin_1640995200123",
  "success": true,
  "full_url": "https://example.com/screenshot.jpg",
  "message": "截屏成功",
  "timestamp": 1640995200
}
```

#### 命令响应消息 (command_response)
```json
{
  "type": "command_response",
  "command": "toggle_log_display",
  "success": true,
  "message": "日志显示已开启",
  "timestamp": 1640995200
}
```

### 命令签名
- 管理控制台发送的命令不需要签名
- 信令服务器自动为转发给发送端的命令添加签名
- 确保命令的安全性和完整性

## 使用方法

1. **访问控制台**: 打开 `http://服务器地址:8080/admin.html`
2. **查看设备状态**: 在"发送端状态监控"区域查看所有在线设备
3. **快速操作**: 点击主按钮执行常用操作
4. **高级功能**: 点击下拉箭头访问完整功能菜单
5. **批量操作**: 在"批量操作"区域选择多个设备进行统一管理

## 注意事项

- 所有操作都会在日志区域显示结果
- 重启设备等危险操作需要确认
- 截屏功能会返回图片URL
- 日志下载支持FTP和HTTP两种方式
- 设备必须在线才能接收命令

## 兼容性

- 新功能向后兼容现有的Android发送端
- 旧版本发送端会忽略不支持的命令
- 建议升级到最新版本以获得完整功能支持
