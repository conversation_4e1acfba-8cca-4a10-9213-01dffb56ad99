package com.ironnet.http_live_game.streaming

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.media.Image
import android.util.Log
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.OutputStream
import java.util.concurrent.atomic.AtomicBoolean

/**
 * MJPEG流处理类
 */
class MjpegStreamer(private val outputStream: OutputStream) {
    private val TAG = "MjpegStreamer"
    private val running = AtomicBoolean(false)
    private val boundary = "mjpegstream"
    private val jpegQuality = 80

    // 发送HTTP头
    fun start() {
        if (running.getAndSet(true)) {
            // 已经在运行
            return
        }

        try {
            val headers = "HTTP/1.0 200 OK\r\n" +
                    "Server: MjpegStreamer\r\n" +
                    "Connection: close\r\n" +
                    "Cache-Control: no-cache, no-store, must-revalidate, private\r\n" +
                    "Pragma: no-cache\r\n" +
                    "Expires: 0\r\n" +
                    "Access-Control-Allow-Origin: *\r\n" +
                    "Content-Type: multipart/x-mixed-replace; boundary=$boundary\r\n" +
                    "\r\n"
            outputStream.write(headers.toByteArray())
            outputStream.flush()
        } catch (e: IOException) {
            Log.e(TAG, "Error sending MJPEG headers: ${e.message}", e)
            stop()
        }
    }

    // 发送一帧JPEG图像
    fun sendFrame(bitmap: Bitmap) {
        if (!running.get()) {
            // 如果流已经停止，回收位图
            Log.d(TAG, "Stream not running, recycling bitmap")
            bitmap.recycle()
            return
        }

        try {
            if (bitmap.isRecycled) {
                Log.e(TAG, "Cannot send a recycled bitmap")
                return
            }

            val jpegBytes = compressBitmap(bitmap)

            // 无论压缩是否成功，都回收位图
            bitmap.recycle()

            if (jpegBytes != null) {
                try {
                    val header = "--$boundary\r\n" +
                            "Content-Type: image/jpeg\r\n" +
                            "Content-Length: ${jpegBytes.size}\r\n" +
                            "\r\n"

                    Log.d(TAG, "Sending MJPEG frame: ${jpegBytes.size} bytes")
                    outputStream.write(header.toByteArray())
                    outputStream.write(jpegBytes)
                    outputStream.write("\r\n".toByteArray())
                    outputStream.flush()
                } catch (e: IOException) {
                    Log.e(TAG, "Error sending MJPEG frame: ${e.message}")
                    stop()
                }
            } else {
                Log.e(TAG, "Failed to compress bitmap to JPEG")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in sendFrame: ${e.message}", e)
            stop()
        }
    }

    // 从Image对象发送一帧
    fun sendFrame(image: Image) {
        if (!running.get()) return

        val bitmap = imageToBitmap(image)
        if (bitmap != null) {
            sendFrame(bitmap)
            bitmap.recycle()
        }
    }

    // 停止流
    fun stop() {
        running.set(false)
        try {
            outputStream.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing output stream: ${e.message}")
        }
    }

    // 检查是否正在运行
    fun isRunning(): Boolean {
        return running.get()
    }

    // 将Bitmap压缩为JPEG字节数组
    private fun compressBitmap(bitmap: Bitmap): ByteArray? {
        if (bitmap.isRecycled) {
            Log.e(TAG, "Cannot compress a recycled bitmap")
            return null
        }

        val outputStream = ByteArrayOutputStream()
        try {
            val success = bitmap.compress(Bitmap.CompressFormat.JPEG, jpegQuality, outputStream)

            if (!success) {
                Log.e(TAG, "Failed to compress bitmap")
                return null
            }

            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e(TAG, "Error compressing bitmap: ${e.message}")
        } finally {
            try {
                outputStream.close()
            } catch (e: IOException) {
                Log.e(TAG, "Error closing output stream: ${e.message}")
            }
        }
        return null
    }

    // 将YUV_420_888格式的Image转换为Bitmap
    private fun imageToBitmap(image: Image): Bitmap? {
        try {
            val width = image.width
            val height = image.height

            // 创建输出Bitmap
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)

            // 获取YUV平面
            val yPlane = image.planes[0]
            val uPlane = image.planes[1]
            val vPlane = image.planes[2]

            val yBuffer = yPlane.buffer
            val uBuffer = uPlane.buffer
            val vBuffer = vPlane.buffer

            val yPixelStride = yPlane.pixelStride
            val yRowStride = yPlane.rowStride
            val uPixelStride = uPlane.pixelStride
            val uRowStride = uPlane.rowStride
            val vPixelStride = vPlane.pixelStride
            val vRowStride = vPlane.rowStride

            // 创建输出数组
            val argbArray = IntArray(width * height)

            // 转换YUV到ARGB
            var yPos = 0
            for (y in 0 until height) {
                val uRow = (y shr 1) * uRowStride
                val vRow = (y shr 1) * vRowStride

                for (x in 0 until width) {
                    val yValue = (yBuffer.get(y * yRowStride + x * yPixelStride).toInt() and 0xFF) - 16

                    val uIndex = uRow + ((x shr 1) * uPixelStride)
                    val vIndex = vRow + ((x shr 1) * vPixelStride)

                    val uValue = (uBuffer.get(uIndex).toInt() and 0xFF) - 128
                    val vValue = (vBuffer.get(vIndex).toInt() and 0xFF) - 128

                    // YUV到RGB转换
                    var r = (1.164 * yValue + 1.596 * vValue).toInt()
                    var g = (1.164 * yValue - 0.813 * vValue - 0.391 * uValue).toInt()
                    var b = (1.164 * yValue + 2.018 * uValue).toInt()

                    // 裁剪值到0-255范围
                    r = r.coerceIn(0, 255)
                    g = g.coerceIn(0, 255)
                    b = b.coerceIn(0, 255)

                    // 组合ARGB
                    argbArray[yPos++] = 0xFF000000.toInt() or (r shl 16) or (g shl 8) or b
                }
            }

            // 将数组设置到Bitmap
            bitmap.setPixels(argbArray, 0, width, 0, 0, width, height)

            return bitmap
        } catch (e: Exception) {
            Log.e(TAG, "Error converting YUV to bitmap: ${e.message}", e)
        }
        return null
    }
}
