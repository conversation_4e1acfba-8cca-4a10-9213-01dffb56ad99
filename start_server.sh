#!/bin/bash
# WebRTC视频流服务器启动脚本 (Linux/macOS版本)

echo "==================================="
echo "    WebRTC视频流服务器启动脚本"
echo "==================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python，请安装Python 3.7或更高版本"
    exit 1
fi

# 检查依赖
echo "正在检查依赖..."
python3 -c "import websockets" &> /dev/null
if [ $? -ne 0 ]; then
    echo "正在安装websockets..."
    pip3 install websockets
fi

# 获取本地IP地址
LOCAL_IP=$(hostname -I | awk '{print $1}')
if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP="127.0.0.1"
fi

# 创建web目录（如果不存在）
mkdir -p web/img web/css web/js

# 启动服务器
echo
echo "正在启动WebRTC视频流服务器..."
echo
echo "服务器信息:"
echo "- 信令服务器: ws://$LOCAL_IP:8765"
echo "- Web界面: http://$LOCAL_IP:8080"
echo
echo "您可以通过以下方式访问:"
echo "- 本机: http://localhost:8080"
echo "- 局域网: http://$LOCAL_IP:8080"
echo
echo "按Ctrl+C停止服务器"
echo

python3 enhanced_signaling_server.py --ws-port 8765 --http-port 8080 --web-dir ./web
