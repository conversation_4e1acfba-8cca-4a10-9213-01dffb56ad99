# 升级后自动重启功能

## 🎯 问题分析

升级完成后应用不会自动重启，用户需要手动打开应用才能使用新版本。这影响了自动升级的完整性和用户体验。

## 🔧 解决方案

### 1. 多层重启机制

```kotlin
// 安装APK后启动重启监控
startAppRestartMonitoring()    // 主要方案：应用重启
scheduleSystemReboot()         // 备用方案：系统重启
```

### 2. 应用重启监控

```kotlin
private fun startAppRestartMonitoring() {
    // 每3秒检查一次应用版本
    // 检测到版本更新后立即重启应用
    // 最多检查20次（60秒）
    
    val restartRunnable = object : Runnable {
        override fun run() {
            // 检查当前版本是否已更新
            val currentVersionCode = packageInfo.versionCode
            if (currentVersionCode >= targetVersion) {
                restartApplication() // 立即重启
            } else {
                handler.postDelayed(this, 3000) // 继续检查
            }
        }
    }
}
```

### 3. 双重重启策略

#### 主要方案：PackageManager重启
```kotlin
private fun restartApplication() {
    val intent = packageManager.getLaunchIntentForPackage(context.packageName)
    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
    
    context.startActivity(intent)
    android.os.Process.killProcess(android.os.Process.myPid())
}
```

#### 备用方案：AlarmManager重启
```kotlin
private fun restartApplicationAlternative() {
    val restartIntent = Intent(context, MainActivity::class.java)
    val pendingIntent = PendingIntent.getActivity(context, 0, restartIntent, flags)
    
    val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    alarmManager.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis() + 3000, pendingIntent)
    
    android.os.Process.killProcess(android.os.Process.myPid())
}
```

## 📊 重启流程

### 完整升级流程
```
1. 下载APK ✅
2. 验证APK ✅
3. 启动安装程序 ✅
4. 开始应用重启监控 🆕
5. 用户确认安装
6. 检测到版本更新 🆕
7. 自动重启应用 🆕
8. 升级完成 ✅
```

### 重启监控时间线
```
00:00  启动安装程序
00:05  开始版本检查（延迟5秒）
00:08  第1次检查版本
00:11  第2次检查版本
...
00:XX  检测到版本更新
00:XX  立即重启应用
```

### 备用系统重启
```
如果60秒内应用重启失败：
00:30  系统重启（原有机制）
```

## 🛡️ 多重保障

### 1. 版本检测
```kotlin
// 检查当前版本是否已更新
val currentVersionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
    packageInfo.longVersionCode.toInt()
} else {
    packageInfo.versionCode
}

if (currentVersionCode >= targetVersion) {
    restartApplication() // 版本已更新，立即重启
}
```

### 2. 超时保护
```kotlin
private val maxChecks = 20 // 最多检查20次（60秒）

if (checkCount < maxChecks) {
    handler.postDelayed(this, 3000) // 继续检查
} else {
    Logger.w(TAG, "应用重启监控超时，将依赖系统重启")
}
```

### 3. 错误处理
```kotlin
try {
    restartApplication()
} catch (e: Exception) {
    restartApplicationAlternative() // 使用备用方法
}
```

## 📝 详细日志

### 成功重启
```
✅ 启动APK安装
🔄 启动应用重启监控机制
🔄 检查应用安装状态 (1/20)
🔄 当前版本: 13, 目标版本: 14
🔄 检查应用安装状态 (3/20)
🔄 当前版本: 14, 目标版本: 14
✅ 检测到应用已更新，立即重启应用
🔄 开始重启应用...
🔄 使用PackageManager重启应用
✅ 应用重启成功
```

### 备用重启
```
🔄 PackageManager重启失败: xxx
🔄 使用备用方法重启应用
✅ 已安排3秒后重启应用
```

### 超时处理
```
🔄 检查应用安装状态 (20/20)
⚠️ 应用重启监控超时，将依赖系统重启
```

## 🎯 状态回调

### 新增状态
```kotlin
statusCallback("upgrade", "app_updated", 98, "应用已更新，正在重启...")
statusCallback("upgrade", "restarting_app", 99, "正在重启应用...")
statusCallback("upgrade", "app_restarted", 100, "应用重启成功，升级完成")
statusCallback("upgrade", "restart_scheduled", 100, "已安排应用重启，升级即将完成")
statusCallback("upgrade", "restart_timeout", 99, "应用重启监控超时，将使用系统重启")
statusCallback("upgrade", "restart_failed", 100, "应用重启失败，请手动重启应用")
```

### 状态进度
```
95% - 安装程序已启动
97% - 应用将自动重启
98% - 应用已更新，正在重启
99% - 正在重启应用
100% - 应用重启成功，升级完成
```

## ⚠️ 注意事项

### 1. 权限要求
- 需要安装未知来源权限
- 需要系统设置权限（用于AlarmManager）
- 需要前台服务权限

### 2. 兼容性
- 支持Android 7.0+的FileProvider
- 兼容不同版本的PendingIntent标志
- 处理不同Android版本的版本号获取

### 3. 用户体验
```kotlin
// 给用户明确的提示
statusCallback("upgrade", "reboot_scheduled", 97, 
    "应用将自动重启，如未成功则系统将在30秒后重启")
```

## 🚀 测试验证

### 1. 正常升级测试
```bash
# 编译新版本
./gradlew assembleDebug

# 触发升级
# 观察是否自动重启
```

### 2. 日志监控
```bash
adb logcat | grep "🔄\|✅\|⚠️"
```

### 3. 验证重启
```bash
# 检查应用进程ID变化
adb shell ps | grep webrtcsender

# 检查应用版本
adb shell dumpsys package com.example.webrtcsender | grep versionCode
```

## 🎯 预期效果

### 1. 自动化程度
- ✅ 完全自动化升级流程
- ✅ 无需用户手动重启应用
- ✅ 升级完成后立即可用

### 2. 可靠性
- ✅ 多重重启保障机制
- ✅ 超时和错误处理
- ✅ 备用系统重启方案

### 3. 用户体验
- ✅ 升级过程透明
- ✅ 状态实时反馈
- ✅ 最小化用户干预

## 📋 升级状态流程

```
下载中... (40-70%)
    ↓
下载完成 (70%)
    ↓
开始安装 (80%)
    ↓
安装程序已启动 (95%)
    ↓
应用将自动重启 (97%)
    ↓
应用已更新，正在重启 (98%)
    ↓
正在重启应用 (99%)
    ↓
应用重启成功，升级完成 (100%)
```

现在升级完成后应用会自动重启，实现真正的无人值守自动升级！
