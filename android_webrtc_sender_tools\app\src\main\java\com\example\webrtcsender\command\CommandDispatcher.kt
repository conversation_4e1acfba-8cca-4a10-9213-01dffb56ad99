package com.example.webrtcsender.command

import android.content.Context
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import org.json.JSONObject

/**
 * 命令分发器
 * 负责将收到的消息分发到对应的命令处理器
 */
class CommandDispatcher(
    private val context: Context,
    private val webrtcManager: WebRTCManager,
    private val deviceId: String,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit,
    private val extendedResponseCallback: ((String, Boolean, String?) -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "CommandDispatcher"
    }
    
    // 各种命令处理器
    private val serviceCommandHandler = ServiceCommandHandler(context, webrtcManager, responseCallback, statusCallback)
    private val videoCommandHandler = VideoCommandHandler(webrtcManager, responseCallback, statusCallback)
    private val systemCommandHandler = SystemCommandHandler(context, responseCallback, statusCallback)
    private val upgradeCommandHandler = UpgradeCommandHandler(context, responseCallback, statusCallback)
    private val configCommandHandler = ConfigCommandHandler(webrtcManager, responseCallback, statusCallback)

    private val extendedCommandHandler = ExtendedCommandHandler(
        context,
        webrtcManager,
        extendedResponseCallback ?: { command, success, filename ->
            // 如果没有提供extendedResponseCallback，使用默认行为
            responseCallback(command, success)
            if (filename != null) {
                Logger.i(TAG, "📁 命令 $command 包含文件名: $filename")
            }
        },
        statusCallback
    )
    
    /**
     * 分发消息到对应的处理器
     */
    fun dispatchMessage(message: String) {
        try {
            val jsonObject = JSONObject(message)
            val messageType = jsonObject.optString("type")
            
            Logger.i(TAG, "📨 收到消息类型: $messageType")
            
            when (messageType) {
                "control_command" -> dispatchControlCommand(jsonObject)
                "upgrade_command" -> dispatchUpgradeCommand(jsonObject)
                "server_config" -> dispatchConfigCommand(jsonObject)
                "registered" -> handleRegistered(jsonObject)
                "heartbeat_response" -> handleHeartbeatResponse(jsonObject)
                else -> {
                    Logger.w(TAG, "❓ 未知消息类型: $messageType")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 分发消息失败: ${e.message}")
        }
    }
    
    /**
     * 分发控制命令
     */
    private fun dispatchControlCommand(jsonObject: JSONObject) {
        try {
            val command = jsonObject.optString("command")
            Logger.i(TAG, "🎮 分发控制命令: $command")
            Logger.d(TAG, "🔍 命令详细信息: command='$command', length=${command.length}")

            val handler = when (command) {
                "start_service", "stop_service", "restart_service" -> {
                    Logger.d(TAG, "✅ 匹配到服务命令，使用服务命令处理器")
                    serviceCommandHandler
                }
                "change_resolution", "change_bitrate", "change_codec" -> {
                    Logger.d(TAG, "✅ 匹配到视频命令，使用视频命令处理器")
                    videoCommandHandler
                }
                "reboot_device" -> {
                    Logger.d(TAG, "✅ 匹配到系统命令，使用系统命令处理器")
                    systemCommandHandler
                }
                "set_auto_start_game", "toggle_log_display", "download_logs", "take_screenshot" -> {
                    Logger.d(TAG, "✅ 匹配到扩展命令，使用扩展命令处理器")
                    extendedCommandHandler
                }
                else -> {
                    Logger.w(TAG, "❓ 未知控制命令: '$command'")
                    Logger.w(TAG, "🔍 支持的扩展命令: set_auto_start_game, toggle_log_display, download_logs, take_screenshot")
                    null
                }
            }

            if (handler != null) {
                Logger.d(TAG, "📤 调用处理器处理命令: $command")
                // 确保消息包含正确的type字段
                val messageWithType = JSONObject(jsonObject.toString())
                messageWithType.put("type", "control_command")
                handler.handleMessage(messageWithType.toString(), deviceId)
            } else {
                Logger.e(TAG, "❌ 没有找到合适的处理器处理命令: $command")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 分发控制命令失败: ${e.message}")
        }
    }
    
    /**
     * 分发升级命令
     */
    private fun dispatchUpgradeCommand(jsonObject: JSONObject) {
        try {
            Logger.i(TAG, "📦 分发升级命令")
            upgradeCommandHandler.handleMessage(jsonObject.toString(), deviceId)
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 分发升级命令失败: ${e.message}")
        }
    }
    
    /**
     * 分发配置命令
     */
    private fun dispatchConfigCommand(jsonObject: JSONObject) {
        try {
            Logger.i(TAG, "⚙️ 分发配置命令")
            configCommandHandler.handleMessage(jsonObject.toString(), deviceId)
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 分发配置命令失败: ${e.message}")
        }
    }
    
    /**
     * 处理注册确认
     */
    private fun handleRegistered(jsonObject: JSONObject) {
        try {
            val id = jsonObject.optString("id")
            Logger.i(TAG, "✅ 注册确认: $id")
            
            // 可以在这里处理注册成功后的逻辑
            // 例如：发送初始状态、启动定时任务等
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理注册确认失败: ${e.message}")
        }
    }
    
    /**
     * 处理心跳响应
     */
    private fun handleHeartbeatResponse(jsonObject: JSONObject) {
        try {
            val timestamp = jsonObject.optLong("timestamp")
            Logger.d(TAG, "💓 收到心跳响应: $timestamp")
            
            // 可以在这里处理心跳响应逻辑
            // 例如：更新连接状态、计算延迟等
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理心跳响应失败: ${e.message}")
        }
    }
    
    /**
     * 获取支持的命令列表
     */
    fun getSupportedCommands(): List<String> {
        return listOf(
            // 服务控制命令
            "start_service",
            "stop_service",
            "restart_service",

            // 视频参数命令
            "change_resolution",
            "change_bitrate",
            "change_codec",

            // 系统控制命令
            "reboot_device",

            // 升级命令
            "upgrade",

            // 配置命令
            "server_config",

            // 扩展命令
            "set_auto_start_game",
            "toggle_log_display",
            "download_logs",
            "take_screenshot"
        )
    }
    
    /**
     * 检查命令是否支持
     */
    fun isCommandSupported(command: String): Boolean {
        return getSupportedCommands().contains(command)
    }
}
