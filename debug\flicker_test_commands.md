# 下拉菜单闪烁检测命令

## 🎯 针对闪烁问题的专门检测

基于你的反馈，闪烁时没有JavaScript状态变化日志，说明问题可能是CSS层面的视觉闪烁。

## 🔧 新增的检测工具

### 1. 样式变化监控
现在会监控所有样式属性变化：
- `display`
- `visibility` 
- `opacity`
- `left`、`top` 位置
- `transform` 变换

### 2. 视觉闪烁检测
使用 `requestAnimationFrame` 进行60fps的视觉检测。

## 🚀 测试步骤

### 步骤1: 打开页面并准备
```javascript
// 清除之前的日志
dropdownDebug.clearLog()

// 开始监控
dropdownDebug.startMonitoring()
```

### 步骤2: 点击⚙️按钮并立即检测闪烁
```javascript
// 点击按钮后立即运行（替换为实际的设备ID）
dropdownDebug.detectFlicker('gamev-aab76437')
```

### 步骤3: 查看详细结果
```javascript
// 查看状态变化日志
dropdownDebug.viewLog()

// 查看当前菜单状态
dropdownDebug.checkVisible()
```

## 📊 预期结果

### 如果是CSS闪烁
会看到类似这样的日志：
```
🎨 [17:07:05] 样式变化检测: gamev-aab76437 {
    属性: "style",
    旧值: "display: none;",
    新样式: {display: "block", visibility: "visible", opacity: "1"},
    计算样式: {display: "block", visibility: "visible", opacity: "1"}
}
```

### 如果检测到闪烁
会看到：
```
📊 闪烁检测结果 (gamev-aab76437): {
    总帧数: 60,
    可见性变化次数: 4,
    变化详情: [...],
    是否检测到闪烁: true
}
⚠️ 检测到闪烁! 菜单在1秒内发生了 4 次可见性变化
```

### 如果没有闪烁
会看到：
```
📊 闪烁检测结果 (gamev-aab76437): {
    总帧数: 60,
    可见性变化次数: 1,
    变化详情: [...],
    是否检测到闪烁: false
}
✅ 未检测到闪烁，菜单显示稳定
```

## 🔍 一键测试命令

复制以下代码到控制台，一次性执行所有检测：

```javascript
// 一键闪烁检测
(function() {
    console.log('🚀 开始一键闪烁检测...');
    
    // 清除日志
    dropdownDebug.clearLog();
    
    // 找到第一个⚙️按钮
    const firstButton = document.querySelector('.dropdown-toggle');
    if (!firstButton) {
        console.log('❌ 未找到下拉按钮');
        return;
    }
    
    // 获取设备ID
    const onclick = firstButton.getAttribute('onclick');
    const deviceIdMatch = onclick.match(/toggleDropdown\('([^']+)'\)/);
    if (!deviceIdMatch) {
        console.log('❌ 无法解析设备ID');
        return;
    }
    
    const deviceId = deviceIdMatch[1];
    console.log('🎯 目标设备:', deviceId);
    
    // 点击按钮
    console.log('🖱️ 点击按钮...');
    firstButton.click();
    
    // 等待100ms后开始检测闪烁
    setTimeout(() => {
        console.log('🔍 开始检测闪烁...');
        dropdownDebug.detectFlicker(deviceId);
        
        // 2秒后显示结果
        setTimeout(() => {
            console.log('📊 检测完成，查看结果:');
            dropdownDebug.viewLog();
            dropdownDebug.checkVisible();
        }, 2000);
    }, 100);
})();
```

## 🎯 问题定位

### 如果检测到样式变化但没有闪烁
说明是CSS transition/animation导致的视觉效果。

### 如果检测到闪烁
说明菜单在短时间内多次显示/隐藏。

### 如果都没有检测到
说明问题可能在其他地方，比如：
- 浏览器渲染问题
- 硬件加速问题
- 其他CSS冲突

现在请运行这些命令，看看能否捕获到闪烁的具体原因！
