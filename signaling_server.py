#!/usr/bin/env python3
# signaling_server.py - 简单的WebSocket信令服务器
import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('signaling-server')

# 存储连接的客户端
connected_clients = {}

async def handle_client(websocket, path):
    client_id = None
    try:
        async for message in websocket:
            data = json.loads(message)
            logger.info(f"收到消息: {data['type']}")

            if data['type'] == 'register':
                # 客户端注册
                client_id = data['id']
                connected_clients[client_id] = websocket
                logger.info(f"客户端注册: {client_id}")

                # 通知客户端注册成功
                await websocket.send(json.dumps({
                    'type': 'registered',
                    'id': client_id
                }))

                # 通知所有客户端有新客户端加入
                for cid, ws in connected_clients.items():
                    if cid != client_id:
                        try:
                            await ws.send(json.dumps({
                                'type': 'client_joined',
                                'id': client_id
                            }))
                        except:
                            pass

            elif data['type'] == 'list':
                # 返回客户端列表
                await websocket.send(json.dumps({
                    'type': 'client_list',
                    'clients': list(connected_clients.keys())
                }))

            elif data['type'] == 'offer' and 'target' in data:
                # 转发offer
                target_id = data['target']
                if target_id in connected_clients:
                    try:
                        await connected_clients[target_id].send(json.dumps({
                            'type': 'offer',
                            'sdp': data['sdp'],
                            'from': client_id
                        }))
                        logger.info(f"转发offer: {client_id} -> {target_id}")
                    except Exception as e:
                        logger.error(f"转发offer失败: {e}")

            elif data['type'] == 'answer' and 'target' in data:
                # 转发answer
                target_id = data['target']
                if target_id in connected_clients:
                    try:
                        await connected_clients[target_id].send(json.dumps({
                            'type': 'answer',
                            'sdp': data['sdp'],
                            'from': client_id
                        }))
                        logger.info(f"转发answer: {client_id} -> {target_id}")
                    except Exception as e:
                        logger.error(f"转发answer失败: {e}")

            elif data['type'] == 'candidate' and 'target' in data:
                # 转发ICE候选
                target_id = data['target']
                if target_id in connected_clients:
                    try:
                        await connected_clients[target_id].send(json.dumps({
                            'type': 'candidate',
                            'candidate': data['candidate'],
                            'from': client_id
                        }))
                        logger.info(f"转发ICE候选: {client_id} -> {target_id}")
                    except Exception as e:
                        logger.error(f"转发ICE候选失败: {e}")

    except Exception as e:
        logger.error(f"处理客户端消息错误: {e}")

    finally:
        # 客户端断开连接
        if client_id and client_id in connected_clients:
            del connected_clients[client_id]
            logger.info(f"客户端断开连接: {client_id}")

            # 通知其他客户端
            for cid, ws in connected_clients.items():
                try:
                    await ws.send(json.dumps({
                        'type': 'client_left',
                        'id': client_id
                    }))
                except:
                    pass

async def main():
    # 启动WebSocket服务器
    server = await websockets.serve(handle_client, '0.0.0.0', 8765)
    logger.info("信令服务器启动在 ws://0.0.0.0:8765")
    logger.info("当前可用的STUN/TURN服务器:")
    logger.info("  - stun:stun.l.google.com:19302")
    logger.info("  - stun:stun1.l.google.com:19302")
    logger.info("  - stun:stun2.l.google.com:19302")
    logger.info("  - turn:numb.viagenie.ca (<EMAIL>:muazkh)")

    # 保持服务器运行
    await server.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())
