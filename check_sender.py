#!/usr/bin/env python3
# check_sender.py - 检查发送端状态
import asyncio
import json
import logging
import argparse
import uuid
import websockets

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check-sender')

# 解析命令行参数
parser = argparse.ArgumentParser(description="检查发送端状态")
parser.add_argument("--sender", help="发送端ID", required=True)
parser.add_argument("--signaling", help="信令服务器URL", default="wss://sling.91jdcd.com/ws/")
args = parser.parse_args()

async def run():
    # 连接到信令服务器
    logger.info(f"正在连接到信令服务器: {args.signaling}")
    
    try:
        async with websockets.connect(args.signaling) as websocket:
            # 生成随机ID
            checker_id = f"checker-{uuid.uuid4().hex[:8]}"
            
            # 注册客户端
            await websocket.send(json.dumps({
                "type": "register",
                "id": checker_id
            }))
            logger.info(f"已注册检查工具ID: {checker_id}")
            
            # 等待注册确认
            response = await websocket.recv()
            data = json.loads(response)
            if data["type"] == "registered":
                logger.info("注册成功")
            else:
                logger.error(f"注册失败: {data}")
                return
            
            # 发送ping消息到发送端
            await websocket.send(json.dumps({
                "type": "ping",
                "target": args.sender
            }))
            logger.info(f"已发送ping到发送端: {args.sender}")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                
                if data["type"] == "pong" and data.get("from") == args.sender:
                    logger.info(f"发送端 {args.sender} 在线且响应")
                    return True
                elif data["type"] == "error" and "not found" in data.get("message", "").lower():
                    logger.error(f"发送端 {args.sender} 不存在")
                    return False
                else:
                    logger.warning(f"收到未预期的响应: {data}")
                    return False
            
            except asyncio.TimeoutError:
                logger.error(f"等待发送端 {args.sender} 响应超时")
                return False
    
    except Exception as e:
        logger.error(f"连接错误: {e}")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(run())
        if result:
            print(f"\n发送端 {args.sender} 在线且可连接")
        else:
            print(f"\n发送端 {args.sender} 不在线或不可连接")
    except KeyboardInterrupt:
        logger.info("程序已终止")
