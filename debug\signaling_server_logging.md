# 信令服务器日志配置说明

## 概述

`enhanced_signaling_server.py` 现在支持自定义日志保存位置和配置。

## 新增参数

### 日志相关参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--log-dir` | 空 | 日志文件保存目录（为空则不保存文件） |
| `--log-level` | INFO | 日志级别：DEBUG, INFO, WARNING, ERROR |
| `--no-console` | False | 禁用控制台输出（仅输出到文件） |

## 使用方法

### 1. 仅控制台输出（默认）
```bash
python3 enhanced_signaling_server.py
```

### 2. 保存到指定目录
```bash
# 保存到 logs 目录
python3 enhanced_signaling_server.py --log-dir ./logs

# 保存到绝对路径
python3 enhanced_signaling_server.py --log-dir /var/log/signaling
```

### 3. 设置日志级别
```bash
# DEBUG 级别（最详细）
python3 enhanced_signaling_server.py --log-dir ./logs --log-level DEBUG

# WARNING 级别（仅警告和错误）
python3 enhanced_signaling_server.py --log-dir ./logs --log-level WARNING
```

### 4. 仅输出到文件（不显示控制台）
```bash
python3 enhanced_signaling_server.py --log-dir ./logs --no-console
```

### 5. 完整配置示例
```bash
python3 enhanced_signaling_server.py \
  --ws-host 0.0.0.0 \
  --ws-port 28765 \
  --http-host 0.0.0.0 \
  --http-port 28080 \
  --web-dir /www/wwwroot/sj/web \
  --log-dir /var/log/signaling \
  --log-level INFO
```

## 日志文件特性

### 文件命名规则
```
signaling_server_yyyyMMdd_HHMMSS.log
```
例如：`signaling_server_20250825_143022.log`

### 文件轮转
- **最大文件大小**：50MB
- **备份文件数量**：10个
- **自动轮转**：文件达到50MB时自动创建新文件
- **备份命名**：`signaling_server_20250825_143022.log.1`, `.2`, `.3` 等

### 编码格式
- **文件编码**：UTF-8
- **支持中文**：完全支持中文日志内容

## 日志格式

```
2025-08-25 14:30:22 - INFO - 🗄️ 初始化数据库连接...
2025-08-25 14:30:22 - INFO - ✅ 数据库连接池初始化成功
2025-08-25 14:30:22 - INFO - 📁 日志目录: ./logs
2025-08-25 14:30:22 - INFO - 📝 日志文件已创建: ./logs/signaling_server_20250825_143022.log
```

## 推荐配置

### 开发环境
```bash
# 详细日志，同时输出到控制台和文件
python3 enhanced_signaling_server.py --log-dir ./logs --log-level DEBUG
```

### 生产环境
```bash
# 标准日志，保存到系统日志目录
python3 enhanced_signaling_server.py --log-dir /var/log/signaling --log-level INFO
```

### 后台运行
```bash
# 使用 nohup 后台运行，仅输出到文件
nohup python3 enhanced_signaling_server.py --log-dir ./logs --no-console &

# 或使用 systemd 服务
```

## 日志目录权限

确保运行用户对日志目录有写权限：

```bash
# 创建日志目录
sudo mkdir -p /var/log/signaling

# 设置权限
sudo chown $USER:$USER /var/log/signaling
sudo chmod 755 /var/log/signaling
```

## 日志查看

### 实时查看
```bash
# 查看最新日志
tail -f ./logs/signaling_server_*.log

# 查看最近100行
tail -n 100 ./logs/signaling_server_*.log
```

### 搜索日志
```bash
# 搜索错误日志
grep "ERROR" ./logs/signaling_server_*.log

# 搜索特定客户端
grep "client_id_123" ./logs/signaling_server_*.log

# 搜索连接事件
grep "连接" ./logs/signaling_server_*.log
```

### 日志分析
```bash
# 统计日志级别
grep -c "INFO" ./logs/signaling_server_*.log
grep -c "ERROR" ./logs/signaling_server_*.log

# 查看最近的错误
grep "ERROR" ./logs/signaling_server_*.log | tail -10
```

## 故障排除

### 1. 日志目录创建失败
```
❌ 创建日志文件失败: [Errno 13] Permission denied
```
**解决方案**：检查目录权限，确保有写权限

### 2. 磁盘空间不足
```
❌ 创建日志文件失败: [Errno 28] No space left on device
```
**解决方案**：清理磁盘空间或更改日志目录

### 3. 日志文件过多
**解决方案**：日志轮转会自动管理，保留最新10个文件

## 系统集成

### systemd 服务配置
创建 `/etc/systemd/system/signaling-server.service`：

```ini
[Unit]
Description=WebRTC Signaling Server
After=network.target

[Service]
Type=simple
User=signaling
WorkingDirectory=/opt/signaling-server
ExecStart=/usr/bin/python3 enhanced_signaling_server.py --log-dir /var/log/signaling --log-level INFO --no-console
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 日志轮转配置
创建 `/etc/logrotate.d/signaling-server`：

```
/var/log/signaling/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

## 版本更新

当前版本：v1.20 → v1.21

主要改进：
- 添加自定义日志目录支持
- 支持日志级别配置
- 实现日志文件轮转
- 支持仅文件输出模式
- 完善日志格式和编码
