# 开机监控显示优化

## 修改内容

### 1. CPU ID 显示完整

**修改前**：
```javascript
<div class="device-id">CPU ID: ${device.cpu_id_short}</div>
// 显示: CPU ID: CPU_1234...
```

**修改后**：
```javascript
<div class="device-id">CPU ID: ${device.cpu_unique_id}</div>
// 显示: CPU ID: CPU_12345678901234567890
```

### 2. 游戏信息使用正确字段

**修改前**：
```javascript
${device.game_package ? `
    <strong>🎮 启动游戏:</strong> ${device.game_package}
` : `
    <strong>⚠️ 未配置游戏</strong>
`}
```

**修改后**：
```javascript
${device.auto_start_game_package ? `
    <strong>🎮 启动游戏:</strong> ${device.auto_start_game_package}
` : `
    <strong>⚠️ 未配置游戏</strong>
`}
```

## 数据流修改

### 1. Android端上报

**文件**: `DeviceInfoReporter.kt`

**修改前**：
```kotlin
bootInfo.put("game_package", gamePackage)
```

**修改后**：
```kotlin
bootInfo.put("auto_start_game_package", gamePackage)
```

### 2. 服务器端存储

**文件**: `enhanced_signaling_server.py`

**修改前**：
```python
'game_package': boot_info.get('game_package', ''),
```

**修改后**：
```python
'auto_start_game_package': boot_info.get('auto_start_game_package', ''),
```

### 3. 服务器端发送

**修改前**：
```python
device_data = {
    'cpu_unique_id': cpu_id,
    'cpu_id_short': cpu_id[:8] + '...' if len(cpu_id) > 8 else cpu_id,
    'game_package': device_info['device_info'].get('game_package', ''),
    ...
}
```

**修改后**：
```python
device_data = {
    'cpu_unique_id': cpu_id,
    'auto_start_game_package': device_info['device_info'].get('auto_start_game_package', ''),
    ...
}
```

### 4. H5页面显示

**修改前**：
```javascript
const gameDevices = devices.filter(device => device.game_package).length;
```

**修改后**：
```javascript
const gameDevices = devices.filter(device => device.auto_start_game_package).length;
```

## 字段对应关系

| 位置 | 原字段名 | 新字段名 | 说明 |
|------|----------|----------|------|
| Android端 | `game_package` | `auto_start_game_package` | 上报字段名 |
| 服务器存储 | `game_package` | `auto_start_game_package` | 存储字段名 |
| 服务器发送 | `game_package` | `auto_start_game_package` | 发送字段名 |
| H5显示 | `game_package` | `auto_start_game_package` | 显示字段名 |
| CPU ID | `cpu_id_short` | `cpu_unique_id` | 显示完整ID |

## 显示效果对比

### CPU ID 显示

**修改前**：
```
CPU ID: CPU_1234...
CPU ID: CPU_5678...
CPU ID: CPU_9012...
```

**修改后**：
```
CPU ID: CPU_12345678901234567890
CPU ID: CPU_56789012345678901234
CPU ID: CPU_90123456789012345678
```

### 游戏信息显示

**修改前**（可能显示空白或错误）：
```
🎮 启动游戏: (空白)
⚠️ 未配置游戏
```

**修改后**（显示正确的游戏包名）：
```
🎮 启动游戏: com.tencent.tmgp.pubgmhd
🎮 启动游戏: com.tencent.tmgp.sgame
🎮 启动游戏: com.netease.dwrg
```

## 数据示例

### 开机上报数据

**Android端发送**：
```json
{
    "type": "boot_report",
    "sender_id": "gamev-001",
    "boot_info": {
        "cpu_unique_id": "CPU_12345678901234567890",
        "auto_start_game_package": "com.tencent.tmgp.pubgmhd",
        "device_brand": "Samsung",
        "device_model": "SM-G973F",
        ...
    }
}
```

### 服务器存储格式

```python
boot_devices = {
    "CPU_12345678901234567890": {
        'cpu_unique_id': 'CPU_12345678901234567890',
        'device_info': {
            'auto_start_game_package': 'com.tencent.tmgp.pubgmhd',
            'brand': 'Samsung',
            'model': 'SM-G973F',
            ...
        }
    }
}
```

### H5页面接收数据

```json
{
    "type": "boot_devices_update",
    "devices": [
        {
            "cpu_unique_id": "CPU_12345678901234567890",
            "auto_start_game_package": "com.tencent.tmgp.pubgmhd",
            "device_brand": "Samsung",
            "device_model": "SM-G973F",
            ...
        }
    ]
}
```

## 兼容性说明

### 向后兼容

- 如果设备没有配置游戏，`auto_start_game_package` 为空字符串
- H5页面会显示"⚠️ 未配置游戏"
- 不会影响现有功能

### 数据迁移

- 新字段名立即生效
- 旧数据中的 `game_package` 字段会被忽略
- 需要设备重新开机上报才能看到新字段

## 测试验证

### 1. Android端测试

检查日志中的开机上报内容：
```
[DeviceInfoReporter] 开机信息内容: CPU_ID=CPU_1234..., 游戏=com.tencent.tmgp.pubgmhd
```

### 2. 服务器端测试

检查服务器接收到的数据：
```
📱 收到开机信息: gamev-001 | CPU_ID=CPU_1234... | 游戏=com.tencent.tmgp.pubgmhd
```

### 3. H5页面测试

1. 打开监控页面：`http://your-server:28080/boot_monitor.html`
2. 输入密码：`tb###`
3. 查看设备列表：
   - CPU ID 显示完整
   - 游戏信息显示正确的包名

### 4. 功能验证

- ✅ CPU ID 完整显示
- ✅ 游戏包名正确显示
- ✅ 统计信息正确计算
- ✅ 实时更新正常工作

## 注意事项

### 1. 字段一致性

确保所有位置使用相同的字段名：
- Android端：`auto_start_game_package`
- 服务器端：`auto_start_game_package`
- H5页面：`auto_start_game_package`

### 2. 数据验证

在每个环节都要验证数据：
- Android端：确保字段存在
- 服务器端：检查字段类型
- H5页面：处理空值情况

### 3. 调试方法

如果显示不正确：
1. 检查Android端日志
2. 检查服务器端日志
3. 检查浏览器控制台
4. 验证WebSocket消息内容

## 版本更新

当前版本：v1.27 → v1.28

主要改进：
- CPU ID 显示完整标识符
- 游戏信息使用正确的字段名
- 统一数据流中的字段命名
- 改进显示效果和用户体验
