# 纯实时开机监控

## 功能说明

实现了完全基于实时开机上报的监控系统，不查询任何已有设备，只在收到新的开机上报时才显示设备。

## 核心特性

### 1. 纯实时监控
- **不查询已有设备**: 用户连接时不发送任何已开机的设备列表
- **只接收新开机**: 只在收到新的 `boot_report` 时才显示设备
- **实时推送**: 每个新开机设备立即推送给监控页面

### 2. 数据流程
```
发送端开机 → WebSocket(boot_report) → 信令服务器 → WebSocket(new_boot_device) → 监控页面
```

### 3. 用户体验
- **连接后等待**: 认证成功后显示"等待设备开机上报..."
- **实时通知**: 新设备开机时立即显示
- **桌面通知**: 支持浏览器桌面通知（可选）

## 技术实现

### 1. 服务器端

#### 认证成功处理
```python
# 发送认证成功消息，不发送已有设备
await websocket.send(json.dumps({
    'type': 'auth_result',
    'success': True,
    'message': '认证成功，等待设备开机上报...',
    'allowed_domains': allowed_domains
}))
```

#### 新开机设备处理
```python
async def send_new_boot_device(cpu_unique_id, device_info):
    """发送新开机设备信息到所有监控订阅者"""
    # 构建单个设备数据
    device_data = {...}
    
    # 发送给所有有权限的订阅者
    message = {
        'type': 'new_boot_device',
        'success': True,
        'device': device_data,
        'timestamp': current_time
    }
```

#### 开机信息处理
```python
# 存储到内存
boot_devices[cpu_unique_id] = {...}

# 实时转发新开机设备
await send_new_boot_device(cpu_unique_id, boot_devices[cpu_unique_id])
```

### 2. 前端处理

#### 消息处理
```javascript
case 'new_boot_device':
    if (data.success && data.device) {
        // 添加新设备到列表开头
        devices.unshift(data.device);
        
        // 更新显示
        renderDevices(devices);
        
        // 显示通知
        showNewDeviceNotification(data.device);
    }
```

#### 新设备通知
```javascript
function showNewDeviceNotification(device) {
    console.log('🆕 新设备开机:', device.sender_id);
    
    // 桌面通知
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('设备开机通知', {
            body: `${device.sender_id} 已开机`,
            icon: '/favicon.ico'
        });
    }
}
```

## 消息格式

### 1. 认证成功消息
```json
{
    "type": "auth_result",
    "success": true,
    "message": "认证成功，等待设备开机上报...",
    "allowed_domains": ["*"]
}
```

### 2. 新开机设备消息
```json
{
    "type": "new_boot_device",
    "success": true,
    "device": {
        "cpu_unique_id": "1234567890abcdef",
        "sender_id": "gamev-b246c42d",
        "boot_time": 1756355794000,
        "uptime_seconds": 120,
        "uptime_display": "2分0秒",
        "auto_start_game_package": "com.example.game",
        "device_brand": "Samsung",
        "device_model": "Galaxy Tab",
        "android_version": "11",
        "app_version": "1.0.0",
        "local_ip": "*************",
        "public_ip": "*******",
        "is_online": true,
        "room_name": "水浒传_DF14",
        "room_server_domain": "http://testva2.91jdcd.com",
        "room_category_id": 1,
        "data_source": "boot_report"
    },
    "timestamp": 1756355794000
}
```

## 权限控制

### 1. 服务器端过滤
```python
# 权限检查
if '*' not in allowed_domains:
    if not room_server_domain or room_server_domain not in allowed_domains:
        continue  # 跳过无权限访问的设备
```

### 2. 支持的权限
- **管理员密码**: `tb###` - 接收所有新开机设备
- **域名密码**: 只接收对应服务器域名的新开机设备

## 用户界面

### 1. 初始状态
```
🚀 设备开机监控
实时接收设备开机上报信息

📊 统计信息
总设备: 0 | 在线设备: 0

📱 暂无设备开机信息
```

### 2. 收到新设备后
```
🚀 设备开机监控
实时接收设备开机上报信息

📊 统计信息  
总设备: 1 | 在线设备: 1

📱 设备列表
CPU ID: 1234567890abcdef 🟢 在线
发送端: gamev-b246c42d | 游戏: 水浒传 | 📱 开机上报
🏠 房间: 水浒传_DF14 | 🌐 服务器: http://testva2.91jdcd.com
```

## 日志信息

### 1. 服务器端日志
```
📱 收到开机信息: gamev-b246c42d | CPU_ID=12345678... | 游戏=com.example.game
📱 实时转发新开机设备到监控页面: gamev-b246c42d
📤 发送新开机设备到监控页面: gamev-b246c42d -> 192.168.1.50
```

### 2. 前端日志
```
🆕 新设备开机: gamev-b246c42d 水浒传_DF14
```

## 性能优势

### 1. 极简数据传输
- **单设备推送**: 每次只传输一个新开机设备
- **无历史数据**: 不传输任何历史设备信息
- **实时性**: 开机信息立即推送

### 2. 服务器性能
- **无查询负载**: 不查询已有设备列表
- **内存操作**: 所有操作基于内存数据
- **缓存优化**: 房间信息使用缓存

### 3. 网络效率
- **按需传输**: 只在有新设备时才传输数据
- **权限过滤**: 服务器端过滤，减少无效传输
- **消息精简**: 每个消息只包含必要信息

## 版本信息

- **当前版本**: 1.2.0
- **更新内容**: 
  - 纯实时开机监控，不查询已有设备
  - 新增 `new_boot_device` 消息类型
  - 实时设备通知功能
  - 移除设备列表查询逻辑
- **更新时间**: 2025-08-28

## 使用场景

### 1. 实时监控
- 监控设备开机状态
- 及时发现新上线设备
- 实时查看设备信息

### 2. 故障排除
- 快速识别开机问题
- 监控设备启动流程
- 查看开机设备详情

### 3. 运维管理
- 设备上线通知
- 按权限查看设备
- 实时状态监控

## 注意事项

1. **只显示新开机**: 连接后只能看到新开机的设备
2. **历史设备不显示**: 已开机的设备不会在新连接中显示
3. **实时性要求**: 需要设备正常发送开机上报
4. **权限控制**: 确保设置正确的监控密码

现在监控页面完全基于实时开机上报，真正做到了"纯实时"监控！
