# 增强版控制台功能指南

## 🎯 更新概述

根据您的要求，我们对控制台进行了全面升级：

1. **模态对话框**: 所有设置都通过弹出选项框进行，用户可以确定发送或取消操作
2. **实时消息反馈**: 信令服务器收到设备回复后，会同步消息到控制台并显示提示框
3. **智能参数设置**: 根据不同命令类型设计专门的参数输入界面
4. **视频流截屏**: 截屏功能从视频录制流中抽取帧，而非调用系统截屏API

## 🎛️ 新界面特性

### 主操作按钮 + 下拉菜单
- **主按钮**: 根据设备状态显示"启动服务"或"重启服务"
- **下拉箭头**: 点击展开完整功能菜单，包含所有控制选项
- **动画效果**: 流畅的展开/收起动画，箭头旋转效果

### 模态对话框系统
- **确认机制**: 所有操作都需要用户确认才发送
- **参数验证**: 输入参数会进行格式和有效性检查
- **取消功能**: 用户可以随时取消操作，不发送命令

### 实时消息提示
- **Toast通知**: 右上角显示操作结果和设备响应
- **WebSocket监听**: 实时接收设备反馈消息
- **分类显示**: 成功、错误、警告、信息四种类型

## 📋 功能分类详解

### 🚀 服务控制
- **启动服务**: 启动视频推流服务
- **停止服务**: 停止视频推流服务
- **重启服务**: 重启视频推流服务

### 🎥 视频控制

#### 视频参数设置
**模态对话框包含:**
- 分辨率选择: 576p/720p/1080p/自定义
- 码率设置: 500-10000 kbps
- 帧率选择: 30fps/60fps

**发送命令:**
```json
{
  "command": "change_resolution",
  "params": {
    "resolution": "720p",
    "bitrate": 3000,
    "framerate": 60
  }
}
```

#### 视频流截屏
**特殊说明**: 此功能从正在录制的视频流中抽取当前帧，而不是调用系统截屏API

**模态对话框包含:**
- 截屏说明输入框
- 功能说明提示

**发送命令:**
```json
{
  "command": "take_screenshot",
  "params": {
    "request_id": "admin_1640995200123",
    "note": "用户备注",
    "source": "video_stream"
  }
}
```

### 🎮 游戏控制

#### 游戏启动设置
**模态对话框包含:**
- 启用/禁用自动启动游戏复选框
- 游戏包名输入框（可选）
- 常用游戏包名下拉选择
- 自动检测说明

**常用游戏包名:**
- 王者荣耀: `com.tencent.tmgp.sgame`
- 和平精英: `com.tencent.tmgp.pubgmhd`
- 第五人格: `com.netease.dwrg`
- 原神: `com.miHoYo.GenshinImpact`
- 部落冲突: `com.supercell.clashofclans`

**发送命令:**
```json
{
  "command": "set_auto_start_game",
  "params": {
    "enabled": true,
    "package_name": "com.example.game"
  }
}
```

### 📝 日志管理
- **开启日志显示**: 启用设备端日志显示
- **关闭日志显示**: 禁用设备端日志显示
- **下载日志(FTP)**: 通过FTP方式下载最近5MB日志
- **下载日志(HTTP)**: 通过HTTP方式下载最近5MB日志

### 🔧 系统控制

#### 重启设备
**确认对话框**: 需要用户二次确认，防止误操作

#### 应用升级
**模态对话框包含:**
- APK下载地址输入框（必填）
- 版本号输入框（必填）
- 升级类型选择（普通/强制）
- 安全警告提示

**发送命令:**
```json
{
  "command": "upgrade",
  "params": {
    "apk_url": "https://example.com/app.apk",
    "version": "2.0.0",
    "force": true
  }
}
```

## 📨 消息反馈系统

### WebSocket监听
控制台会自动连接到信令服务器的WebSocket，监听设备响应：

```javascript
// 注册为管理控制台
{
  "type": "register",
  "id": "admin-console-" + timestamp,
  "role": "admin",
  "name": "管理控制台"
}
```

### 设备响应处理

#### 命令响应消息
```json
{
  "type": "command_response",
  "from": "device-001",
  "command": "change_resolution",
  "success": true,
  "message": "分辨率已更改为720p",
  "timestamp": 1640995200
}
```

#### 截屏结果消息
```json
{
  "type": "screenshot_result",
  "from": "device-001",
  "request_id": "admin_1640995200123",
  "success": true,
  "full_url": "https://example.com/screenshots/screenshot.jpg",
  "message": "截屏成功",
  "timestamp": 1640995200
}
```

### Toast通知显示
- **成功**: 绿色边框，显示操作成功信息
- **错误**: 红色边框，显示错误详情
- **警告**: 黄色边框，显示警告信息
- **信息**: 蓝色边框，显示一般信息

## 🔧 技术实现

### 前端技术
- **模态对话框**: CSS3动画 + JavaScript控制
- **WebSocket**: 实时双向通信
- **响应式设计**: 支持移动设备访问
- **参数验证**: 客户端输入验证

### 后端增强
- **管理控制台识别**: 特殊角色标识和连接管理
- **消息转发**: 自动转发设备响应给所有管理控制台
- **连接清理**: 完善的连接生命周期管理

## 🚀 使用流程

1. **访问控制台**: 打开 `http://服务器地址:8080/admin.html`
2. **查看设备**: 在发送端状态监控区域查看在线设备
3. **选择操作**: 点击主按钮执行常用操作，或点击下拉箭头选择高级功能
4. **参数设置**: 在弹出的模态对话框中设置参数
5. **确认发送**: 点击"确定"发送命令，或"取消"放弃操作
6. **查看反馈**: 在右上角Toast通知中查看操作结果

## 🧪 测试功能

### 测试页面
访问 `http://服务器地址:8080/test_modal.html` 可以测试所有新功能：
- 模态对话框演示
- WebSocket连接测试
- 消息提示测试
- 实时日志查看

### 测试脚本
运行 `python test_new_commands.py` 可以模拟设备连接和命令响应。

## 📱 移动端适配

- **响应式布局**: 自动适应不同屏幕尺寸
- **触摸优化**: 按钮大小和间距适合触摸操作
- **菜单位置**: 移动端下拉菜单自动调整位置

## 🔒 安全特性

- **命令签名**: 所有命令都会自动添加签名验证
- **参数验证**: 输入参数格式和范围检查
- **操作确认**: 危险操作需要二次确认
- **连接验证**: 只有有效连接才能发送命令

现在控制台提供了完整的交互式设备管理功能，用户可以通过直观的对话框进行精确的参数设置，并实时查看设备的响应结果！
