# Android端设备信息上报集成指南

## 🎯 概述

已为Android WebRTC发送端应用集成了完整的设备信息收集和上报功能，在连接成功和重连成功时自动上报设备详细信息给信令服务器。

## 📱 Android端实现

### 1. 设备信息收集器 (`DeviceInfoCollector.kt`)

**功能**:
- 收集CPU唯一ID、微信SN、主板型号
- 获取系统版本、存储、内存信息
- 检测网络状态、IP地址、MAC地址
- **获取公网IP和地理位置信息**（通过ipinfo.io等服务）
- 读取CPU温度、屏幕信息
- **收集应用设置信息**（从SharedPreferences）

**关键方法**:
```kotlin
suspend fun collectDeviceInfo(): JSONObject  // 收集完整设备信息（异步）
private fun getAppSettings(): JSONObject  // 获取应用设置
private suspend fun getPublicIpInfo(): JSONObject  // 获取公网IP信息
```

**应用设置包含**:
- 信令服务器地址
- 发送端ID配置
- 视频源类型、分辨率、码率、帧率
- 音频源配置
- 游戏自动启动设置
- 日志显示开关
- 应用版本信息

### 2. 设备信息上报器 (`DeviceInfoReporter.kt`)

**功能**:
- 管理设备信息上报流程
- 处理连接成功和重连成功的上报
- 定期上报机制（5分钟间隔）
- 自动重试和错误处理

**关键方法**:
```kotlin
suspend fun reportDeviceInfo(senderId: String): Boolean  // 立即上报
suspend fun onConnectionEstablished(senderId: String)    // 连接成功时上报
suspend fun onReconnectionEstablished(senderId: String) // 重连成功时上报
fun startPeriodicReporting(senderId: String)            // 开始定期上报
```

### 3. WebRTCManager集成

**修改内容**:
- 在`initialize()`中创建`DeviceInfoReporter`实例
- 在`onSignalingConnected()`中区分首次连接和重连
- 连接成功时立即上报设备信息
- 重连成功时重新上报设备信息
- 断开连接时停止定期上报

**集成代码**:
```kotlin
// 初始化时创建上报器
deviceInfoReporter = DeviceInfoReporter(context, signalingClient!!)

// 连接成功时上报
deviceInfoReporter?.let { reporter ->
    CoroutineScope(Dispatchers.IO).launch {
        if (isReconnection) {
            reporter.onReconnectionEstablished(senderId)
        } else {
            reporter.onConnectionEstablished(senderId)
            reporter.startPeriodicReporting(senderId)
        }
    }
}
```

## 🗄️ 数据库扩展

### 新增应用设置字段

在`sender_device_info`表中新增：
- `app_signaling_url` - 信令服务器地址
- `app_sender_id` - 应用配置的发送端ID
- `app_video_source` - 视频源类型
- `app_video_resolution` - 视频分辨率设置
- `app_video_bitrate` - 视频码率设置
- `app_video_codec` - 视频编码器
- `app_video_framerate` - 视频帧率设置
- `app_camera_id` - 摄像头ID
- `app_screen_capture_quality` - 屏幕录制质量
- `app_audio_source` - 音频源
- `app_auto_start_game` - 自动启动游戏
- `app_auto_start_game_package` - 游戏包名
- `app_log_display_enabled` - 日志显示开关
- `app_version` - 应用版本
- `app_version_code` - 应用版本号
- `app_build_type` - 构建类型

## 📡 通信协议

### 设备信息上报消息

```json
{
    "type": "device_info",
    "sender_id": "gamev-001",
    "device_info": {
        "cpu_unique_id": "CPU_123456789",
        "wechat_sn": "WX_SN_001",
        "motherboard_model": "Qualcomm SM8350",
        "android_version": "Android 13 (API 33)",
        "system_version": "MIUI 14.0.1",
        "available_storage": 15360,
        "total_storage": 65536,
        "total_memory": 8192,
        "available_memory": 4096,
        "cpu_temperature": 45.5,
        "local_ip": "*************",
        "network_type": "wifi",
        "mac_address": "00:11:22:33:44:55",
        "screen_resolution": "1920x1080",
        "screen_orientation": "landscape",
        "system_time": "2024-01-01T12:00:00.000Z",
        "app_settings": {
            "signaling_url": "wss://sling.91jdcd.com/ws/",
            "sender_id": "gamev-001",
            "video_source": "screen",
            "video_resolution": "1080p",
            "video_bitrate": 3000,
            "video_codec": "H264",
            "video_framerate": 60,
            "camera_id": "0",
            "screen_capture_quality": "high",
            "audio_source": "remote_submix",
            "auto_start_game": true,
            "auto_start_game_package": "com.example.ocean3",
            "log_display_enabled": false,
            "app_version": "1.0.0",
            "app_version_code": 1,
            "build_type": "release"
        }
    },
    "timestamp": 1704096000
}
```

## 🚀 部署和测试

### 1. 数据库更新
```sql
-- 更新数据库表结构
ALTER TABLE sender_device_info ADD COLUMN app_signaling_url VARCHAR(255) DEFAULT NULL COMMENT '信令服务器地址';
-- ... 添加其他应用设置字段
```

### 2. Android应用集成
1. 将新文件添加到项目：
   - `DeviceInfoCollector.kt`
   - `DeviceInfoReporter.kt`

2. 确保权限：
   ```xml
   <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
   <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
   ```

3. 编译和安装应用

### 3. 测试流程

1. **启动应用并连接**:
   - 应用启动后连接信令服务器
   - 检查日志确认设备信息上报成功

2. **验证数据库**:
   ```sql
   SELECT sender_id, android_version, app_video_resolution, app_version 
   FROM sender_device_info 
   ORDER BY last_online_time DESC;
   ```

3. **测试重连**:
   - 断开网络连接
   - 恢复网络连接
   - 确认重连后重新上报设备信息

4. **Web管理界面验证**:
   - 访问admin.html
   - 查看发送端列表
   - 确认显示详细的设备信息和应用设置

## 📊 监控和日志

### Android端日志
```
🔗 [信令连接] ✅ 已连接到信令服务器 (首次连接)
🔗 [信令连接] 设备信息上报已启动
📱 开始收集设备信息: gamev-001
📱 发送设备信息到信令服务器: gamev-001
📱 设备信息上报成功: gamev-001
```

### 服务器端日志
```
📱 收到设备信息上报: gamev-001
✅ 设备信息已更新: gamev-001
📤 设备信息确认已发送: gamev-001 | 成功: True
```

## 🔧 配置选项

### 上报频率调整
在`DeviceInfoReporter.kt`中修改：
```kotlin
private const val REPORT_INTERVAL = 5 * 60 * 1000L // 5分钟
```

### 禁用定期上报
如果只需要连接时上报，可以注释掉：
```kotlin
// reporter.startPeriodicReporting(senderId)
```

## ⚠️ 注意事项

1. **权限要求**: 确保应用有网络和WiFi状态访问权限
2. **性能影响**: 设备信息收集是异步进行，不会阻塞主线程
3. **网络消耗**: 定期上报会产生少量网络流量
4. **隐私保护**: 确保设备信息的安全传输和存储
5. **错误处理**: 上报失败不会影响WebRTC功能

## 🎯 预期效果

集成完成后：
- ✅ 连接成功时自动上报完整设备信息
- ✅ 重连成功时重新上报设备信息  
- ✅ 定期更新设备状态（可选）
- ✅ Web管理界面显示详细设备信息
- ✅ 包含应用配置和硬件信息
- ✅ 支持地理位置和网络信息（Android端获取公网IP）

## 🌐 公网IP获取机制

### Android端获取公网IP
- **主要服务**: ipinfo.io/json（包含地理位置信息）
- **备选服务1**: httpbin.org/ip
- **备选服务2**: ifconfig.me/ip
- **IPv6支持**: ifconfig.me

### 获取的信息
- 公网IPv4地址
- 公网IPv6地址（如果可用）
- 城市、省份、国家
- 经纬度坐标
- ISP组织信息
- 时区信息

### 容错机制
- 多个服务备选，确保获取成功率
- 5秒超时设置
- 异步获取，不阻塞主流程
- 获取失败时标记为"unknown"

现在Android发送端应用已经完全集成了设备信息上报功能！
