<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单位置修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible; /* 允许下拉菜单超出容器边界 */
            min-height: 100vh; /* 使用最小高度而不是固定高度 */
            padding: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 18px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            position: relative;
        }
        
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }
        
        .dropdown-toggle {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            position: relative;
            z-index: 1001; /* 确保按钮在下拉菜单之上 */
        }
        
        .dropdown-menu {
            position: fixed; /* 使用fixed定位避免被父容器裁剪 */
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw; /* 限制最大宽度避免超出屏幕 */
            z-index: 99999; /* 提高到最高层级 */
            display: none;
            padding: 15px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
        }
        
        .dropdown-menu.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }
        
        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }
        
        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 0.9em;
        }
        
        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .device-info-value {
            color: #495057;
            font-weight: 600;
        }
        
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        
        .game-name {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 下拉菜单位置修复测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <ul>
                <li>✅ 使用 <code>position: fixed</code> 避免被容器裁剪</li>
                <li>✅ 智能位置计算，避免超出屏幕边界</li>
                <li>✅ 超高 z-index (99999) 确保在所有元素之上</li>
                <li>✅ 响应式宽度限制 (max-width: 90vw)</li>
                <li>✅ 点击外部自动关闭</li>
            </ul>
        </div>
        
        <h2>📊 模拟设备状态 - 按IP分类显示</h2>
        
        <div class="ip-group">
            <div class="ip-group-header">
                <h3>📍 ************* (3 设备)</h3>
            </div>
            
            <div class="test-grid">
                <!-- 左侧设备 -->
                <div class="test-card">
                    <h3>🎮 gamev-left001</h3>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value"><span class="game-name">ocean3</span></span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">最近上线:</span>
                        <span class="device-info-value">在线 (2024-01-15 14:30:25)</span>
                    </div>
                    
                    <div class="control-dropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('left001')">
                            ⚙️ 功能菜单
                        </button>
                        <div class="dropdown-menu" id="dropdown-left001">
                            <div class="dropdown-columns">
                                <div class="dropdown-section">
                                    <h4>服务控制</h4>
                                    <button class="dropdown-btn">🚀 启动服务</button>
                                    <button class="dropdown-btn">⏹️ 停止服务</button>
                                    <button class="dropdown-btn">🔄 重启服务</button>
                                    <h4>视频控制</h4>
                                    <button class="dropdown-btn">🎥 视频参数设置</button>
                                    <button class="dropdown-btn">📸 视频流截屏</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>游戏控制</h4>
                                    <button class="dropdown-btn">🎮 游戏设置</button>
                                    <h4>日志管理</h4>
                                    <button class="dropdown-btn">📝 开启日志显示</button>
                                    <button class="dropdown-btn">🚫 关闭日志显示</button>
                                    <button class="dropdown-btn">📥 下载日志(FTP)</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>网络配置</h4>
                                    <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                    <button class="dropdown-btn">📡 发送网络配置</button>
                                    <h4>系统控制</h4>
                                    <button class="dropdown-btn">🔄 重启设备</button>
                                    <button class="dropdown-btn">📦 升级应用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 中间设备 -->
                <div class="test-card">
                    <h3>🎮 gamev-center002</h3>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value"><span class="game-name">mygame</span></span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">最近上线:</span>
                        <span class="device-info-value">在线 (2024-01-15 13:45:10)</span>
                    </div>
                    
                    <div class="control-dropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('center002')">
                            ⚙️ 功能菜单
                        </button>
                        <div class="dropdown-menu" id="dropdown-center002">
                            <div class="dropdown-columns">
                                <div class="dropdown-section">
                                    <h4>服务控制</h4>
                                    <button class="dropdown-btn">🚀 启动服务</button>
                                    <button class="dropdown-btn">⏹️ 停止服务</button>
                                    <button class="dropdown-btn">🔄 重启服务</button>
                                    <h4>视频控制</h4>
                                    <button class="dropdown-btn">🎥 视频参数设置</button>
                                    <button class="dropdown-btn">📸 视频流截屏</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>游戏控制</h4>
                                    <button class="dropdown-btn">🎮 游戏设置</button>
                                    <h4>日志管理</h4>
                                    <button class="dropdown-btn">📝 开启日志显示</button>
                                    <button class="dropdown-btn">🚫 关闭日志显示</button>
                                    <button class="dropdown-btn">📥 下载日志(FTP)</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>网络配置</h4>
                                    <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                    <button class="dropdown-btn">📡 发送网络配置</button>
                                    <h4>系统控制</h4>
                                    <button class="dropdown-btn">🔄 重启设备</button>
                                    <button class="dropdown-btn">📦 升级应用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧设备 -->
                <div class="test-card">
                    <h3>🎮 gamev-right003</h3>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value">● 离线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">无</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">最近上线:</span>
                        <span class="device-info-value">2024-01-15 12:20:15 (2小时前)</span>
                    </div>
                    
                    <div class="control-dropdown">
                        <button class="dropdown-toggle" onclick="toggleDropdown('right003')">
                            ⚙️ 功能菜单
                        </button>
                        <div class="dropdown-menu" id="dropdown-right003">
                            <div class="dropdown-columns">
                                <div class="dropdown-section">
                                    <h4>服务控制</h4>
                                    <button class="dropdown-btn">🚀 启动服务</button>
                                    <button class="dropdown-btn">⏹️ 停止服务</button>
                                    <button class="dropdown-btn">🔄 重启服务</button>
                                    <h4>视频控制</h4>
                                    <button class="dropdown-btn">🎥 视频参数设置</button>
                                    <button class="dropdown-btn">📸 视频流截屏</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>游戏控制</h4>
                                    <button class="dropdown-btn">🎮 游戏设置</button>
                                    <h4>日志管理</h4>
                                    <button class="dropdown-btn">📝 开启日志显示</button>
                                    <button class="dropdown-btn">🚫 关闭日志显示</button>
                                    <button class="dropdown-btn">📥 下载日志(FTP)</button>
                                </div>
                                <div class="dropdown-section">
                                    <h4>网络配置</h4>
                                    <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                    <button class="dropdown-btn">📡 发送网络配置</button>
                                    <h4>系统控制</h4>
                                    <button class="dropdown-btn">🔄 重启设备</button>
                                    <button class="dropdown-btn">📦 升级应用</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(deviceId) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${deviceId}`) {
                    menu.classList.remove('show');
                    menu.style.left = '';
                    menu.style.top = '';
                }
            });

            const dropdown = document.getElementById(`dropdown-${deviceId}`);
            const button = document.querySelector(`[onclick="toggleDropdown('${deviceId}')"]`);
            const isShowing = dropdown.classList.contains('show');

            if (isShowing) {
                dropdown.classList.remove('show');
                dropdown.style.left = '';
                dropdown.style.top = '';
            } else {
                dropdown.classList.add('show');
                
                // 调整位置
                setTimeout(() => {
                    adjustDropdownPosition(dropdown, button);
                }, 10);
            }
        }

        function adjustDropdownPosition(dropdown, button) {
            const buttonRect = button.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const dropdownWidth = 720;
            const dropdownHeight = 400;

            // 计算最佳位置
            let left = buttonRect.right - dropdownWidth; // 默认右对齐
            let top = buttonRect.bottom + 5; // 按钮下方

            // 检查右边界，如果超出则左对齐
            if (left < 10) {
                left = buttonRect.left;
            }

            // 检查右边界，如果还是超出则居中
            if (left + dropdownWidth > windowWidth - 10) {
                left = Math.max(10, (windowWidth - dropdownWidth) / 2);
            }

            // 检查下边界，如果超出则显示在按钮上方
            if (top + dropdownHeight > windowHeight - 10) {
                top = buttonRect.top - dropdownHeight - 5;
            }

            // 确保不超出上边界
            if (top < 10) {
                top = 10;
            }

            // 应用位置
            dropdown.style.left = `${left}px`;
            dropdown.style.top = `${top}px`;
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.style.left = '';
                    menu.style.top = '';
                });
            }
        });
    </script>
</body>
</html>
