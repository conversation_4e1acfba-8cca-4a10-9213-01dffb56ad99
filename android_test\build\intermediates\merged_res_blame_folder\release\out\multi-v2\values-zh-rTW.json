{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,440,540,622,719,827,904,979,1071,1165,1262,1358,1453,1547,1643,1735,1827,1919,1997,2093,2188,2283,2380,2476,2574,2724,6600", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "342,435,535,617,714,822,899,974,1066,1160,1257,1353,1448,1542,1638,1730,1822,1914,1992,2088,2183,2278,2375,2471,2569,2719,2813,6674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,363,470,543,605,683,743,803,881,939,995,1055,1113,1167,1252,1308,1366,1420,1485,1577,1651,1728,1818,1881,1944,2021,2088,2154,2217,2285,2363,2424,2495,2562,2624,2703,2768,2851,2936,3010,3074,3150,3198,3262,3338,3416,3478,3542,3605,3685,3762,3838,3915", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "197,264,358,465,538,600,678,738,798,876,934,990,1050,1108,1162,1247,1303,1361,1415,1480,1572,1646,1723,1813,1876,1939,2016,2083,2149,2212,2280,2358,2419,2490,2557,2619,2698,2763,2846,2931,3005,3069,3145,3193,3257,3333,3411,3473,3537,3600,3680,3757,3833,3910,3979"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2818,2885,2979,3086,3159,3221,3299,3359,3419,3497,3555,3611,3671,3729,3783,3868,3924,3982,4036,4101,4193,4267,4344,4434,4497,4560,4637,4704,4770,4833,4901,4979,5040,5111,5178,5240,5319,5384,5467,5552,5626,5690,5766,5814,5878,5954,6032,6094,6158,6221,6301,6378,6454,6531", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "247,2880,2974,3081,3154,3216,3294,3354,3414,3492,3550,3606,3666,3724,3778,3863,3919,3977,4031,4096,4188,4262,4339,4429,4492,4555,4632,4699,4765,4828,4896,4974,5035,5106,5173,5235,5314,5379,5462,5547,5621,5685,5761,5809,5873,5949,6027,6089,6153,6216,6296,6373,6449,6526,6595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "6679", "endColumns": "100", "endOffsets": "6775"}}]}]}