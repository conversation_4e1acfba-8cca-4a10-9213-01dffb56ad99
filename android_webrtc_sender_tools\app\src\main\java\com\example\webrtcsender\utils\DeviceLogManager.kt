package com.example.webrtcsender.utils

import android.content.Context
import android.content.SharedPreferences
import com.example.webrtcsender.signaling.SignalingClient
import com.example.webrtcsender.utils.Logger
import kotlinx.coroutines.*
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 设备日志管理器 - 负责记录设备重要日志并上报到信令服务器
 */
class DeviceLogManager private constructor() {
    
    companion object {
        private const val TAG = "DeviceLogManager"
        private const val PREFS_NAME = "device_log_prefs"
        private const val KEY_REBOOT_COUNT = "reboot_count"
        private const val KEY_LAST_RESET_DATE = "last_reset_date"
        private const val MAX_DAILY_REBOOTS = 10
        
        @Volatile
        private var INSTANCE: DeviceLogManager? = null
        
        fun getInstance(): DeviceLogManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeviceLogManager().also { INSTANCE = it }
            }
        }
    }
    
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var signalingClient: SignalingClient? = null
    
    /**
     * 初始化设备日志管理器
     */
    fun initialize(signalingClient: SignalingClient) {
        this.signalingClient = signalingClient
        Logger.i(TAG, "🔧 设备日志管理器初始化完成")
    }
    
    /**
     * 记录设备日志事件
     */
    fun logDeviceEvent(
        context: Context,
        eventType: String,
        eventLevel: String,
        message: String,
        details: String? = null
    ) {
        coroutineScope.launch {
            try {
                val deviceId = DeviceUtils.getDeviceId(context)
                val timestamp = System.currentTimeMillis()
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                
                val logData = JSONObject().apply {
                    put("device_id", deviceId)
                    put("event_type", eventType)
                    put("event_level", eventLevel)
                    put("message", message)
                    put("details", details ?: "")
                    put("timestamp", timestamp)
                    put("formatted_time", dateFormat.format(Date(timestamp)))
                    put("app_version", getAppVersion(context))
                    put("system_info", getSystemInfo())
                }
                
                Logger.i(TAG, "📝 记录设备日志: $eventType - $message")
                
                // 发送到信令服务器
                sendLogToSignalingServer(logData)
                
            } catch (e: Exception) {
                Logger.e(TAG, "❌ 记录设备日志失败", e)
            }
        }
    }
    
    /**
     * 发送日志到信令服务器
     */
    private suspend fun sendLogToSignalingServer(logData: JSONObject) {
        try {
            val message = JSONObject().apply {
                put("type", "device_log")
                put("data", logData)
            }
            
            signalingClient?.sendMessage(message.toString())
            Logger.d(TAG, "📤 设备日志已发送到信令服务器")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 发送设备日志到信令服务器失败", e)
        }
    }
    
    /**
     * 检查今日重启次数
     */
    fun checkDailyRebootCount(context: Context): Int {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val lastResetDate = prefs.getString(KEY_LAST_RESET_DATE, "")
        
        // 如果是新的一天，重置计数
        if (lastResetDate != today) {
            prefs.edit()
                .putString(KEY_LAST_RESET_DATE, today)
                .putInt(KEY_REBOOT_COUNT, 0)
                .apply()
            Logger.i(TAG, "🔄 新的一天，重启计数已重置")
            return 0
        }
        
        return prefs.getInt(KEY_REBOOT_COUNT, 0)
    }
    
    /**
     * 增加重启计数
     */
    fun incrementRebootCount(context: Context): Int {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentCount = checkDailyRebootCount(context)
        val newCount = currentCount + 1
        
        prefs.edit()
            .putInt(KEY_REBOOT_COUNT, newCount)
            .apply()
            
        Logger.i(TAG, "📊 重启计数更新: $newCount/$MAX_DAILY_REBOOTS")
        return newCount
    }
    
    /**
     * 检查是否可以重启
     */
    fun canReboot(context: Context): Boolean {
        val currentCount = checkDailyRebootCount(context)
        val canReboot = currentCount < MAX_DAILY_REBOOTS
        
        if (!canReboot) {
            Logger.w(TAG, "⚠️ 今日重启次数已达上限: $currentCount/$MAX_DAILY_REBOOTS")
            logDeviceEvent(
                context,
                "reboot_limit_reached",
                "WARNING",
                "今日重启次数已达上限",
                "当前次数: $currentCount, 上限: $MAX_DAILY_REBOOTS"
            )
        }
        
        return canReboot
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName}(${packageInfo.versionCode})"
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * 获取系统信息
     */
    private fun getSystemInfo(): JSONObject {
        return JSONObject().apply {
            put("android_version", android.os.Build.VERSION.RELEASE)
            put("api_level", android.os.Build.VERSION.SDK_INT)
            put("brand", android.os.Build.BRAND)
            put("model", android.os.Build.MODEL)
            put("manufacturer", android.os.Build.MANUFACTURER)
        }
    }
    
    /**
     * 获取重启统计信息（用于调试）
     */
    fun getRebootStats(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val lastResetDate = prefs.getString(KEY_LAST_RESET_DATE, "未设置")
        val currentCount = prefs.getInt(KEY_REBOOT_COUNT, 0)

        return "今日重启统计: $currentCount/$MAX_DAILY_REBOOTS, 日期: $today, 上次重置: $lastResetDate"
    }

    /**
     * 重置重启计数（用于测试）
     */
    fun resetRebootCount(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putInt(KEY_REBOOT_COUNT, 0)
            .apply()
        Logger.i(TAG, "🔄 重启计数已手动重置")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        coroutineScope.cancel()
        signalingClient = null
        Logger.i(TAG, "🧹 设备日志管理器资源已清理")
    }
}
