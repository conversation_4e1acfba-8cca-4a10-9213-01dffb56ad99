# 管理控制台功能增强说明

## 新增功能概述

本次更新为WebRTC信令服务器管理控制台添加了多项重要功能，提升了设备管理和监控能力。

## 1. 发送端状态监控增强

### 按IP地址分类显示
- **功能**: 发送端设备按IP地址自动分组显示
- **优势**: 便于识别同一网络下的多个设备
- **显示内容**:
  - IP地址分组标题
  - 每组设备数量统计
  - 设备详细信息卡片

### 游戏信息显示
- **功能**: 显示当前运行的游戏应用
- **显示方式**: 提取包名最后一个单词作为游戏名
- **示例**: 
  - `com.ironnet.ocean3` → 显示为 `ocean3`
  - `com.example.mygame` → 显示为 `mygame`

### 最近上线时间
- **功能**: 智能显示设备最后在线时间
- **时间格式**:
  - 当前在线: "当前在线"
  - 1分钟内: "刚刚"
  - 1小时内: "X分钟前"
  - 1天内: "X小时前"
  - 超过1天: "X天前"

## 2. 操作日志增强

### 发送端执行消息记录
- **功能**: 实时记录发送端上报的执行状态
- **消息类型**:
  - 命令响应日志
  - 升级进度日志
  - 设备状态变更日志
  - 截屏操作结果

### 日志分类显示
- **成功操作**: 绿色背景显示
- **错误信息**: 红色背景显示
- **一般信息**: 蓝色背景显示

## 3. 功能菜单3栏布局

### 新的菜单结构
原来的单栏下拉菜单改为3栏布局，提高操作效率：

#### 第一栏 - 服务与视频控制
- 启动/停止/重启服务
- 视频参数设置
- 视频流截屏

#### 第二栏 - 游戏与日志管理
- 游戏设置
- 开启/关闭日志显示
- FTP/HTTP日志下载

#### 第三栏 - 网络与系统控制
- STUN/TURN配置
- 发送网络配置
- 设备重启
- 应用升级

## 4. STUN/TURN服务器配置

### 配置模态框
- **功能**: 专门的STUN/TURN服务器配置界面
- **预设配置**: 提供常用服务器配置模板
- **实时保存**: 支持保存并立即广播配置

### 预设服务器选项
#### STUN服务器预设
- Google STUN服务器
- Mozilla STUN服务器
- 自定义配置

#### TURN服务器预设
- Numb TURN服务器
- Xirsys TURN服务器
- 自定义配置

## 5. 发送端升级支持FTP下载

### FTP下载功能
- **支持协议**: 自动检测FTP和HTTP/HTTPS协议
- **FTP解析**: 自动解析FTP URL中的主机、端口、用户名、密码
- **下载进度**: 实时显示FTP下载进度
- **文件验证**: 下载完成后自动验证APK文件

### 使用方法
1. 在升级管理中输入FTP地址
2. 格式: `ftp://username:password@host:port/path/to/file.apk`
3. 系统自动识别协议并选择相应下载方式

### Android端依赖
添加了Apache Commons Net库支持FTP下载：
```kotlin
implementation("commons-net:commons-net:3.9.0")
```

## 6. 状态上报增强

### 新增上报字段
发送端现在会上报更多设备信息：
- **IP地址**: 设备当前网络IP
- **游戏包名**: 当前配置的游戏应用包名
- **连接时间**: 设备首次连接到信令服务器的时间
- **最后心跳**: 最近一次心跳时间戳

### 数据格式
```json
{
  "type": "status_update",
  "status": "streaming",
  "viewers": 2,
  "resolution": "1920x1080",
  "fps": 60,
  "ip": "*************",
  "game_package": "com.ironnet.ocean3",
  "connection_time": 1703123456,
  "last_heartbeat": 1703123500
}
```

## 使用说明

### 启动管理控制台
1. 确保信令服务器正在运行
2. 打开浏览器访问管理控制台页面
3. 控制台会自动连接到WebSocket并显示设备状态

### 配置STUN/TURN服务器
1. 点击设备控制菜单中的"🌐 STUN/TURN配置"
2. 选择预设配置或自定义输入
3. 点击"保存并广播"将配置发送到所有设备

### 升级设备应用
1. 在升级管理区域输入APK下载地址
2. 支持HTTP/HTTPS和FTP协议
3. 设置版本号和是否强制升级
4. 选择目标设备或留空表示所有设备

### 查看操作日志
- 所有设备操作和状态变更都会记录在操作日志区域
- 日志按时间顺序显示，最新的在底部
- 不同类型的日志用不同颜色区分

## 技术实现

### 前端改进
- 响应式3栏布局设计
- 实时WebSocket消息处理
- 动态IP分组算法
- 时间格式化显示

### 后端增强
- 扩展状态上报字段
- FTP下载支持
- 改进的消息路由

### Android端更新
- 新增FTP下载功能
- 增强状态上报
- 改进网络信息获取

## 注意事项

1. **FTP下载**: 需要确保FTP服务器可访问且凭据正确
2. **网络配置**: STUN/TURN配置需要根据实际网络环境调整
3. **设备权限**: 某些功能需要设备具有相应的系统权限
4. **日志存储**: 操作日志仅在当前会话中保存，刷新页面会清空

## 故障排除

### 常见问题
1. **设备不显示IP**: 检查设备网络连接和权限
2. **FTP下载失败**: 验证FTP地址格式和服务器可访问性
3. **配置不生效**: 确保设备在线且WebSocket连接正常
4. **日志不更新**: 检查WebSocket连接状态

### 调试建议
1. 使用浏览器开发者工具查看WebSocket消息
2. 检查信令服务器日志
3. 验证设备端日志输出
4. 确认网络连通性
