<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/appIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="16dp"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/appName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textColor="@android:color/black"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/packageName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Caption"
            android:textColor="@android:color/darker_gray"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="4dp" />

    </LinearLayout>

</LinearLayout>
