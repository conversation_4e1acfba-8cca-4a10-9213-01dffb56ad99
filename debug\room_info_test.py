#!/usr/bin/env python3
"""
房间信息更新功能测试脚本
"""

import asyncio
import aiohttp
import json
import pymysql

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'fa_game',
    'charset': 'utf8mb4'
}

# 服务器群配置
SERVER_GROUPS = [
    'http://testva2.91jdcd.com',
]

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        print(f"获取数据库连接失败: {e}")
        return None

async def fetch_room_info_from_server(domain):
    """从服务器获取房间信息"""
    try:
        api_url = f"{domain}/api/game/list_dcl_ast4"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == 1 and 'data' in data:
                        print(f"成功获取 {domain} 的房间信息，共 {len(data['data'])} 个房间")
                        return data['data']
                    else:
                        print(f"服务器 {domain} 返回错误: {data.get('msg', '未知错误')}")
                        return []
                else:
                    print(f"服务器 {domain} 响应状态码: {response.status}")
                    return []
    except Exception as e:
        print(f"获取服务器 {domain} 房间信息失败: {e}")
        return []

async def create_room_info_tables():
    """创建房间信息相关表"""
    connection = get_db_connection()
    if not connection:
        return False

    cursor = None
    try:
        cursor = connection.cursor()
        
        # 创建服务器群表
        server_groups_sql = """
        CREATE TABLE IF NOT EXISTS fa_server_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            domain VARCHAR(255) NOT NULL UNIQUE,
            name VARCHAR(100) DEFAULT '',
            status TINYINT DEFAULT 1 COMMENT '1=启用 0=禁用',
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器群配置表'
        """
        cursor.execute(server_groups_sql)
        print("服务器群表创建成功")
        
        # 创建房间信息表
        room_info_sql = """
        CREATE TABLE IF NOT EXISTS fa_room_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            server_domain VARCHAR(255) NOT NULL,
            room_id INT NOT NULL,
            heiqiplayer_sender_id VARCHAR(50) DEFAULT '',
            room_name VARCHAR(100) DEFAULT '',
            category_id INT DEFAULT 0,
            sort_order INT DEFAULT 0,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_server_room (server_domain, room_id),
            INDEX idx_sender_id (heiqiplayer_sender_id),
            INDEX idx_server_domain (server_domain)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间信息表'
        """
        cursor.execute(room_info_sql)
        print("房间信息表创建成功")
        
        connection.commit()
        print("房间信息相关表创建/更新完成")
        return True

    except Exception as e:
        print(f"创建房间信息表失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def update_room_info_to_database(domain, rooms_data):
    """更新房间信息到数据库"""
    connection = get_db_connection()
    if not connection:
        return False

    cursor = None
    try:
        cursor = connection.cursor()
        
        # 清理该服务器的旧数据
        delete_sql = "DELETE FROM fa_room_info WHERE server_domain = %s"
        cursor.execute(delete_sql, (domain,))
        print(f"清理 {domain} 的旧房间数据")
        
        # 插入新数据
        insert_sql = """
        INSERT INTO fa_room_info (server_domain, room_id, heiqiplayer_sender_id, room_name, category_id, sort_order)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        for room in rooms_data:
            cursor.execute(insert_sql, (
                domain,
                room.get('id', 0),
                room.get('heiqiplayer_sender_id', ''),
                room.get('name', ''),
                room.get('c_id', 0),
                room.get('sort', 0)
            ))
            print(f"插入房间: {room.get('name', '')} (ID: {room.get('id', 0)})")
        
        connection.commit()
        print(f"成功更新 {domain} 的房间信息到数据库，共 {len(rooms_data)} 个房间")
        return True

    except Exception as e:
        print(f"更新房间信息到数据库失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def test_room_info_update():
    """测试房间信息更新功能"""
    print("开始测试房间信息更新功能...")
    
    # 创建数据库表
    print("\n1. 创建数据库表...")
    await create_room_info_tables()
    
    # 获取并更新房间信息
    print("\n2. 获取并更新房间信息...")
    for domain in SERVER_GROUPS:
        print(f"\n处理服务器: {domain}")
        rooms_data = await fetch_room_info_from_server(domain)
        if rooms_data:
            await update_room_info_to_database(domain, rooms_data)
            
            # 显示部分房间信息
            print(f"\n房间信息示例:")
            for i, room in enumerate(rooms_data[:5]):  # 只显示前5个
                print(f"  {i+1}. {room.get('name', '')} (ID: {room.get('id', 0)}, 发送端: {room.get('heiqiplayer_sender_id', '无')})")
            
            if len(rooms_data) > 5:
                print(f"  ... 还有 {len(rooms_data) - 5} 个房间")
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(test_room_info_update())
