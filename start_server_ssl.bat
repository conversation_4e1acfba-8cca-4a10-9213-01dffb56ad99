@echo off
chcp 65001 > nul
echo ===================================
echo    WebRTC视频流服务器启动脚本 (SSL)
echo ===================================
echo.
REM 证书路径 - 请修改为您的证书路径
set SSL_CERT=/www/server/panel/vhost/cert/sling.91jdcd.com/fullchain.pem
set SSL_KEY=/www/server/panel/vhost/cert/sling.91jdcd.com/privkey.pem

REM 检查证书文件是否存在
if not exist "%SSL_CERT%" (
    echo 错误: SSL证书文件不存在: %SSL_CERT%
    echo 请确保已经获取了SSL证书，或者修改脚本中的证书路径
    goto :end
)

if not exist "%SSL_KEY%" (
    echo 错误: SSL密钥文件不存在: %SSL_KEY%
    echo 请确保已经获取了SSL证书，或者修改脚本中的证书路径
    goto :end
)

REM 启动信令服务器
echo 正在启动带SSL的信令服务器...
python enhanced_signaling_server.py ^
    --ws-host 127.0.0.1 ^
    --ws-port 8765 ^
    --http-host 127.0.0.1 ^
    --http-port 8080 ^
    --web-dir ./web ^
    --use-ssl ^
    --ssl-cert "%SSL_CERT%" ^
    --ssl-key "%SSL_KEY%"

:end
pause
