# 动态摄像头列表功能实现

## 🎯 **功能概述**

根据您的需求，我已经实现了以下功能：

### **1. 配置页面实时读取系统摄像头列表**
- ✅ **动态枚举摄像头** - 实时检测系统中所有可用摄像头
- ✅ **详细摄像头信息** - 显示摄像头ID、类型、API版本等
- ✅ **智能摄像头识别** - 自动识别前置、后置摄像头
- ✅ **硬件兼容性处理** - 支持Camera2和Camera1 API

### **2. 摄像头切换按钮基于动态列表**
- ✅ **循环切换机制** - 按照可用摄像头列表顺序切换
- ✅ **智能摄像头选择** - 自动跳过不可用的摄像头
- ✅ **用户友好提示** - 显示具体切换到哪个摄像头

## 🔧 **核心实现**

### **1. WebRTCManager中的摄像头枚举功能**

#### **动态摄像头列表获取：**
```kotlin
fun getAvailableCameras(context: Context): List<CameraInfo> {
    val cameras = mutableListOf<CameraInfo>()
    
    try {
        // 尝试Camera2
        val camera2Enumerator = Camera2Enumerator(context)
        val camera2DeviceNames = camera2Enumerator.deviceNames
        
        Logger.i(TAG, "🎥 [摄像头列表] Camera2可用设备: ${camera2DeviceNames.joinToString()}")
        
        for (deviceName in camera2DeviceNames) {
            try {
                val isFrontFacing = camera2Enumerator.isFrontFacing(deviceName)
                val isBackFacing = camera2Enumerator.isBackFacing(deviceName)
                
                val displayName = when {
                    isFrontFacing -> "前置摄像头 ($deviceName)"
                    isBackFacing -> "后置摄像头 ($deviceName)"
                    else -> "摄像头 $deviceName"
                }
                
                cameras.add(CameraInfo(
                    id = deviceName,
                    displayName = displayName,
                    isFrontFacing = isFrontFacing,
                    isBackFacing = isBackFacing,
                    apiType = "Camera2"
                ))
            } catch (e: Exception) {
                Logger.w(TAG, "🎥 [摄像头列表] Camera2设备 $deviceName 信息获取失败: ${e.message}")
            }
        }
    } catch (e: Exception) {
        Logger.e(TAG, "🎥 [摄像头列表] Camera2枚举失败: ${e.message}")
    }
    
    // 如果Camera2没有找到摄像头，尝试Camera1
    if (cameras.isEmpty()) {
        try {
            val camera1Enumerator = Camera1Enumerator(false)
            val camera1DeviceNames = camera1Enumerator.deviceNames
            
            Logger.i(TAG, "🎥 [摄像头列表] Camera1可用设备: ${camera1DeviceNames.joinToString()}")
            
            for (deviceName in camera1DeviceNames) {
                // Camera1处理逻辑...
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头列表] Camera1枚举失败: ${e.message}")
        }
    }
    
    return cameras
}
```

#### **摄像头信息数据类：**
```kotlin
data class CameraInfo(
    val id: String,              // 摄像头ID
    val displayName: String,     // 显示名称
    val isFrontFacing: Boolean,  // 是否前置
    val isBackFacing: Boolean,   // 是否后置
    val apiType: String          // API类型 (Camera2/Camera1)
)
```

### **2. 智能摄像头切换逻辑**

#### **基于动态列表的切换：**
```kotlin
private fun switchCameraThroughServiceRestart() {
    try {
        val currentSourceType = WebRTCManager.getVideoSourceType()

        if (currentSourceType == "camera") {
            // 获取可用摄像头列表
            val availableCameras = WebRTCManager.getAvailableCameras(this)
            
            if (availableCameras.isEmpty()) {
                Logger.w(TAG, "没有可用的摄像头")
                Toast.makeText(this, "没有可用的摄像头", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 找到当前摄像头在列表中的位置
            val currentCameraId = WebRTCManager.getCameraId()
            val currentIndex = availableCameras.indexOfFirst { it.id == currentCameraId }
            
            // 计算下一个摄像头的索引（循环）
            val nextIndex = if (currentIndex >= 0) {
                (currentIndex + 1) % availableCameras.size
            } else {
                0 // 如果当前摄像头不在列表中，使用第一个
            }
            
            val nextCamera = availableCameras[nextIndex]
            
            Logger.i(TAG, "🎥 [摄像头切换] 从 ${currentCameraId} 切换到 ${nextCamera.id} (${nextCamera.displayName})")

            // 设置新的摄像头ID
            WebRTCManager.setCameraId(nextCamera.id)

            // 重启服务应用新摄像头
            stopServiceForCameraSwitch()
            Handler(mainLooper).postDelayed({
                startServiceForCameraSwitch()
                Toast.makeText(this, "已切换到: ${nextCamera.displayName}", Toast.LENGTH_SHORT).show()
            }, 3000)

        } else {
            // 如果当前不是摄像头模式，切换到摄像头模式
            val availableCameras = WebRTCManager.getAvailableCameras(this)
            
            if (availableCameras.isEmpty()) {
                Toast.makeText(this, "没有可用的摄像头", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 使用第一个可用摄像头
            val firstCamera = availableCameras[0]
            WebRTCManager.setCameraId(firstCamera.id)
            
            // 启动摄像头模式
            // ...
        }
    } catch (e: Exception) {
        Logger.e(TAG, "摄像头切换失败", e)
        showError("切换摄像头失败: ${e.message}")
    }
}
```

### **3. 摄像头配置页面**

#### **CameraConfigActivity功能：**
- ✅ **实时摄像头列表** - 页面打开时自动刷新摄像头列表
- ✅ **下拉选择器** - 用户可以从列表中选择摄像头
- ✅ **详细信息显示** - 显示选中摄像头的详细信息
- ✅ **配置保存** - 保存用户选择的摄像头配置
- ✅ **手动刷新** - 提供刷新按钮重新扫描摄像头

#### **主要功能方法：**
```kotlin
private fun loadAvailableCameras() {
    try {
        Logger.i(TAG, "🎥 [配置页面] 开始加载可用摄像头列表")
        
        availableCameras = WebRTCManager.getAvailableCameras(this)
        
        if (availableCameras.isEmpty()) {
            // 处理没有摄像头的情况
            cameraInfoText.text = "没有找到可用摄像头\n请检查摄像头权限和硬件状态"
            saveButton.isEnabled = false
            return
        }
        
        // 创建显示名称列表
        val displayNames = availableCameras.map { it.displayName }
        
        cameraAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, displayNames)
        cameraAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        cameraSpinner.adapter = cameraAdapter
        
        // 选择当前使用的摄像头
        val currentCameraId = WebRTCManager.getCameraId()
        val currentIndex = availableCameras.indexOfFirst { it.id == currentCameraId }
        if (currentIndex >= 0) {
            cameraSpinner.setSelection(currentIndex)
        }
        
        Logger.i(TAG, "🎥 [配置页面] 成功加载 ${availableCameras.size} 个摄像头")
        
    } catch (e: Exception) {
        Logger.e(TAG, "🎥 [配置页面] 加载摄像头列表失败", e)
        Toast.makeText(this, "加载摄像头列表失败: ${e.message}", Toast.LENGTH_LONG).show()
    }
}
```

## 📱 **用户界面改进**

### **1. 主界面新增按钮**
- ✅ **摄像头配置按钮** - 直接进入摄像头配置页面
- ✅ **关于按钮** - 显示应用信息和功能特性

### **2. 配置页面布局**
- ✅ **摄像头选择区域** - 下拉选择器 + 刷新按钮
- ✅ **信息显示区域** - 滚动显示摄像头详细信息
- ✅ **操作按钮区域** - 保存配置 + 返回按钮

## 🎯 **功能特性**

### **1. 智能摄像头识别**
```kotlin
val displayName = when {
    isFrontFacing -> "前置摄像头 ($deviceName)"
    isBackFacing -> "后置摄像头 ($deviceName)"
    else -> "摄像头 $deviceName"
}
```

### **2. 硬件兼容性处理**
- ✅ **Camera2优先** - 优先使用Camera2 API
- ✅ **Camera1备用** - Camera2失败时自动降级到Camera1
- ✅ **错误容忍** - 单个摄像头失败不影响其他摄像头枚举

### **3. 循环切换机制**
```kotlin
val nextIndex = if (currentIndex >= 0) {
    (currentIndex + 1) % availableCameras.size  // 循环切换
} else {
    0 // 如果当前摄像头不在列表中，使用第一个
}
```

### **4. 用户友好提示**
```kotlin
Toast.makeText(this, "已切换到: ${nextCamera.displayName}", Toast.LENGTH_SHORT).show()
```

## 📊 **日志输出示例**

### **摄像头列表枚举：**
```
🎥 [摄像头列表] Camera2可用设备: 112
🎥 [摄像头列表] 总共找到 1 个摄像头
🎥 [摄像头列表] - 摄像头 112 (ID: 112, API: Camera2)
```

### **摄像头切换：**
```
🎥 [摄像头切换] 从 112 切换到 0 (后置摄像头 (0))
3秒延迟后开始重启服务进行摄像头切换
已切换到: 后置摄像头 (0)
```

### **配置页面：**
```
🎥 [配置页面] 开始加载可用摄像头列表
🎥 [配置页面] 成功加载 2 个摄像头
🎥 [配置页面] [0] 后置摄像头 (0) (ID: 0, API: Camera2)
🎥 [配置页面] [1] 前置摄像头 (1) (ID: 1, API: Camera2)
```

## 🚀 **使用方法**

### **1. 摄像头切换**
1. 点击主界面的"摄像头"按钮
2. 系统会自动切换到下一个可用摄像头
3. 显示切换结果提示

### **2. 摄像头配置**
1. 点击主界面的"摄像头配置"按钮
2. 在配置页面查看所有可用摄像头
3. 选择想要使用的摄像头
4. 点击"保存配置"

### **3. 实时刷新**
1. 在配置页面点击"刷新"按钮
2. 系统会重新扫描可用摄像头
3. 列表会实时更新

现在您的应用具备了完整的动态摄像头管理功能！
