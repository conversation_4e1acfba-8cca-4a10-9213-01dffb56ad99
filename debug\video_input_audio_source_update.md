# 新增"来自视频输入"音频源

## 🎯 更新概述

为Android WebRTC发送端应用新增了"来自视频输入"音频源，专门用于采集卡的音频数据，并将其设置为软件的默认音频来源。

## 🔧 主要变更

### 1. Constants.kt - 音频源配置
```kotlin
// 新增音频源选项
val AUDIO_SOURCES = mapOf(
    "video_input" to "来自视频输入",  // 新增：采集卡音频
    "microphone" to "麦克风",
    "system" to "系统声音",
    // ... 其他音频源
)

// 更新默认音频源
const val DEFAULT_AUDIO_SOURCE = "video_input"  // 首次安装默认来自视频输入
```

### 2. WebRTCClient.kt - 音频源处理
```kotlin
// 音频源映射
private fun getAudioSourceFromString(sourceType: String): Int {
    return when (sourceType) {
        "video_input" -> android.media.MediaRecorder.AudioSource.REMOTE_SUBMIX  // 采集卡音频
        "remote_submix" -> android.media.MediaRecorder.AudioSource.REMOTE_SUBMIX
        // ... 其他音频源
    }
}

// 音频约束配置
when (audioSourceType) {
    "video_input" -> {
        // 来自视频输入（采集卡音频数据）
        constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", "remote_submix"))
        
        // 设置采集卡音频相关参数
        constraints.optional.add(MediaConstraints.KeyValuePair("audioSource", "video_input"))
        constraints.optional.add(MediaConstraints.KeyValuePair("captureCard", "true"))
        
        // 禁用音频处理，保持原始音频质量
        constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "false"))
        constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "false"))
        constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "false"))
        constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "false"))
    }
}
```

### 3. 数据库表更新
```sql
-- 更新字段注释
app_audio_source VARCHAR(50) DEFAULT NULL COMMENT '音频源(video_input/microphone/system/both/none)',

-- 更新示例数据
'video_input',  -- 使用新的默认音频源
```

## 🎵 音频源特性

### "来自视频输入"音频源特点
1. **用途**: 专门用于采集卡的音频数据
2. **底层实现**: 使用`REMOTE_SUBMIX`音频源
3. **音频处理**: 禁用所有音频处理算法，保持原始质量
4. **适用场景**: 
   - HDMI采集卡音频
   - USB采集卡音频
   - 外部视频设备音频

### 音频质量优化
- ❌ 回声消除 (googEchoCancellation: false)
- ❌ 自动增益控制 (googAutoGainControl: false)
- ❌ 高通滤波器 (googHighpassFilter: false)
- ❌ 噪声抑制 (googNoiseSuppression: false)
- ❌ 音频镜像 (googAudioMirroring: false)
- ❌ 网络自适应 (googAudioNetworkAdaptorConfig: false)

## 📊 音频源优先级

### 新的音频源列表（按推荐顺序）
1. **video_input** - 来自视频输入 ⭐ **默认选择**
2. **remote_submix** - 远程混音
3. **microphone** - 麦克风
4. **system** - 系统声音
5. **voice_communication** - 语音通信
6. **both** - 麦克风和系统声音
7. **none** - 无音频

### 使用场景建议
- 🎮 **游戏采集**: video_input（采集卡音频）
- 🎙️ **直播解说**: microphone（麦克风）
- 🔊 **系统录制**: remote_submix（系统音频）
- 📞 **语音通话**: voice_communication（通话优化）

## 🚀 部署和测试

### 1. 编译应用
```bash
./gradlew clean
./gradlew assembleDebug
```

### 2. 安装测试
- 全新安装应用
- 检查默认音频源是否为"来自视频输入"
- 测试采集卡音频是否正常

### 3. 验证配置
在设置页面确认：
- 音频源列表包含"来自视频输入"选项
- 默认选中"来自视频输入"
- 切换音频源功能正常

### 4. 数据库验证
```sql
-- 检查新安装设备的音频源配置
SELECT sender_id, app_audio_source, app_version 
FROM fa_sender_device_info 
WHERE app_audio_source = 'video_input';
```

## 🔧 技术实现细节

### 音频源映射关系
```
用户选择          Android音频源                说明
video_input   -> REMOTE_SUBMIX              采集卡音频数据
remote_submix -> REMOTE_SUBMIX              系统内部音频
microphone    -> MIC                        麦克风输入
voice_comm    -> VOICE_COMMUNICATION        语音通信优化
```

### WebRTC约束配置
```javascript
// video_input音频源的WebRTC约束
{
    mandatory: [
        { googAudioSource: "remote_submix" },
        { googEchoCancellation: "false" },
        { googAutoGainControl: "false" },
        { googHighpassFilter: "false" },
        { googNoiseSuppression: "false" }
    ],
    optional: [
        { audioSource: "video_input" },
        { captureCard: "true" },
        { googAudioMirroring: "false" },
        { googAudioNetworkAdaptorConfig: "false" }
    ]
}
```

## ⚠️ 注意事项

1. **硬件要求**: 需要支持音频采集的设备
2. **权限需求**: 需要录音权限
3. **兼容性**: 基于REMOTE_SUBMIX实现，兼容性良好
4. **音质保证**: 禁用音频处理算法，保持原始质量
5. **首次安装**: 新安装的应用将默认使用此音频源

## 🎯 预期效果

更新完成后：
- ✅ 新安装应用默认使用"来自视频输入"音频源
- ✅ 采集卡音频质量更好（无音频处理干扰）
- ✅ 设置界面显示新的音频源选项
- ✅ 数据库正确记录音频源配置
- ✅ 日志显示正确的音频源类型

现在"来自视频输入"已成为默认音频源，专门优化了采集卡音频的处理！
