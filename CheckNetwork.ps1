# CheckNetwork.ps1 - 检查网络信息的PowerShell脚本

Write-Host "网络信息检查工具" -ForegroundColor Cyan
Write-Host ""

# 获取本地IP地址
Write-Host "正在获取本地IP地址..." -ForegroundColor Yellow
$localIPs = Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.InterfaceAlias -notmatch 'Loopback' -and $_.IPAddress -notmatch '^169\.254\.' }
Write-Host "本地IP地址:"
foreach ($ip in $localIPs) {
    Write-Host "  $($ip.IPAddress) ($($ip.InterfaceAlias))"
}

# 获取默认网关
Write-Host "`n正在获取默认网关..." -ForegroundColor Yellow
$defaultGateways = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Select-Object -ExpandProperty NextHop
Write-Host "默认网关:"
foreach ($gateway in $defaultGateways) {
    Write-Host "  $gateway"
}

# 获取DNS服务器
Write-Host "`n正在获取DNS服务器..." -ForegroundColor Yellow
$dnsServers = Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object { $_.ServerAddresses -ne $null -and $_.ServerAddresses.Count -gt 0 }
Write-Host "DNS服务器:"
foreach ($dns in $dnsServers) {
    if ($dns.ServerAddresses.Count -gt 0) {
        Write-Host "  $($dns.InterfaceAlias): $($dns.ServerAddresses -join ', ')"
    }
}

# 测试网络连接
Write-Host "`n正在测试网络连接..." -ForegroundColor Yellow
$testConnection = Test-Connection -ComputerName "*******" -Count 2 -Quiet
if ($testConnection) {
    Write-Host "Internet连接: 正常" -ForegroundColor Green
} else {
    Write-Host "Internet连接: 失败" -ForegroundColor Red
}

# 尝试获取公网IP
Write-Host "`n正在获取公网IP..." -ForegroundColor Yellow
try {
    $publicIP = (Invoke-WebRequest -Uri "https://api.ipify.org" -UseBasicParsing).Content
    Write-Host "公网IP地址: $publicIP"
    
    # 检查是否是CGNAT地址
    if ($publicIP -match "^100\.64\.") {
        Write-Host "`n注意: 您的公网IP属于CGNAT地址范围 (**********/10)" -ForegroundColor Yellow
        Write-Host "这表明您的ISP使用了运营商级NAT，您没有真正的公网IP" -ForegroundColor Yellow
        Write-Host "在这种情况下，外部互联网无法直接访问您的服务" -ForegroundColor Yellow
    }
} catch {
    Write-Host "无法获取公网IP: $_" -ForegroundColor Red
}

# 测试STUN服务器连接
Write-Host "`n正在测试STUN服务器连接..." -ForegroundColor Yellow
$stunServer = "stun.l.google.com"
$stunPort = 19302

try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $result = $tcpClient.BeginConnect($stunServer, $stunPort, $null, $null)
    $success = $result.AsyncWaitHandle.WaitOne(2000, $false)
    
    if ($success) {
        Write-Host "STUN服务器连接: 成功" -ForegroundColor Green
        $tcpClient.EndConnect($result)
    } else {
        Write-Host "STUN服务器连接: 失败" -ForegroundColor Red
    }
    
    $tcpClient.Close()
} catch {
    Write-Host "STUN服务器连接测试失败: $_" -ForegroundColor Red
}

# 总结
Write-Host "`n网络状况总结:" -ForegroundColor Cyan
if ($publicIP) {
    $localIP = $localIPs | Select-Object -First 1 -ExpandProperty IPAddress
    if ($localIP -ne $publicIP) {
        Write-Host "您的设备在NAT后面" -ForegroundColor Yellow
        Write-Host "如果要实现外部访问，您需要使用端口映射或内网穿透技术" -ForegroundColor Yellow
    } else {
        Write-Host "您的设备有公网IP，可以直接被外部访问" -ForegroundColor Green
    }
} else {
    Write-Host "无法确定您的网络状况" -ForegroundColor Red
}

Write-Host "`n检查完成!" -ForegroundColor Cyan
