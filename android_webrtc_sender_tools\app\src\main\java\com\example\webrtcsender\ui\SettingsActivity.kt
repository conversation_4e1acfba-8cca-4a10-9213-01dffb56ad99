package com.example.webrtcsender.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.MenuItem
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.CheckBox
import android.widget.EditText
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.example.webrtcsender.R
import com.example.webrtcsender.service.WebRTCSenderService
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager

class SettingsActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "SettingsActivity"
        private const val SCREEN_CAPTURE_REQUEST_CODE = 1001
    }

    // 媒体投影
    private lateinit var mediaProjectionManager: MediaProjectionManager

    // UI组件
    private lateinit var cameraSpinner: Spinner
    private lateinit var resolutionSpinner: Spinner
    private lateinit var bitrateInput: EditText
    private lateinit var framerateSpinner: Spinner
    private lateinit var codecSpinner: Spinner
    private lateinit var audioSourceSpinner: Spinner
    private lateinit var audioChannelSpinner: Spinner
    private lateinit var audioSampleRateSpinner: Spinner
    private lateinit var audioEncodingSpinner: Spinner
    private lateinit var autoStartGameCheckBox: CheckBox
    private lateinit var selectGameButton: Button
    private lateinit var selectedGameText: TextView
    private lateinit var saveButton: Button

    // 游戏选择相关
    private var selectedGamePackage: String = ""
    private var selectedGameName: String = ""

    // 摄像头配置
    private var availableCameras: List<WebRTCManager.CameraInfo> = emptyList()

    // 应用选择器启动器
    private val appSelectorLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.let { data ->
                selectedGamePackage = data.getStringExtra(AppSelectorActivity.EXTRA_SELECTED_PACKAGE) ?: ""
                selectedGameName = data.getStringExtra(AppSelectorActivity.EXTRA_SELECTED_APP_NAME) ?: ""
                updateSelectedGameDisplay()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // 初始化UI组件
        cameraSpinner = findViewById(R.id.cameraSpinner)
        resolutionSpinner = findViewById(R.id.resolutionSpinner)
        bitrateInput = findViewById(R.id.bitrateInput)
        framerateSpinner = findViewById(R.id.framerateSpinner)
        codecSpinner = findViewById(R.id.codecSpinner)
        audioSourceSpinner = findViewById(R.id.audioSourceSpinner)
        audioChannelSpinner = findViewById(R.id.audioChannelSpinner)
        audioSampleRateSpinner = findViewById(R.id.audioSampleRateSpinner)
        audioEncodingSpinner = findViewById(R.id.audioEncodingSpinner)
        autoStartGameCheckBox = findViewById(R.id.autoStartGameCheckBox)
        selectGameButton = findViewById(R.id.selectGameButton)
        selectedGameText = findViewById(R.id.selectedGameText)
        saveButton = findViewById(R.id.saveButton)

        // 设置标题
        title = "设置"

        // 显示返回按钮
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 初始化媒体投影管理器
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager

        // 初始化UI
        initUI()

        // 加载当前设置
        loadSettings()

        // 设置游戏选择按钮点击事件
        selectGameButton.setOnClickListener {
            val intent = Intent(this, AppSelectorActivity::class.java)
            appSelectorLauncher.launch(intent)
        }

        // 设置保存按钮点击事件
        saveButton.setOnClickListener {
            saveSettings()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                // 返回按钮
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 初始化UI
     */
    private fun initUI() {
        // 动态获取可用摄像头并设置选项
        initializeCameraOptions()

        // 设置分辨率选项
        val resolutionOptions = arrayOf(
            "360p (640x360)",
            "480p (640x480)",
            "576p (1024x576)",
            "720p (1280x720)",
            "1080p (1920x1080)",
            "1440p (2560x1440)",
            "4K (3840x2160)"
        )
        val resolutionAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, resolutionOptions)
        resolutionSpinner.adapter = resolutionAdapter

        // 设置帧率选项
        val framerateOptions = Constants.VIDEO_FRAMERATES.map { "$it fps" }.toTypedArray()
        val framerateAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, framerateOptions)
        framerateSpinner.adapter = framerateAdapter

        // 设置编码格式选项
        val codecOptions = arrayOf("VP8", "VP9", "H264")
        val codecAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, codecOptions)
        codecSpinner.adapter = codecAdapter

        // 设置音频来源选项
        val audioSourceOptions = Constants.AUDIO_SOURCES.values.toTypedArray()
        val audioSourceAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, audioSourceOptions)
        audioSourceSpinner.adapter = audioSourceAdapter

        // 设置音频通道选项
        val audioChannelOptions = Constants.AUDIO_CHANNELS.values.toTypedArray()
        val audioChannelAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, audioChannelOptions)
        audioChannelSpinner.adapter = audioChannelAdapter

        // 设置音频采样率选项
        val audioSampleRateOptions = Constants.AUDIO_SAMPLE_RATES.map { "$it Hz" }.toTypedArray()
        val audioSampleRateAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, audioSampleRateOptions)
        audioSampleRateSpinner.adapter = audioSampleRateAdapter

        // 设置音频编码选项
        val audioEncodingOptions = Constants.AUDIO_ENCODINGS.values.toTypedArray()
        val audioEncodingAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, audioEncodingOptions)
        audioEncodingSpinner.adapter = audioEncodingAdapter
    }

    /**
     * 动态初始化摄像头选项
     */
    private fun initializeCameraOptions() {
        try {
            Logger.i(TAG, "🎥 [摄像头配置] 开始动态检测可用摄像头...")

            // 获取可用摄像头列表
            val availableCameras = WebRTCManager.getAvailableCameras(this)

            if (availableCameras.isEmpty()) {
                Logger.w(TAG, "🎥 [摄像头配置] 没有检测到可用摄像头，使用默认选项")
                // 使用默认选项
                val defaultOptions = arrayOf("后置摄像头", "前置摄像头")
                val defaultAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, defaultOptions)
                cameraSpinner.adapter = defaultAdapter
                return
            }

            // 创建摄像头显示名称列表
            val cameraDisplayNames = availableCameras.map { it.displayName }
            Logger.i(TAG, "🎥 [摄像头配置] 检测到 ${availableCameras.size} 个摄像头: ${cameraDisplayNames.joinToString()}")

            // 设置摄像头适配器
            val cameraAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, cameraDisplayNames)
            cameraSpinner.adapter = cameraAdapter

            // 保存摄像头信息供后续使用
            this.availableCameras = availableCameras

            Logger.i(TAG, "🎥 [摄像头配置] ✅ 摄像头选项初始化完成")

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头配置] ❌ 初始化摄像头选项失败", e)
            // 出错时使用默认选项
            val defaultOptions = arrayOf("后置摄像头", "前置摄像头")
            val defaultAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_dropdown_item, defaultOptions)
            cameraSpinner.adapter = defaultAdapter
        }
    }

    /**
     * 加载当前设置
     */
    private fun loadSettings() {
        try {
            // 加载摄像头设置 - 智能匹配摄像头ID或索引
            val cameraId = WebRTCManager.getCameraId()
            var selectedCameraIndex = 0

            if (availableCameras.isNotEmpty()) {
                // 首先尝试通过摄像头ID匹配
                val cameraIndexById = availableCameras.indexOfFirst { it.id == cameraId }
                if (cameraIndexById != -1) {
                    selectedCameraIndex = cameraIndexById
                    Logger.i(TAG, "🎥 [摄像头配置] 通过ID匹配到摄像头: ${availableCameras[selectedCameraIndex].displayName}")
                } else {
                    // 如果ID匹配失败，尝试索引匹配（兼容性）
                    val cameraIndexByNumber = cameraId.toIntOrNull()
                    if (cameraIndexByNumber != null && cameraIndexByNumber >= 0 && cameraIndexByNumber < availableCameras.size) {
                        selectedCameraIndex = cameraIndexByNumber
                        Logger.i(TAG, "🎥 [摄像头配置] 通过索引匹配到摄像头: ${availableCameras[selectedCameraIndex].displayName}")
                    } else {
                        Logger.w(TAG, "🎥 [摄像头配置] 无法匹配摄像头ID: $cameraId，使用默认摄像头")
                        selectedCameraIndex = 0
                    }
                }
            } else {
                // 没有可用摄像头信息时，使用索引模式
                selectedCameraIndex = cameraId.toIntOrNull() ?: 0
            }

            val safeCameraIndex = selectedCameraIndex.coerceIn(0, cameraSpinner.adapter.count - 1)
            cameraSpinner.setSelection(safeCameraIndex)

            if (selectedCameraIndex != safeCameraIndex) {
                Logger.w(TAG, "摄像头索引超出范围，已调整: $selectedCameraIndex -> $safeCameraIndex")
            }

            // 加载分辨率设置 - 安全地设置选择项
            val resolution = WebRTCManager.getVideoResolution()
            val resolutionIndex = when (resolution) {
                "360p" -> 0
                "480p" -> 1
                "576p" -> 2
                "720p" -> 3
                "1080p" -> 4
                "1440p" -> 5
                "4K" -> 6
                else -> 2 // 默认720p
            }
            val safeResolutionIndex = resolutionIndex.coerceIn(0, resolutionSpinner.adapter.count - 1)
            resolutionSpinner.setSelection(safeResolutionIndex)

            if (resolutionIndex != safeResolutionIndex) {
                Logger.w(TAG, "分辨率索引超出范围，已调整: $resolutionIndex -> $safeResolutionIndex")
            }

            // 加载比特率设置
            bitrateInput.setText(WebRTCManager.getVideoBitrate().toString())

            // 加载帧率设置 - 安全地设置选择项
            val framerate = WebRTCManager.getVideoFramerate()
            val framerateIndex = Constants.VIDEO_FRAMERATES.indexOf(framerate)
            val finalFramerateIndex = if (framerateIndex != -1) {
                framerateIndex
            } else {
                // 如果找不到匹配的帧率，选择默认帧率
                val defaultFramerateIndex = Constants.VIDEO_FRAMERATES.indexOf(Constants.DEFAULT_VIDEO_FRAMERATE)
                if (defaultFramerateIndex != -1) defaultFramerateIndex else 0
            }
            val safeFramerateIndex = finalFramerateIndex.coerceIn(0, framerateSpinner.adapter.count - 1)
            framerateSpinner.setSelection(safeFramerateIndex)

            if (finalFramerateIndex != safeFramerateIndex) {
                Logger.w(TAG, "帧率索引超出范围，已调整: $finalFramerateIndex -> $safeFramerateIndex")
            }

            // 加载编码格式设置 - 安全地设置选择项
            val codec = WebRTCManager.getVideoCodec()
            val codecIndex = when (codec) {
                "VP8" -> 0
                "VP9" -> 1
                "H264" -> 2
                else -> 2 // 默认H264
            }
            val safeCodecIndex = codecIndex.coerceIn(0, codecSpinner.adapter.count - 1)
            codecSpinner.setSelection(safeCodecIndex)

            if (codecIndex != safeCodecIndex) {
                Logger.w(TAG, "编码格式索引超出范围，已调整: $codecIndex -> $safeCodecIndex")
            }

            // 加载音频来源设置 - 安全地设置选择项
            val audioSource = WebRTCManager.getAudioSource()
            val audioSourceIndex = Constants.AUDIO_SOURCES.entries.indexOfFirst { it.key == audioSource }
            val finalAudioSourceIndex = if (audioSourceIndex != -1) {
                audioSourceIndex
            } else {
                // 如果找不到匹配的音频来源，选择默认音频来源
                val defaultAudioSourceIndex = Constants.AUDIO_SOURCES.entries.indexOfFirst { it.key == Constants.DEFAULT_AUDIO_SOURCE }
                if (defaultAudioSourceIndex != -1) defaultAudioSourceIndex else 0
            }
            val safeAudioSourceIndex = finalAudioSourceIndex.coerceIn(0, audioSourceSpinner.adapter.count - 1)
            audioSourceSpinner.setSelection(safeAudioSourceIndex)

            if (finalAudioSourceIndex != safeAudioSourceIndex) {
                Logger.w(TAG, "音频来源索引超出范围，已调整: $finalAudioSourceIndex -> $safeAudioSourceIndex")
            }

            // 加载音频通道设置 - 安全地设置选择项
            val audioChannel = WebRTCManager.getAudioChannel()
            val audioChannelIndex = Constants.AUDIO_CHANNELS.entries.indexOfFirst { it.key == audioChannel }
            val finalAudioChannelIndex = if (audioChannelIndex != -1) {
                audioChannelIndex
            } else {
                // 如果找不到匹配的音频通道，选择默认音频通道
                val defaultAudioChannelIndex = Constants.AUDIO_CHANNELS.entries.indexOfFirst { it.key == "stereo" }
                if (defaultAudioChannelIndex != -1) defaultAudioChannelIndex else 0
            }
            val safeAudioChannelIndex = finalAudioChannelIndex.coerceIn(0, audioChannelSpinner.adapter.count - 1)
            audioChannelSpinner.setSelection(safeAudioChannelIndex)

            if (finalAudioChannelIndex != safeAudioChannelIndex) {
                Logger.w(TAG, "音频通道索引超出范围，已调整: $finalAudioChannelIndex -> $safeAudioChannelIndex")
            }

            // 加载音频采样率设置 - 安全地设置选择项
            val audioSampleRate = WebRTCManager.getAudioSampleRate()
            val audioSampleRateIndex = Constants.AUDIO_SAMPLE_RATES.indexOf(audioSampleRate)
            val finalAudioSampleRateIndex = if (audioSampleRateIndex != -1) {
                audioSampleRateIndex
            } else {
                // 如果找不到匹配的音频采样率，选择默认音频采样率
                val defaultAudioSampleRateIndex = Constants.AUDIO_SAMPLE_RATES.indexOf(48000)
                if (defaultAudioSampleRateIndex != -1) defaultAudioSampleRateIndex else 0
            }
            val safeAudioSampleRateIndex = finalAudioSampleRateIndex.coerceIn(0, audioSampleRateSpinner.adapter.count - 1)
            audioSampleRateSpinner.setSelection(safeAudioSampleRateIndex)

            if (finalAudioSampleRateIndex != safeAudioSampleRateIndex) {
                Logger.w(TAG, "音频采样率索引超出范围，已调整: $finalAudioSampleRateIndex -> $safeAudioSampleRateIndex")
            }

            // 加载音频编码设置 - 安全地设置选择项
            val audioEncoding = WebRTCManager.getAudioEncoding()
            val audioEncodingIndex = Constants.AUDIO_ENCODINGS.entries.indexOfFirst { it.key == audioEncoding }
            val finalAudioEncodingIndex = if (audioEncodingIndex != -1) {
                audioEncodingIndex
            } else {
                // 如果找不到匹配的音频编码，选择默认音频编码
                val defaultAudioEncodingIndex = Constants.AUDIO_ENCODINGS.entries.indexOfFirst { it.key == "pcm" }
                if (defaultAudioEncodingIndex != -1) defaultAudioEncodingIndex else 0
            }
            val safeAudioEncodingIndex = finalAudioEncodingIndex.coerceIn(0, audioEncodingSpinner.adapter.count - 1)
            audioEncodingSpinner.setSelection(safeAudioEncodingIndex)

            if (finalAudioEncodingIndex != safeAudioEncodingIndex) {
                Logger.w(TAG, "音频编码索引超出范围，已调整: $finalAudioEncodingIndex -> $safeAudioEncodingIndex")
            }

            // 加载游戏设置
            loadGameSettings()
        } catch (e: Exception) {
            Logger.e(TAG, "加载设置失败", e)
            Toast.makeText(this, "加载设置失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 保存设置
     */
    private fun saveSettings() {
        try {
            // 保存摄像头设置 - 使用实际的摄像头ID
            val cameraIndex = cameraSpinner.selectedItemPosition
            if (availableCameras.isNotEmpty() && cameraIndex >= 0 && cameraIndex < availableCameras.size) {
                // 使用实际的摄像头ID
                val selectedCamera = availableCameras[cameraIndex]
                WebRTCManager.setCameraId(selectedCamera.id)
                Logger.i(TAG, "🎥 [摄像头配置] 保存摄像头设置: ${selectedCamera.displayName} (ID: ${selectedCamera.id})")
            } else {
                // 回退到索引模式（兼容性）
                WebRTCManager.setCameraId(cameraIndex.toString())
                Logger.w(TAG, "🎥 [摄像头配置] 使用索引模式保存摄像头: $cameraIndex")
            }

            // 保存分辨率设置
            val resolutionIndex = resolutionSpinner.selectedItemPosition
            val resolution = when (resolutionIndex) {
                0 -> "360p"
                1 -> "480p"
                2 -> "576p"
                3 -> "720p"
                4 -> "1080p"
                5 -> "1440p"
                6 -> "4K"
                else -> "720p" // 默认720p
            }
            WebRTCManager.setVideoResolution(resolution)

            // 保存比特率设置
            val bitrateStr = bitrateInput.text.toString()
            if (bitrateStr.isNotEmpty()) {
                val bitrate = bitrateStr.toIntOrNull() ?: Constants.DEFAULT_VIDEO_BITRATE
                WebRTCManager.setVideoBitrate(bitrate)
            }

            // 保存帧率设置
            val framerateIndex = framerateSpinner.selectedItemPosition
            if (framerateIndex >= 0 && framerateIndex < Constants.VIDEO_FRAMERATES.size) {
                val framerate = Constants.VIDEO_FRAMERATES[framerateIndex]
                WebRTCManager.setVideoFramerate(framerate)
            }

            // 保存编码格式设置
            val codecIndex = codecSpinner.selectedItemPosition
            val codec = when (codecIndex) {
                0 -> "VP8"
                1 -> "VP9"
                2 -> "H264"
                else -> "H264" // 默认VP8
            }
            WebRTCManager.setVideoCodec(codec)

            // 保存音频来源设置
            val audioSourceIndex = audioSourceSpinner.selectedItemPosition
            if (audioSourceIndex >= 0 && audioSourceIndex < Constants.AUDIO_SOURCES.size) {
                val audioSource = Constants.AUDIO_SOURCES.keys.toList()[audioSourceIndex]
                WebRTCManager.setAudioSource(audioSource)
            }

            // 保存音频通道设置
            val audioChannelIndex = audioChannelSpinner.selectedItemPosition
            if (audioChannelIndex >= 0 && audioChannelIndex < Constants.AUDIO_CHANNELS.size) {
                val audioChannel = Constants.AUDIO_CHANNELS.keys.toList()[audioChannelIndex]
                WebRTCManager.setAudioChannel(audioChannel)
            }

            // 保存音频采样率设置
            val audioSampleRateIndex = audioSampleRateSpinner.selectedItemPosition
            if (audioSampleRateIndex >= 0 && audioSampleRateIndex < Constants.AUDIO_SAMPLE_RATES.size) {
                val audioSampleRate = Constants.AUDIO_SAMPLE_RATES[audioSampleRateIndex]
                WebRTCManager.setAudioSampleRate(audioSampleRate)
            }

            // 保存音频编码设置
            val audioEncodingIndex = audioEncodingSpinner.selectedItemPosition
            if (audioEncodingIndex >= 0 && audioEncodingIndex < Constants.AUDIO_ENCODINGS.size) {
                val audioEncoding = Constants.AUDIO_ENCODINGS.keys.toList()[audioEncodingIndex]
                WebRTCManager.setAudioEncoding(audioEncoding)
            }

            // 保存游戏设置
            saveGameSettings()

            Toast.makeText(this, "设置已保存，正在重启服务...", Toast.LENGTH_SHORT).show()

            // 重启服务以应用新设置
            restartServiceWithNewSettings()

            finish()
        } catch (e: Exception) {
            Logger.e(TAG, "保存设置失败", e)
            Toast.makeText(this, "保存设置失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 重启服务以应用新设置
     */
    private fun restartServiceWithNewSettings() {
        try {
            Logger.i(TAG, "开始重启服务以应用新设置...")

            // 先完全停止视频源，释放摄像头资源
            Logger.i(TAG, "停止视频源...")
            WebRTCManager.stopVideoSource(this)

            // 停止信令连接
            if (WebRTCManager.isConnected()) {
                Logger.i(TAG, "断开信令服务器连接...")
                WebRTCManager.disconnectFromSignalingServerWithExitMessage()
            }

            // 停止后台服务
            val stopIntent = Intent(this, WebRTCSenderService::class.java).apply {
                action = WebRTCSenderService.ACTION_STOP_SERVICE
            }
            startService(stopIntent)

            Logger.i(TAG, "服务停止完成，等待重启...")

            // 延迟重新启动服务，确保资源完全释放
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    Logger.i(TAG, "开始重新启动服务...")

                    // 连接信令服务器
                    WebRTCManager.connectToSignalingServer()

                    // 启动后台服务
                    val startIntent = Intent(this, WebRTCSenderService::class.java).apply {
                        action = WebRTCSenderService.ACTION_START_SERVICE
                    }
                    ContextCompat.startForegroundService(this, startIntent)

                    // 延迟更长时间启动视频源，确保摄像头资源完全释放
                    Handler(Looper.getMainLooper()).postDelayed({
                        val videoSource = WebRTCManager.getVideoSourceType()
                        Logger.i(TAG, "重新启动视频源: $videoSource")

                        when (videoSource) {
                            "camera" -> {
                                Logger.i(TAG, "重启后自动启动摄像头")
                                // 使用自动启动方法，避免切换逻辑
                                autoStartCameraAfterRestart()
                            }
                            "screen" -> {
                                Logger.i(TAG, "重启后自动启动屏幕录制")
                                requestScreenCapture()
                            }
                            "hdmiin" -> {
                                Logger.i(TAG, "重启后自动启动HDMI输入")
                                startHdmiCapture()
                            }
                            "usbcapture" -> {
                                Logger.i(TAG, "重启后自动启动USB采集卡")
                                startUsbCapture()
                            }
                            else -> {
                                Logger.i(TAG, "重启后默认启动屏幕录制")
                                requestScreenCapture()
                            }
                        }
                    }, 2000) // 增加到2秒，确保摄像头资源完全释放

                } catch (e: Exception) {
                    Logger.e(TAG, "重启服务失败", e)
                    Toast.makeText(this, "重启服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }, 2000) // 增加到2秒，确保服务完全停止

        } catch (e: Exception) {
            Logger.e(TAG, "重启服务失败", e)
            Toast.makeText(this, "重启服务失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 加载游戏设置
     */
    private fun loadGameSettings() {
        try {
            val preferences = WebRTCManager.getPreferences()

            // 加载自动启动游戏设置
            val autoStartGame = preferences.getBoolean(Constants.PREF_AUTO_START_GAME, Constants.DEFAULT_AUTO_START_GAME)
            autoStartGameCheckBox.isChecked = autoStartGame

            // 加载选择的游戏
            selectedGamePackage = preferences.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""

            // 如果有选择的游戏，获取应用名称
            if (selectedGamePackage.isNotEmpty()) {
                try {
                    val packageManager = packageManager
                    val appInfo = packageManager.getApplicationInfo(selectedGamePackage, 0)
                    selectedGameName = packageManager.getApplicationLabel(appInfo).toString()
                } catch (e: Exception) {
                    Logger.w(TAG, "无法获取应用名称: $selectedGamePackage - ${e.message}")
                    selectedGameName = selectedGamePackage
                }
            }

            updateSelectedGameDisplay()

        } catch (e: Exception) {
            Logger.e(TAG, "加载游戏设置失败", e)
        }
    }

    /**
     * 保存游戏设置
     */
    private fun saveGameSettings() {
        try {
            val preferences = WebRTCManager.getPreferences()
            val editor = preferences.edit()

            // 保存自动启动游戏设置
            editor.putBoolean(Constants.PREF_AUTO_START_GAME, autoStartGameCheckBox.isChecked)

            // 保存选择的游戏包名
            editor.putString(Constants.PREF_AUTO_START_GAME_PACKAGE, selectedGamePackage)

            editor.apply()

            Logger.i(TAG, "游戏设置已保存: autoStart=${autoStartGameCheckBox.isChecked}, package=$selectedGamePackage")

        } catch (e: Exception) {
            Logger.e(TAG, "保存游戏设置失败", e)
        }
    }

    /**
     * 更新选择的游戏显示
     */
    private fun updateSelectedGameDisplay() {
        if (selectedGameName.isNotEmpty()) {
            selectedGameText.text = "已选择: $selectedGameName"
        } else {
            selectedGameText.text = "未选择游戏"
        }
    }

    /**
     * 重启后自动启动摄像头（确保资源完全释放后启动）
     */
    private fun autoStartCameraAfterRestart() {
        try {
            Logger.i(TAG, "重启后自动启动摄像头")

            // 设置视频源类型为摄像头
            WebRTCManager.setVideoSourceType("camera")

            // 启动视频源
            WebRTCManager.startVideoSource(this)

            Logger.i(TAG, "重启后摄像头启动完成")
        } catch (e: Exception) {
            Logger.e(TAG, "重启后启动摄像头失败", e)
            Toast.makeText(this, "重启后启动摄像头失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 请求屏幕捕获权限
     */
    private fun requestScreenCapture() {
        try {
            // 设置视频源类型
            WebRTCManager.setVideoSourceType("screen")

            // 请求屏幕捕获权限
            val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
            startActivityForResult(captureIntent, SCREEN_CAPTURE_REQUEST_CODE)
        } catch (e: Exception) {
            Logger.e(TAG, "请求屏幕捕获权限失败", e)
            Toast.makeText(this, "请求屏幕捕获权限失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 处理屏幕捕获结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == SCREEN_CAPTURE_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                // 创建媒体投影
                val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)

                // 设置媒体投影
                WebRTCManager.setMediaProjection(mediaProjection)

                // 设置媒体投影数据
                WebRTCManager.setMediaProjectionData(data)

                // 启动屏幕捕获
                startScreenCapture()
            } else {
                Toast.makeText(this, "用户取消了屏幕捕获", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 启动屏幕捕获
     */
    private fun startScreenCapture() {
        val intent = Intent(this, WebRTCSenderService::class.java).apply {
            action = WebRTCSenderService.ACTION_START_SCREEN_CAPTURE
        }

        startService(intent)
    }

    /**
     * 启动HDMI输入捕获
     */
    private fun startHdmiCapture() {
        try {
            Logger.i(TAG, "SettingsActivity: 启动HDMI输入")

            // 记录用户HDMI输入选择
            recordVideoSourceChoice("hdmiin")

            // 设置视频源类型
            WebRTCManager.setVideoSourceType("hdmiin")

            // 启动HDMI输入
            WebRTCManager.startVideoSource(this)

            Logger.i(TAG, "SettingsActivity: HDMI输入启动完成")
        } catch (e: Exception) {
            Logger.e(TAG, "SettingsActivity: 启动HDMI输入失败", e)
        }
    }

    /**
     * 启动USB采集卡捕获
     */
    private fun startUsbCapture() {
        try {
            Logger.i(TAG, "SettingsActivity: 启动USB采集卡")

            // 记录用户USB采集卡选择
            recordVideoSourceChoice("usbcapture")

            // 设置视频源类型
            WebRTCManager.setVideoSourceType("usbcapture")

            // 启动USB采集卡
            WebRTCManager.startVideoSource(this)

            Logger.i(TAG, "SettingsActivity: USB采集卡启动完成")
        } catch (e: Exception) {
            Logger.e(TAG, "SettingsActivity: 启动USB采集卡失败", e)
        }
    }

    /**
     * 记录用户视频源选择
     */
    private fun recordVideoSourceChoice(videoSource: String) {
        val prefs = getSharedPreferences("webrtc_sender_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString(Constants.PREF_VIDEO_SOURCE, videoSource)
            .putBoolean(Constants.PREF_HAS_MANUAL_OPERATION, true)
            .apply()

        Logger.i(TAG, "已记录用户视频源选择: $videoSource")
    }
}
