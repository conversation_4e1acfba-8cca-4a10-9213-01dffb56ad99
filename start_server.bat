@echo off
chcp 65001 > nul
echo ===================================
echo    WebRTC视频流服务器启动脚本
echo ===================================
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请安装Python 3.7或更高版本
    goto :end
)

REM 检查依赖
echo 正在检查依赖...
python -c "import websockets" > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装websockets...
    pip install websockets
)

REM 获取本地IP地址
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set LOCAL_IP=%%a
    goto :found_ip
)
:found_ip
set LOCAL_IP=%LOCAL_IP:~1%

REM 创建web目录（如果不存在）
if not exist web\img mkdir web\img
if not exist web\css mkdir web\css
if not exist web\js mkdir web\js

REM 启动服务器
echo.
echo 正在启动WebRTC视频流服务器...
echo.
echo 服务器信息:
echo - 信令服务器: ws://%LOCAL_IP%:8765
echo - Web界面: http://%LOCAL_IP%:8080
echo.
echo 您可以通过以下方式访问:
echo - 本机: http://localhost:8080
echo - 局域网: http://%LOCAL_IP%:8080
echo.
echo 按Ctrl+C停止服务器
echo.

python enhanced_signaling_server.py --ws-port 8765 --http-port 8080 --web-dir ./web

:end
pause
