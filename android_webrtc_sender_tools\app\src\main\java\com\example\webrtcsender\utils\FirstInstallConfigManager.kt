package com.example.webrtcsender.utils

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.ApplicationInfo

/**
 * 首次安装配置管理器
 * 负责设置首次安装时的默认配置
 */
object FirstInstallConfigManager {
    private const val TAG = "FirstInstallConfigManager"
    
    /**
     * 检查并设置首次安装的默认配置
     */
    fun setupFirstInstallDefaults(context: Context, preferences: SharedPreferences) {
        val isFirstInstall = preferences.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)
        
        if (isFirstInstall) {
            Logger.i(TAG, "检测到首次安装，设置默认配置")
            
            val editor = preferences.edit()
            
            // 设置默认视频参数
            editor.putString(Constants.PREF_VIDEO_SOURCE, Constants.DEFAULT_VIDEO_SOURCE) // 摄像头
            editor.putString(Constants.PREF_VIDEO_RESOLUTION, Constants.DEFAULT_VIDEO_RESOLUTION) // 576p
            editor.putInt(Constants.PREF_VIDEO_BITRATE, Constants.DEFAULT_VIDEO_BITRATE) // 3000
            editor.putInt(Constants.PREF_VIDEO_FRAMERATE, Constants.DEFAULT_VIDEO_FRAMERATE) // 60
            editor.putString(Constants.PREF_AUDIO_SOURCE, Constants.DEFAULT_AUDIO_SOURCE) // 远程混音
            
            // 设置默认日志显示为关闭
            editor.putBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
            
            // 检查并设置默认游戏
            setupDefaultGame(context, editor)

            // 注意：不在这里清除首次安装标志，而是在实际启动服务时清除
            // 这样MainActivity可以检测到首次安装并自动启动服务和摄像头

            editor.apply()

            // 设置Logger的显示状态
            Logger.setDisplayEnabled(Constants.DEFAULT_LOG_DISPLAY_ENABLED)

            Logger.i(TAG, "🎉 首次安装默认配置设置完成，等待自动启动服务")
        } else {
            // 非首次安装，加载日志显示设置
            val logDisplayEnabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
            Logger.setDisplayEnabled(logDisplayEnabled)
            Logger.i(TAG, "加载日志显示设置: $logDisplayEnabled")
        }
    }
    
    /**
     * 设置默认游戏（如果找到包含关键字的游戏）
     */
    private fun setupDefaultGame(context: Context, editor: SharedPreferences.Editor) {
        try {
            val packageManager = context.packageManager
            val installedApps = packageManager.getInstalledApplications(0)
            
            for (app in installedApps) {
                val packageName = app.packageName.lowercase()
                
                // 检查是否包含内置游戏关键字
                for (keyword in Constants.BUILTIN_GAME_KEYWORDS) {
                    if (packageName.contains(keyword.lowercase())) {
                        // 找到匹配的游戏，设置为默认
                        editor.putBoolean(Constants.PREF_AUTO_START_GAME, true)
                        editor.putString(Constants.PREF_AUTO_START_GAME_PACKAGE, app.packageName)
                        
                        val appName = packageManager.getApplicationLabel(app).toString()
                        Logger.i(TAG, "找到内置游戏关键字 '$keyword'，设置默认游戏: $appName (${app.packageName})")
                        return
                    }
                }
            }
            
            Logger.i(TAG, "未找到包含内置游戏关键字的应用")
            
        } catch (e: Exception) {
            Logger.e(TAG, "设置默认游戏失败", e)
        }
    }
    
    /**
     * 获取已安装的游戏列表（包含关键字的应用）
     */
    fun getInstalledGames(context: Context): List<GameInfo> {
        val games = mutableListOf<GameInfo>()
        
        try {
            val packageManager = context.packageManager
            val installedApps = packageManager.getInstalledApplications(0)
            
            for (app in installedApps) {
                val packageName = app.packageName.lowercase()
                
                // 检查是否包含内置游戏关键字
                for (keyword in Constants.BUILTIN_GAME_KEYWORDS) {
                    if (packageName.contains(keyword.lowercase())) {
                        val appName = packageManager.getApplicationLabel(app).toString()
                        games.add(GameInfo(app.packageName, appName, keyword))
                        break
                    }
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "获取已安装游戏列表失败", e)
        }
        
        return games
    }
    
    /**
     * 游戏信息数据类
     */
    data class GameInfo(
        val packageName: String,
        val appName: String,
        val matchedKeyword: String
    )
}
