//  ./gradlew installToAllDevices
//  ./gradlew assembleDebug
// Top-level build file where you can add configuration options common to all sub-projects/modules.
// adb -s 3bbe432da0d29d4e install -r "C:\Users\<USER>\Downloads\OK3_testva.apk"

plugins {
    id("com.android.application") version "8.1.1" apply false
    id("org.jetbrains.kotlin.android") version "1.8.10" apply false
}

// 添加FTP上传依赖
buildscript {
    dependencies {
        classpath("commons-net:commons-net:3.9.0")
    }
}


// 添加自动安装到所有设备的任务
tasks.register("installToAllDevices") {
    dependsOn(":app:assembleDebug")

    doLast {
        val apkPath = "${project.rootDir}/app/build/outputs/apk/debug/app-debug.apk"
        val apkFile = file(apkPath)

        if (!apkFile.exists()) {
            throw GradleException("APK文件不存在: $apkPath")
        }

        println("找到APK: $apkPath")

        // 获取所有已连接设备
        val deviceListProcess = ProcessBuilder("adb", "devices").start()
        deviceListProcess.waitFor()
        val deviceOutput = deviceListProcess.inputStream.bufferedReader().readText()

        // 解析设备列表
        val devices = deviceOutput.lines()
            .filter { it.contains("device") && !it.contains("List") }
            .map { it.split("\\s+".toRegex())[0] }
            .toList()

        if (devices.isEmpty()) {
            println("没有找到已连接的设备")
            return@doLast
        }

        println("找到设备: ${devices.joinToString(", ")}")

        // 遍历所有设备并安装APK
        devices.forEach { deviceId ->
            println("正在安装到设备: $deviceId")

            if (deviceId == "UDU0221415002600") {
                println("跳过UDU0221415002600机器")
                return@forEach
            }
            // 安装APK到设备
            val installProcess = ProcessBuilder("adb", "-s", deviceId, "install", "-r", apkPath).start()
            installProcess.waitFor()
            val installOutput = installProcess.inputStream.bufferedReader().readText()

            if (installOutput.contains("Success")) {
                println("安装到设备 $deviceId 成功")

                // 启动应用
                println("正在启动应用...")
                val launchProcess = ProcessBuilder(
                    "adb",
                    "-s",
                    deviceId,
                    "shell",
                    "am",
                    "start",
                    "-n",
                    "com.example.webrtcsender/.ui.MainActivity"
                ).start()
                launchProcess.waitFor()
                println("应用已在设备 $deviceId 上启动")
            } else {
                println("安装到设备 $deviceId 失败: $installOutput")
            }
        }

        println("安装过程完成")
    }
}

// 添加FTP上传任务
tasks.register("uploadToFtp") {
    dependsOn(":app:assembleDebug")

    doLast {
        val apkPath = "${project.rootDir}/app/build/outputs/apk/debug/app-debug.apk"
        val apkFile = file(apkPath)

        if (!apkFile.exists()) {
            throw GradleException("APK文件不存在: $apkPath")
        }

        println("📦 准备上传APK到FTP服务器...")
        println("📁 APK路径: $apkPath")

        // FTP服务器配置
        val ftpServer = "39.96.165.173"
        val ftpPort = 21
        val ftpUser = "testota"
        val ftpPassword = "YM3GDxWHXGedfykY"
        val remotePath = "/webrtc_sender_builds/"

        // 使用固定的文件名
        val remoteFileName = "webrtc_sender.apk"

        try {
            // 使用Apache Commons Net进行FTP上传
            val ftpClient = org.apache.commons.net.ftp.FTPClient()

            println("🔗 连接到FTP服务器: $ftpServer:$ftpPort")
            ftpClient.connect(ftpServer, ftpPort)

            val loginSuccess = ftpClient.login(ftpUser, ftpPassword)
            if (!loginSuccess) {
                throw GradleException("FTP登录失败")
            }

            println("✅ FTP登录成功")

            // 设置传输模式
            ftpClient.enterLocalPassiveMode()
            ftpClient.setFileType(org.apache.commons.net.ftp.FTP.BINARY_FILE_TYPE)

            // 创建远程目录（如果不存在）
            try {
                ftpClient.makeDirectory(remotePath)
                println("📁 创建远程目录: $remotePath")
            } catch (e: Exception) {
                println("📁 远程目录可能已存在: $remotePath")
            }

            // 切换到远程目录
            val changeDirSuccess = ftpClient.changeWorkingDirectory(remotePath)
            if (!changeDirSuccess) {
                throw GradleException("无法切换到远程目录: $remotePath")
            }

            println("📁 切换到远程目录: $remotePath")

            // 上传文件
            println("⬆️ 开始上传文件: $remoteFileName")
            val inputStream = apkFile.inputStream()
            val uploadSuccess = ftpClient.storeFile(remoteFileName, inputStream)
            inputStream.close()

            if (!uploadSuccess) {
                throw GradleException("文件上传失败")
            }

            println("✅ 文件上传成功!")
            println("🌐 FTP地址: ftp://$ftpServer$remotePath$remoteFileName")
            println("📊 文件大小: ${String.format("%.2f", apkFile.length() / 1024.0 / 1024.0)} MB")

            // 断开连接
            ftpClient.logout()
            ftpClient.disconnect()

            println("🎉 FTP上传完成!")

        } catch (e: Exception) {
            throw GradleException("FTP上传失败: ${e.message}", e)
        }
    }
}

// 添加清除数据并重新安装的任务
tasks.register("cleanInstallToAllDevices") {
    dependsOn(":app:assembleDebug")

    doLast {
        val apkPath = "${project.rootDir}/app/build/outputs/apk/debug/app-debug.apk"
        val apkFile = file(apkPath)

        if (!apkFile.exists()) {
            throw GradleException("APK文件不存在: $apkPath")
        }

        println("找到APK: $apkPath")

        // 获取所有已连接设备
        val deviceListProcess = ProcessBuilder("adb", "devices").start()
        deviceListProcess.waitFor()
        val deviceOutput = deviceListProcess.inputStream.bufferedReader().readText()

        // 解析设备列表
        val devices = deviceOutput.lines()
            .filter { it.contains("device") && !it.contains("List") }
            .map { it.split("\\s+".toRegex())[0] }
            .toList()

        if (devices.isEmpty()) {
            println("没有找到已连接的设备")
            return@doLast
        }

        println("找到设备: ${devices.joinToString(", ")}")

        // 遍历所有设备并安装APK
        devices.forEach { deviceId ->
            println("正在处理设备: $deviceId")

            // 停止应用
            println("停止应用...")
            ProcessBuilder(
                "adb",
                "-s",
                deviceId,
                "shell",
                "am",
                "force-stop",
                "com.example.webrtcsender"
            ).start().waitFor()

            // 清除应用数据
            println("清除应用数据...")
            ProcessBuilder(
                "adb",
                "-s",
                deviceId,
                "shell",
                "pm",
                "clear",
                "com.example.webrtcsender"
            ).start().waitFor()

            // 卸载应用
            println("卸载应用...")
            ProcessBuilder(
                "adb",
                "-s",
                deviceId,
                "uninstall",
                "com.example.webrtcsender"
            ).start().waitFor()

            // 安装APK到设备
            println("重新安装应用...")
            val installProcess = ProcessBuilder("adb", "-s", deviceId, "install", apkPath).start()
            installProcess.waitFor()
            val installOutput = installProcess.inputStream.bufferedReader().readText()

            if (installOutput.contains("Success")) {
                println("安装到设备 $deviceId 成功")

                // 启动应用
                println("正在启动应用...")
                val launchProcess = ProcessBuilder(
                    "adb",
                    "-s",
                    deviceId,
                    "shell",
                    "am",
                    "start",
                    "-n",
                    "com.example.webrtcsender/.ui.MainActivity"
                ).start()
                launchProcess.waitFor()
                println("应用已在设备 $deviceId 上启动")
            } else {
                println("安装到设备 $deviceId 失败: $installOutput")
            }
        }

        println("清除数据并重新安装过程完成")
    }
}

// 添加一个安装任务（可指定设备ID，默认使用第一个可用设备）
// 使用方法: ./gradlew installAndRun -PdeviceId=20575779c1858812394
tasks.register("installAndRun") {
    dependsOn(":app:assembleDebug")
    doLast {
        val apkPath = "${project.rootDir}/app/build/outputs/apk/debug/app-debug.apk"
        val apkFile = file(apkPath)

        if (!apkFile.exists()) {
            throw GradleException("APK文件不存在: $apkPath")
        }

        println("找到APK: $apkPath")

        // 获取指定的设备ID（如果有的话）
        val specifiedDeviceId = project.findProperty("deviceId") as String?

        val deviceId = if (specifiedDeviceId != null) {
            println("使用指定设备: $specifiedDeviceId")
            specifiedDeviceId
        } else {
            // 获取所有已连接设备
            val deviceListProcess = ProcessBuilder("adb", "devices").start()
            deviceListProcess.waitFor()
            val deviceOutput = deviceListProcess.inputStream.bufferedReader().readText()

            // 解析设备列表
            val devices = deviceOutput.lines()
                .filter { it.contains("device") && !it.contains("List") }
                .map { it.split("\\s+".toRegex())[0] }
                .toList()

            if (devices.isEmpty()) {
                throw GradleException("没有找到已连接的设备")
            }

            // 使用第一个可用设备
            val firstDevice = devices.first()
            println("自动选择设备: $firstDevice (可用设备: ${devices.joinToString(", ")})")
            firstDevice
        }

        // 安装APK到设备
        val installProcess = ProcessBuilder("adb", "-s", deviceId, "install", "-r", apkPath).start()
        installProcess.waitFor()
        val installOutput = installProcess.inputStream.bufferedReader().readText()

        if (installOutput.contains("Success")) {
            println("安装到设备 $deviceId 成功")

            // 启动应用
            println("正在启动应用...")
            val launchProcess = ProcessBuilder(
                "adb",
                "-s",
                deviceId,
                "shell",
                "am",
                "start",
                "-n",
                "com.example.webrtcsender/.ui.MainActivity"
            ).start()
            launchProcess.waitFor()
            println("应用已在设备 $deviceId 上启动")
        } else {
            throw GradleException("安装到设备 $deviceId 失败: $installOutput")
        }
    }
}

// 添加编译并上传到FTP的组合任务
tasks.register("buildAndUpload") {
    dependsOn("uploadToFtp")

    doLast {
        println("🎉 编译并上传完成!")
        println("💡 使用方法:")
        println("   ./gradlew buildAndUpload          # 编译并上传到FTP")
        println("   ./gradlew uploadToFtp             # 仅上传已编译的APK")
        println("   ./gradlew installToAllDevices     # 编译并安装到所有设备")
    }
}

// 添加编译、上传并安装的完整任务
tasks.register("buildUploadAndInstall") {
    dependsOn("uploadToFtp", "installToAllDevices")

    doLast {
        println("🎉 编译、上传并安装完成!")
        println("✅ APK已上传到FTP服务器")
        println("✅ APK已安装到所有连接的设备")
    }
}