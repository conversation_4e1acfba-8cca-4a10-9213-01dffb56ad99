# "来自视频输入"音频源实现修正

## 🎯 问题说明

之前的实现中，"来自视频输入"和"remote_submix"使用了相同的底层音频源（`REMOTE_SUBMIX`），导致效果完全一样，没有体现出采集卡音频的特殊性。

## 🔧 修正后的实现

### 1. 不同的底层音频源

```kotlin
private fun getAudioSourceFromString(sourceType: String): Int {
    return when (sourceType) {
        "video_input" -> {
            // 来自视频输入（采集卡音频）
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                android.media.MediaRecorder.AudioSource.UNPROCESSED  // API 24+: 未处理的原始音频
            } else {
                android.media.MediaRecorder.AudioSource.CAMCORDER    // API < 24: 摄像机音频源
            }
        }
        "remote_submix" -> android.media.MediaRecorder.AudioSource.REMOTE_SUBMIX  // 系统内部音频
        // ... 其他音频源
    }
}
```

### 2. 专门的WebRTC约束配置

```kotlin
"video_input" -> {
    // 根据Android版本选择最佳音频源
    val audioSourceType = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
        "unprocessed"  // 原始未处理音频
    } else {
        "camcorder"    // 摄像机音频源
    }
    
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googAudioSource", audioSourceType))
    
    // 采集卡专用参数
    constraints.optional.add(MediaConstraints.KeyValuePair("captureCard", "true"))
    constraints.optional.add(MediaConstraints.KeyValuePair("externalAudio", "true"))
    
    // 禁用所有音频处理算法
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "false"))
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "false"))
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "false"))
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "false"))
    constraints.mandatory.add(MediaConstraints.KeyValuePair("googTypingNoiseDetection", "false"))
    
    // 高质量音频设置
    constraints.optional.add(MediaConstraints.KeyValuePair("googAudioProcessing", "false"))
}
```

## 📊 音频源对比

| 音频源选项 | Android音频源 | 适用场景 | 音频处理 |
|-----------|--------------|----------|----------|
| **video_input** | `UNPROCESSED`/`CAMCORDER` | 采集卡音频 | ❌ 完全禁用 |
| **remote_submix** | `REMOTE_SUBMIX` | 系统内部音频 | ❌ 禁用大部分 |
| **microphone** | `MIC` | 麦克风录音 | ✅ 启用优化 |
| **voice_communication** | `VOICE_COMMUNICATION` | 语音通话 | ✅ 启用优化 |

## 🎵 音频源特性详解

### "来自视频输入" (video_input)
- **Android API 24+**: 使用`UNPROCESSED`音频源
  - 获取完全未处理的原始音频数据
  - 最适合外部音频设备（采集卡）
  - 无任何系统音频处理干扰

- **Android API < 24**: 使用`CAMCORDER`音频源
  - 摄像机录制模式，适合外部音频
  - 相对较少的音频处理
  - 向下兼容旧版本Android

### "远程混音" (remote_submix)
- **用途**: 系统内部音频流
- **特点**: 捕获系统播放的音频
- **限制**: 需要系统支持，主要用于屏幕录制

## 🔧 技术优势

### 1. 版本适配
```kotlin
// 根据Android版本自动选择最佳音频源
if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
    // Android 7.0+ 使用UNPROCESSED
    android.media.MediaRecorder.AudioSource.UNPROCESSED
} else {
    // 旧版本使用CAMCORDER
    android.media.MediaRecorder.AudioSource.CAMCORDER
}
```

### 2. 音频质量保证
- ❌ 回声消除 (googEchoCancellation)
- ❌ 自动增益控制 (googAutoGainControl)
- ❌ 高通滤波器 (googHighpassFilter)
- ❌ 噪声抑制 (googNoiseSuppression)
- ❌ 打字噪声检测 (googTypingNoiseDetection)
- ❌ 音频处理 (googAudioProcessing)

### 3. 采集卡优化
- ✅ 外部音频设备标识 (externalAudio: true)
- ✅ 采集卡标识 (captureCard: true)
- ✅ 原始音频质量保持
- ✅ 低延迟音频传输

## 🚀 实际效果差异

### "来自视频输入" vs "远程混音"

| 特性 | video_input | remote_submix |
|------|-------------|---------------|
| **音频源** | UNPROCESSED/CAMCORDER | REMOTE_SUBMIX |
| **用途** | 外部设备音频 | 系统内部音频 |
| **音质** | 原始未处理 | 系统处理后 |
| **延迟** | 更低 | 稍高 |
| **兼容性** | 外部设备优化 | 系统音频优化 |

### 使用场景建议

1. **🎮 游戏采集卡**: 选择"来自视频输入"
   - HDMI采集卡音频
   - USB采集卡音频
   - 外部游戏机音频

2. **🔊 系统音频录制**: 选择"远程混音"
   - 电脑播放的音乐
   - 系统通知声音
   - 应用程序音频

3. **🎙️ 麦克风录音**: 选择"麦克风"
   - 解说录制
   - 语音通话
   - 现场收音

## ⚠️ 注意事项

1. **Android版本**: API 24+才支持UNPROCESSED音频源
2. **硬件支持**: 需要设备支持相应的音频源
3. **权限要求**: 仍需要录音权限
4. **测试建议**: 在不同设备上测试音频效果

## 🎯 预期改进

修正后的实现应该能够：
- ✅ 为采集卡提供更好的音频质量
- ✅ 减少音频处理延迟
- ✅ 保持原始音频特性
- ✅ 与系统音频源明确区分
- ✅ 根据Android版本自动优化

现在"来自视频输入"真正成为了专门为采集卡音频优化的独特音频源！
