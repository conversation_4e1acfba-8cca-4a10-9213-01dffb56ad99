<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>设备开机监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .controls {
            padding: 20px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .device-list {
            padding: 20px;
            background: #fff;
        }

        .device-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .device-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .device-item.offline {
            background: #f8f9fa;
            border: 2px solid #dc3545;
            opacity: 0.7;
        }

        .status-badge {
            font-size: 0.8em;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 10px;
            font-weight: bold;
        }

        .status-badge.online {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.offline {
            background: #f8d7da;
            color: #721c24;
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-id {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .uptime {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .device-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            padding: 8px 10px;
            border-radius: 6px;
            font-size: 0.9em;
        }

        .info-label {
            font-size: 1.1em;
            flex-shrink: 0;
            min-width: auto;
        }

        .info-value {
            color: #495057;
            flex: 1;
            word-break: break-word;
            flex: 1;
        }

        .game-info {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .no-game {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }

        .last-update {
            text-align: center;
            padding: 10px;
            color: #6c757d;
            font-size: 0.9em;
            background: #f8f9fa;
        }

        .login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .login-box {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .login-box h2 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .login-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1.1em;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .login-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }

        .status-indicator.connected {
            background: #28a745;
        }

        .status-indicator.connecting {
            background: #ffc107;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .error-message {
            color: #dc3545;
            margin-top: 10px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
                max-width: 100%;
            }

            .header h1 {
                font-size: 1.8em;
                margin-bottom: 5px;
            }

            .subtitle {
                font-size: 0.9em;
            }

            .stats {
                flex-wrap: wrap;
                gap: 8px;
            }

            .stat-item {
                flex: 1;
                min-width: 80px;
                padding: 8px;
                font-size: 0.9em;
            }

            .stat-item .number {
                font-size: 1.2em;
            }

            .controls {
                flex-wrap: wrap;
                gap: 10px;
                justify-content: center;
            }

            .connection-status {
                font-size: 0.9em;
            }

            .device-item {
                padding: 12px;
                margin-bottom: 12px;
            }

            .device-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .device-id {
                width: 100%;
            }

            .device-id > div:first-child {
                font-size: 0.9em;
                word-break: break-all;
            }

            .device-id > div:last-child {
                font-size: 0.8em;
                margin-top: 4px;
            }

            .uptime {
                font-size: 0.9em;
                align-self: flex-end;
            }

            .status-badge {
                font-size: 0.7em;
                padding: 1px 6px;
                margin-left: 5px;
            }

            .device-info {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
                font-size: 0.8em;
                margin-top: 10px;
            }

            .info-item {
                padding: 6px 8px;
                font-size: 0.8em;
            }

            .info-label {
                font-size: 1em;
            }

            .login-box {
                padding: 20px 15px;
                margin: 0 10px;
            }

            .login-input {
                padding: 12px;
                font-size: 1em;
            }

            .login-btn {
                padding: 12px;
                font-size: 1em;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 8px;
            }

            .header h1 {
                font-size: 1.6em;
            }

            .stats {
                gap: 6px;
            }

            .stat-item {
                min-width: 70px;
                padding: 6px;
                font-size: 0.8em;
            }

            .stat-item .number {
                font-size: 1.1em;
            }

            .device-item {
                padding: 10px;
                margin-bottom: 10px;
            }

            .device-id > div:first-child {
                font-size: 0.8em;
            }

            .device-id > div:last-child {
                font-size: 0.75em;
            }

            .uptime {
                font-size: 0.8em;
            }

            .device-info {
                grid-template-columns: repeat(2, 1fr);
                font-size: 0.75em;
                gap: 4px;
                margin-top: 8px;
            }

            .info-item {
                padding: 4px 6px;
                font-size: 0.75em;
            }

            .info-label {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div class="login-overlay" id="loginOverlay">
        <div class="login-box">
            <h2>🔐 设备监控认证</h2>
            <p>请输入监控密码以访问在线设备信息</p>
            <input type="password" class="login-input" id="passwordInput" placeholder="请输入密码" />
            <button class="login-btn" onclick="authenticate()">登录</button>
            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 设备开机监控</h1>
            <div class="subtitle">实时接收设备开机上报信息</div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalDevices">0</div>
                <div class="stat-label">总设备数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="onlineDevices">0</div>
                <div class="stat-label">在线设备</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="gameDevices">0</div>
                <div class="stat-label">配置游戏</div>
            </div>
        </div>

        <div class="controls">
            <div class="connection-status">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="connectionStatus">未连接</span>
            </div>
            <button class="refresh-btn" onclick="reconnectWebSocket()">🔄 重新连接</button>
        </div>

        <div class="device-list" id="deviceList">
            <div class="loading">📡 正在加载设备信息...</div>
        </div>

        <div class="last-update" id="lastUpdate"></div>
    </div>

    <script>
        let websocket = null;
        let devices = []; // 初始为空，只在收到新开机设备时添加
        let password = '';
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;
        let reconnectInterval = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有保存的密码
            const savedPassword = localStorage.getItem('monitor_password');
            if (savedPassword) {
                password = savedPassword;
                document.getElementById('passwordInput').value = savedPassword;
                // 直接隐藏登录界面并连接
                document.getElementById('loginOverlay').style.display = 'none';
                connectWebSocket();
            }

            // 监听回车键登录
            document.getElementById('passwordInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    authenticate();
                }
            });
        });

        // 认证函数
        function authenticate() {
            const passwordInput = document.getElementById('passwordInput');
            const errorMessage = document.getElementById('errorMessage');

            password = passwordInput.value.trim();

            if (!password) {
                errorMessage.textContent = '请输入密码';
                return;
            }

            errorMessage.textContent = '';

            // 保存密码到本地存储
            localStorage.setItem('monitor_password', password);

            // 连接WebSocket
            connectWebSocket();
        }

        // 连接WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            // WebSocket使用不同的端口（28765），HTTP使用28080
            const hostname = window.location.hostname;
            const wsPort = '28765'; // WebSocket专用端口
            const wsUrl = `${protocol}//${hostname}:${wsPort}`;

            console.log('连接WebSocket:', wsUrl);
            updateConnectionStatus('connecting', '正在连接...');

            // 设置连接超时
            const connectTimeout = setTimeout(() => {
                if (websocket && websocket.readyState === WebSocket.CONNECTING) {
                    console.log('WebSocket连接超时，关闭连接');
                    websocket.close();
                    updateConnectionStatus('disconnected', '连接超时');
                    scheduleReconnect();
                }
            }, 10000); // 10秒超时

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    clearTimeout(connectTimeout);
                    console.log('WebSocket连接已建立');
                    updateConnectionStatus('connected', '已连接');
                    reconnectAttempts = 0;

                    // 发送订阅请求
                    subscribeToDeviceUpdates();
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('解析WebSocket消息失败:', error);
                    }
                };

                websocket.onclose = function(event) {
                    clearTimeout(connectTimeout);
                    console.log('WebSocket连接已关闭:', event.code, event.reason);
                    updateConnectionStatus('disconnected', '连接已断开');

                    // 如果是认证失败，显示登录界面
                    if (event.code === 1008 || (event.reason && event.reason.includes('认证失败'))) {
                        showLoginError('密码错误，请重新输入');
                        document.getElementById('loginOverlay').style.display = 'flex';
                        localStorage.removeItem('monitor_password');
                    } else if (event.code === 1006) {
                        // 连接失败，可能是网络问题或服务器未启动
                        console.log('连接失败，可能是网络问题或服务器未启动');
                        updateConnectionStatus('disconnected', '连接失败，请检查网络');
                        scheduleReconnect();
                    } else {
                        // 其他情况自动重连
                        scheduleReconnect();
                    }
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    updateConnectionStatus('disconnected', '连接错误');
                };

            } catch (error) {
                console.error('创建WebSocket连接失败:', error);
                updateConnectionStatus('disconnected', '连接失败');
                showLoginError('连接失败: ' + error.message);
            }
        }

        // 发送订阅请求
        function subscribeToDeviceUpdates() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const subscribeMessage = {
                    type: 'subscribe_boot_monitor',
                    password: password
                };
                websocket.send(JSON.stringify(subscribeMessage));
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'new_boot_device':
                    if (data.success && data.device) {
                        // 添加新开机设备到列表
                        const existingIndex = devices.findIndex(d => d.cpu_unique_id === data.device.cpu_unique_id);
                        if (existingIndex >= 0) {
                            // 移除已存在的设备
                            devices.splice(existingIndex, 1);
                        }

                        // 总是将新开机设备添加到列表最前面
                        devices.unshift(data.device);

                        updateStats({ devices: devices, total: devices.length });
                        renderDevices(devices);
                        updateLastUpdateTime();

                        // 显示新设备通知
                        showNewDeviceNotification(data.device);
                    } else {
                        showError('接收设备数据失败: ' + (data.error || '未知错误'));
                    }
                    break;

                case 'auth_result':
                    if (data.success) {
                        console.log('认证成功');
                        // 隐藏登录界面
                        document.getElementById('loginOverlay').style.display = 'none';
                        updateConnectionStatus('connected', '已连接');
                    } else {
                        console.log('认证失败:', data.message);
                        showLoginError(data.message || '认证失败');
                        document.getElementById('loginOverlay').style.display = 'flex';
                        localStorage.removeItem('monitor_password');
                        // 关闭WebSocket连接
                        if (websocket) {
                            websocket.close();
                        }
                    }
                    break;

                case 'device_boot':
                    // 新设备开机通知
                    console.log('新设备开机:', data.device);
                    // 可以在这里添加通知或特殊处理
                    break;

                default:
                    console.log('未知消息类型:', data.type);
            }
        }

        // 更新连接状态
        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('connectionStatus');

            indicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }

        // 显示登录错误
        function showLoginError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
        }

        // 计划重连
        function scheduleReconnect() {
            if (reconnectAttempts >= maxReconnectAttempts) {
                updateConnectionStatus('disconnected', '重连失败，请手动重连');
                return;
            }

            reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 指数退避，最大30秒

            updateConnectionStatus('connecting', `${delay/1000}秒后重连 (${reconnectAttempts}/${maxReconnectAttempts})`);

            reconnectInterval = setTimeout(() => {
                connectWebSocket();
            }, delay);
        }

        // 手动重连
        function reconnectWebSocket() {
            if (reconnectInterval) {
                clearTimeout(reconnectInterval);
                reconnectInterval = null;
            }

            if (websocket) {
                websocket.close();
            }

            reconnectAttempts = 0;
            connectWebSocket();
        }

        // 更新统计信息
        function updateStats(data) {
            const total = data.total || devices.length;
            document.getElementById('totalDevices').textContent = total;

            const gameDevices = devices.filter(device => device.auto_start_game_package).length;
            document.getElementById('gameDevices').textContent = gameDevices;

            // 假设所有上报的设备都是在线的
            document.getElementById('onlineDevices').textContent = total;
        }

        // 渲染设备列表
        function renderDevices(devices) {
            const deviceList = document.getElementById('deviceList');
            
            if (devices.length === 0) {
                deviceList.innerHTML = '<div class="loading">📱 暂无设备开机信息</div>';
                return;
            }

            const html = devices.map(device => `
                <div class="device-item ${device.is_online === false ? 'offline' : ''}">
                    <div class="device-header">
                        <div class="device-id">
                            <div>
                                CPU ID: ${device.cpu_unique_id}
                                <span class="status-badge ${device.is_online === false ? 'offline' : 'online'}">
                                    ${device.is_online === false ? '🔴 离线' : '🟢 在线'}
                                </span>
                            </div>
                            <div style="font-size: 0.9em; color: #666; margin-top: 2px;">
                                发送端: ${device.sender_id || 'unknown'} |
                                游戏: ${getGameDisplayName(device.auto_start_game_package)} |
                                <span style="color: ${device.data_source === 'boot_report' ? '#28a745' : '#6c757d'};">
                                    ${device.data_source === 'boot_report' ? '📱 开机上报' : '💾 数据库'}
                                </span>
                            </div>
                            ${device.room_name ? `<div style="font-size: 0.85em; color: #007bff; margin-top: 2px;">
                                🏠 房间: ${device.room_name} | 🌐 服务器: ${device.room_server_domain || '未分配'}
                            </div>` : ''}
                        </div>
                        <div class="uptime" id="uptime-${device.cpu_unique_id}">已开机${device.uptime_display}</div>
                    </div>
                    
                    <div class="device-info">
                        <div class="info-item">
                            <span class="info-label">📱</span>
                            <span class="info-value">${device.device_brand} ${device.device_model}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">🤖</span>
                            <span class="info-value">Android ${device.android_version}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">📦</span>
                            <span class="info-value">v${device.app_version}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">🌐</span>
                            <span class="info-value">${device.local_ip}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">🌍</span>
                            <span class="info-value">${device.public_ip}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">⏰</span>
                            <span class="info-value">${formatBootTime(device.boot_time)}</span>
                        </div>
                    </div>

                </div>
            `).join('');

            deviceList.innerHTML = html;
            
            // 启动运行时间更新
            updateUptimes();
        }

        // 更新运行时间显示
        function updateUptimes() {
            devices.forEach(device => {
                const uptimeElement = document.getElementById(`uptime-${device.cpu_unique_id}`);
                if (uptimeElement) {
                    const currentTime = Date.now();
                    const uptimeSeconds = Math.floor((currentTime - device.boot_time) / 1000);
                    uptimeElement.textContent = formatUptime(uptimeSeconds);
                }
            });
        }

        // 格式化开机时间
        function formatBootTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        }

        // 格式化运行时间
        function formatUptime(seconds) {
            if (seconds < 60) {
                return `${seconds}秒`;
            } else if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const secs = seconds % 60;
                return `${minutes}分${secs}秒`;
            } else if (seconds < 86400) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${hours}小时${minutes}分`;
            } else {
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                return `${days}天${hours}小时`;
            }
        }

        // 游戏包名转中文显示名（包含匹配）
        function getGameDisplayName(packageName) {
            if (!packageName) return '未配置';

            const gameKeywords = {
                'BirdKing': '鸟王',
                'ocean3': '海王',
                '.example.demo': '一筒天下',
                'WaterMargin': '水浒传'
            };

            // 检查包名是否包含关键字
            for (const [keyword, displayName] of Object.entries(gameKeywords)) {
                if (packageName.toLowerCase().includes(keyword.toLowerCase())) {
                    return displayName;
                }
            }

            // 如果没有匹配的关键字，返回原包名
            return packageName;
        }

        // 显示错误信息
        function showError(message) {
            const deviceList = document.getElementById('deviceList');
            deviceList.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // 显示新设备通知
        function showNewDeviceNotification(device) {
            console.log('🆕 新设备开机:', device.sender_id, device.room_name || '未知房间');

            // 可以添加桌面通知或其他提示
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('设备开机通知', {
                    body: `${device.sender_id} 已开机${device.room_name ? ` (${device.room_name})` : ''}`,
                    icon: '/favicon.ico'
                });
            }
        }

        // 更新最后更新时间
        function updateLastUpdateTime() {
            const lastUpdate = document.getElementById('lastUpdate');
            lastUpdate.textContent = `最后更新: ${new Date().toLocaleString('zh-CN')}`;
        }

        // 每秒更新运行时间
        setInterval(updateUptimes, 1000);
    </script>
</body>
</html>
