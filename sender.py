#!/usr/bin/env python3
# sender.py - 视频流发送端
import asyncio
import traceback
import websockets
import json
import cv2
import base64
import argparse
import logging
import time
import uuid
import numpy as np
import traceback
import os
import sys
import fractions
from pathlib import Path
from av import VideoFrame
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack, RTCConfiguration, MediaStreamTrack, RTCIceCandidate
from aiortc.contrib.media import MediaPlayer, MediaRelay
from aiortc.mediastreams import AudioFrame

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('sender')

# 设置aiortc日志级别
aiortc_logger = logging.getLogger('aiortc')
aiortc_logger.setLevel(logging.INFO)

# 设置aioice日志级别
aioice_logger = logging.getLogger('aioice')
aioice_logger.setLevel(logging.INFO)

# 启用更详细的调试信息
if '--debug' in sys.argv:
    logger.setLevel(logging.DEBUG)
    aiortc_logger.setLevel(logging.DEBUG)
    aioice_logger.setLevel(logging.DEBUG)
    logging.getLogger().setLevel(logging.DEBUG)
    print("已启用调试模式")

# 解析命令行参数
parser = argparse.ArgumentParser(description="STUN视频流发送端")
parser.add_argument("--video", help="视频源URL (例如 http://*************:80/0.mp4 或 0 表示摄像头 或 file:///path/to/video.mp4 表示本地视频文件)",default="http://*************/ts/1_0")
parser.add_argument("--file", help="本地视频文件路径 (优先级高于--video)", default="")
parser.add_argument("--id", help="发送端ID", default=f"sender-{uuid.uuid4().hex[:8]}")
parser.add_argument("--name", help="视频源名称", default="")
parser.add_argument("--description", help="视频源描述", default="")
parser.add_argument("--loop", help="循环播放本地视频文件", action="store_true")
parser.add_argument("--width", help="视频宽度 (仅对摄像头和测试视频有效)", type=int, default=640)
parser.add_argument("--height", help="视频高度 (仅对摄像头和测试视频有效)", type=int, default=480)
parser.add_argument("--fps", help="视频帧率 (仅对测试视频有效)", type=int, default=30)
parser.add_argument("--signaling", help="信令服务器URL", default="wss://sling.91jdcd.com/ws/")
parser.add_argument("--debug", help="启用调试模式", action="store_true")
parser.add_argument("--force-vp8", help="强制使用VP8编码", action="store_true")
parser.add_argument("--force-h264", help="强制使用H264编码", action="store_true")
args = parser.parse_args()

# STUN和TURN服务器配置
from aiortc.rtcicetransport import RTCIceServer

ICE_SERVERS = [
    RTCIceServer(urls=["stun:stun.l.google.com:19302"]),
    RTCIceServer(urls=["stun:stun1.l.google.com:19302"]),
    RTCIceServer(urls=["stun:stun2.l.google.com:19302"]),
    RTCIceServer(
        urls=["turn:numb.viagenie.ca"],
        username="<EMAIL>",
        credential="muazkh"
    )
]

# 存储对等连接
peer_connections = {}

# 测试视频生成器 - 当所有其他视频源都不可用时使用
class TestVideoSource(VideoStreamTrack):
    """生成测试视频模式，显示移动的文本和时间"""
    def __init__(self, width=640, height=480, fps=30):
        super().__init__()
        self.width = width
        self.height = height
        self.fps = fps
        self.frame_count = 0
        self.text_x = 50
        self.text_direction = 1  # 1 = 右移, -1 = 左移
        self.frame_start_time = time.time()

        # 创建彩色背景
        self.background_hue = 0  # 色调值 (0-180)

        # 添加一些随机形状
        self.shapes = []
        for _ in range(5):
            shape = {
                'type': np.random.choice(['circle', 'rectangle']),
                'x': np.random.randint(0, width),
                'y': np.random.randint(0, height),
                'size': np.random.randint(20, 100),
                'color': (np.random.randint(0, 255), np.random.randint(0, 255), np.random.randint(0, 255)),
                'speed_x': np.random.randint(-5, 5),
                'speed_y': np.random.randint(-5, 5)
            }
            self.shapes.append(shape)

        logger.info(f"创建测试视频源: 分辨率 {self.width}x{self.height}, FPS: {self.fps}")

    async def recv(self):
        # 确保fps是有效的有理数值
        try:
            # 尝试将fps转换为整数，如果可以的话
            fps_value = int(self.fps)
            time_base = fractions.Fraction(1, fps_value)
        except (TypeError, ValueError):
            # 如果转换失败，使用默认值
            fps_value = 30
            time_base = fractions.Fraction(1, fps_value)

        # 初始化开始时间（如果尚未初始化）
        if not hasattr(self, 'frame_start_time'):
            self.frame_start_time = time.time()
            self.frame_count = 0

        # 计算当前帧应该在什么时间显示
        frame_duration = 1.0 / fps_value
        target_time = self.frame_start_time + (self.frame_count * frame_duration)

        # 如果当前时间早于目标时间，等待直到达到目标时间
        now = time.time()
        if now < target_time:
            await asyncio.sleep(target_time - now)

        # 计算pts基于帧计数而不是当前时间
        pts = int(self.frame_count * time_base.denominator / time_base.numerator)

        # 增加帧计数
        self.frame_count += 1

        # 创建渐变背景
        self.background_hue = (self.background_hue + 1) % 180
        hsv_background = np.ones((self.height, self.width, 3), dtype=np.uint8)
        hsv_background[:, :, 0] = self.background_hue
        hsv_background[:, :, 1] = 100  # 饱和度
        hsv_background[:, :, 2] = 100  # 亮度
        frame = cv2.cvtColor(hsv_background, cv2.COLOR_HSV2BGR)

        # 绘制移动的形状
        for shape in self.shapes:
            # 更新位置
            shape['x'] += shape['speed_x']
            shape['y'] += shape['speed_y']

            # 边界检查
            if shape['x'] < 0 or shape['x'] > self.width:
                shape['speed_x'] *= -1
            if shape['y'] < 0 or shape['y'] > self.height:
                shape['speed_y'] *= -1

            # 绘制形状
            if shape['type'] == 'circle':
                cv2.circle(frame, (shape['x'], shape['y']), shape['size'], shape['color'], -1)
            else:
                x1 = max(0, shape['x'] - shape['size']//2)
                y1 = max(0, shape['y'] - shape['size']//2)
                x2 = min(self.width, shape['x'] + shape['size']//2)
                y2 = min(self.height, shape['y'] + shape['size']//2)
                cv2.rectangle(frame, (x1, y1), (x2, y2), shape['color'], -1)

        # 添加移动的文本
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")

        # 计算实际FPS
        elapsed_time = time.time() - self.frame_start_time
        actual_fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0

        # 移动文本位置
        self.text_x += 5 * self.text_direction
        if self.text_x > self.width - 300 or self.text_x < 10:
            self.text_direction *= -1

        # 添加半透明覆盖层
        overlay = frame.copy()
        cv2.rectangle(overlay, (0, 0), (self.width, 70), (0, 0, 0), -1)
        cv2.rectangle(overlay, (0, self.height-70), (self.width, self.height), (0, 0, 0), -1)
        frame = cv2.addWeighted(overlay, 0.5, frame, 0.5, 0)

        # 添加标题
        cv2.putText(frame, "WebRTC Test Stream", (self.width//2 - 150, 40),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # 添加时间
        cv2.putText(frame, current_time, (self.text_x, self.height//2),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # 添加帧计数和FPS
        cv2.putText(frame, f"Frame: {self.frame_count} | FPS: {actual_fps:.1f}", (20, self.height - 40),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 添加分辨率信息
        cv2.putText(frame, f"Resolution: {self.width}x{self.height}", (20, self.height - 15),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # 转换为VideoFrame
        video_frame = VideoFrame.from_ndarray(frame, format="bgr24")
        video_frame.pts = pts
        video_frame.time_base = time_base

        return video_frame

# 自定义视频轨道类 - 用于摄像头或视频文件
class VideoSource(VideoStreamTrack):
    def __init__(self, source, loop=False, width=640, height=480):
        super().__init__()
        self.source = source
        self.cap = None
        self.loop = loop
        self.frame_count = 0
        self.target_width = width
        self.target_height = height
        self.is_local_file = False
        self.file_path = None
        self.paused = False  # 添加暂停状态标志
        self.last_frame = None  # 存储最后一帧，用于暂停时显示

        # 检查是否是本地文件路径
        if source.startswith("file:///"):
            self.file_path = source[8:]  # 移除 "file:///"
            self.is_local_file = True
            logger.info(f"检测到本地文件路径: {self.file_path}")
        elif os.path.exists(source) and os.path.isfile(source):
            self.file_path = source
            self.is_local_file = True
            logger.info(f"检测到本地文件: {self.file_path}")

        # 检查是否是摄像头索引
        try:
            if source.isdigit():
                # 尝试打开摄像头
                self.cap = cv2.VideoCapture(int(source))
                if not self.cap.isOpened():
                    # 尝试其他摄像头索引
                    for i in range(5):
                        if i == int(source):
                            continue
                        logger.info(f"尝试打开摄像头 {i}...")
                        self.cap = cv2.VideoCapture(i)
                        if self.cap.isOpened():
                            logger.info(f"成功打开摄像头 {i}")
                            break
            elif self.is_local_file:
                # 打开本地视频文件
                self.cap = cv2.VideoCapture(self.file_path)
                if not self.cap.isOpened():
                    logger.error(f"无法打开本地视频文件: {self.file_path}")
                    raise ValueError(f"无法打开本地视频文件: {self.file_path}")
                logger.info(f"成功打开本地视频文件: {self.file_path}")
            else:
                # 尝试打开视频URL
                self.cap = cv2.VideoCapture(source)
        except Exception as e:
            logger.error(f"打开视频源时出错: {e}")
            raise ValueError(f"无法打开视频源: {source}")

        if not self.cap.isOpened():
            logger.error(f"无法打开视频源: {source}")
            raise ValueError(f"无法打开视频源: {source}")

        # 获取视频属性
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        if self.fps <= 0:
            self.fps = 30

        # 获取视频总帧数（仅对本地文件有效）
        if self.is_local_file:
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.duration = self.total_frames / self.fps if self.fps > 0 else 0
            logger.info(f"视频总帧数: {self.total_frames}, 时长: {self.duration:.2f}秒")

            # 如果视频分辨率过大，可以考虑调整大小
            if self.width > 1920 or self.height > 1080:
                logger.warning(f"视频分辨率较大 ({self.width}x{self.height})，可能会影响性能")

        # 对于摄像头，可以设置目标分辨率
        if source.isdigit():
            if self.target_width > 0 and self.target_height > 0:
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.target_width)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.target_height)
                # 重新获取实际设置的分辨率
                self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        logger.info(f"视频源: {source}, 分辨率: {self.width}x{self.height}, FPS: {self.fps}")

    # 暂停视频播放
    def pause(self):
        if not self.paused:
            self.paused = True
            logger.info("视频已暂停")
            return True
        return False

    # 恢复视频播放
    def resume(self):
        if self.paused:
            self.paused = False
            logger.info("视频已恢复播放")
            return True
        return False

    # 切换暂停/播放状态
    def toggle_pause(self):
        if self.paused:
            return self.resume()
        else:
            return self.pause()

    # 跳转到指定位置（仅对本地文件有效）
    def seek(self, position):
        if not self.is_local_file:
            logger.warning("只有本地视频文件支持跳转")
            return False

        if position < 0:
            position = 0
        elif position > self.total_frames:
            position = self.total_frames - 1

        success = self.cap.set(cv2.CAP_PROP_POS_FRAMES, position)
        if success:
            self.frame_count = position
            logger.info(f"已跳转到帧: {position}")
            # 重置时间戳计算的起始时间
            self.start_time = time.time() - (position / self.fps)
            self.last_frame_time = time.time()
        else:
            logger.error(f"跳转失败: {position}")

        return success

    async def recv(self):
        # 确保fps是有效的有理数值
        try:
            # 尝试将fps转换为整数，如果可以的话
            if self.fps > 0 and self.fps < 1000:
                fps_value = int(self.fps)
            else:
                # 如果fps值异常大或小，使用默认值30
                fps_value = 30
            time_base = fractions.Fraction(1, fps_value)
        except (TypeError, ValueError):
            # 如果转换失败，使用默认值
            fps_value = 30
            time_base = fractions.Fraction(1, fps_value)

        # 初始化开始时间和帧计数器（如果尚未初始化）
        if not hasattr(self, 'start_time'):
            self.start_time = time.time()
            self.last_frame_time = self.start_time
            self.frame_count = 0

        # 计算当前帧应该在什么时间显示
        frame_duration = 1.0 / fps_value
        target_time = self.start_time + (self.frame_count * frame_duration)

        # 如果当前时间早于目标时间，等待直到达到目标时间
        now = time.time()
        if now < target_time:
            await asyncio.sleep(target_time - now)

        # 计算pts基于帧计数而不是当前时间
        pts = int(self.frame_count * time_base.denominator / time_base.numerator)

        # 如果暂停状态，返回上一帧
        if self.paused:
            if self.last_frame is not None:
                frame = self.last_frame.copy()
                ret = True
            else:
                # 如果没有上一帧，尝试读取一帧但不前进
                current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                ret, frame = self.cap.read()
                if ret:
                    self.last_frame = frame.copy()
                    # 重置到当前位置
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos)
                else:
                    # 如果读取失败，创建一个黑色帧
                    frame = np.zeros((self.height, self.width, 3), np.uint8)
                    cv2.putText(frame, "Paused", (self.width//2 - 50, self.height//2),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    ret = True
        else:
            # 正常读取视频帧
            ret, frame = self.cap.read()
            if ret:
                self.last_frame = frame.copy()

        # 记录实际帧率（每30帧记录一次）
        if self.frame_count % 30 == 0 and self.frame_count > 0:
            current_time = time.time()
            elapsed = current_time - self.last_frame_time
            if elapsed > 0:
                actual_fps = 30 / elapsed
                logger.debug(f"实际帧率: {actual_fps:.2f} FPS (目标: {fps_value} FPS)")
            self.last_frame_time = current_time

        self.frame_count += 1

        if not ret:
            # 如果是视频文件且启用了循环播放，则重新开始
            if (self.is_local_file or (self.source != "0" and not self.source.isdigit())) and self.loop:
                # 只在第一次循环和每10次循环时记录日志，减少日志输出
                if not hasattr(self, 'loop_count'):
                    self.loop_count = 0
                self.loop_count += 1

                if self.loop_count == 1 or self.loop_count % 10 == 0:
                    logger.info(f"视频文件播放完毕，重新开始 (第 {self.loop_count} 次循环，已播放 {self.frame_count} 帧)")
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self.frame_count = 0
                # 重置时间戳计算的起始时间，确保循环播放时速度正常
                self.start_time = time.time()
                self.last_frame_time = self.start_time
                ret, frame = self.cap.read()

                if not ret:
                    # 如果仍然失败，返回彩色测试帧
                    frame = np.zeros((self.height, self.width, 3), np.uint8)
                    # 创建彩色背景
                    for y in range(self.height):
                        for x in range(self.width):
                            b = int(255 * x / self.width)
                            g = int(255 * y / self.height)
                            r = int(255 * (1 - x / self.width))
                            frame[y, x] = [b, g, r]
                    cv2.putText(frame, "Video file error - Cannot restart", (50, self.height//2),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            else:
                # 视频文件播放完毕或摄像头出错，返回彩色测试帧
                frame = np.zeros((self.height, self.width, 3), np.uint8)
                # 创建彩色背景
                for y in range(self.height):
                    for x in range(self.width):
                        b = int(255 * x / self.width)
                        g = int(255 * y / self.height)
                        r = int(255 * (1 - x / self.width))
                        frame[y, x] = [b, g, r]

                if self.is_local_file:
                    message = "Video file ended"
                    if self.loop:
                        message += " (Loop failed)"
                    cv2.putText(frame, message, (50, self.height//2),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    # 尝试再次打开文件
                    try:
                        self.cap.release()
                        self.cap = cv2.VideoCapture(self.file_path)
                        if self.cap.isOpened():
                            logger.info("重新打开视频文件成功")
                    except Exception as e:
                        logger.error(f"重新打开视频文件失败: {e}")
                else:
                    cv2.putText(frame, "Camera error", (50, self.height//2),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # 添加帧计数和时间戳
        if frame is not None:
            # 确保帧不是空的
            if frame.size == 0:
                logger.error("帧数据为空，创建替代帧")
                frame = np.zeros((self.height, self.width, 3), np.uint8)
                cv2.putText(frame, "Empty frame", (50, self.height//2),
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            # 添加时间戳
            cv2.putText(frame, time.strftime("%Y-%m-%d %H:%M:%S"), (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 如果是本地文件，添加进度信息
            if self.is_local_file and self.total_frames > 0:
                progress = (self.frame_count % self.total_frames) / self.total_frames * 100
                cv2.putText(frame, f"Frame: {self.frame_count} / {self.total_frames} ({progress:.1f}%)",
                            (10, self.height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 不再添加边框和对角线，保持视频原样

        # 转换为VideoFrame
        try:
            video_frame = VideoFrame.from_ndarray(frame, format="bgr24")
            video_frame.pts = pts
            video_frame.time_base = time_base

            # 记录帧信息
            if self.frame_count % 30 == 0:  # 每30帧记录一次
                logger.debug(f"发送视频帧: {self.frame_count}, 尺寸: {frame.shape}, pts: {pts}")

            return video_frame
        except Exception as e:
            logger.error(f"创建视频帧失败: {e}")
            # 创建一个备用帧
            backup_frame = np.zeros((self.height, self.width, 3), np.uint8)
            cv2.putText(backup_frame, "Frame conversion error", (50, self.height//2),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            backup_video_frame = VideoFrame.from_ndarray(backup_frame, format="bgr24")
            backup_video_frame.pts = pts
            backup_video_frame.time_base = time_base
            return backup_video_frame

async def handle_signaling():
    # 连接到信令服务器
    uri = args.signaling
    async with websockets.connect(uri) as websocket:
        # 注册客户端
        await websocket.send(json.dumps({
            "type": "register",
            "id": args.id,
            "role": "source",  # 确保这里指定了role为source
            "name": args.name if args.name else "未知",
            "description": args.description if args.description else "WebRTC视频流"
        }))
        logger.info(f"已连接到信令服务器，ID: {args.id}")

        # 创建视频源
        if args.file:
            # 优先使用--file参数指定的本地视频文件
            logger.info(f"使用本地视频文件: {args.file}")
            try:
                # 检查文件是否存在
                if not os.path.exists(args.file):
                    logger.error(f"本地视频文件不存在: {args.file}")
                    raise FileNotFoundError(f"文件不存在: {args.file}")

                # 创建本地视频文件源
                video_source = VideoSource(args.file, loop=args.loop, width=args.width, height=args.height)
                logger.info("本地视频文件源创建成功")
            except Exception as e:
                logger.error(f"创建本地视频文件源失败: {e}")
                logger.info("使用测试视频源作为备选")
                # 使用测试视频源作为备选
                video_source = TestVideoSource(width=args.width, height=args.height, fps=args.fps)
                logger.info("测试视频源创建成功")
        elif args.video.lower() == "test":
            # 直接使用测试视频源
            logger.info("使用测试视频源")
            video_source = TestVideoSource(width=args.width, height=args.height, fps=args.fps)
            logger.info("测试视频源创建成功")
        else:
            try:
                # 尝试创建指定的视频源
                video_source = VideoSource(args.video, loop=args.loop, width=args.width, height=args.height)
                logger.info("视频源创建成功")
            except Exception as e:
                logger.error(f"创建视频源失败: {e}")
                logger.info("使用测试视频源作为备选")
                # 使用测试视频源作为备选
                video_source = TestVideoSource(width=args.width, height=args.height, fps=args.fps)
                logger.info("测试视频源创建成功")

        # 处理信令消息
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"收到消息: {data['type']}")

                if data["type"] == "registered":
                    logger.info("注册成功")

                elif data["type"] == "client_joined":
                    viewer_id = data["id"]
                    logger.info(f"客户端加入: {viewer_id}")

                    # 如果已经有与该观众的连接，并且有数据通道，则发送播放端ID
                    if viewer_id in peer_connections:
                        pc = peer_connections[viewer_id]
                        for dc in pc._data_channels.values():
                            if dc.readyState == "open":
                                # 发送播放端ID和信息
                                source_info = {
                                    "type": "source_info",
                                    "source_id": args.id,
                                    "name": args.name if args.name else "未知",
                                    "description": args.description if args.description else "WebRTC视频流"
                                }
                                dc.send(json.dumps(source_info))
                                logger.info(f"已向观众 {viewer_id} 发送播放端ID: {args.id}")

                elif data["type"] == "offer" and "from" in data:
                    peer_id = data["from"]
                    logger.info(f"收到来自 {peer_id} 的offer")

                    try:
                        # 创建新的对等连接
                        pc = RTCPeerConnection(configuration=RTCConfiguration(iceServers=ICE_SERVERS))
                        peer_connections[peer_id] = pc

                        # 添加视频轨道，并明确设置方向为 sendonly
                        video_track = pc.addTrack(video_source)
                        logger.info(f"已添加视频轨道: {video_track}")

                        # 设置视频轨道方向为 sendonly
                        for transceiver in pc.getTransceivers():
                            if transceiver.sender == video_track:
                                transceiver.direction = "sendonly"
                                logger.info(f"设置视频轨道方向为 sendonly")

                        # 添加一个虚拟音频轨道以确保媒体方向正确
                        class DummyAudioTrack(MediaStreamTrack):
                            kind = "audio"

                            async def recv(self):
                                # 创建一个静音音频帧，使用兼容性更好的方式
                                # 使用自定义时间戳生成方法
                                sample_rate = 8000
                                time_base = fractions.Fraction(1, sample_rate)
                                pts = int(time.time() * sample_rate)

                                try:
                                    # 尝试新版本的 PyAV 方式
                                    frame = AudioFrame(channels=1, sample_rate=8000, samples=160)
                                    frame.pts = pts
                                    frame.time_base = time_base
                                except TypeError:
                                    try:
                                        # 尝试旧版本的 PyAV 方式
                                        frame = AudioFrame(format='s16', layout='mono', samples=160)
                                        frame.sample_rate = 8000
                                        frame.pts = pts
                                        frame.time_base = time_base
                                    except Exception as e:
                                        logger.error(f"创建音频帧失败: {e}")
                                        # 最后的备选方案：创建一个空的音频帧
                                        frame = AudioFrame()
                                        frame.sample_rate = 8000
                                        frame.pts = pts
                                        frame.time_base = time_base
                                return frame

                        # 添加虚拟音频轨道，并明确设置方向为 sendonly
                        audio_track = pc.addTrack(DummyAudioTrack())
                        logger.info(f"已添加音频轨道: {audio_track}")

                        # 设置音频轨道方向为 sendonly
                        for transceiver in pc.getTransceivers():
                            if transceiver.sender == audio_track:
                                transceiver.direction = "sendonly"
                                logger.info(f"设置音频轨道方向为 sendonly")

                        # 记录轨道信息
                        for transceiver in pc.getTransceivers():
                            logger.info(f"转码器: {transceiver}, 方向: {transceiver.direction}, 当前方向: {transceiver.currentDirection if hasattr(transceiver, 'currentDirection') else 'None'}")
                            logger.info(f"转码器中间描述: {transceiver.mid}, 发送器: {transceiver.sender}, 接收器: {transceiver.receiver}")

                        # 创建一个数据通道，确保数据通道方向正确
                        channel = pc.createDataChannel('control')

                        @channel.on('open')
                        def on_open():
                            logger.info(f"数据通道已打开")
                            # 发送问候消息
                            channel.send('Hello from sender!')

                            # 发送播放端ID和信息
                            source_info = {
                                "type": "source_info",
                                "source_id": args.id,
                                "name": args.name if args.name else "未知",
                                "description": args.description if args.description else "WebRTC视频流"
                            }
                            channel.send(json.dumps(source_info))
                            logger.info(f"已向观众 {peer_id} 发送播放端ID: {args.id}")

                        @channel.on('message')
                        def on_message(message):
                            logger.info(f"数据通道收到消息: {message}")

                            # 尝试解析JSON消息
                            try:
                                # 检查是否是JSON格式
                                if message.startswith('{') and message.endswith('}'):
                                    data = json.loads(message)

                                    # 处理控制命令
                                    if 'command' in data:
                                        command = data['command']

                                        # 处理暂停/播放命令
                                        if command == 'pause':
                                            if hasattr(video_source, 'pause'):
                                                result = video_source.pause()
                                                channel.send(json.dumps({
                                                    "type": "command_result",
                                                    "command": "pause",
                                                    "success": result,
                                                    "state": "paused" if result else "playing"
                                                }))

                                        # 处理恢复播放命令
                                        elif command == 'resume' or command == 'play':
                                            if hasattr(video_source, 'resume'):
                                                result = video_source.resume()
                                                channel.send(json.dumps({
                                                    "type": "command_result",
                                                    "command": "resume",
                                                    "success": result,
                                                    "state": "playing" if result else "paused"
                                                }))

                                        # 处理切换暂停/播放状态命令
                                        elif command == 'toggle_pause':
                                            if hasattr(video_source, 'toggle_pause'):
                                                result = video_source.toggle_pause()
                                                is_paused = hasattr(video_source, 'paused') and video_source.paused
                                                channel.send(json.dumps({
                                                    "type": "command_result",
                                                    "command": "toggle_pause",
                                                    "success": True,
                                                    "state": "paused" if is_paused else "playing"
                                                }))

                                        # 处理跳转命令
                                        elif command == 'seek' and 'position' in data:
                                            if hasattr(video_source, 'seek'):
                                                position = data['position']
                                                result = video_source.seek(position)
                                                channel.send(json.dumps({
                                                    "type": "command_result",
                                                    "command": "seek",
                                                    "success": result,
                                                    "position": position if result else -1
                                                }))

                                        # 处理获取状态命令
                                        elif command == 'get_status':
                                            is_paused = hasattr(video_source, 'paused') and video_source.paused
                                            position = -1
                                            duration = -1

                                            if hasattr(video_source, 'frame_count'):
                                                position = video_source.frame_count

                                            if hasattr(video_source, 'total_frames'):
                                                duration = video_source.total_frames

                                            channel.send(json.dumps({
                                                "type": "status",
                                                "state": "paused" if is_paused else "playing",
                                                "position": position,
                                                "duration": duration,
                                                "is_local_file": hasattr(video_source, 'is_local_file') and video_source.is_local_file
                                            }))

                                        # 未知命令
                                        else:
                                            channel.send(json.dumps({
                                                "type": "error",
                                                "message": f"未知命令: {command}"
                                            }))
                                    else:
                                        # 回显消息
                                        channel.send(f"Echo: {message}")
                                else:
                                    # 回显消息
                                    channel.send(f"Echo: {message}")
                            except Exception as e:
                                logger.error(f"处理消息错误: {e}")
                                channel.send(json.dumps({
                                    "type": "error",
                                    "message": f"处理消息错误: {str(e)}"
                                }))

                        # 处理接收到的轨道
                        @pc.on("track")
                        def on_track(track):
                            logger.info(f"接收到轨道: {track.kind}")

                            if track.kind == "audio":
                                # 可以忽略音频轨道或进行处理
                                pass

                            @track.on("ended")
                            def on_ended():
                                logger.info(f"轨道结束: {track.kind}")

                        # 处理数据通道
                        @pc.on("datachannel")
                        def on_datachannel(channel):
                            logger.info(f"收到数据通道: {channel.label}")

                            @channel.on("open")
                            def on_open():
                                logger.info(f"数据通道 {channel.label} 已打开")
                                # 发送初始消息
                                channel.send("Hello from sender!")

                                # 发送播放端ID和信息
                                source_info = {
                                    "type": "source_info",
                                    "source_id": args.id,
                                    "name": args.name if args.name else "未知",
                                    "description": args.description if args.description else "WebRTC视频流"
                                }
                                channel.send(json.dumps(source_info))
                                logger.info(f"已向观众发送播放端ID: {args.id}")

                            @channel.on("message")
                            def on_message(message):
                                logger.info(f"收到数据通道消息: {message}")

                                # 尝试解析JSON消息
                                try:
                                    # 检查是否是JSON格式
                                    if message.startswith('{') and message.endswith('}'):
                                        data = json.loads(message)

                                        # 处理控制命令
                                        if 'command' in data:
                                            command = data['command']

                                            # 处理暂停/播放命令
                                            if command == 'pause':
                                                if hasattr(video_source, 'pause'):
                                                    result = video_source.pause()
                                                    channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "pause",
                                                        "success": result,
                                                        "state": "paused" if result else "playing"
                                                    }))

                                            # 处理恢复播放命令
                                            elif command == 'resume' or command == 'play':
                                                if hasattr(video_source, 'resume'):
                                                    result = video_source.resume()
                                                    channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "resume",
                                                        "success": result,
                                                        "state": "playing" if result else "paused"
                                                    }))

                                            # 处理切换暂停/播放状态命令
                                            elif command == 'toggle_pause':
                                                if hasattr(video_source, 'toggle_pause'):
                                                    result = video_source.toggle_pause()
                                                    is_paused = hasattr(video_source, 'paused') and video_source.paused
                                                    channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "toggle_pause",
                                                        "success": True,
                                                        "state": "paused" if is_paused else "playing"
                                                    }))

                                            # 处理跳转命令
                                            elif command == 'seek' and 'position' in data:
                                                if hasattr(video_source, 'seek'):
                                                    position = data['position']
                                                    result = video_source.seek(position)
                                                    channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "seek",
                                                        "success": result,
                                                        "position": position if result else -1
                                                    }))

                                            # 处理获取状态命令
                                            elif command == 'get_status':
                                                is_paused = hasattr(video_source, 'paused') and video_source.paused
                                                position = -1
                                                duration = -1

                                                if hasattr(video_source, 'frame_count'):
                                                    position = video_source.frame_count

                                                if hasattr(video_source, 'total_frames'):
                                                    duration = video_source.total_frames

                                                channel.send(json.dumps({
                                                    "type": "status",
                                                    "state": "paused" if is_paused else "playing",
                                                    "position": position,
                                                    "duration": duration,
                                                    "is_local_file": hasattr(video_source, 'is_local_file') and video_source.is_local_file
                                                }))

                                            # 未知命令
                                            else:
                                                channel.send(json.dumps({
                                                    "type": "error",
                                                    "message": f"未知命令: {command}"
                                                }))
                                        else:
                                            # 回显消息
                                            channel.send(f"Echo: {message}")
                                    else:
                                        # 回显消息
                                        channel.send(f"Echo: {message}")
                                except Exception as e:
                                    logger.error(f"处理消息错误: {e}")
                                    channel.send(json.dumps({
                                        "type": "error",
                                        "message": f"处理消息错误: {str(e)}"
                                    }))

                        # 处理ICE候选
                        @pc.on("icecandidate")
                        def on_icecandidate(event):
                            if event.candidate:
                                candidate_dict = {
                                    "candidate": event.candidate.candidate,
                                    "sdpMid": event.candidate.sdpMid,
                                    "sdpMLineIndex": event.candidate.sdpMLineIndex
                                }

                                asyncio.create_task(websocket.send(json.dumps({
                                    "type": "candidate",
                                    "target": peer_id,
                                    "candidate": candidate_dict
                                })))
                                logger.info(f"发送ICE候选到 {peer_id}")

                        # 设置远程描述
                        offer = RTCSessionDescription(sdp=data["sdp"], type="offer")
                        await pc.setRemoteDescription(offer)
                        logger.info("设置远程描述成功")

                        # 检查媒体方向
                        for transceiver in pc.getTransceivers():
                            if transceiver.direction is None:
                                # 如果方向为None，设置为sendrecv
                                transceiver._direction = "sendrecv"
                                logger.info(f"设置 {transceiver.kind} 轨道方向为 sendrecv")
                            logger.info(f"轨道 {transceiver.kind} 方向: {transceiver.direction}")

                        # 创建应答
                        answer = await pc.createAnswer()
                        logger.info("应答创建成功")

                        # 修改SDP，确保支持多种编解码器
                        original_sdp = answer.sdp

                        # 检查是否需要修改SDP
                        need_modify = False

                        # 检查是否强制使用特定编码器
                        if args.force_vp8 and "VP8" not in original_sdp:
                            need_modify = True
                            logger.info("强制使用VP8编码")
                        elif args.force_h264 and "H264" not in original_sdp:
                            need_modify = True
                            logger.info("强制使用H264编码")
                        # 如果没有强制指定编码器，确保同时支持VP8和H264
                        elif not args.force_vp8 and not args.force_h264 and ("VP8" not in original_sdp or "H264" not in original_sdp):
                            need_modify = True
                            logger.info("添加多种编码器支持")

                        if need_modify:
                            logger.info("修改SDP，调整编码器支持")
                            # 查找视频媒体部分
                            lines = original_sdp.split("\r\n")
                            video_section_start = -1

                            # 找到视频媒体部分的开始
                            for i, line in enumerate(lines):
                                if line.startswith("m=video"):
                                    video_section_start = i
                                    break

                            if video_section_start >= 0:
                                # 找到视频媒体部分的结束
                                video_section_end = len(lines)
                                for i in range(video_section_start + 1, len(lines)):
                                    if lines[i].startswith("m="):
                                        video_section_end = i
                                        break

                                # 提取视频媒体行
                                video_line = lines[video_section_start]
                                video_parts = video_line.split()

                                # 找到现有的编码器
                                existing_codecs = []
                                for i in range(video_section_start + 1, video_section_end):
                                    if lines[i].startswith("a=rtpmap:"):
                                        codec_parts = lines[i].split()
                                        codec_info = codec_parts[0].split(":")[1]
                                        codec_name = codec_parts[1].split("/")[0]
                                        existing_codecs.append((codec_info, codec_name))

                                # 根据需要添加或调整编码器
                                if args.force_vp8:
                                    # 强制使用VP8，将VP8放在第一位
                                    vp8_found = False
                                    for codec_info, codec_name in existing_codecs:
                                        if codec_name.upper() == "VP8":
                                            vp8_found = True
                                            # 修改视频媒体行，将VP8放在第一位
                                            payload_types = video_parts[3:]
                                            if codec_info in payload_types:
                                                payload_types.remove(codec_info)
                                                payload_types.insert(0, codec_info)
                                                video_parts[3:] = payload_types
                                                lines[video_section_start] = " ".join(video_parts)
                                            break

                                    if not vp8_found:
                                        # 添加VP8编码器
                                        payload_type = "96"  # 使用标准的VP8负载类型
                                        # 添加到媒体行
                                        video_parts.insert(3, payload_type)
                                        lines[video_section_start] = " ".join(video_parts)
                                        # 添加rtpmap和rtcp-fb
                                        lines.insert(video_section_start + 1, f"a=rtpmap:{payload_type} VP8/90000")
                                        lines.insert(video_section_start + 2, f"a=rtcp-fb:{payload_type} nack")
                                        lines.insert(video_section_start + 3, f"a=rtcp-fb:{payload_type} nack pli")
                                        lines.insert(video_section_start + 4, f"a=rtcp-fb:{payload_type} ccm fir")

                                elif args.force_h264:
                                    # 强制使用H264，将H264放在第一位
                                    h264_found = False
                                    for codec_info, codec_name in existing_codecs:
                                        if codec_name.upper() == "H264":
                                            h264_found = True
                                            # 修改视频媒体行，将H264放在第一位
                                            payload_types = video_parts[3:]
                                            if codec_info in payload_types:
                                                payload_types.remove(codec_info)
                                                payload_types.insert(0, codec_info)
                                                video_parts[3:] = payload_types
                                                lines[video_section_start] = " ".join(video_parts)
                                            break

                                    if not h264_found:
                                        # 添加H264编码器
                                        payload_type = "97"  # 使用标准的H264负载类型
                                        # 添加到媒体行
                                        video_parts.insert(3, payload_type)
                                        lines[video_section_start] = " ".join(video_parts)
                                        # 添加rtpmap和rtcp-fb
                                        lines.insert(video_section_start + 1, f"a=rtpmap:{payload_type} H264/90000")
                                        lines.insert(video_section_start + 2, f"a=rtcp-fb:{payload_type} nack")
                                        lines.insert(video_section_start + 3, f"a=rtcp-fb:{payload_type} nack pli")
                                        lines.insert(video_section_start + 4, f"a=rtcp-fb:{payload_type} ccm fir")
                                        lines.insert(video_section_start + 5, f"a=fmtp:{payload_type} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f")

                                else:
                                    # 确保同时支持VP8和H264
                                    vp8_found = False
                                    h264_found = False

                                    for codec_info, codec_name in existing_codecs:
                                        if codec_name.upper() == "VP8":
                                            vp8_found = True
                                        elif codec_name.upper() == "H264":
                                            h264_found = True

                                    # 添加缺少的编码器
                                    if not vp8_found:
                                        # 添加VP8编码器
                                        payload_type = "96"  # 使用标准的VP8负载类型
                                        # 添加到媒体行
                                        video_parts.append(payload_type)
                                        lines[video_section_start] = " ".join(video_parts)
                                        # 添加rtpmap和rtcp-fb
                                        lines.insert(video_section_end, f"a=rtpmap:{payload_type} VP8/90000")
                                        lines.insert(video_section_end + 1, f"a=rtcp-fb:{payload_type} nack")
                                        lines.insert(video_section_end + 2, f"a=rtcp-fb:{payload_type} nack pli")
                                        lines.insert(video_section_end + 3, f"a=rtcp-fb:{payload_type} ccm fir")
                                        video_section_end += 4

                                    if not h264_found:
                                        # 添加H264编码器
                                        payload_type = "97"  # 使用标准的H264负载类型
                                        # 添加到媒体行
                                        video_parts.append(payload_type)
                                        lines[video_section_start] = " ".join(video_parts)
                                        # 添加rtpmap和rtcp-fb
                                        lines.insert(video_section_end, f"a=rtpmap:{payload_type} H264/90000")
                                        lines.insert(video_section_end + 1, f"a=rtcp-fb:{payload_type} nack")
                                        lines.insert(video_section_end + 2, f"a=rtcp-fb:{payload_type} nack pli")
                                        lines.insert(video_section_end + 3, f"a=rtcp-fb:{payload_type} ccm fir")
                                        lines.insert(video_section_end + 4, f"a=fmtp:{payload_type} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f")

                            # 重新组合SDP
                            modified_sdp = "\r\n".join(lines)
                            answer.sdp = modified_sdp
                            logger.info("SDP已修改，调整了编码器支持")

                        logger.info(f"应答SDP: {answer.sdp[:100]}...")  # 只打印前100个字符

                        # 设置本地描述
                        logger.info("设置本地描述...")

                        # 在尝试设置本地描述之前，先修复所有轨道的方向
                        try:
                            for transceiver in pc.getTransceivers():
                                # 确保所有方向属性都设置为sendrecv
                                if hasattr(transceiver, 'direction') and transceiver.direction is None:
                                    transceiver.direction = "sendrecv"
                                # 尝试修复内部属性，但这可能会失败，所以用try/except包装
                                try:
                                    if hasattr(transceiver, '_direction') and transceiver._direction is None:
                                        transceiver._direction = "sendrecv"
                                    if hasattr(transceiver, '_offerDirection') and transceiver._offerDirection is None:
                                        transceiver._offerDirection = "sendrecv"
                                    if hasattr(transceiver, '_currentDirection') and transceiver._currentDirection is None:
                                        transceiver._currentDirection = "sendrecv"
                                except:
                                    pass

                                logger.info(f"已设置 {transceiver.kind} 轨道方向为 sendrecv")
                        except Exception as e:
                            logger.error(f"设置轨道方向时出错: {e}")

                        # 尝试设置本地描述
                        try:
                            await pc.setLocalDescription(answer)
                            logger.info("设置本地描述成功")
                        except ValueError as e:
                            # 如果设置本地描述失败，尝试使用一个完全新的PeerConnection
                            logger.error(f"设置本地描述失败: {e}")
                            logger.info("尝试创建新的PeerConnection...")

                            # 创建一个新的PeerConnection
                            new_pc = RTCPeerConnection(configuration=RTCConfiguration(iceServers=ICE_SERVERS))

                            # 添加相同的轨道，并明确设置方向为 sendonly
                            video_track = new_pc.addTrack(video_source)
                            logger.info(f"已添加视频轨道到新的PeerConnection: {video_track}")

                            # 设置视频轨道方向为 sendonly
                            for transceiver in new_pc.getTransceivers():
                                if transceiver.sender == video_track:
                                    transceiver.direction = "sendonly"
                                    logger.info(f"设置新PC视频轨道方向为 sendonly")

                            # 添加虚拟音频轨道
                            class DummyAudioTrack(MediaStreamTrack):
                                kind = "audio"
                                async def recv(self):
                                    # 创建一个静音音频帧，使用兼容性更好的方式
                                    from aiortc.mediastreams import AudioFrame
                                    try:
                                        # 使用自定义时间戳生成方法
                                        sample_rate = 8000
                                        time_base = fractions.Fraction(1, sample_rate)
                                        pts = int(time.time() * sample_rate)

                                        # 尝试新版本的 PyAV 方式
                                        frame = AudioFrame(channels=1, sample_rate=8000, samples=160)
                                        frame.pts = pts
                                        frame.time_base = time_base
                                    except TypeError:
                                        try:
                                            # 尝试旧版本的 PyAV 方式
                                            frame = AudioFrame(format='s16', layout='mono', samples=160)
                                            frame.sample_rate = 8000
                                            frame.pts = pts
                                            frame.time_base = time_base
                                        except Exception as e:
                                            logger.error(f"创建音频帧失败: {e}")
                                            # 最后的备选方案：创建一个空的音频帧
                                            frame = AudioFrame()
                                            frame.sample_rate = 8000
                                            frame.pts = pts
                                            frame.time_base = time_base
                                    return frame

                            # 添加虚拟音频轨道，并明确设置方向为 sendonly
                            audio_track = new_pc.addTrack(DummyAudioTrack())
                            logger.info(f"已添加音频轨道到新的PeerConnection: {audio_track}")

                            # 设置音频轨道方向为 sendonly
                            for transceiver in new_pc.getTransceivers():
                                if transceiver.sender == audio_track:
                                    transceiver.direction = "sendonly"
                                    logger.info(f"设置新PC音频轨道方向为 sendonly")

                            # 记录轨道信息
                            for transceiver in new_pc.getTransceivers():
                                logger.info(f"新PC转码器: {transceiver}, 方向: {transceiver.direction}, 当前方向: {transceiver.currentDirection if hasattr(transceiver, 'currentDirection') else 'None'}")
                                logger.info(f"新PC转码器中间描述: {transceiver.mid}, 发送器: {transceiver.sender}, 接收器: {transceiver.receiver}")

                            # 创建数据通道
                            control_channel = new_pc.createDataChannel('control')
                            logger.info(f"已创建数据通道: {control_channel}")

                            # 添加数据通道事件处理程序
                            @control_channel.on('open')
                            def on_control_channel_open():
                                logger.info(f"新PC数据通道已打开")
                                # 发送问候消息
                                control_channel.send('Hello from sender!')

                                # 发送播放端ID和信息
                                source_info = {
                                    "type": "source_info",
                                    "source_id": args.id,
                                    "name": args.name if args.name else "未知",
                                    "description": args.description if args.description else "WebRTC视频流"
                                }
                                control_channel.send(json.dumps(source_info))
                                logger.info(f"已向观众 {peer_id} 发送播放端ID: {args.id}")

                            @control_channel.on('message')
                            def on_control_channel_message(message):
                                logger.info(f"新PC数据通道收到消息: {message}")

                                # 尝试解析JSON消息
                                try:
                                    # 检查是否是JSON格式
                                    if message.startswith('{') and message.endswith('}'):
                                        data = json.loads(message)

                                        # 处理控制命令
                                        if 'command' in data:
                                            command = data['command']

                                            # 处理暂停/播放命令
                                            if command == 'pause':
                                                if hasattr(video_source, 'pause'):
                                                    result = video_source.pause()
                                                    control_channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "pause",
                                                        "success": result,
                                                        "state": "paused" if result else "playing"
                                                    }))

                                            # 处理恢复播放命令
                                            elif command == 'resume' or command == 'play':
                                                if hasattr(video_source, 'resume'):
                                                    result = video_source.resume()
                                                    control_channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "resume",
                                                        "success": result,
                                                        "state": "playing" if result else "paused"
                                                    }))

                                            # 处理切换暂停/播放状态命令
                                            elif command == 'toggle_pause':
                                                if hasattr(video_source, 'toggle_pause'):
                                                    result = video_source.toggle_pause()
                                                    is_paused = hasattr(video_source, 'paused') and video_source.paused
                                                    control_channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "toggle_pause",
                                                        "success": True,
                                                        "state": "paused" if is_paused else "playing"
                                                    }))

                                            # 处理跳转命令
                                            elif command == 'seek' and 'position' in data:
                                                if hasattr(video_source, 'seek'):
                                                    position = data['position']
                                                    result = video_source.seek(position)
                                                    control_channel.send(json.dumps({
                                                        "type": "command_result",
                                                        "command": "seek",
                                                        "success": result,
                                                        "position": position if result else -1
                                                    }))

                                            # 处理获取状态命令
                                            elif command == 'get_status':
                                                is_paused = hasattr(video_source, 'paused') and video_source.paused
                                                position = -1
                                                duration = -1

                                                if hasattr(video_source, 'frame_count'):
                                                    position = video_source.frame_count

                                                if hasattr(video_source, 'total_frames'):
                                                    duration = video_source.total_frames

                                                control_channel.send(json.dumps({
                                                    "type": "status",
                                                    "state": "paused" if is_paused else "playing",
                                                    "position": position,
                                                    "duration": duration,
                                                    "is_local_file": hasattr(video_source, 'is_local_file') and video_source.is_local_file
                                                }))

                                            # 未知命令
                                            else:
                                                control_channel.send(json.dumps({
                                                    "type": "error",
                                                    "message": f"未知命令: {command}"
                                                }))
                                        else:
                                            # 回显消息
                                            control_channel.send(f"Echo: {message}")
                                    else:
                                        # 回显消息
                                        control_channel.send(f"Echo: {message}")
                                except Exception as e:
                                    logger.error(f"处理消息错误: {e}")
                                    control_channel.send(json.dumps({
                                        "type": "error",
                                        "message": f"处理消息错误: {str(e)}"
                                    }))

                            # 设置远程描述
                            offer = RTCSessionDescription(sdp=data["sdp"], type="offer")
                            await new_pc.setRemoteDescription(offer)

                            # 创建应答
                            new_answer = await new_pc.createAnswer()

                            # 设置本地描述
                            await new_pc.setLocalDescription(new_answer)

                            # 替换旧的PeerConnection
                            peer_connections[peer_id] = new_pc
                            pc = new_pc
                            answer = new_answer

                            logger.info("使用新的PeerConnection成功设置本地描述")

                        # 发送应答
                        # 获取本地描述的SDP
                        local_sdp = None
                        if pc.localDescription:
                            local_sdp = pc.localDescription.sdp
                        else:
                            local_sdp = answer.sdp

                        await websocket.send(json.dumps({
                            "type": "answer",
                            "target": peer_id,
                            "sdp": local_sdp
                        }))
                        logger.info(f"已发送answer到 {peer_id}")
                    except Exception as e:
                        logger.error(f"处理offer错误: {e}")
                        logger.error(traceback.format_exc())  # 打印完整的堆栈跟踪

                elif data["type"] == "candidate" and "from" in data:
                    # 处理ICE候选
                    try:
                        peer_id = data["from"]
                        if peer_id in peer_connections:
                            candidate = data["candidate"]

                            # 检查是否是空候选（结束指示符）
                            if not candidate.get("candidate"):
                                logger.info(f"收到空ICE候选，忽略")
                                continue

                            # 从candidate字符串中解析信息
                            candidate_str = candidate.get("candidate", "")
                            logger.info(f"解析ICE候选: {candidate_str}")

                            # 解析candidate字符串
                            parts = candidate_str.split()
                            if len(parts) >= 8:
                                foundation = parts[0].split(':')[1]
                                component = int(parts[1])
                                protocol = parts[2].lower()
                                priority = int(parts[3])
                                ip = parts[4]
                                port = int(parts[5])
                                typ = parts[7]

                                # 创建RTCIceCandidate对象
                                ice = RTCIceCandidate(
                                    foundation=foundation,
                                    component=component,
                                    protocol=protocol,
                                    priority=priority,
                                    ip=ip,
                                    port=port,
                                    type=typ,
                                    sdpMid=candidate.get("sdpMid"),
                                    sdpMLineIndex=candidate.get("sdpMLineIndex")
                                )

                                await peer_connections[peer_id].addIceCandidate(ice)
                                logger.info(f"添加来自 {peer_id} 的ICE候选: {ip}:{port}")
                            else:
                                logger.warning(f"无法解析ICE候选: {candidate_str}")
                    except Exception as e:
                        logger.error(f"处理ICE候选错误: {e}")
                        logger.error(traceback.format_exc())  # 打印完整的堆栈跟踪

                elif data["type"] == "client_left":
                    # 客户端离开，关闭连接
                    peer_id = data["id"]
                    if peer_id in peer_connections:
                        await peer_connections[peer_id].close()
                        del peer_connections[peer_id]
                        logger.info(f"客户端 {peer_id} 已离开，关闭连接")

            except Exception as e:
                logger.error(f"处理消息错误: {e}")
                logger.error(traceback.format_exc())  # 打印完整的堆栈跟踪

if __name__ == "__main__":
    try:
        asyncio.run(handle_signaling())
    except KeyboardInterrupt:
        logger.info("程序已终止")
    finally:
        # 关闭所有连接
        for pc in peer_connections.values():
            loop = asyncio.new_event_loop()
            loop.run_until_complete(pc.close())
            loop.close()

        cv2.destroyAllWindows()




