<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin下拉菜单调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible; /* 允许下拉菜单超出容器边界 */
            min-height: 100vh; /* 使用最小高度而不是固定高度 */
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 15px 15px 0 0;
        }

        .main-content {
            padding: 30px;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .device-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
        }

        .device-info {
            margin-bottom: 15px;
        }

        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }

        .device-info-value {
            color: #495057;
            font-weight: 600;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .device-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .control-main-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            transition: all 0.2s;
        }

        .control-dropdown {
            position: relative;
            z-index: 1000;
        }

        /* 删除重复的dropdown-toggle定义，保留下面统一的定义 */

        .dropdown-menu {
            position: fixed; /* 使用fixed定位避免被父容器裁剪 */
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw; /* 限制最大宽度避免超出屏幕 */
            z-index: 999999; /* 提高到最高层级，确保在按钮之上 */
            display: none;
            padding: 15px;
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out; /* 减少动画时间，避免闪烁 */
            visibility: hidden; /* 初始隐藏 */
        }

        /* 下拉菜单显示状态 */
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }

        /* 确保下拉菜单容器有正确的定位 */
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }

        /* 下拉按钮样式优化 */
        .dropdown-toggle {
            position: relative;
            z-index: 100000; /* 确保按钮在下拉菜单之上 */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .dropdown-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .dropdown-toggle.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        /* 删除重复的dropdown-menu.show定义，保留上面带!important的版本 */

        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }

        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }

        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000000;
            max-width: 300px;
        }

        @media (max-width: 768px) {
            .dropdown-menu {
                min-width: 250px;
                /* 移除固定的left: 0，让JavaScript动态设置位置 */
                right: auto;
            }
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        调试信息将显示在这里
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 Admin下拉菜单调试</h1>
            <p>测试下拉菜单位置和闪烁问题</p>
        </div>

        <div class="main-content">
            <h2>📊 模拟设备列表</h2>
            
            <div class="devices-grid">
                <!-- 设备1 -->
                <div class="device-card">
                    <div class="device-info">
                        <div class="device-info-item">
                            <span class="device-info-label">设备ID:</span>
                            <span class="device-info-value">gamev-test001</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value">ocean3</span>
                        </div>
                    </div>
                    
                    <div class="device-controls">
                        <button class="control-main-btn">
                            🔄 重启服务
                        </button>
                        <div class="control-dropdown">
                            <button class="dropdown-toggle" onclick="toggleDropdown('test001')">
                                ⚙️
                            </button>
                            <div class="dropdown-menu" id="dropdown-test001">
                                <div class="dropdown-columns">
                                    <div class="dropdown-section">
                                        <h4>服务控制</h4>
                                        <button class="dropdown-btn">🚀 启动服务</button>
                                        <button class="dropdown-btn">⏹️ 停止服务</button>
                                        <button class="dropdown-btn">🔄 重启服务</button>
                                        <h4>视频控制</h4>
                                        <button class="dropdown-btn">🎥 视频参数设置</button>
                                        <button class="dropdown-btn">📸 视频流截屏</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>游戏控制</h4>
                                        <button class="dropdown-btn">🎮 游戏设置</button>
                                        <h4>日志管理</h4>
                                        <button class="dropdown-btn">📝 开启日志显示</button>
                                        <button class="dropdown-btn">🚫 关闭日志显示</button>
                                        <button class="dropdown-btn">📥 下载日志(FTP)</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>网络配置</h4>
                                        <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                        <button class="dropdown-btn">📡 发送网络配置</button>
                                        <h4>系统控制</h4>
                                        <button class="dropdown-btn">🔄 重启设备</button>
                                        <button class="dropdown-btn">📦 升级应用</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备2 -->
                <div class="device-card">
                    <div class="device-info">
                        <div class="device-info-item">
                            <span class="device-info-label">设备ID:</span>
                            <span class="device-info-value">gamev-test002</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">状态:</span>
                            <span class="device-info-value status-online">● 在线</span>
                        </div>
                        <div class="device-info-item">
                            <span class="device-info-label">游戏:</span>
                            <span class="device-info-value">mygame</span>
                        </div>
                    </div>
                    
                    <div class="device-controls">
                        <button class="control-main-btn">
                            🔄 重启服务
                        </button>
                        <div class="control-dropdown">
                            <button class="dropdown-toggle" onclick="toggleDropdown('test002')">
                                ⚙️
                            </button>
                            <div class="dropdown-menu" id="dropdown-test002">
                                <div class="dropdown-columns">
                                    <div class="dropdown-section">
                                        <h4>服务控制</h4>
                                        <button class="dropdown-btn">🚀 启动服务</button>
                                        <button class="dropdown-btn">⏹️ 停止服务</button>
                                        <button class="dropdown-btn">🔄 重启服务</button>
                                        <h4>视频控制</h4>
                                        <button class="dropdown-btn">🎥 视频参数设置</button>
                                        <button class="dropdown-btn">📸 视频流截屏</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>游戏控制</h4>
                                        <button class="dropdown-btn">🎮 游戏设置</button>
                                        <h4>日志管理</h4>
                                        <button class="dropdown-btn">📝 开启日志显示</button>
                                        <button class="dropdown-btn">🚫 关闭日志显示</button>
                                        <button class="dropdown-btn">📥 下载日志(FTP)</button>
                                    </div>
                                    <div class="dropdown-section">
                                        <h4>网络配置</h4>
                                        <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                        <button class="dropdown-btn">📡 发送网络配置</button>
                                        <h4>系统控制</h4>
                                        <button class="dropdown-btn">🔄 重启设备</button>
                                        <button class="dropdown-btn">📦 升级应用</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>调试信息:</strong><br>
                ${message}<br>
                <small>时间: ${new Date().toLocaleTimeString()}</small>
            `;
        }

        function toggleDropdown(deviceId) {
            updateDebugInfo(`点击设备: ${deviceId}`);
            
            // 关闭所有其他下拉菜单和按钮状态
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${deviceId}`) {
                    menu.classList.remove('show');
                    // 重置位置样式
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                }
            });
            document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                if (!btn.onclick.toString().includes(deviceId)) {
                    btn.classList.remove('active');
                }
            });

            // 切换当前下拉菜单和按钮状态
            const dropdown = document.getElementById(`dropdown-${deviceId}`);
            const button = document.querySelector(`[onclick="toggleDropdown('${deviceId}')"]`);

            if (dropdown && button) {
                const isShowing = dropdown.classList.contains('show');

                if (isShowing) {
                    // 隐藏下拉菜单
                    dropdown.classList.remove('show');
                    button.classList.remove('active');
                    // 重置位置样式
                    dropdown.style.removeProperty('left');
                    dropdown.style.removeProperty('top');
                    dropdown.style.removeProperty('visibility');
                    updateDebugInfo(`隐藏下拉菜单: ${deviceId}`);
                } else {
                    // 先计算位置，再显示下拉菜单，避免闪烁
                    adjustDropdownPosition(dropdown, button);
                    
                    // 显示下拉菜单
                    dropdown.classList.add('show');
                    button.classList.add('active');
                    updateDebugInfo(`显示下拉菜单: ${deviceId}`);
                }
            }
        }

        function adjustDropdownPosition(dropdown, button) {
            // 先显示下拉菜单以获取实际尺寸
            dropdown.style.visibility = 'hidden';
            dropdown.style.display = 'block';
            
            const buttonRect = button.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            const dropdownWidth = dropdownRect.width || 720;
            const dropdownHeight = dropdownRect.height || 400;

            // 计算最佳位置 - 默认在按钮正下方
            let left = buttonRect.left;
            let top = buttonRect.bottom + 8; // 按钮下方，留8px间距

            // 检查右边界
            if (left + dropdownWidth > windowWidth - 20) {
                // 右对齐到按钮右边
                left = buttonRect.right - dropdownWidth;
            }

            // 再次检查左边界
            if (left < 20) {
                left = 20; // 最小左边距
            }

            // 检查下边界
            if (top + dropdownHeight > windowHeight - 20) {
                // 显示在按钮上方
                top = buttonRect.top - dropdownHeight - 8;
            }

            // 确保不超出上边界
            if (top < 20) {
                top = 20; // 最小上边距
            }

            // 应用位置，使用setProperty确保优先级
            dropdown.style.setProperty('left', `${left}px`, 'important');
            dropdown.style.setProperty('top', `${top}px`, 'important');
            dropdown.style.setProperty('visibility', 'visible', 'important');
            
            updateDebugInfo(`位置计算: left=${left}, top=${top}, 按钮位置: ${buttonRect.left}, ${buttonRect.bottom}`);
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.style.removeProperty('left');
                    menu.style.removeProperty('top');
                    menu.style.removeProperty('visibility');
                });
                document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                    btn.classList.remove('active');
                });
                updateDebugInfo('点击外部，关闭所有下拉菜单');
            }
        });

        updateDebugInfo('页面加载完成，准备测试');
    </script>
</body>
</html>
