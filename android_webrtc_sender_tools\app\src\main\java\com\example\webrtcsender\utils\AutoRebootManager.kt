package com.example.webrtcsender.utils

import android.content.Context
import com.example.webrtcsender.utils.Logger
import kotlinx.coroutines.*
import ZtlApi.ZtlManager

/**
 * 自动重启管理器 - 负责在视频推流失败时执行自动重启
 */
class AutoRebootManager private constructor() {
    
    companion object {
        private const val TAG = "AutoRebootManager"
        private const val REBOOT_DELAY_SECONDS = 10
        
        @Volatile
        private var INSTANCE: AutoRebootManager? = null
        
        fun getInstance(): AutoRebootManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AutoRebootManager().also { INSTANCE = it }
            }
        }
    }
    
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var isRebootScheduled = false
    
    /**
     * 触发自动重启（视频推流失败时调用）
     */
    fun triggerAutoReboot(
        context: Context,
        reason: String,
        errorDetails: String? = null
    ) {
        if (isRebootScheduled) {
            Logger.w(TAG, "⚠️ 重启已安排，忽略重复请求")
            return
        }
        
        coroutineScope.launch {
            try {
                Logger.w(TAG, "🚨 触发自动重启: $reason")
                
                // 检查是否可以重启
                val deviceLogManager = DeviceLogManager.getInstance()
                if (!deviceLogManager.canReboot(context)) {
                    Logger.e(TAG, "❌ 今日重启次数已达上限，取消自动重启")
                    
                    // 记录重启被拒绝的日志
                    deviceLogManager.logDeviceEvent(
                        context,
                        "auto_reboot_rejected",
                        "ERROR",
                        "自动重启被拒绝：今日重启次数已达上限",
                        "原因: $reason, 详情: $errorDetails"
                    )
                    return@launch
                }
                
                isRebootScheduled = true
                
                // 记录重启触发日志
                deviceLogManager.logDeviceEvent(
                    context,
                    "auto_reboot_triggered",
                    "CRITICAL",
                    "自动重启已触发",
                    "原因: $reason, 详情: $errorDetails, 延迟: ${REBOOT_DELAY_SECONDS}秒"
                )
                
                // 增加重启计数
                val rebootCount = deviceLogManager.incrementRebootCount(context)
                Logger.i(TAG, "📊 重启计数已更新: $rebootCount")
                
                // 上传日志到FTP
                Logger.i(TAG, "📤 重启前上传日志到FTP...")
                uploadLogsBeforeReboot(context)
                
                // 延迟重启
                Logger.i(TAG, "⏰ 安排${REBOOT_DELAY_SECONDS}秒后重启设备")
                delay(REBOOT_DELAY_SECONDS * 1000L)
                
                // 执行重启
                executeReboot(context, reason)
                
            } catch (e: Exception) {
                Logger.e(TAG, "❌ 自动重启执行失败", e)
                isRebootScheduled = false
                
                // 记录重启失败日志
                DeviceLogManager.getInstance().logDeviceEvent(
                    context,
                    "auto_reboot_failed",
                    "ERROR",
                    "自动重启执行失败",
                    "错误: ${e.message}, 原因: $reason"
                )
            }
        }
    }
    
    /**
     * 重启前上传日志
     */
    private suspend fun uploadLogsBeforeReboot(context: Context) {
        try {
            val deviceId = DeviceUtils.getDeviceId(context)
            val uploadResult = LogManager.uploadLogToFtp(context, deviceId)

            if (uploadResult.success) {
                Logger.i(TAG, "✅ 重启前日志上传成功")
                DeviceLogManager.getInstance().logDeviceEvent(
                    context,
                    "pre_reboot_log_upload",
                    "INFO",
                    "重启前日志上传成功",
                    "设备ID: $deviceId"
                )
            } else {
                Logger.w(TAG, "⚠️ 重启前日志上传失败: ${uploadResult.message}")
                DeviceLogManager.getInstance().logDeviceEvent(
                    context,
                    "pre_reboot_log_upload_failed",
                    "WARNING",
                    "重启前日志上传失败: ${uploadResult.message}",
                    "设备ID: $deviceId"
                )
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 重启前日志上传异常", e)
            DeviceLogManager.getInstance().logDeviceEvent(
                context,
                "pre_reboot_log_upload_error",
                "ERROR",
                "重启前日志上传异常",
                "错误: ${e.message}"
            )
        }
    }
    
    /**
     * 执行设备重启
     */
    private suspend fun executeReboot(context: Context, reason: String) {
        try {
            Logger.i(TAG, "🔄 开始执行设备重启")
            
            // 记录重启开始日志
            DeviceLogManager.getInstance().logDeviceEvent(
                context,
                "device_reboot_start",
                "CRITICAL",
                "设备重启开始执行",
                "原因: $reason, 方法: ZtlManager"
            )
            
            // 等待日志发送完成
            delay(2000)
            
            // 使用ZtlManager执行重启
            withContext(Dispatchers.IO) {
                try {
                    ZtlManager.GetInstance().setContext(context)
                    ZtlManager.GetInstance().reboot(REBOOT_DELAY_SECONDS)
                    Logger.i(TAG, "✅ ZtlManager重启命令已执行")
                } catch (e: Exception) {
                    Logger.e(TAG, "❌ ZtlManager重启失败，尝试备用方法", e)
                    
                    // 备用重启方法
                    executeBackupReboot(context)
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 设备重启执行失败", e)
            
            // 记录重启失败日志
            DeviceLogManager.getInstance().logDeviceEvent(
                context,
                "device_reboot_failed",
                "ERROR",
                "设备重启执行失败",
                "错误: ${e.message}, 原因: $reason"
            )
            
            isRebootScheduled = false
        }
    }
    
    /**
     * 备用重启方法
     */
    private fun executeBackupReboot(context: Context) {
        try {
            Logger.i(TAG, "🔄 执行备用重启方法")
            
            // 方法1: 使用Runtime执行shell命令
            try {
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", "reboot"))
                process.waitFor()
                Logger.i(TAG, "✅ Shell重启命令已执行")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ Shell重启失败: ${e.message}")
            }
            
            // 方法2: 使用PowerManager
            try {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
                powerManager.reboot("auto_reboot")
                Logger.i(TAG, "✅ PowerManager重启命令已执行")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ PowerManager重启失败: ${e.message}")
            }
            
            Logger.e(TAG, "❌ 所有备用重启方法都失败")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 备用重启方法执行异常", e)
        }
    }
    
    /**
     * 取消已安排的重启
     */
    fun cancelScheduledReboot() {
        if (isRebootScheduled) {
            Logger.i(TAG, "🚫 取消已安排的重启")
            isRebootScheduled = false
        }
    }
    
    /**
     * 检查是否有重启安排
     */
    fun isRebootScheduled(): Boolean {
        return isRebootScheduled
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        coroutineScope.cancel()
        isRebootScheduled = false
        Logger.i(TAG, "🧹 自动重启管理器资源已清理")
    }
}
