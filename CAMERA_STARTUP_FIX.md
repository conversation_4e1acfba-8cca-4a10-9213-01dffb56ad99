# 摄像头启动问题修复

## 🔍 **问题分析**

从您提供的日志可以看出两个主要问题：

### **1. MediaSource重复释放问题**
```
java.lang.IllegalStateException: MediaSource has been disposed.
at org.webrtc.VideoSource.dispose(VideoSource.java:160)
at com.example.webrtcsender.webrtc.WebRTCClient.setSharedVideoSource(WebRTCClient.kt:2157)
```

### **2. 摄像头硬件错误**
```
Camera2Session: Error: Failed to start capture request
CAMERA_ERROR (3): createDefaultRequest:1276: Camera 112: Error creating default request for template 3: Function not implemented (-38)
```

## 🔧 **已实施的修复方案**

### **1. 修复MediaSource重复释放问题**

#### **问题原因：**
在`setSharedVideoSource`方法中直接调用`dispose()`，没有检查MediaSource是否已经被释放。

#### **修复方案：**
```kotlin
// 修复前 ❌
fun setSharedVideoSource(videoSource: VideoSource) {
    // 释放之前的视频源
    sharedVideoSource?.dispose()  // 可能重复释放
    sharedVideoTrack?.dispose()
    
    // 创建新的共享视频轨道
    sharedVideoSource = videoSource
    sharedVideoTrack = peerConnectionFactory.createVideoTrack("shared_video", videoSource)
}

// 修复后 ✅
fun setSharedVideoSource(videoSource: VideoSource) {
    // 安全释放之前的视频源
    safeDisposeVideoTrack(sharedVideoTrack)
    safeDisposeVideoSource(sharedVideoSource)
    
    // 创建新的共享视频轨道
    sharedVideoSource = videoSource
    sharedVideoTrack = peerConnectionFactory.createVideoTrack("shared_video", videoSource)
}
```

#### **安全释放机制：**
```kotlin
private fun safeDisposeVideoSource(videoSource: VideoSource?) {
    if (videoSource == null) return
    
    try {
        // 检查MediaSource是否已经被释放
        val field = MediaSource::class.java.getDeclaredField("nativeSource")
        field.isAccessible = true
        val nativeSource = field.getLong(videoSource)
        
        if (nativeSource != 0L) {
            videoSource.dispose()
            Logger.d(TAG, "VideoSource已释放")
        } else {
            Logger.d(TAG, "VideoSource已经被释放，跳过dispose")
        }
    } catch (e: IllegalStateException) {
        if (e.message?.contains("disposed") == true) {
            Logger.d(TAG, "VideoSource已经被释放，忽略错误")
        } else {
            Logger.w(TAG, "释放VideoSource失败: ${e.message}")
        }
    }
}
```

### **2. 改进摄像头选择和错误处理**

#### **增强的摄像头选择逻辑：**
```kotlin
// 修复前 ❌
fun createCameraVideoSource(cameraId: String): VideoSource {
    val cameraEnumerator = Camera2Enumerator(context)
    val deviceNames = cameraEnumerator.deviceNames
    
    // 简单选择摄像头
    val targetCameraId = when (cameraId) {
        "0" -> deviceNames.find { cameraEnumerator.isBackFacing(it) } ?: deviceNames[0]
        "1" -> deviceNames.find { cameraEnumerator.isFrontFacing(it) } ?: deviceNames[0]
        else -> deviceNames[0]
    }
    
    // 直接创建，没有错误处理
    val videoCapturer = cameraEnumerator.createCapturer(targetCameraId, null)
}

// 修复后 ✅
fun createCameraVideoSource(cameraId: String): VideoSource {
    Logger.i(TAG, "🎥 [摄像头] 开始创建摄像头视频源: $cameraId")
    
    try {
        val cameraEnumerator = Camera2Enumerator(context)
        val deviceNames = cameraEnumerator.deviceNames
        Logger.i(TAG, "🎥 [摄像头] 可用摄像头列表: ${deviceNames.joinToString()}")
        
        // 智能选择摄像头
        val targetCameraId = selectBestCamera(cameraEnumerator, deviceNames, cameraId)
        Logger.i(TAG, "🎥 [摄像头] 选择的摄像头: $targetCameraId")
        
        // 验证摄像头可用性
        if (!deviceNames.contains(targetCameraId)) {
            val fallbackCamera = deviceNames.firstOrNull()
                ?: throw IllegalStateException("没有可用的摄像头")
            Logger.i(TAG, "🎥 [摄像头] 使用备用摄像头: $fallbackCamera")
            return createCameraVideoSourceWithId(cameraEnumerator, fallbackCamera)
        }
        
        return createCameraVideoSourceWithId(cameraEnumerator, targetCameraId)
    } catch (e: Exception) {
        Logger.e(TAG, "🎥 [摄像头] 创建摄像头视频源失败", e)
        throw e
    }
}
```

#### **智能摄像头选择：**
```kotlin
private fun selectBestCamera(cameraEnumerator: Camera2Enumerator, deviceNames: Array<String>, requestedId: String): String {
    return when (requestedId) {
        "0" -> { // 后置摄像头
            val backCamera = deviceNames.find { cameraEnumerator.isBackFacing(it) }
            if (backCamera != null) {
                Logger.i(TAG, "🎥 [摄像头] 找到后置摄像头: $backCamera")
                backCamera
            } else {
                Logger.w(TAG, "🎥 [摄像头] 未找到后置摄像头，使用第一个可用摄像头")
                deviceNames[0]
            }
        }
        "1" -> { // 前置摄像头
            val frontCamera = deviceNames.find { cameraEnumerator.isFrontFacing(it) }
            if (frontCamera != null) {
                Logger.i(TAG, "🎥 [摄像头] 找到前置摄像头: $frontCamera")
                frontCamera
            } else {
                Logger.w(TAG, "🎥 [摄像头] 未找到前置摄像头，使用第一个可用摄像头")
                deviceNames[0]
            }
        }
        else -> { // 指定索引
            val index = requestedId.toIntOrNull()
            if (index != null && index >= 0 && index < deviceNames.size) {
                Logger.i(TAG, "🎥 [摄像头] 使用索引摄像头: ${deviceNames[index]}")
                deviceNames[index]
            } else {
                Logger.w(TAG, "🎥 [摄像头] 无效的摄像头索引: $requestedId，使用第一个可用摄像头")
                deviceNames[0]
            }
        }
    }
}
```

#### **详细的摄像头事件处理：**
```kotlin
private fun createCameraVideoSourceWithId(cameraEnumerator: Camera2Enumerator, cameraId: String): VideoSource {
    val videoCapturer = cameraEnumerator.createCapturer(cameraId, object : CameraVideoCapturer.CameraEventsHandler {
        override fun onCameraError(errorDescription: String?) {
            Logger.e(TAG, "🎥 [摄像头] 摄像头错误: $errorDescription")
        }
        
        override fun onCameraDisconnected() {
            Logger.w(TAG, "🎥 [摄像头] 摄像头断开连接")
        }
        
        override fun onCameraFreezed(errorDescription: String?) {
            Logger.w(TAG, "🎥 [摄像头] 摄像头冻结: $errorDescription")
        }
        
        override fun onCameraOpening(cameraName: String?) {
            Logger.i(TAG, "🎥 [摄像头] 正在打开摄像头: $cameraName")
        }
        
        override fun onFirstFrameAvailable() {
            Logger.i(TAG, "🎥 [摄像头] ✅ 第一帧可用")
        }
        
        override fun onCameraClosed() {
            Logger.i(TAG, "🎥 [摄像头] 摄像头已关闭")
        }
    }) ?: throw IllegalStateException("无法创建视频捕获器: $cameraId")
    
    // 其余创建逻辑...
}
```

## 📱 **新的摄像头启动日志**

### **成功启动时的日志：**
```kotlin
🎥 [摄像头] 开始创建摄像头视频源: 1
🎥 [摄像头] 可用摄像头列表: [0, 1]
🎥 [摄像头] 找到前置摄像头: 1
🎥 [摄像头] 选择的摄像头: 1
🎥 [摄像头] 使用摄像头ID创建视频源: 1
🎥 [摄像头] 正在打开摄像头: 1
🎥 [摄像头] VideoSource已创建
🎥 [摄像头] 配置参数: 分辨率=1280x720, 帧率=60 fps
🎥 [摄像头] SurfaceTextureHelper已创建
🎥 [摄像头] VideoCapturer已初始化
🎥 [摄像头] ✅ 摄像头捕获已启动: 1280x720@60fps
🎥 [摄像头] ✅ 第一帧可用
```

### **错误处理时的日志：**
```kotlin
🎥 [摄像头] 摄像头不在设备列表中: 112，使用第一个可用摄像头
🎥 [摄像头] 使用备用摄像头: 0
🎥 [摄像头] 摄像头错误: CAMERA_ERROR (3): Function not implemented
🎥 [摄像头] 创建摄像头视频源失败: IllegalStateException
```

## 🎯 **关于摄像头硬件错误**

### **错误分析：**
```
CAMERA_ERROR (3): createDefaultRequest:1276: Camera 112: Error creating default request for template 3: Function not implemented (-38)
```

这个错误表明：
- ✅ **摄像头ID 112不存在** - 可能是虚拟摄像头或错误ID
- ✅ **硬件不支持某些功能** - 模板3可能不被支持
- ✅ **驱动程序问题** - 某些设备的摄像头驱动有问题

### **修复策略：**
1. **智能摄像头选择** - 自动选择可用的摄像头
2. **备用摄像头机制** - 失败时自动切换到其他摄像头
3. **详细错误日志** - 帮助诊断具体问题
4. **优雅降级** - 确保应用不会崩溃

## 🚀 **预期效果**

现在摄像头启动会：
- ✅ **不再出现MediaSource重复释放错误**
- ✅ **智能选择可用的摄像头**
- ✅ **提供详细的启动日志**
- ✅ **优雅处理硬件错误**
- ✅ **自动备用摄像头机制**

### **不再出现的错误：**
```
❌ MediaSource has been disposed.
❌ 无法创建视频捕获器
```

### **新的成功体验：**
```
✅ 摄像头自动选择和启动
✅ 详细的调试信息
✅ 优雅的错误恢复
```

现在摄像头应该能正常启动，即使遇到硬件问题也会自动选择备用摄像头！
