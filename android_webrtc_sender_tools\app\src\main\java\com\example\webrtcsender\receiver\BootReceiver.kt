package com.example.webrtcsender.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.example.webrtcsender.ui.MainActivity
import com.example.webrtcsender.utils.Logger

/**
 * 开机自启动广播接收器
 * 系统重启后自动启动应用
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            val action = intent.action
            Logger.i(TAG, "🔄 收到开机广播: $action")
            
            when (action) {
                Intent.ACTION_BOOT_COMPLETED,
                "android.intent.action.QUICKBOOT_POWERON",
                "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                    Logger.i(TAG, "🚀 系统启动完成，准备自动启动应用")
                    
                    // 延迟10秒启动应用，确保系统完全启动
                    Handler(Looper.getMainLooper()).postDelayed({
                        startMainActivity(context)
                    }, 10000)
                }
                else -> {
                    Logger.d(TAG, "📡 未处理的广播: $action")
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理开机广播失败", e)
        }
    }
    
    /**
     * 启动主活动
     */
    private fun startMainActivity(context: Context) {
        try {
            Logger.i(TAG, "🚀 自动启动主应用")
            
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("auto_start", true) // 标记为自动启动
            }
            
            context.startActivity(intent)
            Logger.i(TAG, "✅ 主应用启动成功")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 自动启动主应用失败", e)
        }
    }
}
