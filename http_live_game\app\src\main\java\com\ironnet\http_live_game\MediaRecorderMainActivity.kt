package com.ironnet.http_live_game

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.ironnet.http_live_game.streaming.MediaRecorderStreamingService
import com.ironnet.http_live_game.streaming.StreamConfig
import com.ironnet.http_live_game.ui.theme.Http_live_gameTheme

class MediaRecorderMainActivity : ComponentActivity() {
    private val TAG = "MediaRecorderMainActivity"

    private var streamingService: MediaRecorderStreamingService? = null
    private var bound = false
    private var mediaProjection: MediaProjection? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as MediaRecorderStreamingService.LocalBinder
            streamingService = binder.getService()
            bound = true
            Log.d(TAG, "MediaRecorder Service connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            streamingService = null
            bound = false
            Log.d(TAG, "MediaRecorder Service disconnected")
        }
    }
    
    // 屏幕捕获权限请求
    private val screenCaptureRequest = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK && result.data != null) {
            try {
                val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
                mediaProjection = projectionManager.getMediaProjection(result.resultCode, result.data!!)
                
                if (mediaProjection == null) {
                    Log.e(TAG, "Failed to get MediaProjection from result")
                    Toast.makeText(this, "无法获取屏幕捕获权限", Toast.LENGTH_SHORT).show()
                    return@registerForActivityResult
                }

                // 设置媒体投影
                streamingService?.setMediaProjection(mediaProjection!!)
                
                // 启动流
                streamingService?.startStreaming()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting streaming: ${e.message}", e)
                Toast.makeText(this, "启动流媒体失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        } else {
            // 用户取消了屏幕捕获权限请求
            Toast.makeText(this, "屏幕捕获权限被拒绝", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 启动并绑定服务
        val serviceIntent = Intent(this, MediaRecorderStreamingService::class.java)
        startForegroundService(serviceIntent)
        bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)

        setContent {
            Http_live_gameTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MediaRecorderStreamingApp(
                        onStartStreaming = { config ->
                            startStreaming(config)
                        },
                        onStopStreaming = {
                            stopStreaming()
                        },
                        getStreamUrl = {
                            streamingService?.getStreamUrl() ?: "Not available"
                        },
                        isStreaming = {
                            streamingService?.isStreaming() ?: false
                        }
                    )
                }
            }
        }
    }

    override fun onDestroy() {
        // 确保在Activity销毁时停止流媒体服务
        if (streamingService?.isStreaming() == true) {
            streamingService?.stopStreaming()
        }

        if (bound) {
            unbindService(serviceConnection)
            bound = false
        }
        super.onDestroy()
    }

    private fun startStreaming(config: StreamConfig) {
        // 保存流配置
        streamingService?.setStreamConfig(config)
        
        // 请求屏幕捕获权限
        val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        screenCaptureRequest.launch(projectionManager.createScreenCaptureIntent())
    }

    private fun stopStreaming() {
        streamingService?.stopStreaming()
        mediaProjection?.stop()
        mediaProjection = null
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun MediaRecorderStreamingApp(
    onStartStreaming: (StreamConfig) -> Unit,
    onStopStreaming: () -> Unit,
    getStreamUrl: () -> String,
    isStreaming: () -> Boolean
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current

    var isStreamingState by remember { mutableStateOf(false) }
    var streamUrl by remember { mutableStateOf("") }

    // 流配置
    var selectedResolution by remember { mutableStateOf(1) } // 默认720p
    var selectedFrameRate by remember { mutableStateOf(2) } // 默认30fps
    var selectedBitRate by remember { mutableStateOf(2) } // 默认2Mbps
    var port by remember { mutableStateOf("8080") }

    // 下拉菜单状态
    var showResolutionDropdown by remember { mutableStateOf(false) }
    var showFrameRateDropdown by remember { mutableStateOf(false) }
    var showBitRateDropdown by remember { mutableStateOf(false) }

    // 权限状态
    val permissionsState = rememberMultiplePermissionsState(
        permissions = listOf(
            Manifest.permission.INTERNET,
            Manifest.permission.FOREGROUND_SERVICE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.POST_NOTIFICATIONS
            } else {
                ""
            }
        ).filter { it.isNotEmpty() }
    )

    // 检查权限是否已授予
    LaunchedEffect(Unit) {
        if (!permissionsState.allPermissionsGranted) {
            permissionsState.launchMultiplePermissionRequest()
        }
    }

    // 更新流状态
    LaunchedEffect(Unit) {
        isStreamingState = isStreaming()
        if (isStreamingState) {
            streamUrl = getStreamUrl()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top
    ) {
        // 标题
        Text(
            text = "MediaRecorder屏幕直播",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 权限卡片
        if (!permissionsState.allPermissionsGranted) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "需要权限",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("此应用需要网络和存储权限才能正常运行。")
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = { permissionsState.launchMultiplePermissionRequest() }
                    ) {
                        Text("授予权限")
                    }
                }
            }
        }

        // 流URL卡片（当流活跃时）
        if (isStreamingState) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "流URL",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(streamUrl)
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            clipboardManager.setText(AnnotatedString(streamUrl))
                            Toast.makeText(context, "URL已复制到剪贴板", Toast.LENGTH_SHORT).show()
                        }
                    ) {
                        Text("复制URL")
                    }
                }
            }
        }

        // 视频配置
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "视频配置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(16.dp))

                // 分辨率
                Column {
                    Text("分辨率")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showResolutionDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val resolution = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution]
                        Text("${resolution.first}x${resolution.second}")
                    }
                    DropdownMenu(
                        expanded = showResolutionDropdown,
                        onDismissRequest = { showResolutionDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_RESOLUTIONS.forEachIndexed { index, resolution ->
                            DropdownMenuItem(
                                text = { Text("${resolution.first}x${resolution.second}") },
                                onClick = {
                                    selectedResolution = index
                                    showResolutionDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // 帧率
                Column {
                    Text("帧率")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showFrameRateDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("${StreamConfig.SUPPORTED_FRAME_RATES[selectedFrameRate]} fps")
                    }
                    DropdownMenu(
                        expanded = showFrameRateDropdown,
                        onDismissRequest = { showFrameRateDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_FRAME_RATES.forEachIndexed { index, frameRate ->
                            DropdownMenuItem(
                                text = { Text("$frameRate fps") },
                                onClick = {
                                    selectedFrameRate = index
                                    showFrameRateDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // 比特率
                Column {
                    Text("比特率")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showBitRateDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val bitRate = StreamConfig.SUPPORTED_BIT_RATES[selectedBitRate]
                        Text("${bitRate / 1_000_000} Mbps")
                    }
                    DropdownMenu(
                        expanded = showBitRateDropdown,
                        onDismissRequest = { showBitRateDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_BIT_RATES.forEachIndexed { index, bitRate ->
                            DropdownMenuItem(
                                text = { Text("${bitRate / 1_000_000} Mbps") },
                                onClick = {
                                    selectedBitRate = index
                                    showBitRateDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // 端口
                Column {
                    Text("端口")
                    Spacer(modifier = Modifier.height(4.dp))
                    TextField(
                        value = port,
                        onValueChange = {
                            if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                port = it
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        // 控制按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Button(
                onClick = {
                    if (isStreamingState) {
                        onStopStreaming()
                        isStreamingState = false
                        streamUrl = ""
                    } else {
                        val config = StreamConfig(
                            width = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution].first,
                            height = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution].second,
                            frameRate = StreamConfig.SUPPORTED_FRAME_RATES[selectedFrameRate],
                            bitRate = StreamConfig.SUPPORTED_BIT_RATES[selectedBitRate],
                            port = port.toIntOrNull() ?: 8080
                        )
                        onStartStreaming(config)
                        // 状态将在流启动后更新
                    }
                },
                modifier = Modifier.weight(1f)
            ) {
                Text(if (isStreamingState) "停止流媒体" else "开始流媒体")
            }
        }

        // 说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "使用说明",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text("1. 配置视频参数")
                Text("2. 点击\"开始流媒体\"按钮")
                Text("3. 授予屏幕录制权限")
                Text("4. 复制生成的URL")
                Text("5. 在浏览器中打开URL观看直播")
                Spacer(modifier = Modifier.height(8.dp))
                Text("注意：此应用使用MediaRecorder捕获屏幕并提供HTTP流，支持大多数现代浏览器。")
            }
        }
    }
}
