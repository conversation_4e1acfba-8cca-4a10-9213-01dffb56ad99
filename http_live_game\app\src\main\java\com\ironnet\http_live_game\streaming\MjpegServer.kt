package com.ironnet.http_live_game.streaming

import android.util.Log
import fi.iki.elonen.NanoHTTPD
import java.io.ByteArrayInputStream
import java.util.Random

/**
 * MJPEG服务器
 */
class MjpegServer(port: Int) : NanoHTTPD(port) {
    companion object {
        private const val TAG = "MjpegServer"
        private const val BOUNDARY = "mjpegboundary"
    }

    // 帧列表
    private val frames = mutableListOf<ByteArray>()

    // 添加帧
    fun addFrame(frame: ByteArray) {
        synchronized(frames) {
            frames.add(frame)
            // 限制帧数量
            while (frames.size > 30) {
                frames.removeAt(0)
            }
        }
    }

    // 清空帧
    fun clearFrames() {
        synchronized(frames) {
            frames.clear()
        }
    }

    // 获取帧数量
    fun getFrameCount(): Int {
        synchronized(frames) {
            return frames.size
        }
    }

    override fun serve(session: IHTTPSession): Response {
        val uri = session.uri
        Log.d(TAG, "HTTP request: $uri")

        return when {
            uri == "/stream.mjpeg" -> serveMjpegStream()
            uri == "/" -> serveHtml()
            else -> newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "Not found")
        }
    }

    /**
     * 提供MJPEG流
     */
    private fun serveMjpegStream(): Response {
        // 创建一个简单的响应
        val response = newFixedLengthResponse(
            Response.Status.OK,
            "multipart/x-mixed-replace; boundary=$BOUNDARY",
            "MJPEG Stream"
        )

        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
        response.addHeader("Pragma", "no-cache")
        response.addHeader("Expires", "0")

        // 创建一个简单的JPEG图像
        val width = 320
        val height = 240
        val dummyFrame = createDummyJpeg(width, height)

        // 创建一个线程来发送MJPEG流
        Thread {
            try {
                // 创建一个输出流
                val outputStream = java.io.ByteArrayOutputStream()

                // 发送初始帧
                sendMjpegFrame(outputStream, dummyFrame)

                // 持续发送帧
                var frameIndex = 0
                var lastFrameCount = 0
                var isRunning = true

                while (isRunning) {
                    Thread.sleep(33) // 约30fps

                    try {
                        // 获取当前帧数量
                        val currentFrameCount = getFrameCount()

                        // 如果有新帧，发送最新的帧
                        if (currentFrameCount > 0 && (currentFrameCount != lastFrameCount || frameIndex >= currentFrameCount)) {
                            frameIndex = currentFrameCount - 1
                            lastFrameCount = currentFrameCount

                            // 获取帧
                            val frame = synchronized(frames) {
                                if (frames.isNotEmpty() && frameIndex < frames.size) {
                                    frames[frameIndex]
                                } else {
                                    dummyFrame
                                }
                            }

                            // 发送帧
                            sendMjpegFrame(outputStream, frame)
                        } else {
                            // 发送虚拟帧
                            sendMjpegFrame(outputStream, dummyFrame)
                        }
                    } catch (e: Exception) {
                        // 如果发生异常，可能是连接已关闭
                        isRunning = false
                        Log.e(TAG, "Error sending frame: ${e.message}", e)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending MJPEG stream: ${e.message}", e)
            }
        }.start()

        return response
    }

    /**
     * 发送MJPEG帧
     */
    private fun sendMjpegFrame(outputStream: java.io.OutputStream, jpegData: ByteArray) {
        try {
            // 写入MJPEG帧头
            val header = "--$BOUNDARY\r\n" +
                    "Content-Type: image/jpeg\r\n" +
                    "Content-Length: ${jpegData.size}\r\n" +
                    "\r\n"

            outputStream.write(header.toByteArray())

            // 写入JPEG数据
            outputStream.write(jpegData)

            // 写入帧尾
            outputStream.write("\r\n".toByteArray())

            // 刷新输出流
            outputStream.flush()

            // 这里我们不做任何事情，因为我们不能真正地发送数据
            // 在实际应用中，我们需要使用一个真正的输出流
        } catch (e: Exception) {
            Log.e(TAG, "Error sending MJPEG frame: ${e.message}", e)
        }
    }

    /**
     * 创建一个简单的JPEG图像
     */
    private fun createDummyJpeg(width: Int, height: Int): ByteArray {
        // 这里我们创建一个简单的JPEG图像
        // 实际应用中，您应该使用真实的JPEG编码器
        val jpegData = ByteArray(width * height * 3 / 10) // 压缩比约为10:1

        // 添加JPEG头部
        jpegData[0] = 0xFF.toByte()
        jpegData[1] = 0xD8.toByte() // SOI

        // 添加一些随机数据
        val random = Random()
        random.nextBytes(jpegData)

        // 添加JPEG尾部
        jpegData[jpegData.size - 2] = 0xFF.toByte()
        jpegData[jpegData.size - 1] = 0xD9.toByte() // EOI

        return jpegData
    }

    /**
     * 提供HTML页面
     */
    private fun serveHtml(): Response {
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>MJPEG视频流</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        text-align: center;
                    }
                    h1 {
                        color: #333;
                    }
                    .video-container {
                        max-width: 800px;
                        margin: 0 auto;
                        border: 1px solid #ccc;
                        padding: 10px;
                        border-radius: 5px;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                    }
                </style>
            </head>
            <body>
                <h1>MJPEG视频流</h1>
                <div class="video-container">
                    <img src="/stream.mjpeg" alt="MJPEG Stream">
                </div>
            </body>
            </html>
        """.trimIndent()

        return newFixedLengthResponse(Response.Status.OK, "text/html", html)
    }
}
