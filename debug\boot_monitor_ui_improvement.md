# 开机监控界面优化

## 修改内容

### 1. 设备标题区域优化

**修改前**：
```html
<div class="device-header">
    <div class="device-id">CPU ID: ${device.cpu_unique_id}</div>
    <div class="uptime">已开机${device.uptime_display}</div>
</div>
```

**修改后**：
```html
<div class="device-header">
    <div class="device-id">
        <div>CPU ID: ${device.cpu_unique_id}</div>
        <div style="font-size: 0.9em; color: #666; margin-top: 2px;">
            发送端: ${device.sender_id || 'unknown'} | 
            游戏: ${device.auto_start_game_package ? getGameDisplayName(device.auto_start_game_package) : '未配置'}
        </div>
    </div>
    <div class="uptime">已开机${device.uptime_display}</div>
</div>
```

### 2. 移除独立游戏信息块

**删除的内容**：
```html
${device.auto_start_game_package ? `
    <div class="game-info">
        <strong>🎮 启动游戏:</strong> ${device.auto_start_game_package}
    </div>
` : `
    <div class="game-info no-game">
        <strong>⚠️ 未配置游戏</strong>
    </div>
`}
```

### 3. 新增游戏名称转换功能

**新增函数**：
```javascript
function getGameDisplayName(packageName) {
    const gameNames = {
        'com.tencent.tmgp.pubgmhd': '和平精英',
        'com.tencent.tmgp.sgame': '王者荣耀',
        'com.netease.dwrg': '第五人格',
        'com.tencent.tmgp.cf': 'CF手游',
        'com.tencent.tmgp.speedmobile': 'QQ飞车',
        'com.miHoYo.GenshinImpact': '原神',
        'com.miHoYo.hkrpg': '崩坏：星穹铁道',
        'com.netease.onmyoji': '阴阳师',
        'com.netease.hyxd': '荒野行动',
        'com.tencent.lolm': '英雄联盟手游',
        'com.tencent.tmgp.cod': '使命召唤手游',
        'com.netease.mrzhna': '明日之后',
        'com.netease.zjz': '终结者2',
        'com.tencent.tmgp.ak': 'AK手游',
        'com.supercell.clashofclans': '部落冲突',
        'com.supercell.clashroyale': '皇室战争',
        'com.king.candycrushsaga': '糖果传奇',
        'com.mojang.minecraftpe': '我的世界',
        'com.roblox.client': 'Roblox',
        'com.ea.gp.fifamobile': 'FIFA足球世界'
    };
    
    return gameNames[packageName] || packageName;
}
```

### 4. 服务器端数据补充

**添加 sender_id 字段**：
```python
device_data = {
    'cpu_unique_id': cpu_id,
    'sender_id': device_info.get('sender_id', 'unknown'),  # 新增
    'boot_time': boot_time,
    'uptime_seconds': uptime_seconds,
    'uptime_display': format_uptime(uptime_seconds),
    'auto_start_game_package': device_info['device_info'].get('auto_start_game_package', ''),
    ...
}
```

## 显示效果对比

### 修改前
```
┌─────────────────────────────────────┐
│ CPU ID: CPU_12345678901234567890    │ 已开机3分9秒
├─────────────────────────────────────┤
│ 📱 设备: Allwinner A527 PRO        │
│ 🤖 系统: Android 13                │
│ 📦 版本: 1.29                      │
│ 🌐 IP: *************** / 113...    │
│ ⏰ 开机: 2025/8/25 15:15:01        │
├─────────────────────────────────────┤
│ 🎮 启动游戏: com.tencent.tmgp.pubgmhd │
└─────────────────────────────────────┘
```

### 修改后
```
┌─────────────────────────────────────┐
│ CPU ID: CPU_12345678901234567890    │ 已开机3分9秒
│ 发送端: gamev-b246c42d | 游戏: 和平精英 │
├─────────────────────────────────────┤
│ 📱 设备: Allwinner A527 PRO        │
│ 🤖 系统: Android 13                │
│ 📦 版本: 1.29                      │
│ 🌐 IP: *************** / 113...    │
│ ⏰ 开机: 2025/8/25 15:15:01        │
└─────────────────────────────────────┘
```

## 优势

### 1. 界面更紧凑
- 减少了独立的游戏信息块
- 信息更集中在标题区域
- 整体布局更简洁

### 2. 信息更直观
- 发送端ID和游戏信息在同一行
- 游戏名称显示中文，更易理解
- 关键信息一目了然

### 3. 空间利用更高效
- 减少垂直空间占用
- 可以显示更多设备
- 信息密度更合理

## 支持的游戏列表

| 包名 | 中文名称 |
|------|----------|
| com.tencent.tmgp.pubgmhd | 和平精英 |
| com.tencent.tmgp.sgame | 王者荣耀 |
| com.netease.dwrg | 第五人格 |
| com.tencent.tmgp.cf | CF手游 |
| com.tencent.tmgp.speedmobile | QQ飞车 |
| com.miHoYo.GenshinImpact | 原神 |
| com.miHoYo.hkrpg | 崩坏：星穹铁道 |
| com.netease.onmyoji | 阴阳师 |
| com.netease.hyxd | 荒野行动 |
| com.tencent.lolm | 英雄联盟手游 |
| com.tencent.tmgp.cod | 使命召唤手游 |
| com.netease.mrzhna | 明日之后 |
| com.netease.zjz | 终结者2 |
| com.tencent.tmgp.ak | AK手游 |
| com.supercell.clashofclans | 部落冲突 |
| com.supercell.clashroyale | 皇室战争 |
| com.king.candycrushsaga | 糖果传奇 |
| com.mojang.minecraftpe | 我的世界 |
| com.roblox.client | Roblox |
| com.ea.gp.fifamobile | FIFA足球世界 |

## 扩展性

### 1. 添加新游戏
只需在 `getGameDisplayName` 函数中添加新的映射：
```javascript
const gameNames = {
    // 现有游戏...
    'com.new.game.package': '新游戏中文名',
};
```

### 2. 自定义显示格式
可以修改标题区域的样式和布局：
```html
<div style="font-size: 0.9em; color: #666; margin-top: 2px;">
    发送端: ${device.sender_id} | 游戏: ${gameName} | 其他信息: ${otherInfo}
</div>
```

### 3. 动态游戏名称
可以从服务器获取游戏名称映射：
```javascript
// 从服务器获取游戏名称映射
async function loadGameNames() {
    const response = await fetch('/api/game_names');
    const gameNames = await response.json();
    return gameNames;
}
```

## 数据流

### 1. Android端上报
```kotlin
bootInfo.put("sender_id", senderId)
bootInfo.put("auto_start_game_package", gamePackage)
```

### 2. 服务器存储
```python
boot_devices[cpu_unique_id] = {
    'sender_id': sender_id,
    'device_info': {
        'auto_start_game_package': boot_info.get('auto_start_game_package', ''),
        ...
    }
}
```

### 3. 前端显示
```javascript
发送端: ${device.sender_id || 'unknown'} | 
游戏: ${device.auto_start_game_package ? getGameDisplayName(device.auto_start_game_package) : '未配置'}
```

## 版本更新

当前版本：v1.29 → v1.30

主要改进：
- 优化设备信息显示布局
- 添加发送端ID显示
- 游戏名称中文化显示
- 移除冗余的游戏信息块
- 提升界面紧凑性和可读性
