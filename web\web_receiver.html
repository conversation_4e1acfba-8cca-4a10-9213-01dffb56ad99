<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC 视频流接收端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .video-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .video-wrapper {
            flex: 1;
            min-width: 300px;
            position: relative;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        input, button, select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        input {
            flex: 1;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
            color: #f44336;
        }
        .log-container {
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-time {
            color: #666;
            margin-right: 10px;
        }
        .log-info {
            color: #2196F3;
        }
        .log-error {
            color: #f44336;
        }
        .log-success {
            color: #4CAF50;
        }
        .sender-list {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .sender-item {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .sender-item:hover {
            background-color: #e0e0e0;
        }
        .sender-item.active {
            background-color: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC 视频流接收端</h1>
        
        <div class="controls">
            <input type="text" id="signaling-url" value="wss://sling.91jdcd.com/ws/" placeholder="信令服务器URL">
            <input type="text" id="sender-id" placeholder="发送端ID (例如 sender-12345)">
            <button id="connect-btn">连接</button>
            <button id="disconnect-btn" disabled>断开</button>
        </div>
        
        <div class="status" id="status">
            未连接
        </div>
        
        <div class="sender-list" id="sender-list">
            <!-- 发送端列表将在这里动态生成 -->
        </div>
        
        <div class="video-container">
            <div class="video-wrapper">
                <video id="video" autoplay playsinline></video>
            </div>
        </div>
        
        <div class="log-container" id="log">
            <!-- 日志将在这里动态生成 -->
        </div>
    </div>

    <script>
        // 配置
        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' }
            ]
        };
        
        // 元素
        const signalingUrlInput = document.getElementById('signaling-url');
        const senderIdInput = document.getElementById('sender-id');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const statusEl = document.getElementById('status');
        const videoEl = document.getElementById('video');
        const logEl = document.getElementById('log');
        const senderListEl = document.getElementById('sender-list');
        
        // 状态
        let pc = null;
        let dataChannel = null;
        let ws = null;
        let receiverId = `receiver-${Math.random().toString(36).substring(2, 10)}`;
        let activeSenderId = null;
        let senders = [];
        
        // 日志函数
        function log(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const timeSpan = document.createElement('span');
            timeSpan.className = 'log-time';
            timeSpan.textContent = timeStr;
            
            const messageSpan = document.createElement('span');
            messageSpan.className = `log-${type}`;
            messageSpan.textContent = message;
            
            logEntry.appendChild(timeSpan);
            logEntry.appendChild(messageSpan);
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
            
            console.log(`[${type}] ${message}`);
        }
        
        // 更新状态
        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            if (isError) {
                statusEl.classList.add('error');
            } else {
                statusEl.classList.remove('error');
            }
        }
        
        // 连接到信令服务器
        async function connectToSignalingServer() {
            const url = signalingUrlInput.value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = () => {
                    log('已连接到信令服务器');
                    
                    // 注册客户端
                    ws.send(JSON.stringify({
                        type: 'register',
                        id: receiverId,
                        role: 'viewer',
                        name: 'Web接收端',
                        description: 'Web浏览器接收端'
                    }));
                };
                
                ws.onmessage = async (event) => {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${data.type}`);
                    
                    if (data.type === 'registered') {
                        updateStatus(`已注册，ID: ${receiverId}`);
                        // 请求发送端列表
                        ws.send(JSON.stringify({
                            type: 'list_clients'
                        }));
                    }
                    else if (data.type === 'client_list') {
                        handleClientList(data.clients || []);
                    }
                    else if (data.type === 'client_joined') {
                        // 添加新的发送端
                        if (data.role === 'source' || data.id.startsWith('sender-')) {
                            addSender(data);
                        }
                    }
                    else if (data.type === 'client_left') {
                        // 移除发送端
                        removeSender(data.id);
                    }
                    else if (data.type === 'answer' && data.from === activeSenderId) {
                        // 设置远程描述
                        try {
                            const answer = new RTCSessionDescription({
                                type: 'answer',
                                sdp: data.sdp
                            });
                            await pc.setRemoteDescription(answer);
                            log('已设置远程描述');
                        } catch (e) {
                            log(`设置远程描述错误: ${e.message}`, 'error');
                            updateStatus(`设置远程描述错误: ${e.message}`, true);
                        }
                    }
                    else if (data.type === 'candidate' && data.from === activeSenderId) {
                        // 添加ICE候选
                        try {
                            const candidate = data.candidate;
                            if (candidate && candidate.candidate) {
                                await pc.addIceCandidate(candidate);
                                log('已添加ICE候选');
                            }
                        } catch (e) {
                            log(`添加ICE候选错误: ${e.message}`, 'error');
                        }
                    }
                };
                
                ws.onclose = () => {
                    log('信令服务器连接已关闭', 'error');
                    updateStatus('信令服务器连接已关闭', true);
                    disconnectFromPeer();
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                };
                
                ws.onerror = (error) => {
                    log(`信令服务器错误: ${error.message}`, 'error');
                    updateStatus(`信令服务器错误: ${error.message}`, true);
                };
                
            } catch (e) {
                log(`连接信令服务器错误: ${e.message}`, 'error');
                updateStatus(`连接信令服务器错误: ${e.message}`, true);
            }
        }
        
        // 处理客户端列表
        function handleClientList(clients) {
            senders = clients.filter(client => 
                client.role === 'source' || client.id.startsWith('sender-')
            );
            
            updateSenderList();
        }
        
        // 更新发送端列表
        function updateSenderList() {
            senderListEl.innerHTML = '';
            
            if (senders.length === 0) {
                const noSenders = document.createElement('div');
                noSenders.textContent = '没有可用的发送端';
                senderListEl.appendChild(noSenders);
                return;
            }
            
            senders.forEach(sender => {
                const senderItem = document.createElement('div');
                senderItem.className = 'sender-item';
                if (sender.id === activeSenderId) {
                    senderItem.classList.add('active');
                }
                
                senderItem.textContent = `${sender.name || sender.id} ${sender.description ? `(${sender.description})` : ''}`;
                senderItem.dataset.id = sender.id;
                
                senderItem.addEventListener('click', () => {
                    senderIdInput.value = sender.id;
                });
                
                senderListEl.appendChild(senderItem);
            });
        }
        
        // 添加发送端
        function addSender(sender) {
            const existingIndex = senders.findIndex(s => s.id === sender.id);
            if (existingIndex >= 0) {
                senders[existingIndex] = sender;
            } else {
                senders.push(sender);
            }
            updateSenderList();
        }
        
        // 移除发送端
        function removeSender(senderId) {
            senders = senders.filter(s => s.id !== senderId);
            updateSenderList();
            
            if (senderId === activeSenderId) {
                disconnectFromPeer();
                updateStatus(`发送端 ${senderId} 已离开`, true);
            }
        }
        
        // 连接到对等端
        async function connectToPeer() {
            const senderId = senderIdInput.value.trim();
            
            if (!senderId) {
                updateStatus('请输入发送端ID', true);
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                await connectToSignalingServer();
            }
            
            try {
                // 创建对等连接
                pc = new RTCPeerConnection(config);
                
                // 创建数据通道
                dataChannel = pc.createDataChannel('control');
                
                dataChannel.onopen = () => {
                    log('数据通道已打开');
                    dataChannel.send('Hello from web receiver!');
                };
                
                dataChannel.onmessage = (event) => {
                    log(`收到数据通道消息: ${event.data}`);
                };
                
                dataChannel.onclose = () => {
                    log('数据通道已关闭');
                };
                
                // 处理ICE候选
                pc.onicecandidate = (event) => {
                    if (event.candidate) {
                        const candidate = {
                            candidate: event.candidate.candidate,
                            sdpMid: event.candidate.sdpMid,
                            sdpMLineIndex: event.candidate.sdpMLineIndex
                        };
                        
                        ws.send(JSON.stringify({
                            type: 'candidate',
                            target: senderId,
                            candidate: candidate
                        }));
                        
                        log('已发送ICE候选');
                    }
                };
                
                // 处理ICE连接状态变化
                pc.oniceconnectionstatechange = () => {
                    log(`ICE连接状态: ${pc.iceConnectionState}`);
                    
                    if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected' || pc.iceConnectionState === 'closed') {
                        updateStatus(`ICE连接状态: ${pc.iceConnectionState}`, true);
                    } else if (pc.iceConnectionState === 'connected' || pc.iceConnectionState === 'completed') {
                        updateStatus(`已连接到发送端 ${senderId}`);
                    }
                };
                
                // 处理连接状态变化
                pc.onconnectionstatechange = () => {
                    log(`连接状态: ${pc.connectionState}`);
                    
                    if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected' || pc.connectionState === 'closed') {
                        updateStatus(`连接状态: ${pc.connectionState}`, true);
                    } else if (pc.connectionState === 'connected') {
                        updateStatus(`已连接到发送端 ${senderId}`);
                    }
                };
                
                // 处理信令状态变化
                pc.onsignalingstatechange = () => {
                    log(`信令状态: ${pc.signalingState}`);
                };
                
                // 处理轨道
                pc.ontrack = (event) => {
                    log(`收到轨道: ${event.track.kind}`);
                    
                    if (event.track.kind === 'video') {
                        videoEl.srcObject = event.streams[0];
                        log('已设置视频源');
                    }
                };
                
                // 处理数据通道
                pc.ondatachannel = (event) => {
                    const channel = event.channel;
                    log(`收到数据通道: ${channel.label}`);
                    
                    channel.onopen = () => {
                        log(`数据通道 ${channel.label} 已打开`);
                    };
                    
                    channel.onmessage = (event) => {
                        log(`收到数据通道消息: ${event.data}`);
                    };
                };
                
                // 创建offer
                const offer = await pc.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true
                });
                
                // 设置本地描述
                await pc.setLocalDescription(offer);
                log('已设置本地描述');
                
                // 发送offer
                ws.send(JSON.stringify({
                    type: 'offer',
                    target: senderId,
                    sdp: pc.localDescription.sdp
                }));
                log('已发送offer');
                
                activeSenderId = senderId;
                updateStatus(`正在连接到发送端 ${senderId}...`);
                updateSenderList();
                
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                
            } catch (e) {
                log(`连接错误: ${e.message}`, 'error');
                updateStatus(`连接错误: ${e.message}`, true);
                disconnectFromPeer();
            }
        }
        
        // 断开连接
        function disconnectFromPeer() {
            if (pc) {
                pc.close();
                pc = null;
            }
            
            if (dataChannel) {
                dataChannel.close();
                dataChannel = null;
            }
            
            videoEl.srcObject = null;
            activeSenderId = null;
            updateSenderList();
            
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            
            updateStatus('已断开连接');
            log('已断开连接');
        }
        
        // 事件监听
        connectBtn.addEventListener('click', connectToPeer);
        disconnectBtn.addEventListener('click', disconnectFromPeer);

        // 页面卸载时清理连接
        window.addEventListener('beforeunload', () => {
            log('页面即将卸载，清理连接');

            // 断开WebRTC连接
            if (pc) {
                pc.close();
                pc = null;
            }

            // 断开信令服务器连接
            if (ws && ws.readyState === WebSocket.OPEN) {
                // 发送退出消息
                try {
                    ws.send(JSON.stringify({
                        type: 'unregister',
                        id: receiverId
                    }));
                } catch (e) {
                    console.log('发送退出消息失败:', e);
                }

                // 关闭WebSocket连接
                ws.close();
                ws = null;
            }
        });

        // 页面隐藏时也进行清理
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                log('页面已隐藏，清理连接');
                disconnectFromPeer();
            } else if (document.visibilityState === 'visible') {
                // 页面重新可见时，检查是否需要重新连接
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    log('页面重新可见，重新连接');
                    setTimeout(() => {
                        connectToSignalingServer();
                    }, 1000);
                }
            }
        });

        // 初始连接
        connectToSignalingServer();
    </script>
</body>
</html>
