# 开机监控系统

## 功能概述

实现了完整的Android设备开机信息上报和H5监控页面系统。

## 系统架构

### 1. Android端 - 开机信息上报

**文件**: `BootReporter.kt`

**功能**:
- 检测设备开机状态
- 只在第一次开机时上报一次
- 收集完整的设备和应用信息
- 自动发送到服务器

**上报信息**:
```json
{
    "type": "boot_report",
    "timestamp": 1692960000000,
    "boot_time": 1692959000000,
    "cpu_unique_id": "CPU_12345678",
    "device_brand": "Samsung",
    "device_model": "SM-G973F",
    "android_version": "11",
    "app_version": "1.23",
    "game_package": "com.example.game",
    "local_ip": "*************",
    "public_ip": "***********"
}
```

### 2. 服务器端 - 信息接收和存储

**文件**: `enhanced_signaling_server.py`

**新增API**:
- `POST /api/boot_report` - 接收开机信息
- `GET /api/boot_devices` - 获取设备列表

**存储结构**:
```python
boot_devices = {
    "CPU_12345678": {
        "cpu_unique_id": "CPU_12345678",
        "boot_time": 1692959000000,
        "report_time": 1692960000000,
        "device_info": {
            "brand": "Samsung",
            "model": "SM-G973F",
            "android_version": "11",
            "app_version": "1.23",
            "game_package": "com.example.game",
            "local_ip": "*************",
            "public_ip": "***********"
        },
        "last_update": 1692960000000
    }
}
```

### 3. H5监控页面

**文件**: `web/boot_monitor.html`

**功能**:
- 实时显示开机设备列表
- 按开机时间排序（最新在前）
- 实时更新运行时间
- 自动刷新（5秒间隔）
- 响应式设计

**显示信息**:
- CPU ID（前8位+...）
- 设备品牌和型号
- Android版本
- 应用版本
- IP地址（内网/公网）
- 开机时间
- 运行时长（实时递增）
- 启动游戏信息

## 工作流程

### 1. 开机检测流程

```
应用启动 → 检查开机状态 → 比较启动时间 → 新开机？
    ↓                                           ↓
收集设备信息 ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← YES
    ↓
发送到服务器 → 记录已上报 → 完成
```

### 2. 服务器处理流程

```
接收POST请求 → 验证数据 → 提取CPU ID → 存储设备信息 → 返回成功
```

### 3. H5显示流程

```
页面加载 → 请求设备列表 → 渲染设备卡片 → 启动定时器 → 更新运行时间
    ↑                                                    ↓
自动刷新 ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← 5秒后
```

## 关键特性

### 1. 唯一性保证
- **基于系统启动时间**: 使用 `SystemClock.elapsedRealtime()` 检测新开机
- **SharedPreferences存储**: 记录已上报的启动时间
- **避免重复上报**: 同一次开机只上报一次

### 2. CPU唯一ID生成
```kotlin
private fun generateCpuBasedId(): String {
    val cpuInfo = getCpuInfo() // 读取 /proc/cpuinfo
    val hash = cpuInfo.hashCode()
    return "CPU_${Math.abs(hash).toString().padStart(8, '0')}"
}
```

### 3. 实时运行时间
- **服务器端**: 计算 `(当前时间 - 开机时间) / 1000`
- **前端**: JavaScript每秒更新显示
- **格式化**: 自动转换为"X天X小时X分X秒"格式

### 4. 错误处理
- **网络异常**: 自动重试机制
- **数据异常**: 完善的验证和备用方案
- **权限问题**: 多层备用ID生成方案

## 使用方法

### 1. 启动服务器
```bash
# 使用默认日志目录
python3 enhanced_signaling_server.py --log-dir ./logs

# 或使用启动脚本
./start_signaling_server.sh prod
```

### 2. 访问监控页面
```
http://your-server:28080/boot_monitor.html
```

### 3. API接口测试
```bash
# 获取设备列表
curl http://your-server:28080/api/boot_devices

# 模拟开机上报
curl -X POST http://your-server:28080/api/boot_report \
  -H "Content-Type: application/json" \
  -d '{"cpu_unique_id":"CPU_12345678","boot_time":1692959000000}'
```

## 监控页面功能

### 1. 统计面板
- **总设备数**: 已上报开机的设备总数
- **在线设备**: 当前在线设备数（假设所有上报的都在线）
- **配置游戏**: 已配置游戏包名的设备数

### 2. 设备列表
- **排序**: 按开机时间倒序（最新开机在最前）
- **实时更新**: 运行时间每秒自动更新
- **详细信息**: 设备型号、系统版本、IP地址等
- **游戏状态**: 显示是否配置了启动游戏

### 3. 自动刷新
- **默认开启**: 每5秒自动刷新数据
- **可控制**: 用户可以关闭自动刷新
- **手动刷新**: 提供手动刷新按钮

## 数据格式

### 开机上报数据
```json
{
    "type": "boot_report",
    "timestamp": 1692960000000,
    "boot_time": 1692959000000,
    "cpu_unique_id": "CPU_12345678",
    "device_brand": "Samsung",
    "device_model": "SM-G973F",
    "device_name": "beyond1lte",
    "android_version": "11",
    "sdk_version": 30,
    "app_version": "1.23",
    "app_version_code": 23,
    "game_package": "com.example.game",
    "has_game_configured": true,
    "local_ip": "*************",
    "public_ip": "***********"
}
```

### 设备列表响应
```json
{
    "success": true,
    "devices": [
        {
            "cpu_unique_id": "CPU_12345678",
            "cpu_id_short": "CPU_1234...",
            "boot_time": 1692959000000,
            "uptime_seconds": 3600,
            "uptime_display": "1小时0分",
            "game_package": "com.example.game",
            "device_brand": "Samsung",
            "device_model": "SM-G973F",
            "android_version": "11",
            "app_version": "1.23",
            "local_ip": "*************",
            "public_ip": "***********",
            "report_time": 1692960000000,
            "last_update": 1692960000000
        }
    ],
    "total": 1,
    "timestamp": 1692963600000
}
```

## 版本更新

当前版本：v1.23 → v1.24

主要新增功能：
- 开机信息自动上报系统
- 服务器端开机信息接收API
- H5实时监控页面
- 运行时间实时计算和显示
- 完善的错误处理和备用方案
