# 信令服务器数据库兼容性更新

## 🎯 更新概述

为了兼容旧版MySQL和确保数据库稳定性，对信令服务器进行了全面更新，以适应新的数据库表结构。

## 🗄️ 数据库结构变更

### 主要变更
1. **移除JSON数据类型** - 改为独立字段存储
2. **BOOLEAN → TINYINT(1)** - 更好的兼容性
3. **ENUM → VARCHAR** - 避免枚举限制
4. **VARCHAR长度限制** - 避免索引长度超限

### 新增应用设置字段
- `app_has_manual_operation` - 是否有过手动操作
- `app_first_install` - 是否首次安装
- `app_hdmi_device_path` - HDMI设备路径
- `app_usb_capture_device_path` - USB采集设备路径

## 🔧 信令服务器更新

### 1. SQL语句更新
```python
# 布尔值转换
'app_auto_start_game': 1 if app_settings.get('auto_start_game') else 0
'app_log_display_enabled': 1 if app_settings.get('log_display_enabled') else 0
'app_has_manual_operation': 1 if app_settings.get('has_manual_operation') else 0
'app_first_install': 1 if app_settings.get('first_install') else 0

# 数据库操作
is_online = 1  # 替代 TRUE
is_online = 0  # 替代 FALSE
```

### 2. 新增字段处理
```python
# INSERT语句新增字段
app_has_manual_operation, app_first_install,
app_hdmi_device_path, app_usb_capture_device_path

# UPDATE语句新增字段
app_has_manual_operation = VALUES(app_has_manual_operation),
app_first_install = VALUES(app_first_install),
app_hdmi_device_path = VALUES(app_hdmi_device_path),
app_usb_capture_device_path = VALUES(app_usb_capture_device_path)
```

### 3. API响应更新
```python
'app_settings': {
    'signaling_url': sender['app_signaling_url'],
    'sender_id': sender['app_sender_id'],
    'video_source': sender['app_video_source'],
    'video_resolution': sender['app_video_resolution'],
    'video_bitrate': sender['app_video_bitrate'],
    'video_codec': sender['app_video_codec'],
    'video_framerate': sender['app_video_framerate'],
    'camera_id': sender['app_camera_id'],
    'screen_capture_quality': sender['app_screen_capture_quality'],
    'audio_source': sender['app_audio_source'],
    'auto_start_game': bool(sender['app_auto_start_game']),
    'auto_start_game_package': sender['app_auto_start_game_package'],
    'log_display_enabled': bool(sender['app_log_display_enabled']),
    'version': sender['app_version'],
    'version_code': sender['app_version_code'],
    'build_type': sender['app_build_type'],
    'has_manual_operation': bool(sender['app_has_manual_operation']),
    'first_install': bool(sender['app_first_install']),
    'hdmi_device_path': sender['app_hdmi_device_path'],
    'usb_capture_device_path': sender['app_usb_capture_device_path']
}
```

## 📱 Android端更新

### DeviceInfoCollector.kt新增字段
```kotlin
// 添加新的设置字段
settings.put("has_manual_operation", sharedPrefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false))
settings.put("first_install", sharedPrefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL))
settings.put("hdmi_device_path", sharedPrefs.getString(Constants.PREF_HDMI_DEVICE_PATH, Constants.DEFAULT_HDMI_DEVICE_PATH))
settings.put("usb_capture_device_path", sharedPrefs.getString(Constants.PREF_USB_CAPTURE_DEVICE_PATH, Constants.DEFAULT_USB_CAPTURE_DEVICE_PATH))
```

## 🚀 部署步骤

### 1. 数据库更新
```sql
-- 删除旧表（如果存在）
DROP TABLE IF EXISTS sender_status_history;
DROP TABLE IF EXISTS sender_device_info;

-- 执行新的表结构
SOURCE database/sender_device_info_simple.sql;
```

### 2. 信令服务器重启
```bash
# 停止旧服务
pkill -f enhanced_signaling_server.py

# 启动新服务
python enhanced_signaling_server.py --ws-port 8765 --http-port 28080
```

### 3. 验证更新
```sql
-- 检查表结构
DESCRIBE sender_device_info;

-- 检查示例数据
SELECT sender_id, app_version, app_video_resolution, is_online 
FROM sender_device_info 
LIMIT 5;
```

## 📊 数据类型映射

| 旧类型 | 新类型 | 说明 |
|--------|--------|------|
| `JSON` | `TEXT` | 详细信息存储 |
| `BOOLEAN` | `TINYINT(1)` | 0=false, 1=true |
| `ENUM('a','b')` | `VARCHAR(20)` | 字符串存储 |
| `VARCHAR(255)` | `VARCHAR(191)` | 避免索引长度限制 |

## 🔍 兼容性检查

### MySQL版本支持
- ✅ MySQL 5.5+
- ✅ MySQL 5.6+
- ✅ MySQL 5.7+
- ✅ MySQL 8.0+
- ✅ MariaDB 10.0+

### 字符集支持
- ✅ utf8mb4_unicode_ci
- ✅ 支持中文和特殊字符
- ✅ 索引长度优化

## ⚠️ 注意事项

1. **数据迁移**: 如果有旧数据，需要先备份再迁移
2. **应用重启**: Android应用需要重新编译和安装
3. **API兼容**: Web管理界面可能需要适配新的数据结构
4. **监控日志**: 部署后注意观察日志，确认数据正常存储

## 🎯 预期效果

更新完成后：
- ✅ 兼容更多MySQL版本
- ✅ 避免JSON和ENUM兼容性问题
- ✅ 支持更完整的应用设置信息
- ✅ 更稳定的数据存储
- ✅ 更好的查询性能

## 🔧 故障排除

### 常见问题
1. **索引长度错误**: 确保使用 `sender_device_info_simple.sql`
2. **字符集问题**: 确认数据库使用 `utf8mb4`
3. **权限问题**: 确认MySQL用户有CREATE/ALTER权限
4. **连接问题**: 检查数据库连接配置

### 验证命令
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'sender_%';

-- 检查字段类型
SHOW COLUMNS FROM sender_device_info WHERE Field LIKE 'app_%';

-- 测试插入数据
INSERT INTO sender_device_info (sender_id, app_version) VALUES ('test-001', '1.0.0');
```

现在信令服务器已完全适配新的数据库结构！
