# 采集卡专用音频实现

## 🎯 目标：音频只来自采集卡

为了实现音频**只来自采集卡**，我创建了一个专门的采集卡音频管理器，能够检测、识别和配置采集卡音频设备。

## 🔧 技术实现

### 1. CaptureCardAudioManager.kt - 采集卡音频管理器

**核心功能**:
- 🔍 **自动检测采集卡设备**: 扫描所有音频输入设备
- 🎯 **智能识别采集卡**: 基于设备类型和产品名称
- ⚙️ **专用配置**: 为采集卡创建专门的AudioRecord
- 📊 **设备信息**: 提供详细的采集卡信息

**检测逻辑**:
```kotlin
private fun isCaptureCardDevice(device: AudioDeviceInfo): Boolean {
    val deviceType = device.type
    val productName = device.productName?.toString()?.lowercase() ?: ""
    
    // 检查设备类型
    val isCaptureCardType = when (deviceType) {
        AudioDeviceInfo.TYPE_USB_DEVICE -> true           // USB采集卡
        AudioDeviceInfo.TYPE_USB_ACCESSORY -> true        // USB配件
        AudioDeviceInfo.TYPE_HDMI -> true                 // HDMI采集卡
        AudioDeviceInfo.TYPE_LINE_ANALOG -> true          // 模拟线路输入
        AudioDeviceInfo.TYPE_LINE_DIGITAL -> true         // 数字线路输入
        else -> false
    }
    
    // 检查产品名称关键词
    val captureCardKeywords = listOf(
        "capture", "card", "hdmi", "usb", "video", "elgato", 
        "avermedia", "blackmagic", "magewell", "razer", "obs"
    )
    
    val hasKeyword = captureCardKeywords.any { keyword ->
        productName.contains(keyword)
    }
    
    return isCaptureCardType || hasKeyword
}
```

### 2. 专用AudioRecord创建

```kotlin
fun createCaptureCardAudioRecord(device: AudioDeviceInfo): AudioRecord? {
    val audioRecord = AudioRecord.Builder()
        .setAudioSource(MediaRecorder.AudioSource.UNPROCESSED)  // 原始音频
        .setAudioFormat(
            android.media.AudioFormat.Builder()
                .setSampleRate(getSupportedSampleRate(device))
                .setChannelMask(android.media.AudioFormat.CHANNEL_IN_STEREO)
                .setEncoding(android.media.AudioFormat.ENCODING_PCM_16BIT)
                .build()
        )
        .setBufferSizeInBytes(bufferSize * 2)
        .build()
    
    // 关键：设置首选输入设备为采集卡
    audioRecord.setPreferredDevice(device)
    
    return audioRecord
}
```

### 3. WebRTC集成

在`createAudioSource`方法中：
```kotlin
"video_input" -> {
    // 检测并配置采集卡设备
    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
        val captureCardInfo = captureCardAudioManager.getCaptureCardInfo()
        Logger.i(TAG, "🎵 采集卡设备信息:\n$captureCardInfo")
        
        val hasCaptureCard = captureCardAudioManager.hasCaptureCardAvailable()
        if (hasCaptureCard) {
            val recommendedDevice = captureCardAudioManager.getRecommendedCaptureCardDevice()
            if (recommendedDevice != null) {
                // 配置WebRTC使用指定的采集卡设备
                constraints.optional.add(MediaConstraints.KeyValuePair("preferredAudioDevice", recommendedDevice.id.toString()))
                constraints.optional.add(MediaConstraints.KeyValuePair("captureCardName", recommendedDevice.productName.toString()))
            }
        }
    }
}
```

## 🎵 支持的采集卡类型

### 硬件类型检测
- ✅ **USB采集卡**: `TYPE_USB_DEVICE`, `TYPE_USB_ACCESSORY`
- ✅ **HDMI采集卡**: `TYPE_HDMI`
- ✅ **线路输入**: `TYPE_LINE_ANALOG`, `TYPE_LINE_DIGITAL`

### 品牌关键词识别
- ✅ **Elgato**: Game Capture系列
- ✅ **AverMedia**: Live Gamer系列
- ✅ **Blackmagic**: DeckLink系列
- ✅ **Magewell**: USB Capture系列
- ✅ **Razer**: Ripsaw系列
- ✅ **OBS**: Virtual Camera
- ✅ **通用关键词**: capture, card, hdmi, usb, video

## 📊 实现效果

### 1. 设备检测示例
```
检测到 2 个采集卡设备:
1. Elgato Game Capture HD60 S+
   类型: USB设备
   ID: 123
   支持采样率: 48000, 44100 Hz
   支持声道数: 1, 2

2. AverMedia Live Gamer MINI
   类型: USB设备
   ID: 124
   支持采样率: 48000 Hz
   支持声道数: 2
```

### 2. 音频源优先级
1. **USB采集卡** - 最高优先级
2. **HDMI采集卡** - 次优先级
3. **其他采集卡** - 备选

### 3. 日志输出示例
```
🎵 检测到音频设备: Elgato Game Capture HD60 S+ - 类型: USB设备
✅ 发现采集卡音频设备: Elgato Game Capture HD60 S+
🎵 推荐USB采集卡: Elgato Game Capture HD60 S+
🎵 已配置推荐采集卡: Elgato Game Capture HD60 S+
✅ 已设置来自视频输入的音频源（采集卡音频）- 使用: unprocessed
```

## ⚠️ 技术限制

### 1. Android版本要求
- **Android 6.0+ (API 23)**: 基础设备检测
- **Android 7.0+ (API 24)**: UNPROCESSED音频源
- **Android 6.0+ (API 23)**: setPreferredDevice方法

### 2. 硬件要求
- 采集卡必须被Android识别为音频输入设备
- 采集卡驱动必须支持Android Audio HAL
- USB采集卡需要支持USB Audio Class

### 3. 系统限制
- Android系统的音频路由机制
- 某些采集卡可能不被正确识别
- 需要用户授予录音权限

## 🔧 使用方法

### 1. 检查采集卡可用性
```kotlin
val captureCardManager = CaptureCardAudioManager(context)
val hasCaptureCard = captureCardManager.hasCaptureCardAvailable()

if (hasCaptureCard) {
    val info = captureCardManager.getCaptureCardInfo()
    Log.i(TAG, "采集卡信息: $info")
} else {
    Log.w(TAG, "未检测到采集卡")
}
```

### 2. 获取推荐设备
```kotlin
val recommendedDevice = captureCardManager.getRecommendedCaptureCardDevice()
if (recommendedDevice != null) {
    Log.i(TAG, "推荐采集卡: ${recommendedDevice.productName}")
}
```

### 3. 创建专用AudioRecord
```kotlin
val audioRecord = captureCardManager.createCaptureCardAudioRecord(device)
if (audioRecord != null) {
    // 使用采集卡专用的AudioRecord
    audioRecord.startRecording()
}
```

## 🎯 预期效果

实现后的效果：
- ✅ **自动检测**: 启动时自动扫描采集卡设备
- ✅ **智能选择**: 优先选择最合适的采集卡
- ✅ **专用配置**: 为采集卡设备创建专门的音频配置
- ✅ **原始音质**: 使用UNPROCESSED音频源，保持原始质量
- ✅ **设备绑定**: 通过setPreferredDevice确保音频来自指定采集卡
- ✅ **详细日志**: 提供完整的设备检测和配置日志

## 🔍 验证方法

### 1. 日志验证
查看应用日志，确认：
- 采集卡设备被正确检测
- 推荐的采集卡被选中
- WebRTC配置包含采集卡参数

### 2. 音频测试
- 连接采集卡到游戏机/电脑
- 播放音频内容
- 确认WebRTC传输的是采集卡音频，而非设备麦克风

### 3. 设备切换测试
- 拔插采集卡
- 观察应用是否正确检测设备变化
- 验证音频源是否正确切换

现在系统能够智能检测采集卡并优先使用采集卡音频，最大程度确保音频只来自采集卡！
