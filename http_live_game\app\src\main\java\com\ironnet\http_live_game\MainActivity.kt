package com.ironnet.http_live_game

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.ironnet.http_live_game.service.StreamingService
import com.ironnet.http_live_game.streaming.StreamConfig
import com.ironnet.http_live_game.ui.theme.Http_live_gameTheme

class MainActivity : ComponentActivity() {
    private val TAG = "MainActivity"

    private var streamingService: StreamingService? = null
    private var bound = false
    private var mediaProjection: MediaProjection? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as StreamingService.LocalBinder
            streamingService = binder.getService()
            bound = true
            Log.d(TAG, "Service connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            streamingService = null
            bound = false
            Log.d(TAG, "Service disconnected")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Start and bind to the service
        val serviceIntent = Intent(this, StreamingService::class.java)
        startForegroundService(serviceIntent)
        bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)

        setContent {
            Http_live_gameTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    StreamingApp(
                        onStartStreaming = { config, useCamera ->
                            startStreaming(config, useCamera)
                        },
                        onStopStreaming = {
                            stopStreaming()
                        },
                        onUpdateConfig = { config ->
                            updateConfig(config)
                        },
                        getStreamUrl = {
                            streamingService?.getStreamUrl() ?: "Not available"
                        },
                        isStreaming = {
                            streamingService?.isStreaming() ?: false
                        }
                    )
                }
            }
        }
    }

    override fun onDestroy() {
        // 确保在Activity销毁时停止流媒体服务
        if (streamingService?.isStreaming() == true) {
            streamingService?.stopStreaming()
        }

        if (bound) {
            unbindService(serviceConnection)
            bound = false
        }
        super.onDestroy()
    }

    private fun startStreaming(config: StreamConfig, useCamera: Boolean) {
        if (useCamera) {
            // Start camera streaming
            streamingService?.startStreaming(config.copy(useCamera = true))
        } else {
            // Request screen capture permission
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            startActivityForResult(projectionManager.createScreenCaptureIntent(), SCREEN_CAPTURE_REQUEST_CODE)
        }
    }

    private fun stopStreaming() {
        streamingService?.stopStreaming()
        mediaProjection?.stop()
        mediaProjection = null
    }

    private fun updateConfig(config: StreamConfig) {
        streamingService?.updateConfig(config)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == SCREEN_CAPTURE_REQUEST_CODE && resultCode == RESULT_OK && data != null) {
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = projectionManager.getMediaProjection(resultCode, data)

            // Start screen capture streaming
            streamingService?.startStreaming(
                StreamConfig(useCamera = false),
                mediaProjection
            )
        }
    }

    companion object {
        private const val SCREEN_CAPTURE_REQUEST_CODE = 100
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun StreamingApp(
    onStartStreaming: (StreamConfig, Boolean) -> Unit,
    onStopStreaming: () -> Unit,
    onUpdateConfig: (StreamConfig) -> Unit,
    getStreamUrl: () -> String,
    isStreaming: () -> Boolean
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current

    var useCamera by remember { mutableStateOf(true) }
    var isStreaming by remember { mutableStateOf(false) }
    var streamUrl by remember { mutableStateOf("") }

    // Stream configuration
    var selectedResolution by remember { mutableStateOf(1) } // Default to 720p
    var selectedFrameRate by remember { mutableStateOf(2) } // Default to 30fps
    var selectedBitRate by remember { mutableStateOf(2) } // Default to 2Mbps
    var selectedFormat by remember { mutableStateOf(0) } // Default to H.264
    var port by remember { mutableStateOf("8080") }

    // Dropdown states
    var showResolutionDropdown by remember { mutableStateOf(false) }
    var showFrameRateDropdown by remember { mutableStateOf(false) }
    var showBitRateDropdown by remember { mutableStateOf(false) }
    var showFormatDropdown by remember { mutableStateOf(false) }

    // Permission state
    val permissionsState = rememberMultiplePermissionsState(
        permissions = listOf(
            Manifest.permission.CAMERA,
            Manifest.permission.INTERNET,
            Manifest.permission.FOREGROUND_SERVICE,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Manifest.permission.POST_NOTIFICATIONS
            } else {
                ""
            }
        ).filter { it.isNotEmpty() }
    )

    // Check if permissions are granted
    LaunchedEffect(Unit) {
        if (!permissionsState.allPermissionsGranted) {
            permissionsState.launchMultiplePermissionRequest()
        }
    }

    // Update streaming status
    LaunchedEffect(Unit) {
        isStreaming = isStreaming()
        if (isStreaming) {
            streamUrl = getStreamUrl()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top
    ) {
        // Title
        Text(
            text = "HTTP Video Streaming",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Permissions card
        if (!permissionsState.allPermissionsGranted) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Permissions Required",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("This app requires camera and other permissions to function properly.")
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = { permissionsState.launchMultiplePermissionRequest() }
                    ) {
                        Text("Grant Permissions")
                    }
                }
            }
        }

        // Stream URL card (when streaming)
        if (isStreaming) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Stream URL",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(streamUrl)
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            clipboardManager.setText(AnnotatedString(streamUrl))
                            Toast.makeText(context, "URL copied to clipboard", Toast.LENGTH_SHORT).show()
                        }
                    ) {
                        Text("Copy URL")
                    }
                }
            }
        }

        // Source selection
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Video Source",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Use Camera")
                    Spacer(modifier = Modifier.weight(1f))
                    Switch(
                        checked = useCamera,
                        onCheckedChange = { useCamera = it }
                    )
                }
                Text(
                    text = if (useCamera) "Camera will be used as video source" else "Screen will be captured",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }

        // Video configuration
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Video Configuration",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(16.dp))

                // Resolution
                Column {
                    Text("Resolution")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showResolutionDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val resolution = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution]
                        Text("${resolution.first}x${resolution.second}")
                    }
                    DropdownMenu(
                        expanded = showResolutionDropdown,
                        onDismissRequest = { showResolutionDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_RESOLUTIONS.forEachIndexed { index, resolution ->
                            DropdownMenuItem(
                                text = { Text("${resolution.first}x${resolution.second}") },
                                onClick = {
                                    selectedResolution = index
                                    showResolutionDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // Frame Rate
                Column {
                    Text("Frame Rate")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showFrameRateDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("${StreamConfig.SUPPORTED_FRAME_RATES[selectedFrameRate]} fps")
                    }
                    DropdownMenu(
                        expanded = showFrameRateDropdown,
                        onDismissRequest = { showFrameRateDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_FRAME_RATES.forEachIndexed { index, frameRate ->
                            DropdownMenuItem(
                                text = { Text("$frameRate fps") },
                                onClick = {
                                    selectedFrameRate = index
                                    showFrameRateDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // Bit Rate
                Column {
                    Text("Bit Rate")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showBitRateDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val bitRate = StreamConfig.SUPPORTED_BIT_RATES[selectedBitRate]
                        Text("${bitRate / 1_000_000} Mbps")
                    }
                    DropdownMenu(
                        expanded = showBitRateDropdown,
                        onDismissRequest = { showBitRateDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_BIT_RATES.forEachIndexed { index, bitRate ->
                            DropdownMenuItem(
                                text = { Text("${bitRate / 1_000_000} Mbps") },
                                onClick = {
                                    selectedBitRate = index
                                    showBitRateDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // Format
                Column {
                    Text("Video Format")
                    Spacer(modifier = Modifier.height(4.dp))
                    OutlinedButton(
                        onClick = { showFormatDropdown = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        val format = StreamConfig.SUPPORTED_FORMATS[selectedFormat]
                        Text(StreamConfig.getFormatName(format))
                    }
                    DropdownMenu(
                        expanded = showFormatDropdown,
                        onDismissRequest = { showFormatDropdown = false }
                    ) {
                        StreamConfig.SUPPORTED_FORMATS.forEachIndexed { index, format ->
                            DropdownMenuItem(
                                text = { Text(StreamConfig.getFormatName(format)) },
                                onClick = {
                                    selectedFormat = index
                                    showFormatDropdown = false
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                Divider()
                Spacer(modifier = Modifier.height(8.dp))

                // Port
                Column {
                    Text("Port")
                    Spacer(modifier = Modifier.height(4.dp))
                    TextField(
                        value = port,
                        onValueChange = {
                            if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                port = it
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }

        // Control buttons
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Button(
                onClick = {
                    if (!isStreaming) {
                        val resolution = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution]
                        val config = StreamConfig(
                            useCamera = useCamera,
                            width = resolution.first,
                            height = resolution.second,
                            frameRate = StreamConfig.SUPPORTED_FRAME_RATES[selectedFrameRate],
                            bitRate = StreamConfig.SUPPORTED_BIT_RATES[selectedBitRate],
                            videoFormat = StreamConfig.SUPPORTED_FORMATS[selectedFormat],
                            port = port.toIntOrNull() ?: 8080
                        )
                        onStartStreaming(config, useCamera)
                        isStreaming = true
                        streamUrl = getStreamUrl()
                    } else {
                        onStopStreaming()
                        isStreaming = false
                        streamUrl = ""
                    }
                },
                modifier = Modifier.weight(1f)
            ) {
                Text(if (isStreaming) "Stop Streaming" else "Start Streaming")
            }
        }

        // Apply configuration button (only when streaming)
        if (isStreaming) {
            Button(
                onClick = {
                    val resolution = StreamConfig.SUPPORTED_RESOLUTIONS[selectedResolution]
                    val config = StreamConfig(
                        useCamera = useCamera,
                        width = resolution.first,
                        height = resolution.second,
                        frameRate = StreamConfig.SUPPORTED_FRAME_RATES[selectedFrameRate],
                        bitRate = StreamConfig.SUPPORTED_BIT_RATES[selectedBitRate],
                        videoFormat = StreamConfig.SUPPORTED_FORMATS[selectedFormat],
                        port = port.toIntOrNull() ?: 8080
                    )
                    onUpdateConfig(config)
                    streamUrl = getStreamUrl()
                    Toast.makeText(context, "Configuration updated", Toast.LENGTH_SHORT).show()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Apply Configuration")
            }
        }
    }

    // Update streaming status periodically
    DisposableEffect(Unit) {
        val runnable = object : Runnable {
            override fun run() {
                isStreaming = isStreaming()
                if (isStreaming) {
                    streamUrl = getStreamUrl()
                }
            }
        }

        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        handler.postDelayed(runnable, 1000)

        onDispose {
            handler.removeCallbacks(runnable)
        }
    }
}