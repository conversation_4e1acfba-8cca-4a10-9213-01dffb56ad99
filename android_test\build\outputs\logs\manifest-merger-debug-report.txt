-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25012fcc08d4c5cf5a2844c1a21a6fbe\transformed\material-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\603d5b366cec0e2b4f7a40320b7dc059\transformed\constraintlayout-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ecd700c773cf5624732e3577602f1ea\transformed\appcompat-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e35720d387af23b8661e2c3d29cdaa\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\ee0550e8848c5279ef1306690d07508f\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2fec87436ed4b48e2cd27f2bec00bd8\transformed\activity-1.2.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a721783f33f0b0f141ece40123d930b\transformed\appcompat-resources-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5bac1f89003abf622e3422923228db\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\daf60890667c2c1f4f7951bda1ccd740\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09ea9aedca029a896589844b66e8851\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c63f3106ffaa61ce348f6ba5cb615c\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0fe3706260b3bce929f3dd639e33b7e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\de5a7dc1c81a89f7e31dc782229122df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\780765cea0c567bec6ca8db99cd43cba\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\22a51f44cdda660ee1b80324ead41660\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc799678192709bceae81fa20a37187c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4790aa1caca6e7d9bde0bf1adfc442a\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c737135e2039c6aa4422151570c825bf\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b80c278120ff42ff6ece3f9d926c639\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2a2b216c41ef64c23b5a2261da3c566\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\467a835b52a179222c9cd595c4b15361\transformed\savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f25280aa28dd02b1b0fe5958898387a3\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f2676b1c0969ec8c1ba6828bf396548\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a89849bfd38ecbffaf5b00436d2e3bd\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f632648e53e556eed5f40f2824d9ac5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80c89437fc250e842b2f71e0d613c9ff\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc210aa728a4eeea33d4ed954c0cfcd1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4df7864670f88d5668dedcb24fe690f\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1b15eaaf08fb20dd8d722ca164b4b9c\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\140757696eca7ec1efbdf7371a4acde0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989dfec6fc7a4daefb140f0c011fcef1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ca7186ef83368b8549809152ed428ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\485578c608519582fc751ff2f7c88476\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18e5cb54f886f7aa7e26ad4e22b3ac23\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:17:1-24:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
	package
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:3:5-39
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:1-20:12
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:5:5-18:19
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25012fcc08d4c5cf5a2844c1a21a6fbe\transformed\material-1.4.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25012fcc08d4c5cf5a2844c1a21a6fbe\transformed\material-1.4.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\603d5b366cec0e2b4f7a40320b7dc059\transformed\constraintlayout-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\603d5b366cec0e2b4f7a40320b7dc059\transformed\constraintlayout-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f632648e53e556eed5f40f2824d9ac5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f632648e53e556eed5f40f2824d9ac5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:10:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:8:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:9:9-54
	android:icon
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:7:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:11:9-49
activity#com.example.android_test.MainActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:9-17:20
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:12:19-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:13:13-16:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:14:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml:15:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25012fcc08d4c5cf5a2844c1a21a6fbe\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25012fcc08d4c5cf5a2844c1a21a6fbe\transformed\material-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\603d5b366cec0e2b4f7a40320b7dc059\transformed\constraintlayout-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\603d5b366cec0e2b4f7a40320b7dc059\transformed\constraintlayout-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ecd700c773cf5624732e3577602f1ea\transformed\appcompat-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4ecd700c773cf5624732e3577602f1ea\transformed\appcompat-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e35720d387af23b8661e2c3d29cdaa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e35720d387af23b8661e2c3d29cdaa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\ee0550e8848c5279ef1306690d07508f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\ee0550e8848c5279ef1306690d07508f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2fec87436ed4b48e2cd27f2bec00bd8\transformed\activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2fec87436ed4b48e2cd27f2bec00bd8\transformed\activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a721783f33f0b0f141ece40123d930b\transformed\appcompat-resources-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a721783f33f0b0f141ece40123d930b\transformed\appcompat-resources-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5bac1f89003abf622e3422923228db\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e5bac1f89003abf622e3422923228db\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\daf60890667c2c1f4f7951bda1ccd740\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\daf60890667c2c1f4f7951bda1ccd740\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09ea9aedca029a896589844b66e8851\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09ea9aedca029a896589844b66e8851\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c63f3106ffaa61ce348f6ba5cb615c\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48c63f3106ffaa61ce348f6ba5cb615c\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0fe3706260b3bce929f3dd639e33b7e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0fe3706260b3bce929f3dd639e33b7e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\de5a7dc1c81a89f7e31dc782229122df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\de5a7dc1c81a89f7e31dc782229122df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\780765cea0c567bec6ca8db99cd43cba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\780765cea0c567bec6ca8db99cd43cba\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\22a51f44cdda660ee1b80324ead41660\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\22a51f44cdda660ee1b80324ead41660\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc799678192709bceae81fa20a37187c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc799678192709bceae81fa20a37187c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4790aa1caca6e7d9bde0bf1adfc442a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4790aa1caca6e7d9bde0bf1adfc442a\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c737135e2039c6aa4422151570c825bf\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c737135e2039c6aa4422151570c825bf\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\255d63c0f9f8f5d12ab3191ffb377750\transformed\core-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b80c278120ff42ff6ece3f9d926c639\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b80c278120ff42ff6ece3f9d926c639\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2a2b216c41ef64c23b5a2261da3c566\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2a2b216c41ef64c23b5a2261da3c566\transformed\lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\467a835b52a179222c9cd595c4b15361\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\467a835b52a179222c9cd595c4b15361\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f25280aa28dd02b1b0fe5958898387a3\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f25280aa28dd02b1b0fe5958898387a3\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f2676b1c0969ec8c1ba6828bf396548\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f2676b1c0969ec8c1ba6828bf396548\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a89849bfd38ecbffaf5b00436d2e3bd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a89849bfd38ecbffaf5b00436d2e3bd\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f632648e53e556eed5f40f2824d9ac5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\4f632648e53e556eed5f40f2824d9ac5\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80c89437fc250e842b2f71e0d613c9ff\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80c89437fc250e842b2f71e0d613c9ff\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc210aa728a4eeea33d4ed954c0cfcd1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc210aa728a4eeea33d4ed954c0cfcd1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4df7864670f88d5668dedcb24fe690f\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b4df7864670f88d5668dedcb24fe690f\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1b15eaaf08fb20dd8d722ca164b4b9c\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1b15eaaf08fb20dd8d722ca164b4b9c\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\140757696eca7ec1efbdf7371a4acde0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\140757696eca7ec1efbdf7371a4acde0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989dfec6fc7a4daefb140f0c011fcef1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\989dfec6fc7a4daefb140f0c011fcef1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ca7186ef83368b8549809152ed428ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ca7186ef83368b8549809152ed428ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\485578c608519582fc751ff2f7c88476\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\485578c608519582fc751ff2f7c88476\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18e5cb54f886f7aa7e26ad4e22b3ac23\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18e5cb54f886f7aa7e26ad4e22b3ac23\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_test\src\main\AndroidManifest.xml
