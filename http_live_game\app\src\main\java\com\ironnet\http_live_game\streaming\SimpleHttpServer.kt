package com.ironnet.http_live_game.streaming

import android.util.Log
import fi.iki.elonen.NanoHTTPD
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.IOException

/**
 * 简单的HTTP服务器
 * 用于提供HLS流文件
 */
class SimpleHttpServer(private val rootDir: File, port: Int) : NanoHTTPD(port) {
    companion object {
        private const val TAG = "SimpleHttpServer"
    }

    override fun serve(session: IHTTPSession): Response {
        val uri = session.uri
        Log.d(TAG, "Received request: $uri")

        // 处理CORS
        val headers = session.headers
        val response = serveFile(uri)
        response.addHeader("Access-Control-Allow-Origin", "*")
        response.addHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
        response.addHeader("Access-Control-Allow-Headers", "Content-Type")

        return response
    }

    /**
     * 提供文件
     */
    private fun serveFile(uri: String): Response {
        try {
            // 规范化URI
            var path = uri
            if (path.startsWith("/")) {
                path = path.substring(1)
            }
            if (path.isEmpty()) {
                path = "index.m3u8"
            }

            // 获取文件
            val file = File(rootDir, path)
            if (!file.exists()) {
                Log.w(TAG, "File not found: ${file.absolutePath}")
                return newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "File not found")
            }

            // 确定MIME类型
            val mimeType = getMimeType(path)

            // 读取文件
            val fileInputStream = FileInputStream(file)
            val response = newChunkedResponse(Response.Status.OK, mimeType, fileInputStream)

            // 设置缓存控制
            if (path.endsWith(".m3u8")) {
                // 不缓存播放列表
                response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
                response.addHeader("Pragma", "no-cache")
                response.addHeader("Expires", "0")
            } else if (path.endsWith(".ts")) {
                // 缓存分段文件
                response.addHeader("Cache-Control", "public, max-age=86400")
            }

            return response
        } catch (e: FileNotFoundException) {
            Log.e(TAG, "File not found: ${e.message}")
            return newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "File not found")
        } catch (e: IOException) {
            Log.e(TAG, "Error serving file: ${e.message}")
            return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, MIME_PLAINTEXT, "Error serving file")
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error: ${e.message}")
            return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, MIME_PLAINTEXT, "Unexpected error")
        }
    }

    /**
     * 获取MIME类型
     */
    private fun getMimeType(path: String): String {
        return when {
            path.endsWith(".m3u8") -> "application/vnd.apple.mpegurl"
            path.endsWith(".ts") -> "video/mp2t"
            path.endsWith(".mp4") -> "video/mp4"
            path.endsWith(".html") -> "text/html"
            path.endsWith(".css") -> "text/css"
            path.endsWith(".js") -> "application/javascript"
            path.endsWith(".png") -> "image/png"
            path.endsWith(".jpg") -> "image/jpeg"
            path.endsWith(".gif") -> "image/gif"
            else -> "application/octet-stream"
        }
    }
}
