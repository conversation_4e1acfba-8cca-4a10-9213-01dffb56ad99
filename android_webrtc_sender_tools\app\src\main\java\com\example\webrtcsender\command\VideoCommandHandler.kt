package com.example.webrtcsender.command

import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCManager
import org.json.JSONObject

/**
 * 视频参数控制命令处理器
 * 处理 change_resolution, change_bitrate, change_codec 命令
 */
class VideoCommandHandler(
    private val webrtcManager: WebRTCManager,
    private val responseCallback: (String, Boolean) -> Unit,
    private val statusCallback: (String, String, Int, String) -> Unit
) : CommandHandler() {
    
    companion object {
        private const val TAG = "VideoCommandHandler"
    }
    
    override fun executeControlCommand(command: String, params: JSONObject): Boolean {
        return when (command) {
            "change_resolution" -> changeResolution(params)
            "change_bitrate" -> changeBitrate(params)
            "change_codec" -> changeCodec(params)
            else -> {
                Logger.w(TAG, "❓ 不支持的视频命令: $command")
                false
            }
        }
    }
    
    /**
     * 改变分辨率
     */
    private fun changeResolution(params: JSONObject): Boolean {
        return try {
            val resolution = params.optString("resolution", "1920x1080")
            Logger.i(TAG, "📺 改变分辨率: $resolution")
            statusCallback("video_config", "resolution_changing", 50, "正在更改分辨率为: $resolution")
            
            // 解析分辨率
            val parts = resolution.split("x")
            if (parts.size != 2) {
                Logger.e(TAG, "❌ 分辨率格式错误: $resolution")
                statusCallback("video_config", "resolution_failed", 0, "分辨率格式错误: $resolution")
                return false
            }
            
            val width = parts[0].toInt()
            val height = parts[1].toInt()
            
            // 调用WebRTC管理器改变分辨率
            webrtcManager.setVideoResolution(resolution)
            
            Logger.i(TAG, "✅ 分辨率更改成功: ${width}x${height}")
            statusCallback("video_config", "resolution_changed", 100, "分辨率已更改为: $resolution")
            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 改变分辨率失败: ${e.message}")
            statusCallback("video_config", "resolution_failed", 0, "改变分辨率失败: ${e.message}")
            false
        }
    }
    
    /**
     * 改变码率
     */
    private fun changeBitrate(params: JSONObject): Boolean {
        return try {
            val bitrate = params.optInt("bitrate", 3000)
            Logger.i(TAG, "📊 改变码率: $bitrate kbps")
            statusCallback("video_config", "bitrate_changing", 50, "正在更改码率为: $bitrate kbps")
            
            // 调用WebRTC管理器改变码率
            webrtcManager.setVideoBitrate(bitrate)
            
            Logger.i(TAG, "✅ 码率更改成功: $bitrate kbps")
            statusCallback("video_config", "bitrate_changed", 100, "码率已更改为: $bitrate kbps")
            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 改变码率失败: ${e.message}")
            statusCallback("video_config", "bitrate_failed", 0, "改变码率失败: ${e.message}")
            false
        }
    }
    
    /**
     * 改变编码
     */
    private fun changeCodec(params: JSONObject): Boolean {
        return try {
            val codec = params.optString("codec", "H264")
            Logger.i(TAG, "🎬 改变编码: $codec")
            statusCallback("video_config", "codec_changing", 50, "正在更改编码为: $codec")
            
            // 调用WebRTC管理器改变编码
            webrtcManager.setVideoCodec(codec)
            
            Logger.i(TAG, "✅ 编码更改成功: $codec")
            statusCallback("video_config", "codec_changed", 100, "编码已更改为: $codec")
            true
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 改变编码失败: ${e.message}")
            statusCallback("video_config", "codec_failed", 0, "改变编码失败: ${e.message}")
            false
        }
    }
    
    override fun executeUpgrade(apkUrl: String, version: String, force: Boolean) {
        // 视频命令处理器不处理升级命令
        Logger.w(TAG, "⚠️ 视频命令处理器不处理升级命令")
    }
    
    override fun updateServerConfig(configData: JSONObject) {
        // 视频命令处理器不处理配置更新
        Logger.w(TAG, "⚠️ 视频命令处理器不处理配置更新")
    }
    
    override fun sendCommandResponse(command: String, success: Boolean) {
        responseCallback(command, success)
    }
}
