# 设置PowerShell输出编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 清除之前的logcat日志
Write-Host "清除之前的logcat日志..."
adb logcat -c

# 启动应用
Write-Host "启动线上线下服务台应用..."
adb shell am start -n com.example.webrtcsender/.ui.MainActivity

# 提示用户手动操作
Write-Host "请在设备上手动执行以下操作："
Write-Host "1. 点击'启动服务'按钮"
Write-Host "2. 点击'屏幕录制'按钮"
Write-Host "3. 在权限对话框中点击'立即开始'"
Write-Host "4. 等待应用运行一段时间"
Write-Host "完成后，按任意键继续收集日志..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 收集logcat日志
Write-Host "收集logcat日志..."
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "webrtc_sender_log_$timestamp.txt"

# 收集WebRTCSender相关标签的所有级别日志，其他标签只收集错误级别
Write-Host "收集WebRTCSender相关标签的所有级别日志，其他标签只收集错误级别..."
# 使用Out-File确保以UTF-8编码保存
adb logcat -d "WebRTCSender:V" "WebRTCClient:V" "SignalingClient:V" "WebRTCManager:V" "*:E" | Out-File -FilePath $logFile -Encoding utf8

# 同时创建一个只包含错误的日志文件
$errorLogFile = "webrtc_sender_errors_$timestamp.txt"
Write-Host "同时创建一个只包含错误的日志文件: $errorLogFile"
adb logcat -d "*:E" | Out-File -FilePath $errorLogFile -Encoding utf8

Write-Host "日志收集完成！日志已保存到 $logFile"
Write-Host "您可以使用记事本或VS Code等支持UTF-8的编辑器打开日志文件查看"
Write-Host "或者使用以下命令继续查看实时日志："
Write-Host ".\view_logs_utf8.ps1"

# 自动打开日志文件
Write-Host "正在打开日志文件..."
Invoke-Item $logFile
