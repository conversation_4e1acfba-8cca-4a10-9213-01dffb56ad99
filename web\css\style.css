/* 基本样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

h1 {
    font-size: 24px;
    color: #2c3e50;
}

h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.connection-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-offline {
    background-color: #e74c3c;
    box-shadow: 0 0 5px #e74c3c;
}

.status-connecting {
    background-color: #f39c12;
    box-shadow: 0 0 5px #f39c12;
}

.status-online {
    background-color: #2ecc71;
    box-shadow: 0 0 5px #2ecc71;
}

/* 主要内容区域 */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* 视频容器 */
.video-container {
    position: relative;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    grid-column: 1;
    grid-row: 1 / span 2;
    aspect-ratio: 16 / 9;
}

video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 18px;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.video-container:hover .video-controls {
    opacity: 1;
}

.control-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.control-btn svg {
    fill: white;
    width: 24px;
    height: 24px;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.stream-info {
    margin-left: auto;
    color: white;
    font-size: 14px;
}

/* 源列表 */
.sources-panel {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sources-list {
    max-height: 200px;
    overflow-y: auto;
}

.source-item {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.source-item:hover {
    background-color: #f0f0f0;
}

.source-item.active {
    border-color: #3498db;
    background-color: #ebf5fb;
}

.source-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.source-info {
    font-size: 12px;
    color: #666;
}

.no-sources {
    color: #999;
    text-align: center;
    padding: 20px 0;
}

/* 连接面板 */
.connection-panel {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input[type="text"] {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.primary-btn, .secondary-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.primary-btn {
    background-color: #3498db;
    color: white;
}

.primary-btn:hover {
    background-color: #2980b9;
}

.secondary-btn {
    background-color: #e74c3c;
    color: white;
    margin-left: 10px;
}

.secondary-btn:hover {
    background-color: #c0392b;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 状态面板 */
.status-panel {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.status-messages {
    height: 150px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    font-size: 13px;
    background-color: #f9f9f9;
}

.status-message {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.status-message.error {
    color: #e74c3c;
}

.status-message.warning {
    color: #f39c12;
}

.status-message.success {
    color: #2ecc71;
}

/* 页脚 */
footer {
    text-align: center;
    padding-top: 20px;
    color: #7f8c8d;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    .video-container {
        grid-column: 1;
        grid-row: 1;
        margin-bottom: 20px;
    }
    
    .connection-panel, .sources-panel {
        grid-column: 1;
    }
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.small-btn {
    padding: 5px 10px;
    font-size: 12px;
}