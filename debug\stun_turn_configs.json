{"stun_presets": {"google": ["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302", "stun:stun3.l.google.com:19302", "stun:stun4.l.google.com:19302"], "mozilla": ["stun:stun.services.mozilla.com"], "cloudflare": ["stun:stun.cloudflare.com:3478"], "twilio": ["stun:global.stun.twilio.com:3478"]}, "turn_presets": {"numb": [{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}], "xirsys": [{"urls": "turn:xirsys.com", "username": "your_username", "credential": "your_credential"}], "twilio": [{"urls": "turn:global.turn.twilio.com:3478?transport=udp", "username": "your_twilio_username", "credential": "your_twilio_credential"}, {"urls": "turn:global.turn.twilio.com:3478?transport=tcp", "username": "your_twilio_username", "credential": "your_twilio_credential"}], "coturn": [{"urls": "turn:your-server.com:3478", "username": "your_username", "credential": "your_password"}]}, "recommended_configs": {"basic": {"description": "基础配置，适用于大多数网络环境", "stun_servers": ["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302"], "turn_servers": [{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}]}, "enterprise": {"description": "企业级配置，使用多个TURN服务器提供冗余", "stun_servers": ["stun:stun.l.google.com:19302", "stun:stun.services.mozilla.com"], "turn_servers": [{"urls": "turn:your-primary-turn.com:3478", "username": "enterprise_user", "credential": "enterprise_pass"}, {"urls": "turn:your-backup-turn.com:3478", "username": "enterprise_user", "credential": "enterprise_pass"}]}, "high_availability": {"description": "高可用配置，包含多个协议和服务器", "stun_servers": ["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302", "stun:stun.services.mozilla.com", "stun:stun.cloudflare.com:3478"], "turn_servers": [{"urls": "turn:turn1.example.com:3478?transport=udp", "username": "user1", "credential": "pass1"}, {"urls": "turn:turn1.example.com:3478?transport=tcp", "username": "user1", "credential": "pass1"}, {"urls": "turn:turn2.example.com:3478?transport=udp", "username": "user2", "credential": "pass2"}, {"urls": "turn:turn2.example.com:3478?transport=tcp", "username": "user2", "credential": "pass2"}]}}, "testing_configs": {"local_development": {"description": "本地开发测试配置", "stun_servers": ["stun:stun.l.google.com:19302"], "turn_servers": []}, "public_servers_only": {"description": "仅使用公共服务器", "stun_servers": ["stun:stun.l.google.com:19302", "stun:stun.services.mozilla.com"], "turn_servers": [{"urls": "turn:numb.viagenie.ca", "username": "<EMAIL>", "credential": "muazkh"}]}}, "ftp_upgrade_examples": {"basic_ftp": "ftp://username:<EMAIL>/path/to/app.apk", "ftp_with_port": "ftp://username:<EMAIL>:2121/path/to/app.apk", "anonymous_ftp": "ftp://ftp.example.com/public/app.apk", "current_server": "ftp://*************/webrtc_sender_builds/webrtc_sender.apk"}, "usage_notes": {"stun_servers": ["STUN服务器用于NAT穿透，帮助设备发现自己的公网IP地址", "通常使用免费的公共STUN服务器就足够了", "建议配置2-3个STUN服务器作为备份"], "turn_servers": ["TURN服务器用于在无法直接连接时中继流量", "需要用户名和密码认证", "建议使用可靠的TURN服务提供商", "可以配置多个TURN服务器提供冗余"], "ftp_upgrade": ["支持标准FTP协议下载APK文件", "URL格式: ftp://[username:password@]host[:port]/path", "用户名和密码可选，默认使用匿名登录", "端口可选，默认使用21端口", "下载完成后会自动验证APK文件完整性"]}}