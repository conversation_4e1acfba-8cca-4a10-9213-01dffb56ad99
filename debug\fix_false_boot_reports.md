# 修复虚假开机上报问题

## 问题描述

从日志中发现很多设备在非开机状态下也发送 `boot_report` 消息，导致监控页面收到大量虚假的开机通知。

## 问题分析

### 1. 原因分析
- **频繁触发**: 每次应用连接或重连都会调用 `checkAndReportBoot`
- **判断不准确**: 仅依赖系统启动时间差异，但SharedPreferences可能丢失或不准确
- **重连误判**: 应用重连时被误判为新开机

### 2. 日志表现
```
📱 收到开机信息: gamev-962e001c | CPU_ID=3cca20b0... | 游戏=None
📱 收到开机信息: gamev-9c198325 | CPU_ID=48729414... | 游戏=None
📱 收到开机信息: gamev-994c2a55 | CPU_ID=588b3fd4... | 游戏=None
```

大量设备同时发送开机信息，且游戏包名为 `None`，说明不是真正的开机。

## 解决方案

### 1. 增强开机检测逻辑

#### 原逻辑问题
```kotlin
// 原来的简单判断
if (currentBootTime != lastReportedBootTime) {
    // 认为是新开机
}
```

#### 新的严格检测
```kotlin
// 更严格的开机检测条件
val isNewBoot = currentBootTime != lastReportedBootTime && currentBootTime > 0
val timeDifference = Math.abs(currentBootTime - lastReportedBootTime)
val isSignificantTimeDifference = timeDifference > 60000 // 超过1分钟差异

// 只有满足所有条件才认为是真正的新开机
if (isNewBoot && isSignificantTimeDifference && (!bootReported || timeDifference > 60000)) {
    // 上报开机信息
}
```

### 2. 应用启动时间跟踪

#### 新增常量
```kotlin
private const val KEY_APP_START_TIME = "app_start_time"
private const val BOOT_TIME_THRESHOLD = 5 * 60 * 1000L // 5分钟阈值
```

#### 应用启动检测
```kotlin
private suspend fun checkAndReportBootOnAppStart(senderId: String) {
    val currentTime = System.currentTimeMillis()
    val lastAppStartTime = prefs.getLong(KEY_APP_START_TIME, 0L)
    
    // 记录本次应用启动时间
    prefs.edit().putLong(KEY_APP_START_TIME, currentTime).apply()
    
    // 只在应用首次启动或距离上次启动超过阈值时检查开机
    val timeSinceLastStart = currentTime - lastAppStartTime
    if (lastAppStartTime == 0L || timeSinceLastStart > BOOT_TIME_THRESHOLD) {
        checkAndReportBoot(senderId)
    } else {
        Log.d(TAG, "📱 应用重连，跳过开机检查")
    }
}
```

### 3. 调用逻辑优化

#### 修改连接建立处理
```kotlin
suspend fun onConnectionEstablished(senderId: String) {
    // 只在应用首次启动时检查开机信息
    checkAndReportBootOnAppStart(senderId)
    
    // 正常的设备信息上报
    reportDeviceInfo(senderId)
}
```

## 检测条件总结

### 1. 应用启动级别检测
- **首次启动**: `lastAppStartTime == 0L` → 检查开机
- **长时间未启动**: `timeSinceLastStart > 5分钟` → 检查开机  
- **短时间重连**: `timeSinceLastStart < 5分钟` → 跳过检查

### 2. 系统开机级别检测
- **启动时间有效**: `currentBootTime > 0`
- **时间差异显著**: `timeDifference > 1分钟`
- **未上报过或时间差异大**: `!bootReported || timeDifference > 1分钟`

### 3. 综合判断
只有同时满足以下条件才上报开机：
1. 应用启动检测通过
2. 系统开机检测通过
3. 时间差异超过阈值

## 预期效果

### 1. 减少虚假上报
- **应用重连**: 不再触发开机上报
- **短时间重启**: 5分钟内的应用重启不检查开机
- **数据丢失**: 即使SharedPreferences丢失，也有时间差异保护

### 2. 保持真实开机检测
- **系统重启**: 真正的系统重启仍能正确检测
- **长时间离线**: 设备长时间离线后重新上线能正确上报
- **首次安装**: 应用首次安装启动能正确上报

### 3. 日志改进
```
🔍 应用启动检查开机状态 (距离上次启动: 300000ms)
🚀 检测到真正的新开机，准备上报开机信息 (时间差异: 3600000ms)
📱 应用重连，跳过开机检查 (距离上次启动: 30000ms)
```

## 版本信息

### Android应用
- **版本**: 1.0.2 → 1.0.3
- **版本代码**: 102 → 103
- **更新内容**: 修复虚假开机上报问题

### 主要改进
1. **增强开机检测逻辑**: 多重条件验证
2. **应用启动时间跟踪**: 避免重连误判
3. **时间差异阈值**: 防止频繁误报
4. **详细日志记录**: 便于问题排查

## 测试建议

### 1. 正常开机测试
- 设备真正重启后应能正确上报开机信息
- 首次安装应用应能上报开机信息

### 2. 重连测试  
- 应用重启不应触发开机上报
- 网络重连不应触发开机上报
- 短时间内多次连接不应重复上报

### 3. 边界测试
- 应用长时间未启动后重新启动
- SharedPreferences数据丢失情况
- 系统时间异常情况

## 部署建议

1. **更新Android应用**: 部署新版本到所有设备
2. **观察日志**: 监控开机上报的频率和准确性
3. **清理数据**: 可考虑清理旧的SharedPreferences数据
4. **监控效果**: 观察监控页面是否还有大量虚假开机通知

现在应该能显著减少虚假的开机上报，只在真正开机时才发送通知！
