#!/usr/bin/env python3

import asyncio
import websockets
from websockets.exceptions import ConnectionClosed, ConnectionClosedError
import json
import logging
import os
import ssl
import argparse
import mimetypes
import time
import traceback
from http import HTTPStatus
from http.server import HTTPServer, SimpleHTTPRequestHandler
from functools import partial
from threading import Thread
import socket
import sys
import hashlib
import hmac
import uuid
from datetime import datetime
import pymysql
import aiohttp

# 版本信息
VERSION = "1.4.5"


# 导入签名配置
try:
    from signature_config import get_signature_password, is_signature_enabled, get_signature_timeout, is_debug_mode
except ImportError:
    # 如果配置文件不存在，使用默认值
    def get_signature_password():
        return "fa0p23497fh-397rgf3f#"
    def is_signature_enabled():
        return True
    def get_signature_timeout():
        return 300
    def is_debug_mode():
        return True

# 日志配置将在main函数中设置
logger = logging.getLogger('signaling-server')

# 数据库配置
DB_CONFIG = {
    'host': '************',
    'db': 'dsender',
    'user': 'dsender',
    'passwd': 'dyZp7taLAxCZJGcX',
    'charset': 'utf8mb4',
    'port': 3306
}

# 数据库连接
def init_database():
    """初始化数据库连接"""
    try:
        # 测试数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        connection.close()
        logging.info("数据库连接测试成功")
        return True
    except Exception as e:
        logging.error(f"数据库连接测试失败: {e}")
        return False

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logging.error(f"获取数据库连接失败: {e}")
        return None

# 注意：公网IP信息现在由Android端获取，不再需要服务器端获取

async def update_device_info(sender_id, device_info):
    """更新设备信息到数据库"""
    connection = get_db_connection()
    if not connection:
        logging.error("无法获取数据库连接")
        return False

    cursor = None
    try:
        cursor = connection.cursor()

        # 准备SQL语句
        sql = """
        INSERT INTO fa_sender_device_info (
            sender_id, cpu_unique_id, wechat_sn, motherboard_model,
            android_version, system_version, available_storage, total_storage,
            total_memory, available_memory, cpu_temperature,
            public_ip, public_ipv6, local_ip, network_type, mac_address,
            city, region, country, location, isp_org, postal_code, timezone,
            screen_resolution, screen_orientation, system_time,
            app_signaling_url, app_sender_id, app_video_source, app_video_resolution,
            app_video_bitrate, app_video_codec, app_video_framerate, app_camera_id,
            app_screen_capture_quality, app_audio_source, app_auto_start_game,
            app_auto_start_game_package, app_log_display_enabled, app_version,
            app_version_code, app_build_type, app_has_manual_operation, app_first_install,
            app_hdmi_device_path, app_usb_capture_device_path,
            is_online, last_online_time, last_update_time
        ) VALUES (
            %(sender_id)s, %(cpu_unique_id)s, %(wechat_sn)s, %(motherboard_model)s,
            %(android_version)s, %(system_version)s, %(available_storage)s, %(total_storage)s,
            %(total_memory)s, %(available_memory)s, %(cpu_temperature)s,
            %(public_ip)s, %(public_ipv6)s, %(local_ip)s, %(network_type)s, %(mac_address)s,
            %(city)s, %(region)s, %(country)s, %(location)s, %(isp_org)s, %(postal_code)s, %(timezone)s,
            %(screen_resolution)s, %(screen_orientation)s, %(system_time)s,
            %(app_signaling_url)s, %(app_sender_id)s, %(app_video_source)s, %(app_video_resolution)s,
            %(app_video_bitrate)s, %(app_video_codec)s, %(app_video_framerate)s, %(app_camera_id)s,
            %(app_screen_capture_quality)s, %(app_audio_source)s, %(app_auto_start_game)s,
            %(app_auto_start_game_package)s, %(app_log_display_enabled)s, %(app_version)s,
            %(app_version_code)s, %(app_build_type)s, %(app_has_manual_operation)s, %(app_first_install)s,
            %(app_hdmi_device_path)s, %(app_usb_capture_device_path)s,
            1, NOW(), NOW()
        ) ON DUPLICATE KEY UPDATE
            cpu_unique_id = VALUES(cpu_unique_id),
            wechat_sn = VALUES(wechat_sn),
            motherboard_model = VALUES(motherboard_model),
            android_version = VALUES(android_version),
            system_version = VALUES(system_version),
            available_storage = VALUES(available_storage),
            total_storage = VALUES(total_storage),
            total_memory = VALUES(total_memory),
            available_memory = VALUES(available_memory),
            cpu_temperature = VALUES(cpu_temperature),
            public_ip = VALUES(public_ip),
            public_ipv6 = VALUES(public_ipv6),
            local_ip = VALUES(local_ip),
            network_type = VALUES(network_type),
            mac_address = VALUES(mac_address),
            city = VALUES(city),
            region = VALUES(region),
            country = VALUES(country),
            location = VALUES(location),
            isp_org = VALUES(isp_org),
            postal_code = VALUES(postal_code),
            timezone = VALUES(timezone),
            screen_resolution = VALUES(screen_resolution),
            screen_orientation = VALUES(screen_orientation),
            system_time = VALUES(system_time),
            app_signaling_url = VALUES(app_signaling_url),
            app_sender_id = VALUES(app_sender_id),
            app_video_source = VALUES(app_video_source),
            app_video_resolution = VALUES(app_video_resolution),
            app_video_bitrate = VALUES(app_video_bitrate),
            app_video_codec = VALUES(app_video_codec),
            app_video_framerate = VALUES(app_video_framerate),
            app_camera_id = VALUES(app_camera_id),
            app_screen_capture_quality = VALUES(app_screen_capture_quality),
            app_audio_source = VALUES(app_audio_source),
            app_auto_start_game = VALUES(app_auto_start_game),
            app_auto_start_game_package = VALUES(app_auto_start_game_package),
            app_log_display_enabled = VALUES(app_log_display_enabled),
            app_version = VALUES(app_version),
            app_version_code = VALUES(app_version_code),
            app_build_type = VALUES(app_build_type),
            app_has_manual_operation = VALUES(app_has_manual_operation),
            app_first_install = VALUES(app_first_install),
            app_hdmi_device_path = VALUES(app_hdmi_device_path),
            app_usb_capture_device_path = VALUES(app_usb_capture_device_path),
            is_online = 1,
            last_online_time = NOW(),
            last_update_time = NOW(),
            heartbeat_count = heartbeat_count + 1
        """

        # 执行SQL
        cursor.execute(sql, device_info)
        connection.commit()

        logging.info(f"设备信息已更新: {sender_id}")
        return True

    except Exception as e:
        logging.error(f"更新设备信息失败: {e}")
        connection.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def mark_device_offline(sender_id):
    """标记设备离线"""
    connection = get_db_connection()
    if not connection:
        return False

    cursor = None
    try:
        cursor = connection.cursor()
        sql = """
        UPDATE fa_sender_device_info
        SET is_online = 0, last_offline_time = NOW()
        WHERE sender_id = %s
        """
        cursor.execute(sql, (sender_id,))
        connection.commit()

        logging.info(f"设备已标记为离线: {sender_id}")
        return True

    except Exception as e:
        logging.error(f"标记设备离线失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def add_room_info_fields():
    """添加房间信息字段到设备信息表"""
    connection = get_db_connection()
    if not connection:
        return False

    cursor = None
    try:
        cursor = connection.cursor()

        # 添加房间信息字段和监控密码字段到设备信息表
        alter_device_table_sql = """
        ALTER TABLE fa_sender_device_info
        ADD COLUMN IF NOT EXISTS room_server_domain VARCHAR(255) DEFAULT '' COMMENT '关联的服务器域名',
        ADD COLUMN IF NOT EXISTS room_id INT DEFAULT 0 COMMENT '关联的房间ID',
        ADD COLUMN IF NOT EXISTS room_name VARCHAR(100) DEFAULT '' COMMENT '关联的房间名称',
        ADD COLUMN IF NOT EXISTS room_category_id INT DEFAULT 0 COMMENT '房间分类ID',
        ADD COLUMN IF NOT EXISTS room_sort_order INT DEFAULT 0 COMMENT '房间排序',
        ADD COLUMN IF NOT EXISTS room_last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '房间信息最后更新时间',
        ADD COLUMN IF NOT EXISTS monitor_password VARCHAR(100) DEFAULT '' COMMENT '监控密码，用于权限控制'
        """
        try:
            cursor.execute(alter_device_table_sql)
            connection.commit()
            logging.info("房间信息字段添加完成")
        except Exception as e:
            # 字段可能已存在，忽略错误
            logging.debug(f"添加字段可能已存在: {e}")

        return True

    except Exception as e:
        logging.error(f"添加房间信息字段失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def verify_monitor_password(password):
    """验证监控密码并返回允许访问的服务器域名列表"""
    if not password:
        return None

    # 检查是否是管理员密码（可以访问所有域名）
    if password == BOOT_MONITOR_PASSWORD:
        return ['*']  # 通配符表示所有域名

    connection = get_db_connection()
    if not connection:
        return None

    cursor = None
    try:
        cursor = connection.cursor()

        # 查询匹配该密码的设备的服务器域名
        sql = """
        SELECT DISTINCT room_server_domain
        FROM fa_sender_device_info
        WHERE monitor_password = %s AND room_server_domain != ''
        """
        cursor.execute(sql, (password,))
        results = cursor.fetchall()

        if results:
            # 返回该密码可以访问的服务器域名列表
            allowed_domains = [row[0] for row in results]
            logging.info(f"🔐 密码验证成功，允许访问域名: {allowed_domains}")
            return allowed_domains
        else:
            logging.warning(f"🔐 密码验证失败: {password}")
            return None

    except Exception as e:
        logging.error(f"验证监控密码失败: {e}")
        return None
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def get_devices_room_info():
    """获取所有设备的房间信息"""
    connection = get_db_connection()
    if not connection:
        return {}

    cursor = None
    try:
        cursor = connection.cursor()

        # 查询所有设备的房间信息
        sql = """
        SELECT sender_id, room_name, room_server_domain, room_category_id, monitor_password
        FROM fa_sender_device_info
        WHERE sender_id != ''
        """
        cursor.execute(sql)
        results = cursor.fetchall()

        device_room_info = {}
        for row in results:
            sender_id, room_name, room_server_domain, room_category_id, monitor_password = row
            device_room_info[sender_id] = {
                'room_name': room_name or '',
                'room_server_domain': room_server_domain or '',
                'room_category_id': room_category_id or 0,
                'monitor_password': monitor_password or ''
            }

        return device_room_info

    except Exception as e:
        logging.error(f"获取设备房间信息失败: {e}")
        return {}
    finally:
        if cursor:
            cursor.close()
        connection.close()

async def get_devices_room_info_cached():
    """获取设备房间信息（带缓存）"""
    global device_room_info_cache, device_room_info_cache_time

    current_time = time.time()

    # 检查缓存是否有效
    if (current_time - device_room_info_cache_time) < CACHE_EXPIRE_TIME and device_room_info_cache:
        logger.debug("📋 使用缓存的设备房间信息")
        return device_room_info_cache

    # 缓存过期，重新获取
    logger.debug("📋 刷新设备房间信息缓存")
    device_room_info_cache = await get_devices_room_info()
    device_room_info_cache_time = current_time

    return device_room_info_cache

async def fetch_room_info_from_server(server_config):
    """从服务器获取房间信息"""
    # 兼容旧的字符串格式和新的配置格式
    if isinstance(server_config, str):
        domain = server_config
        method = 'GET'
        name = domain
    else:
        domain = server_config.get('domain', '')
        method = server_config.get('method', 'GET').upper()
        name = server_config.get('name', domain)

    api_url = f"{domain}/api/game/list_dcl_ast4"

    # 定义请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json'
    }

    try:
        async with aiohttp.ClientSession() as session:
            if method == 'POST':
                # 使用POST请求
                post_data = {}  # 空的POST数据
                async with session.post(api_url, headers=headers, json=post_data, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('code') == 1 and 'data' in data:
                            logging.info(f"✅ POST请求成功获取 {name} ({domain}) 的房间信息，共 {len(data['data'])} 个房间")
                            return data['data']
                        else:
                            logging.warning(f"⚠️ 服务器 {name} POST请求返回错误: {data.get('msg', '未知错误')}")
                            return []
                    else:
                        logging.warning(f"⚠️ 服务器 {name} POST请求响应状态码: {response.status}")
                        return []
            else:
                # 使用GET请求
                async with session.get(api_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('code') == 1 and 'data' in data:
                            logging.info(f"✅ GET请求成功获取 {name} ({domain}) 的房间信息，共 {len(data['data'])} 个房间")
                            return data['data']
                        else:
                            logging.warning(f"⚠️ 服务器 {name} GET请求返回错误: {data.get('msg', '未知错误')}")
                            return []
                    else:
                        logging.warning(f"⚠️ 服务器 {name} GET请求响应状态码: {response.status}")
                        return []

    except Exception as e:
        logging.error(f"❌ 获取服务器 {name} ({domain}) 房间信息失败: {e}")
        return []

async def update_room_info_to_database(domain, rooms_data):
    """直接更新设备信息表的房间信息"""
    connection = get_db_connection()
    if not connection:
        return False

    cursor = None
    try:
        cursor = connection.cursor()

        # 先清理该服务器域名的房间信息
        clear_sql = """
        UPDATE fa_sender_device_info
        SET room_server_domain = '', room_id = 0, room_name = '',
            room_category_id = 0, room_sort_order = 0, room_last_update = NOW()
        WHERE room_server_domain = %s
        """
        cursor.execute(clear_sql, (domain,))

        # 直接更新设备信息表的房间关联信息
        update_count = 0
        for room in rooms_data:
            sender_id = room.get('heiqiplayer_sender_id', '')
            if sender_id:  # 只处理有发送端ID的房间
                update_device_sql = """
                UPDATE fa_sender_device_info
                SET room_server_domain = %s,
                    room_id = %s,
                    room_name = %s,
                    room_category_id = %s,
                    room_sort_order = %s,
                    room_last_update = NOW()
                WHERE sender_id = %s
                """
                cursor.execute(update_device_sql, (
                    domain,
                    room.get('id', 0),
                    room.get('name', ''),
                    room.get('c_id', 0),
                    room.get('sort', 0),
                    sender_id
                ))
                if cursor.rowcount > 0:
                    update_count += 1

        connection.commit()
        logging.info(f"成功更新 {domain} 的房间信息，共处理 {len(rooms_data)} 个房间，更新 {update_count} 个设备")
        return True

    except Exception as e:
        logging.error(f"更新房间信息到数据库失败: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        connection.close()

connected_clients = {}
stream_sources = {}

# 连接状态跟踪
connection_states = {}
active_connections = {}

# 安全发送消息函数
async def safe_send_message(client_id: str, message: dict) -> bool:
    """
    安全发送消息到客户端，直接尝试发送并处理异常
    """
    if client_id not in connected_clients:
        logger.warning(f"⚠️ 客户端 {client_id} 不在连接列表中")
        return False

    try:
        websocket = connected_clients[client_id]

        # 直接尝试发送消息，不进行类型检查
        await websocket.send(json.dumps(message))
        return True
    except (ConnectionClosed, ConnectionClosedError) as e:
        logger.warning(f"⚠️ 客户端 {client_id} 连接已断开: {e}")
        # 清理已断开的连接
        if client_id in connected_clients:
            del connected_clients[client_id]
        return False
    except AttributeError as e:
        # 处理对象没有send方法的情况
        websocket_type = type(connected_clients[client_id]).__name__
        logger.error(f"❌ 客户端 {client_id} WebSocket对象类型异常: {websocket_type} | 错误: {e}")
        # 清理异常对象
        if client_id in connected_clients:
            del connected_clients[client_id]
        return False
    except Exception as e:
        logger.error(f"❌ 发送消息到 {client_id} 失败: {e}")
        return False

# 开机设备信息存储
boot_devices = {}  # 格式: {cpu_unique_id: {boot_info, boot_time, last_update}}

# 监控订阅者
boot_monitor_subscribers = {}  # 存储订阅开机监控的WebSocket连接和权限信息

# 监控密码配置
BOOT_MONITOR_PASSWORD = "tb###"  # 可以从配置文件读取

# 缓存机制
device_room_info_cache = {}
device_room_info_cache_time = 0
CACHE_EXPIRE_TIME = 30  # 缓存30秒

# 设备在线状态管理
device_online_status = {}  # 格式: {sender_id: {cpu_unique_id, last_heartbeat, is_online}}

def format_uptime(seconds):
    """格式化运行时间显示"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{minutes}分{secs}秒"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}天{hours}小时"

async def check_device_offline():
    """检查设备离线状态"""
    try:
        current_time = time.time()
        offline_devices = []

        for sender_id, status in device_online_status.items():
            if status['is_online'] and current_time - status['last_heartbeat'] > 60:
                # 设备离线
                status['is_online'] = False
                offline_devices.append(sender_id)
                logger.info(f"📱 设备离线: {sender_id} (CPU: {status['cpu_unique_id'][:8]}...)")

        # 如果有设备离线，通知监控订阅者
        if offline_devices and boot_monitor_subscribers:
            await send_boot_devices_update()

    except Exception as e:
        logger.error(f"检查设备离线状态失败: {e}")

async def send_boot_devices_update():
    """发送设备状态更新到所有监控订阅者"""
    try:
        if not boot_monitor_subscribers:
            return

        # 获取所有设备状态
        devices_status = []
        for sender_id, status in boot_devices_status.items():
            devices_status.append({
                'sender_id': sender_id,
                'cpu_unique_id': status.get('cpu_unique_id', ''),
                'is_online': status.get('is_online', False),
                'last_boot_time': status.get('last_boot_time', 0),
                'last_update_time': status.get('last_update_time', 0),
                'room_server_domain': status.get('room_server_domain', ''),
                'room_name': status.get('room_name', ''),
                'game_package': status.get('game_package', '')
            })

        # 构建更新消息
        update_message = {
            'type': 'boot_devices_update',
            'devices': devices_status,
            'timestamp': int(time.time() * 1000)
        }

        # 发送给所有监控订阅者
        disconnected_subscribers = []
        for websocket in list(boot_monitor_subscribers.keys()):
            try:
                await websocket.send(json.dumps(update_message))
            except Exception as e:
                logger.warning(f"发送设备状态更新失败: {e}")
                disconnected_subscribers.append(websocket)

        # 清理断开的订阅者
        for websocket in disconnected_subscribers:
            boot_monitor_subscribers.pop(websocket, None)

        logger.debug(f"📱 已发送设备状态更新给 {len(boot_monitor_subscribers)} 个订阅者")

    except Exception as e:
        logger.error(f"发送设备状态更新失败: {e}")

async def send_new_boot_device(cpu_unique_id, device_info):
    """发送新开机设备信息到所有监控订阅者"""
    try:
        current_time = time.time() * 1000
        sender_id = device_info.get('sender_id', 'unknown')

        # 获取设备的房间信息（缓存优化）
        device_room_info = await get_devices_room_info_cached()
        room_info = device_room_info.get(sender_id, {})
        room_server_domain = room_info.get('room_server_domain', '')

        # 构建设备数据
        boot_time = device_info['boot_time']
        uptime_seconds = int((current_time - boot_time) / 1000)

        device_data = {
            'cpu_unique_id': cpu_unique_id,
            'sender_id': sender_id,
            'boot_time': boot_time,
            'uptime_seconds': uptime_seconds,
            'uptime_display': format_uptime(uptime_seconds),
            'auto_start_game_package': device_info['device_info'].get('auto_start_game_package', ''),
            'device_brand': device_info['device_info'].get('brand', 'Unknown'),
            'device_model': device_info['device_info'].get('model', 'Unknown'),
            'android_version': device_info['device_info'].get('android_version', 'Unknown'),
            'app_version': device_info['device_info'].get('app_version', 'Unknown'),
            'local_ip': device_info['device_info'].get('local_ip', 'Unknown'),
            'public_ip': device_info['device_info'].get('public_ip', 'Unknown'),
            'report_time': device_info.get('report_time', current_time),
            'last_update': device_info.get('last_update', current_time),
            'is_online': True,  # 新开机的设备默认在线
            'room_name': room_info.get('room_name', ''),
            'room_server_domain': room_server_domain,
            'room_category_id': room_info.get('room_category_id', 0),
            'data_source': 'boot_report'
        }

        # 发送给所有监控订阅者（根据权限过滤）
        for ws, subscriber_info in list(boot_monitor_subscribers.items()):
            try:
                allowed_domains = subscriber_info.get('allowed_domains', ['*'])

                # 权限检查
                if '*' not in allowed_domains:
                    if not room_server_domain or room_server_domain not in allowed_domains:
                        continue  # 跳过无权限访问的设备

                # 发送单个设备的开机信息
                message = {
                    'type': 'new_boot_device',
                    'success': True,
                    'device': device_data,
                    'timestamp': current_time
                }

                await ws.send(json.dumps(message))
                logger.debug(f"📤 发送新开机设备到监控页面: {sender_id} -> {subscriber_info.get('remote_address', 'unknown')}")

            except Exception as e:
                logger.error(f"发送新开机设备到订阅者失败: {e}")
                # 移除失效的订阅者
                boot_monitor_subscribers.pop(ws, None)

    except Exception as e:
        logger.error(f"发送新开机设备失败: {e}")






# 心跳超时跟踪
last_heartbeat = {}  # 记录每个客户端的最后心跳时间

# 管理控制台连接
admin_consoles = {}  # 存储管理控制台连接
HEARTBEAT_TIMEOUT = 180  # 180秒心跳超时（增加到3分钟）

# 接收端持久连接管理
receiver_connections = {}  # 专门管理接收端连接
receiver_reconnect_tasks = {}  # 接收端重连任务

# 接收端状态监控
receiver_status = {}  # 接收端状态信息
STATUS_REPORT_INTERVAL = 60  # 60秒状态报告间隔

# 服务器配置管理
server_config = {
    'stun_servers': [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302'
    ],
    'turn_servers': [
        {
            'urls': 'turn:numb.viagenie.ca',
            'username': '<EMAIL>',
            'credential': 'muazkh'
        }
    ]
}

# 服务器群配置
SERVER_GROUPS = [
    {
        'domain': 'http://testva2.91jdcd.com',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '测试服务器V2'
    },
    {
        'domain': 'http://bsth5.yinmengkj.cn',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '银梦科技服务器'
    },
    {
        'domain': 'http://yx.yhdyc.com',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '游戏服务器'
    }
    # 可以添加更多服务器配置
]

# 房间信息更新配置
ROOM_INFO_UPDATE_ON_STARTUP = True  # 启动时更新房间信息

# 安全验证配置（使用配置文件）
SECURITY_PASSWORD = get_signature_password()  # 发送端和信令服务器共享的密码
COMMAND_TIMEOUT = get_signature_timeout()  # 命令有效期
REQUIRE_SIGNATURE = is_signature_enabled()  # 是否要求所有操作都需要验签

# 异步任务队列
pending_tasks = []

# 升级管理
upgrade_config = {
    'apk_url': '',
    'version': '',
    'force_upgrade': False
}


def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return '127.0.0.1'


def generate_device_id(device_info):
    """根据设备信息生成设备ID的MD5"""
    device_string = f"{device_info.get('brand', '')}{device_info.get('model', '')}{device_info.get('serial', '')}"
    return hashlib.md5(device_string.encode()).hexdigest()


def verify_command_signature(command_data, device_id, signature):
    """验证命令签名"""
    try:
        # 提取时间戳
        timestamp = command_data.get('timestamp', 0)
        current_time = int(time.time())

        # 检查时间戳有效性（5分钟内）
        if abs(current_time - timestamp) > COMMAND_TIMEOUT:
            logger.warning(f"命令时间戳过期: {timestamp}, 当前时间: {current_time}")
            return False

        # 生成设备密钥
        device_key = hashlib.md5(device_id.encode()).hexdigest()

        # 生成验证字符串
        command_json = json.dumps(command_data, sort_keys=True)
        verify_string = f"{command_json}{device_key}{timestamp}"

        # 计算期望的签名
        expected_signature = hashlib.md5(verify_string.encode()).hexdigest()

        # 验证签名
        return hmac.compare_digest(signature, expected_signature)
    except Exception as e:
        logger.error(f"验证命令签名失败: {e}")
        return False


def create_signed_command(command_data, device_id):
    """创建带签名的命令：密码 + 设备号 + 命令JSON + 时间戳"""
    try:
        # 添加时间戳
        command_data['timestamp'] = int(time.time())

        # 生成命令JSON（不包含签名）
        command_json = json.dumps(command_data, sort_keys=True)

        # 生成MD5签名：密码 + 设备号 + 命令JSON + 时间戳
        signature = generate_md5_signature(
            SECURITY_PASSWORD,
            device_id,
            command_json,
            command_data['timestamp']
        )

        if signature:
            # 添加签名到命令
            command_data['signature'] = signature
            logger.info(f"🔐 已为设备 {device_id} 生成MD5签名: {signature[:8]}...")
            return command_data
        else:
            logger.error(f"为设备 {device_id} 生成签名失败")
            return None

    except Exception as e:
        logger.error(f"创建签名命令失败: {e}")
        return None


def parse_ice_candidate(candidate_data):
    """解析ICE候选信息"""
    try:
        # 检查候选数据的类型
        if isinstance(candidate_data, dict):
            # 如果是字典，直接提取信息
            candidate_str = candidate_data.get('candidate', '')
            sdp_mid = candidate_data.get('sdpMid', '')
            sdp_m_line_index = candidate_data.get('sdpMLineIndex', '')

            logger.debug(f"ICE候选字典: sdpMid={sdp_mid}, sdpMLineIndex={sdp_m_line_index}")

            if not candidate_str:
                return {
                    'type': 'unknown',
                    'protocol': 'unknown',
                    'ip': 'unknown',
                    'port': 'unknown',
                    'priority': 'unknown',
                    'sdpMid': sdp_mid,
                    'sdpMLineIndex': sdp_m_line_index
                }
        elif isinstance(candidate_data, str):
            candidate_str = candidate_data
        else:
            logger.error(f"未知的候选数据类型: {type(candidate_data)}")
            return None

        # 解析候选字符串
        if candidate_str.startswith('candidate:'):
            candidate_str = candidate_str[10:]  # 移除 "candidate:" 前缀

        parts = candidate_str.split()
        if len(parts) >= 6:
            foundation = parts[0]
            component = parts[1]
            protocol = parts[2]
            priority = parts[3]
            ip = parts[4]
            port = parts[5]
            typ = parts[7] if len(parts) > 7 else "unknown"

            return {
                'foundation': foundation,
                'component': component,
                'protocol': protocol,
                'priority': priority,
                'ip': ip,
                'port': port,
                'type': typ,
                'sdpMid': candidate_data.get('sdpMid', '') if isinstance(candidate_data, dict) else '',
                'sdpMLineIndex': candidate_data.get('sdpMLineIndex', '') if isinstance(candidate_data, dict) else ''
            }
    except Exception as e:
        logger.error(f"解析ICE候选失败: {e}")
        logger.error(f"候选数据: {candidate_data}")
        logger.error(f"数据类型: {type(candidate_data)}")
    return None


def log_connection_state_change(client_id, target_id, state):
    """记录连接状态变化"""
    connection_key = f"{client_id}->{target_id}"
    old_state = connection_states.get(connection_key, "unknown")

    if old_state != state:
        connection_states[connection_key] = state
        logger.info(f"🔗 STATE: {connection_key} | {old_state}->{state}")

        # 记录活跃连接
        if state in ["connected", "completed"]:
            active_connections[connection_key] = {
                'from': client_id,
                'to': target_id,
                'state': state,
                'timestamp': int(time.time())
            }
            logger.info(f"✅ ACTIVE: {connection_key} | 连接建立成功")
        elif state in ["disconnected", "failed", "closed"]:
            if connection_key in active_connections:
                del active_connections[connection_key]
                logger.info(f"❌ INACTIVE: {connection_key} | 连接已断开")


def log_active_connections():
    """打印当前活跃连接"""
    if active_connections:
        logger.info(f"📊 SUMMARY: 活跃连接数:{len(active_connections)}")
        for key, conn in active_connections.items():
            duration = int(time.time() - conn['timestamp'])
            logger.info(f"📊 ACTIVE: {key} | 状态:{conn['state']} | 持续:{duration}秒")
    else:
        logger.info("📊 SUMMARY: 当前无活跃连接")

async def cleanup_client_by_id(client_id):
    """根据客户端ID清理连接"""
    logger.info(f"🧹 开始清理客户端: {client_id}")

    # 从连接列表中移除
    if client_id in connected_clients:
        del connected_clients[client_id]
        logger.info(f"🔌 客户端已从连接列表移除: {client_id}")

    # 清理相关的连接状态
    keys_to_remove = []
    for key in list(connection_states.keys()):
        if client_id in key:
            keys_to_remove.append(key)

    for key in keys_to_remove:
        if key in connection_states:
            del connection_states[key]
            logger.info(f"🔗 清理连接状态: {key}")

    for key in keys_to_remove:
        if key in active_connections:
            del active_connections[key]
            logger.info(f"🔗 清理连接状态: {key}")

    # 清理视频源
    if client_id in stream_sources:
        del stream_sources[client_id]
        logger.info(f"📹 视频源已移除: {client_id}")

    # 清理心跳记录
    if client_id in last_heartbeat:
        del last_heartbeat[client_id]
        logger.info(f"💓 清理心跳记录: {client_id}")

def is_client_really_online(client_id):
    """智能检查客户端是否真正在线"""
    # 1. 检查是否在连接列表中
    if client_id not in connected_clients:
        logger.debug(f"🔍 客户端 {client_id} 不在连接列表中")
        return False

    # 2. 检查WebSocket连接状态
    websocket = connected_clients[client_id]
    try:
        if hasattr(websocket, 'closed') and websocket.closed:
            logger.debug(f"🔍 客户端 {client_id} WebSocket已关闭")
            return False
        if hasattr(websocket, 'close_code') and websocket.close_code is not None:
            logger.debug(f"🔍 客户端 {client_id} WebSocket有关闭代码: {websocket.close_code}")
            return False
    except Exception as e:
        logger.debug(f"🔍 检查客户端 {client_id} WebSocket状态失败: {e}")
        return False

    # 3. 检查心跳时间（更宽松的检查）
    current_time = int(time.time())
    if client_id in last_heartbeat:
        last_time = last_heartbeat[client_id]
        time_since_heartbeat = current_time - last_time
        if time_since_heartbeat > HEARTBEAT_TIMEOUT:
            logger.debug(f"🔍 客户端 {client_id} 心跳超时: {time_since_heartbeat}秒")
            return False
        logger.debug(f"🔍 客户端 {client_id} 心跳正常: {time_since_heartbeat}秒前")
    else:
        logger.debug(f"🔍 客户端 {client_id} 无心跳记录")
        # 如果没有心跳记录但在连接列表中，可能是刚连接，给予宽限
        return True

    return True

async def check_heartbeat_timeouts():
    """检查心跳超时的客户端"""
    current_time = int(time.time())
    timeout_clients = []

    logger.debug(f"💓 开始心跳超时检查，当前时间: {current_time}")

    for client_id, last_time in list(last_heartbeat.items()):
        time_since_heartbeat = current_time - last_time
        if time_since_heartbeat > HEARTBEAT_TIMEOUT:
            timeout_clients.append(client_id)
            logger.debug(f"⏰ 发现超时客户端: {client_id}, 超时时间: {time_since_heartbeat}秒")

    if timeout_clients:
        logger.info(f"⏰ 发现 {len(timeout_clients)} 个心跳超时客户端: {timeout_clients}")

        for client_id in timeout_clients:
            if client_id in connected_clients:
                logger.warning(f"⏰ 客户端 {client_id} 心跳超时，清理连接")
                await cleanup_client_by_id(client_id)
    else:
        logger.debug(f"💓 心跳检查完成，所有客户端正常")


async def send_server_config(client_id):
    """向接收端发送服务器配置"""
    if client_id not in connected_clients:
        return False

    try:
        config_message = {
            'type': 'server_config',
            'stun_servers': server_config['stun_servers'],
            'turn_servers': server_config['turn_servers'],
            'timestamp': int(time.time())
        }

        # 为配置消息添加签名
        signed_config = create_signed_command(config_message, client_id)
        if signed_config:
            config_message = signed_config
            logger.info(f"🔐 已为配置添加签名: {client_id}")
        else:
            logger.warning(f"⚠️ 为配置添加签名失败: {client_id}")

        await connected_clients[client_id].send(json.dumps(config_message))
        logger.info(f"📤 已发送服务器配置给 {client_id}")
        return True
    except Exception as e:
        logger.error(f"发送服务器配置失败: {client_id} | 错误: {e}")
        return False


async def broadcast_config_to_receivers():
    """向所有在线客户端广播最新配置"""
    success_count = 0
    total_count = 0

    # 向所有连接的客户端发送配置（包括发送端和接收端）
    for client_id in list(connected_clients.keys()):
        total_count += 1
        if await send_server_config(client_id):
            success_count += 1

    logger.info(f"📡 配置广播完成: {success_count}/{total_count} 个客户端")
    return success_count, total_count


async def send_config_to_specific_receivers(receiver_ids):
    """向指定的接收端发送配置"""
    success_count = 0

    for client_id in receiver_ids:
        if client_id in connected_clients and client_id in receiver_connections:
            if await send_server_config(client_id):
                success_count += 1
        else:
            logger.warning(f"接收端 {client_id} 不在线或未注册")

    logger.info(f"📡 指定配置发送完成: {success_count}/{len(receiver_ids)} 个接收端")
    return success_count


async def broadcast_to_admin_consoles(message):
    """向所有管理控制台广播消息"""
    if not admin_consoles:
        return

    message_json = json.dumps(message)
    disconnected_consoles = []

    for console_id, websocket in admin_consoles.items():
        try:
            await websocket.send(message_json)
            logger.debug(f"📤 已向管理控制台发送消息: {console_id}")
        except Exception as e:
            logger.warning(f"📤 向管理控制台发送消息失败: {console_id} | 错误: {e}")
            disconnected_consoles.append(console_id)

    # 清理断开的连接
    for console_id in disconnected_consoles:
        admin_consoles.pop(console_id, None)
        logger.info(f"🧹 已清理断开的管理控制台: {console_id}")


async def send_control_command(client_id, command, params=None, device_id=None):
    """向接收端发送控制命令"""
    if client_id not in connected_clients:
        logger.error(f"客户端 {client_id} 不在线，无法发送命令")
        return False

    try:
        command_data = {
            'type': 'control_command',
            'command': command,
            'params': params or {},
            'timestamp': int(time.time())
        }

        # 如果提供了设备ID，添加签名
        if device_id:
            signed_command = create_signed_command(command_data, device_id)
            if not signed_command:
                logger.error(f"创建签名命令失败: {client_id}")
                return False
            command_data = signed_command

        await connected_clients[client_id].send(json.dumps(command_data))
        logger.info(f"📤 已发送控制命令给 {client_id}: {command}")
        return True
    except Exception as e:
        logger.error(f"发送控制命令失败: {client_id} | 错误: {e}")
        return False


async def send_upgrade_command(client_id, apk_url, version, force=False, device_id=None):
    """向接收端发送升级命令"""
    if client_id not in connected_clients:
        logger.error(f"客户端 {client_id} 不在线，无法发送升级命令")
        return False

    try:
        upgrade_data = {
            'type': 'upgrade_command',
            'apk_url': apk_url,
            'version': version,
            'force': force,
            'timestamp': int(time.time())
        }

        # 如果提供了设备ID，添加签名
        if device_id:
            signed_command = create_signed_command(upgrade_data, device_id)
            if not signed_command:
                logger.error(f"创建签名升级命令失败: {client_id}")
                return False
            upgrade_data = signed_command

        await connected_clients[client_id].send(json.dumps(upgrade_data))
        logger.info(f"📤 已发送升级命令给 {client_id}: {version}")
        return True
    except Exception as e:
        logger.error(f"发送升级命令失败: {client_id} | 错误: {e}")
        return False


async def log_receiver_status(client_id, status_data):
    """记录接收端状态到日志"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 更新内存中的状态
        receiver_status[client_id] = {
            **status_data,
            'last_update': timestamp,
            'timestamp': int(time.time())
        }

        # 记录到日志
        logger.info(f"📊 接收端状态 [{client_id}] {timestamp}:")
        logger.info(f"   推流状态: {status_data.get('streaming_status', 'unknown')}")
        logger.info(f"   服务状态: {status_data.get('service_status', 'unknown')}")
        logger.info(f"   分辨率: {status_data.get('resolution', 'unknown')}")
        logger.info(f"   码率: {status_data.get('bitrate', 'unknown')} kbps")
        logger.info(f"   编码: {status_data.get('codec', 'unknown')}")
        logger.info(f"   上传速率: {status_data.get('upload_speed', 'unknown')} kbps")
        logger.info(f"   连接用户数: {status_data.get('connected_users', 0)}")
        logger.info(f"   CPU使用率: {status_data.get('cpu_usage', 'unknown')}%")
        logger.info(f"   内存使用: {status_data.get('memory_usage', 'unknown')} MB")

        # 可以在这里添加写入文件的逻辑
        # await write_status_to_file(client_id, status_data, timestamp)

    except Exception as e:
        logger.error(f"记录接收端状态失败: {client_id} | 错误: {e}")


def add_async_task(task_type, task_data):
    """添加异步任务到队列"""
    task = {
        'type': task_type,
        'data': task_data,
        'timestamp': int(time.time())
    }
    pending_tasks.append(task)
    logger.info(f"📋 添加异步任务: {task_type}, 当前队列长度: {len(pending_tasks)}")

    # 立即处理任务（用于调试）
    if task_type == 'broadcast_config':
        logger.info(f"🔄 立即处理广播任务...")
        # 创建一个新的事件循环来处理异步任务
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(broadcast_config_to_receivers())
            loop.close()
            logger.info(f"📡 立即广播结果: {result}")
        except Exception as e:
            logger.error(f"❌ 立即广播失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")


async def process_pending_tasks():
    """处理待处理的异步任务"""
    while pending_tasks:
        task = pending_tasks.pop(0)
        task_type = task['type']
        task_data = task['data']

        logger.info(f"🔄 开始处理异步任务: {task_type}")

        try:
            if task_type == 'broadcast_config':
                logger.info(f"📡 执行配置广播...")
                success_count, total_count = await broadcast_config_to_receivers()
                logger.info(f"📡 配置广播结果: {success_count}/{total_count}")
            elif task_type == 'send_config':
                receiver_ids = task_data.get('receiver_ids', [])
                logger.info(f"📡 向指定客户端发送配置: {receiver_ids}")
                await send_config_to_specific_receivers(receiver_ids)
            elif task_type == 'send_command':
                client_id = task_data.get('client_id')
                command_data = task_data.get('command_data')
                device_id = task_data.get('device_id')
                logger.info(f"🎮 发送命令到 {client_id}: {command_data.get('command')}")
                await send_control_command(client_id, command_data.get('command'),
                                         command_data.get('params', {}), device_id)

            logger.info(f"✅ 异步任务完成: {task_type}")
        except Exception as e:
            logger.error(f"❌ 异步任务失败: {task_type} | 错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")


def generate_md5_signature(password, device_id, command_json, timestamp):
    """生成MD5签名：密码 + 设备号 + 命令JSON + 时间戳"""
    try:
        # 构造签名字符串：密码 + 设备号 + 命令JSON + 时间戳
        sign_string = f"{password}{device_id}{command_json}{timestamp}"

        # 计算MD5
        md5_hash = hashlib.md5(sign_string.encode('utf-8')).hexdigest()

        if is_debug_mode():
            logger.info(f"🔐 签名生成: 密码={password[:3]}***, 设备={device_id}, 时间戳={timestamp}")
            #logger.info(f"🔐 签名字符串: {sign_string[:100]}...")
            logger.info(f"🔐 MD5签名: {md5_hash}")
        else:
            logger.debug(f"🔐 为设备 {device_id} 生成签名: {md5_hash[:8]}...")

        return md5_hash
    except Exception as e:
        logger.error(f"生成MD5签名失败: {e}")
        return None


def verify_request_signature(request_data, signature, client_id=None):
    """验证请求签名：密码 + 设备号 + 命令JSON + 时间戳"""
    if not REQUIRE_SIGNATURE:
        return True

    if not signature:
        logger.warning("缺少签名")
        return False

    try:
        # 使用提供的client_id或默认设备ID
        device_id = client_id or 'admin-console'

        # 生成请求JSON
        request_json = json.dumps(request_data, sort_keys=True)
        timestamp = request_data.get('timestamp', int(time.time()))

        # 计算期望的MD5签名：密码 + 设备号 + 命令JSON + 时间戳
        expected_signature = generate_md5_signature(
            SECURITY_PASSWORD,
            device_id,
            request_json,
            timestamp
        )

        if not expected_signature:
            logger.error("生成期望签名失败")
            return False

        logger.debug(f"签名验证: 收到={signature}, 期望={expected_signature}")

        # 验证签名
        is_valid = hmac.compare_digest(signature, expected_signature)
        if not is_valid:
            logger.warning(f"签名验证失败: device_id={device_id}, 收到签名={signature}, 期望签名={expected_signature}")

        return is_valid
    except Exception as e:
        logger.error(f"验证请求签名失败: {e}")
        return False


async def handle_device_list_request(websocket, client_id):
    """处理设备列表请求 - 直接从数据库获取所有设备信息"""
    try:
        logger.info(f"📋 处理设备列表请求: {client_id}")

        # 直接从数据库获取所有发送端设备信息
        senders_data = {}
        connection = get_db_connection()
        if connection:
            cursor = None
            try:
                cursor = connection.cursor(pymysql.cursors.DictCursor)

                # 获取所有发送端设备信息
                query = """
                SELECT sender_id, room_server_domain, room_id, room_name, room_category_id, room_sort_order,
                       motherboard_model, android_version, app_version,
                       app_auto_start_game_package, local_ip, public_ip, system_time,
                       first_online_time, last_online_time, last_offline_time, last_update_time,
                       cpu_unique_id, wechat_sn, system_version, available_storage, total_storage,
                       total_memory, available_memory, cpu_temperature, public_ipv6, network_type,
                       mac_address, city, region, country, location, isp_org, postal_code, timezone,
                       screen_resolution, screen_orientation, app_signaling_url, app_sender_id,
                       app_video_source, app_video_resolution, app_video_bitrate, app_video_codec,
                       app_video_framerate, app_camera_id, app_screen_capture_quality, app_audio_source,
                       app_auto_start_game, app_log_display_enabled, app_version_code, app_build_type,
                       app_has_manual_operation, app_first_install, app_hdmi_device_path,
                       app_usb_capture_device_path, is_online, heartbeat_count, room_last_update,
                       monitor_password, remark
                FROM fa_sender_device_info
                ORDER BY room_server_domain, room_category_id, room_sort_order, last_online_time DESC
                """

                cursor.execute(query)
                db_senders = cursor.fetchall()

                for sender in db_senders:
                    sender_id = sender['sender_id']

                    # 格式化时间字段
                    for time_field in ['first_online_time', 'last_online_time', 'last_offline_time', 'last_update_time', 'system_time', 'room_last_update']:
                        if sender[time_field]:
                            if hasattr(sender[time_field], 'isoformat'):
                                sender[time_field] = sender[time_field].isoformat()
                            else:
                                sender[time_field] = str(sender[time_field])

                    # 检查是否在线（结合内存状态）
                    is_online = sender_id in connected_clients or sender_id in stream_sources

                    senders_data[sender_id] = {
                        'sender_id': sender_id,
                        'online': is_online,
                        'last_seen': last_heartbeat.get(sender_id, 0),
                        'room_server_domain': sender['room_server_domain'],
                        'room_id': sender['room_id'],
                        'room_name': sender['room_name'],
                        'room_category_id': sender['room_category_id'],
                        'room_sort_order': sender['room_sort_order'],
                        'motherboard_model': sender['motherboard_model'],
                        'android_version': sender['android_version'],
                        'app_version': sender['app_version'],
                        'app_auto_start_game_package': sender['app_auto_start_game_package'],
                        'local_ip': sender['local_ip'],
                        'public_ip': sender['public_ip'],
                        'system_time': sender['system_time'],
                        'first_online_time': sender['first_online_time'],
                        'last_online_time': sender['last_online_time'],
                        'last_offline_time': sender['last_offline_time'],
                        'last_update_time': sender['last_update_time'],
                        # 额外的设备信息
                        'cpu_unique_id': sender['cpu_unique_id'],
                        'wechat_sn': sender['wechat_sn'],
                        'system_version': sender['system_version'],
                        'available_storage': sender['available_storage'],
                        'total_storage': sender['total_storage'],
                        'total_memory': sender['total_memory'],
                        'available_memory': sender['available_memory'],
                        'cpu_temperature': sender['cpu_temperature'],
                        'public_ipv6': sender['public_ipv6'],
                        'network_type': sender['network_type'],
                        'mac_address': sender['mac_address'],
                        'city': sender['city'],
                        'region': sender['region'],
                        'country': sender['country'],
                        'location': sender['location'],
                        'isp_org': sender['isp_org'],
                        'postal_code': sender['postal_code'],
                        'timezone': sender['timezone'],
                        'screen_resolution': sender['screen_resolution'],
                        'screen_orientation': sender['screen_orientation'],
                        'app_signaling_url': sender['app_signaling_url'],
                        'app_sender_id': sender['app_sender_id'],
                        'app_video_source': sender['app_video_source'],
                        'app_video_resolution': sender['app_video_resolution'],
                        'app_video_bitrate': sender['app_video_bitrate'],
                        'app_video_codec': sender['app_video_codec'],
                        'app_video_framerate': sender['app_video_framerate'],
                        'app_camera_id': sender['app_camera_id'],
                        'app_screen_capture_quality': sender['app_screen_capture_quality'],
                        'app_audio_source': sender['app_audio_source'],
                        'app_auto_start_game': sender['app_auto_start_game'],
                        'app_log_display_enabled': sender['app_log_display_enabled'],
                        'app_version_code': sender['app_version_code'],
                        'app_build_type': sender['app_build_type'],
                        'app_has_manual_operation': sender['app_has_manual_operation'],
                        'app_first_install': sender['app_first_install'],
                        'app_hdmi_device_path': sender['app_hdmi_device_path'],
                        'app_usb_capture_device_path': sender['app_usb_capture_device_path'],
                        'heartbeat_count': sender['heartbeat_count'],
                        'room_last_update': sender['room_last_update'],
                        'monitor_password': sender['monitor_password'],
                        'remark': sender['remark']
                    }

            except Exception as e:
                logger.error(f"❌ 查询发送端设备信息失败: {e}")
            finally:
                if cursor:
                    cursor.close()
                connection.close()

        # 获取接收端列表（简单处理）
        receivers_data = {}
        for receiver_id, receiver_info in receiver_connections.items():
            if receiver_info:
                receivers_data[receiver_id] = {
                    'receiver_id': receiver_id,
                    'online': receiver_id in connected_clients,
                    'last_seen': receiver_status.get(receiver_id, {}).get('last_seen', 0),
                    'ip': receiver_status.get(receiver_id, {}).get('ip', ''),
                    'user_agent': receiver_status.get(receiver_id, {}).get('user_agent', '')
                }

        # 发送设备列表响应
        response = {
            'type': 'device_list_response',
            'senders': senders_data,
            'receivers': receivers_data,
            'timestamp': int(time.time())
        }

        await websocket.send(json.dumps(response))
        logger.info(f"📤 已发送设备列表: {len(senders_data)} 个发送端, {len(receivers_data)} 个接收端")

    except Exception as e:
        logger.error(f"❌ 处理设备列表请求失败: {e}")

# 不再需要单独的设备信息请求，所有信息在初始列表中获取

# 使用服务器已有的get_db_connection函数

async def notify_device_offline(device_id, device_type):
    """通知管理控制台设备离线"""
    if admin_consoles:
        message = {
            'type': 'device_offline',
            'device_id': device_id,
            'device_type': device_type,
            'timestamp': int(time.time())
        }

        for admin_id, admin_ws in list(admin_consoles.items()):
            try:
                await admin_ws.send(json.dumps(message))
                logger.info(f"📤 已通知管理员设备离线: {admin_id} -> {device_id}")
            except Exception as e:
                logger.warning(f"通知管理员设备离线失败 {admin_id}: {e}")

async def notify_device_online(device_id, device_type, device_info=None):
    """通知管理控制台设备上线"""
    if admin_consoles:
        message = {
            'type': 'device_online',
            'device_id': device_id,
            'device_type': device_type,
            'device_info': device_info or {},
            'timestamp': int(time.time())
        }

        for admin_id, admin_ws in list(admin_consoles.items()):
            try:
                await admin_ws.send(json.dumps(message))
                logger.info(f"📤 已通知管理员设备上线: {admin_id} -> {device_id}")
            except Exception as e:
                logger.warning(f"通知管理员设备上线失败 {admin_id}: {e}")

async def forward_command_status_to_admin(sender_id, command_data):
    """转发发送端命令执行状态给管理员"""
    if admin_consoles:
        message = {
            'type': 'command_status',
            'sender_id': sender_id,
            'command': command_data.get('command', ''),
            'success': command_data.get('success', False),
            'message': command_data.get('message', ''),
            'progress': command_data.get('progress', ''),
            'timestamp': int(time.time())
        }

        for admin_id, admin_ws in list(admin_consoles.items()):
            try:
                await admin_ws.send(json.dumps(message))
                logger.debug(f"已转发命令状态给管理员 {admin_id}: {sender_id} - {command_data.get('command', '')}")
            except Exception as e:
                logger.warning(f"转发命令状态给管理员 {admin_id} 失败: {e}")

async def check_heartbeat_timeout():
    """检查心跳超时的设备"""
    while True:
        try:
            current_time = int(time.time())
            timeout_threshold = 90  # 90秒超时

            # 检查发送端心跳超时
            offline_senders = []
            for client_id in list(connected_clients.keys()):
                if client_id.startswith('gamev-'):
                    last_heartbeat_time = last_heartbeat.get(client_id, 0)
                    if current_time - last_heartbeat_time > timeout_threshold:
                        # 设备心跳超时
                        offline_senders.append(client_id)
                        logger.warning(f"⏰ 发送端心跳超时: {client_id} (上次心跳: {current_time - last_heartbeat_time}秒前)")

                        # 从连接列表移除
                        if client_id in connected_clients:
                            del connected_clients[client_id]

                        # 从视频源移除
                        if client_id in stream_sources:
                            del stream_sources[client_id]

                        # 通知管理控制台
                        await notify_device_offline(client_id, 'sender')

            # 检查接收端心跳超时并清理
            offline_receivers = []
            for receiver_id in list(receiver_connections.keys()):
                last_heartbeat_time = receiver_status.get(receiver_id, {}).get('last_seen', 0)
                if current_time - last_heartbeat_time > timeout_threshold:
                    offline_receivers.append(receiver_id)
                    logger.warning(f"⏰ 接收端心跳超时: {receiver_id} (上次心跳: {current_time - last_heartbeat_time}秒前)")

                    # 清理接收端连接
                    if receiver_id in receiver_connections:
                        del receiver_connections[receiver_id]
                    if receiver_id in receiver_status:
                        del receiver_status[receiver_id]
                    if receiver_id in connected_clients:
                        del connected_clients[receiver_id]

            if offline_senders:
                logger.info(f"🧹 清理了 {len(offline_senders)} 个超时发送端")
            if offline_receivers:
                logger.info(f"🧹 清理了 {len(offline_receivers)} 个超时接收端")

        except Exception as e:
            logger.error(f"❌ 心跳超时检查失败: {e}")

        # 每30秒检查一次
        await asyncio.sleep(30)

async def handle_client(websocket):
    client_id = None
    should_cleanup = True  # 标志是否需要清理
    try:
        
        remote_address = getattr(websocket, 'remote_address', 'unknown')
        logger.info(f"新连接: {remote_address}")
        
        
        async for message in websocket:
            try:
                # 检查连接状态
                if hasattr(websocket, 'closed') and websocket.closed:
                    logger.info(f"🔌 检测到连接已关闭: {client_id or 'unknown'}")
                    break
                elif hasattr(websocket, 'close_code') and websocket.close_code is not None:
                    logger.info(f"🔌 检测到连接已关闭: {client_id or 'unknown'} | 关闭码: {websocket.close_code}")
                    break

                data = json.loads(message)
                if 'type' in data:
                    logger.info(f"📨 收到消息: {data['type']} | 来自: {client_id or 'unknown'}")
                else:
                    logger.info(f"📨 收到未知类型消息 | 来自: {client_id or 'unknown'}")
                
                
                if data.get('type') == 'subscribe_boot_monitor':
                    # 处理开机监控订阅
                    password = data.get('password', '')

                    # 先发送认证中的响应，提高用户体验
                    try:
                        await websocket.send(json.dumps({
                            'type': 'auth_result',
                            'success': True,
                            'message': '正在验证...'
                        }))
                    except:
                        pass

                    # 验证密码并获取允许访问的服务器域名
                    allowed_domains = await verify_monitor_password(password)

                    if allowed_domains is not None:
                        # 将订阅者和其权限信息存储
                        boot_monitor_subscribers[websocket] = {
                            'allowed_domains': allowed_domains,
                            'password': password,
                            'remote_address': remote_address
                        }
                        logger.info(f"📱 新的开机监控订阅者: {remote_address}, 权限域名: {allowed_domains}")

                        # 发送认证成功消息
                        await websocket.send(json.dumps({
                            'type': 'auth_result',
                            'success': True,
                            'message': '认证成功，等待设备开机上报...',
                            'allowed_domains': allowed_domains
                        }))

                        # 不发送已有设备列表，只等待新的开机上报

                    else:
                        logger.warning(f"📱 开机监控认证失败: {remote_address}")
                        await websocket.send(json.dumps({
                            'type': 'auth_result',
                            'success': False,
                            'message': '密码错误'
                        }))
                        await websocket.close(code=1008, reason='认证失败')
                        break
                    continue

                elif data.get('type') == 'heartbeat':
                    # 获取发送者ID
                    from_id = data.get('from', client_id or 'unknown')
                    client_timestamp = data.get('timestamp', 0)
                    server_time = int(time.time())

                    # 更新最后心跳时间
                    last_heartbeat[from_id] = server_time

                    # 更新设备在线状态
                    if from_id.startswith('gamev-'):
                        # 查找对应的CPU ID
                        cpu_id = None
                        for cpu_unique_id, device_info in boot_devices.items():
                            if device_info.get('sender_id') == from_id:
                                cpu_id = cpu_unique_id
                                break

                        if cpu_id:
                            # 更新设备在线状态
                            device_online_status[from_id] = {
                                'cpu_unique_id': cpu_id,
                                'last_heartbeat': server_time,
                                'is_online': True
                            }

                    # 检查客户端是否在连接列表中
                    is_in_list = from_id in connected_clients

                    # 如果客户端不在连接列表中但能发送心跳，说明连接状态不一致，重新添加
                    if not is_in_list:
                        # 检查WebSocket对象类型
                        websocket_type = type(websocket).__name__
                        logger.debug(f"🔍 重新添加客户端 {from_id}，WebSocket类型: {websocket_type}")

                        # 确保是正确的WebSocket对象
                        if hasattr(websocket, 'send') and hasattr(websocket, 'close'):
                            connected_clients[from_id] = websocket
                            logger.warning(f"🔄 检测到连接状态不一致，重新添加客户端到连接列表: {from_id}")
                            is_in_list = True
                        else:
                            logger.error(f"❌ WebSocket对象类型异常: {websocket_type}，无法重新添加到连接列表")
                            break

                    logger.info(f"💓 收到心跳: {from_id} | 在线状态: {is_in_list} | 客户端时间: {client_timestamp} | 服务器时间: {server_time}")

                    try:
                        await websocket.send(json.dumps({
                            'type': 'heartbeat_ack',
                            'timestamp': server_time,
                            'from': 'server',
                            'to': from_id
                        }))
                        logger.debug(f"💓 心跳确认已发送: {from_id}")
                    except websockets.exceptions.ConnectionClosed:
                        logger.info(f"💓 心跳响应失败，客户端已断开: {from_id}")
                        break
                    except Exception as e:
                        logger.error(f"💓 心跳响应发送失败: {from_id} | 错误: {e}")
                        break
                    continue


                elif data.get('type') == 'device_info':
                    # 处理设备信息上报
                    sender_id = data.get('sender_id') or client_id
                    device_info_raw = data.get('device_info', {})

                    # 如果device_info是字符串，需要解析JSON
                    if isinstance(device_info_raw, str):
                        try:
                            device_info = json.loads(device_info_raw)
                        except json.JSONDecodeError as e:
                            logger.error(f"解析device_info JSON失败: {e}")
                            device_info = {}
                    else:
                        device_info = device_info_raw

                    if sender_id and device_info:
                        logger.info(f"📱 收到设备信息上报: {sender_id}")

                        # 获取应用设置信息
                        app_settings = device_info.get('app_settings', {})

                        # 准备完整的设备信息（公网IP信息由Android端提供）
                        full_device_info = {
                            'sender_id': sender_id,
                            'cpu_unique_id': device_info.get('cpu_unique_id'),
                            'wechat_sn': device_info.get('wechat_sn'),
                            'motherboard_model': device_info.get('motherboard_model'),
                            'android_version': device_info.get('android_version'),
                            'system_version': device_info.get('system_version'),
                            'available_storage': device_info.get('available_storage'),
                            'total_storage': device_info.get('total_storage'),
                            'total_memory': device_info.get('total_memory'),
                            'available_memory': device_info.get('available_memory'),
                            'cpu_temperature': device_info.get('cpu_temperature'),
                            'public_ip': device_info.get('public_ip'),
                            'public_ipv6': device_info.get('public_ipv6'),
                            'local_ip': device_info.get('local_ip'),
                            'network_type': device_info.get('network_type'),
                            'mac_address': device_info.get('mac_address'),
                            'city': device_info.get('city'),
                            'region': device_info.get('region'),
                            'country': device_info.get('country'),
                            'location': device_info.get('location'),
                            'isp_org': device_info.get('isp_org'),
                            'postal_code': device_info.get('postal_code'),
                            'timezone': device_info.get('timezone'),
                            'screen_resolution': device_info.get('screen_resolution'),
                            'screen_orientation': device_info.get('screen_orientation'),
                            'system_time': device_info.get('system_time'),
                            # 应用设置信息
                            'app_signaling_url': app_settings.get('signaling_url'),
                            'app_sender_id': app_settings.get('sender_id'),
                            'app_video_source': app_settings.get('video_source'),
                            'app_video_resolution': app_settings.get('video_resolution'),
                            'app_video_bitrate': app_settings.get('video_bitrate'),
                            'app_video_codec': app_settings.get('video_codec'),
                            'app_video_framerate': app_settings.get('video_framerate'),
                            'app_camera_id': app_settings.get('camera_id'),
                            'app_screen_capture_quality': app_settings.get('screen_capture_quality'),
                            'app_audio_source': app_settings.get('audio_source'),
                            'app_auto_start_game': 1 if app_settings.get('auto_start_game') else 0,
                            'app_auto_start_game_package': app_settings.get('auto_start_game_package'),
                            'app_log_display_enabled': 1 if app_settings.get('log_display_enabled') else 0,
                            'app_version': app_settings.get('app_version'),
                            'app_version_code': app_settings.get('app_version_code'),
                            'app_build_type': app_settings.get('build_type'),
                            'app_has_manual_operation': 1 if app_settings.get('has_manual_operation') else 0,
                            'app_first_install': 1 if app_settings.get('first_install') else 0,
                            'app_hdmi_device_path': app_settings.get('hdmi_device_path'),
                            'app_usb_capture_device_path': app_settings.get('usb_capture_device_path')
                        }

                        # 更新到数据库
                        success = await update_device_info(sender_id, full_device_info)

                        # 发送确认响应
                        try:
                            await websocket.send(json.dumps({
                                'type': 'device_info_ack',
                                'success': success,
                                'timestamp': int(time.time()),
                                'message': '设备信息已更新' if success else '设备信息更新失败'
                            }))
                            logger.info(f"📤 设备信息确认已发送: {sender_id} | 成功: {success}")
                        except Exception as e:
                            logger.error(f"❌ 设备信息确认发送失败: {sender_id} | 错误: {e}")
                    else:
                        logger.warning(f"⚠️ 设备信息上报缺少必要字段: sender_id={sender_id}, device_info={bool(device_info)}")
                    continue

                elif data.get('type') == 'boot_report':
                    # 处理开机信息上报
                    sender_id = data.get('sender_id') or client_id
                    boot_info_raw = data.get('boot_info', {})

                    # 如果boot_info是字符串，需要解析JSON
                    if isinstance(boot_info_raw, str):
                        try:
                            boot_info = json.loads(boot_info_raw)
                        except json.JSONDecodeError as e:
                            logger.error(f"❌ 开机信息JSON解析失败: {sender_id} | 错误: {e}")
                            continue
                    else:
                        boot_info = boot_info_raw

                    if sender_id and boot_info:
                        logger.info(f"📱 收到开机信息: {sender_id} | CPU_ID={boot_info.get('cpu_unique_id', '')[:8]}... | 游戏={boot_info.get('game_package', 'None')}")

                        # 提取开机信息
                        cpu_unique_id = boot_info.get('cpu_unique_id', '')
                        boot_time = boot_info.get('boot_time', 0)
                        current_time = time.time() * 1000

                        # 存储开机信息
                        if cpu_unique_id:
                            boot_devices[cpu_unique_id] = {
                                'cpu_unique_id': cpu_unique_id,
                                'sender_id': sender_id,
                                'boot_time': boot_time,
                                'report_time': current_time,
                                'device_info': {
                                    'brand': boot_info.get('device_brand', 'Unknown'),
                                    'model': boot_info.get('device_model', 'Unknown'),
                                    'android_version': boot_info.get('android_version', 'Unknown'),
                                    'app_version': boot_info.get('app_version', 'Unknown'),
                                    'auto_start_game_package': boot_info.get('auto_start_game_package', ''),
                                    'local_ip': boot_info.get('local_ip', 'Unknown'),
                                    'public_ip': boot_info.get('public_ip', 'Unknown')
                                },
                                'last_update': current_time,
                                'boot_report_time': current_time  # 添加开机上报时间用于排序
                            }

                            # 实时转发新开机设备信息给监控页面
                            logger.info(f"📱 实时转发新开机设备到监控页面: {sender_id}")
                            await send_new_boot_device(cpu_unique_id, boot_devices[cpu_unique_id])

                        # 发送确认响应
                        try:
                            await websocket.send(json.dumps({
                                'type': 'boot_report_ack',
                                'success': True,
                                'timestamp': int(time.time()),
                                'message': '开机信息已记录',
                                'cpu_unique_id': cpu_unique_id
                            }))
                            logger.info(f"📤 开机信息确认已发送: {sender_id}")
                        except Exception as e:
                            logger.error(f"❌ 开机信息确认发送失败: {sender_id} | 错误: {e}")
                    else:
                        logger.warning(f"⚠️ 开机信息上报缺少必要字段: sender_id={sender_id}, boot_info={bool(boot_info)}")
                    continue

                elif data.get('type') == 'webrtc_signaling':
                    # 处理WebRTC信令转发
                    target = data.get('target')
                    message = data.get('message')

                    logger.info(f"📡 WebRTC信令转发: {client_id} -> {target}")
                    logger.debug(f"   信令内容: {message.get('type', 'unknown') if message else 'empty'}")

                    if target and target in connected_clients:
                        # 转发信令到目标设备
                        target_ws = connected_clients[target]
                        signaling_message = {
                            'type': 'webrtc_signaling',
                            'from': client_id,
                            'message': message,
                            'timestamp': int(time.time())
                        }

                        try:
                            await target_ws.send(json.dumps(signaling_message))
                            logger.info(f"📡 WebRTC信令已转发: {client_id} -> {target}")
                        except Exception as e:
                            logger.error(f"❌ WebRTC信令转发失败: {e}")
                    else:
                        logger.warning(f"⚠️ WebRTC信令目标设备不在线: {target}")

                elif data.get('type') == 'register' and 'id' in data:

                    client_id = data['id']
                    logger.info(f"📝 开始注册客户端: {client_id}")

                    # 检查是否已存在
                    if client_id in connected_clients:
                        logger.warning(f"⚠️ 客户端 {client_id} 已存在，覆盖注册")

                    connected_clients[client_id] = websocket
                    logger.info(f"✅ 客户端已加入连接列表: {client_id}")
                    logger.info(f"📊 当前连接数: {len(connected_clients)}")

                    # 检查客户端角色
                    role = data.get('role', 'unknown')
                    is_receiver = role == 'receiver' or client_id.startswith('receiver-')
                    is_admin = role == 'admin' or client_id.startswith('admin-')

                    if role == 'source':
                        stream_sources[client_id] = {
                            'id': client_id,
                            'name': data.get('name', client_id),
                            'description': data.get('description', '')
                        }
                        logger.info(f"📹 注册视频源: {client_id}")

                        # 通知管理控制台设备上线
                        await notify_device_online(client_id, 'sender')
                        logger.info(f"📊 当前视频源数: {len(stream_sources)}")
                    elif is_receiver:
                        # 注册接收端
                        receiver_connections[client_id] = {
                            'websocket': websocket,
                            'device_info': data.get('device_info', {}),
                            'last_seen': int(time.time()),
                            'auto_reconnect': True
                        }
                        logger.info(f"📱 注册接收端: {client_id}")
                    elif is_admin:
                        # 注册管理控制台
                        admin_consoles[client_id] = websocket
                        logger.info(f"🎛️ 注册管理控制台: {client_id} | 名称: {data.get('name', client_id)}")
                    else:
                        logger.info(f"✅ 注册普通客户端: {client_id} | 角色: {role}")

                    await websocket.send(json.dumps({
                        'type': 'registered',
                        'id': client_id,
                        'server_time': int(time.time())
                    }))
                    logger.info(f"📤 已发送注册确认: {client_id}")

                    # 不再自动广播客户端上线信息
                    # 接收端必须事先知道要连接的发送端ID
                    client_type = '视频源' if client_id in stream_sources else ('接收端' if is_receiver else '未知')
                    logger.info(f"🎯 客户端 {client_id} 注册完成，类型: {client_type}")
                
                
                elif data.get('type') == 'list':
                    # 不允许获取客户端列表和视频源列表
                    # 接收端必须事先知道要连接的发送端ID
                    logger.warning(f"❌ 拒绝客户端列表请求: {client_id} | 接收端不能获取发送端列表")
                    try:
                        await websocket.send(json.dumps({
                            'type': 'error',
                            'message': '不允许获取客户端列表，请直接指定要连接的发送端ID',
                            'code': 'LIST_NOT_ALLOWED'
                        }))
                    except:
                        pass
                
                
                elif data.get('type') == 'offer':
                    # 处理offer消息 - 必须指定target
                    if 'target' in data:
                        target_id = data['target']
                        logger.info(f"🔍 OFFER请求: {client_id}->{target_id}")
                        logger.info(f"📊 当前连接列表: {list(connected_clients.keys())}")
                        logger.info(f"📊 当前视频源列表: {list(stream_sources.keys())}")

                        if target_id in connected_clients:
                            success = await safe_send_message(target_id, {
                                'type': 'offer',
                                'sdp': data['sdp'],
                                'from': client_id
                            })

                            if success:
                                logger.info(f"📤 OFFER: {client_id}->{target_id} | SDP已发送 | 状态:offer_sent")
                                log_connection_state_change(client_id, target_id, "offer_sent")
                            else:
                                logger.error(f"❌ OFFER失败: {client_id}->{target_id} | 连接已断开")
                                log_connection_state_change(client_id, target_id, "offer_failed")
                        else:
                            logger.error(f"❌ OFFER目标不存在: {client_id}->{target_id}")
                            logger.error(f"📊 目标 {target_id} 不在连接列表中")
                            logger.error(f"📊 连接列表内容: {list(connected_clients.keys())}")

                            # 发送错误响应给客户端
                            try:
                                await websocket.send(json.dumps({
                                    'type': 'error',
                                    'message': f'目标客户端 {target_id} 不存在',
                                    'code': 'TARGET_NOT_FOUND'
                                }))
                                logger.info(f"📤 已发送TARGET_NOT_FOUND错误给 {client_id}")
                            except Exception as e:
                                logger.error(f"❌ 发送错误消息失败: {e}")
                    else:
                        # 没有指定目标的offer - 这是错误的
                        logger.error(f"❌ OFFER缺少target字段: {client_id} | 消息被拒绝")
                        try:
                            await websocket.send(json.dumps({
                                'type': 'error',
                                'message': 'OFFER消息必须包含target字段指定目标客户端',
                                'code': 'MISSING_TARGET'
                            }))
                        except:
                            pass
                
                
                elif data.get('type') == 'answer' and 'target' in data:

                    target_id = data['target']
                    if target_id in connected_clients:
                        try:
                            await connected_clients[target_id].send(json.dumps({
                                'type': 'answer',
                                'sdp': data['sdp'],
                                'from': client_id
                            }))
                            logger.info(f"📥 ANSWER: {client_id}->{target_id} | SDP已发送 | 状态:answer_sent")
                            log_connection_state_change(client_id, target_id, "answer_sent")
                        except Exception as e:
                            logger.error(f"❌ ANSWER失败: {client_id}->{target_id} | 错误: {e}")
                            logger.error(f"详细错误: {traceback.format_exc()}")
                            log_connection_state_change(client_id, target_id, "answer_failed")
                
                
                elif data.get('type') == 'candidate':
                    # 处理ICE候选消息 - 必须指定target
                    if 'target' in data:
                        target_id = data['target']
                        if target_id in connected_clients:
                            try:
                                # 解析ICE候选信息
                                candidate_data = data.get('candidate', {})
                                candidate_info = parse_ice_candidate(candidate_data)

                                # 获取当前连接状态
                                connection_key = f"{client_id}->{target_id}"
                                current_state = connection_states.get(connection_key, "unknown")

                                if candidate_info and candidate_info['ip'] != 'unknown':
                                    # 一条记录显示所有信息：源->目标 | IP:端口 | 类型 | 协议 | 状态
                                    logger.info(f"🧊 ICE: {client_id}->{target_id} | {candidate_info['ip']}:{candidate_info['port']} | {candidate_info['type']} | {candidate_info['protocol']} | 状态:{current_state} | 优先级:{candidate_info['priority']}")
                                else:
                                    logger.info(f"🧊 ICE: {client_id}->{target_id} | 解析失败 | 状态:{current_state}")
                                    logger.debug(f"   原始候选数据: {candidate_data}")

                                success = await safe_send_message(target_id, {
                                    'type': 'candidate',
                                    'candidate': data['candidate'],
                                    'from': client_id
                                })

                                if not success:
                                    logger.error(f"❌ ICE转发失败: {client_id}->{target_id} | 连接已断开")
                            except Exception as e:
                                logger.error(f"❌ ICE处理失败: {client_id}->{target_id} | 错误: {e}")
                                logger.error(f"候选数据: {data.get('candidate', 'N/A')}")
                        else:
                            logger.error(f"❌ ICE目标不存在: {client_id}->{target_id}")
                            # 发送错误响应给客户端
                            try:
                                await websocket.send(json.dumps({
                                    'type': 'error',
                                    'message': f'目标客户端 {target_id} 不存在',
                                    'code': 'TARGET_NOT_FOUND'
                                }))
                            except:
                                pass
                    else:
                        # 没有指定目标的candidate - 这是错误的
                        logger.error(f"❌ ICE缺少target字段: {client_id} | 消息被拒绝")
                        try:
                            await websocket.send(json.dumps({
                                'type': 'error',
                                'message': 'ICE候选消息必须包含target字段指定目标客户端',
                                'code': 'MISSING_TARGET'
                            }))
                        except:
                            pass
                
                
                elif data.get('type') == 'connection_state' and 'target' in data:
                    # 处理连接状态更新
                    target_id = data['target']
                    state = data.get('state', 'unknown')
                    log_connection_state_change(client_id, target_id, state)

                    # 定期打印活跃连接
                    if state in ["connected", "completed"]:
                        log_active_connections()

                elif data.get('type') == 'source_info':
                    # 处理视频源信息更新
                    source_id = data.get('source_id', client_id)
                    name = data.get('name', '')
                    description = data.get('description', '')
                    status = data.get('status', 'unknown')

                    logger.info(f"📹 视频源信息: {source_id} | 名称: {name or '未设置'} | 状态: {status}")

                    # 如果客户端还未注册，先注册为视频源
                    if not client_id:
                        client_id = source_id
                        connected_clients[client_id] = websocket
                        logger.info(f"📹 自动注册视频源: {client_id}")

                    # 检查是否是新设备上线
                    is_new_device = source_id not in stream_sources

                    # 更新或创建视频源信息
                    stream_sources[source_id] = {
                        'id': source_id,
                        'name': name,
                        'description': description,
                        'status': status
                    }

                    # 如果是新设备，通知管理控制台
                    if is_new_device:
                        await notify_device_online(source_id, 'sender')
                        logger.info(f"📹 新视频源上线: {source_id}")
                    else:
                        logger.info(f"📹 视频源 {source_id} 信息已更新")

                elif data.get('type') == 'status_update' and client_id in stream_sources:

                    stream_sources[client_id].update({
                        'status': data.get('status', 'unknown'),
                        'viewers': data.get('viewers', 0),
                        'resolution': data.get('resolution', ''),
                        'fps': data.get('fps', 0)
                    })
                    logger.info(f"📊 视频源 {client_id} 状态更新: {data.get('status', 'unknown')}")
                    logger.info(f"   观看者: {data.get('viewers', 0)}, 分辨率: {data.get('resolution', '')}, 帧率: {data.get('fps', 0)}")

                elif data.get('type') == 'receiver_status':
                    # 处理接收端状态报告
                    if client_id in receiver_connections:
                        await log_receiver_status(client_id, data)

                        # 更新接收端连接信息
                        receiver_connections[client_id]['last_seen'] = int(time.time())

                        # 发送状态确认
                        try:
                            await websocket.send(json.dumps({
                                'type': 'status_ack',
                                'timestamp': int(time.time())
                            }))
                        except:
                            pass
                    else:
                        logger.warning(f"收到未注册接收端的状态报告: {client_id}")

                elif data.get('type') == 'command_response':
                    # 处理命令执行结果
                    command = data.get('command', 'unknown')
                    success = data.get('success', False)
                    message = data.get('message', '')
                    progress = data.get('progress', '')

                    logger.info(f"📋 命令执行结果 [{client_id}]: {command}")
                    logger.info(f"   成功: {success}, 消息: {message}")

                    # 如果是升级命令的响应，记录详细信息
                    if command == 'upgrade':
                        step = data.get('step', 'unknown')
                        progress_percent = data.get('progress', 0)
                        logger.info(f"   升级步骤: {step}, 进度: {progress_percent}%")

                    # 转发命令状态给管理员
                    await forward_command_status_to_admin(client_id, {
                        'command': command,
                        'success': success,
                        'message': message,
                        'progress': progress
                    })

                elif data.get('type') == 'upgrade_status':
                    # 处理升级状态报告
                    step = data.get('step', 'unknown')
                    progress = data.get('progress', 0)
                    success = data.get('success', True)
                    message = data.get('message', '')

                    logger.info(f"📦 升级状态 [{client_id}]: {step}")
                    logger.info(f"   进度: {progress}%, 成功: {success}")
                    if message:
                        logger.info(f"   消息: {message}")

                elif data.get('type') == 'keep_alive':
                    # 处理接收端保活消息
                    if client_id in receiver_connections:
                        receiver_connections[client_id]['last_seen'] = int(time.time())

                        # 发送保活确认
                        try:
                            await websocket.send(json.dumps({
                                'type': 'keep_alive_ack',
                                'timestamp': int(time.time())
                            }))
                        except:
                            pass

                elif data.get('type') == 'screenshot_result':
                    # 处理截屏结果消息
                    request_id = data.get('request_id')
                    success = data.get('success', False)
                    full_url = data.get('full_url', '')
                    message = data.get('message', '')
                    timestamp = data.get('timestamp', int(time.time()))

                    logger.info(f"📸 收到截屏结果: {client_id} | 请求ID: {request_id} | 成功: {success}")
                    if success and full_url:
                        logger.info(f"📸 截屏URL: {full_url}")
                    else:
                        logger.warning(f"📸 截屏失败: {message}")

                    # 转发截屏结果给所有管理控制台
                    await broadcast_to_admin_consoles({
                        'type': 'screenshot_result',
                        'from': client_id,
                        'request_id': request_id,
                        'success': success,
                        'full_url': full_url,
                        'message': message,
                        'timestamp': timestamp
                    })

                elif data.get('type') == 'command_response':
                    # 处理命令响应消息
                    command = data.get('command')
                    success = data.get('success', False)
                    message = data.get('message', '')
                    timestamp = data.get('timestamp', int(time.time()))
                    filename = data.get('filename', '')  # 获取文件名

                    logger.info(f"📋 收到命令响应: {client_id} | 命令: {command} | 成功: {success}")
                    if not success:
                        logger.warning(f"📋 命令执行失败: {message}")

                    # 特殊处理日志下载命令
                    if command == 'download_logs' and success:
                        # 发送日志下载结果
                        await broadcast_to_admin_consoles({
                            'type': 'log_download_result',
                            'from': client_id,
                            'success': success,
                            'filename': filename,
                            'message': message,
                            'timestamp': timestamp
                        })
                        logger.info(f"📋 日志下载成功: {client_id} | 文件: {filename}")
                    else:
                        # 转发普通命令响应给所有管理控制台
                        await broadcast_to_admin_consoles({
                            'type': 'command_response',
                            'from': client_id,
                            'command': command,
                            'success': success,
                            'message': message,
                            'timestamp': timestamp
                        })

                elif data.get('type') == 'request_device_list':
                    # 处理设备列表请求
                    await handle_device_list_request(websocket, client_id)

                elif data.get('type') == 'device_config':
                    # 处理设备配置消息
                    logger.info(f"📱 收到设备配置: {client_id}")
                    # 这里可以处理设备配置信息，暂时只记录日志

                else:
                    # 处理未知消息类型
                    message_type = data.get('type', 'unknown')
                    logger.warning(f"⚠️ 未知消息类型: {message_type} | 来自: {client_id}")
                    logger.debug(f"   完整消息: {data}")

            except json.JSONDecodeError as e:
                logger.error(f"❌ 无效的JSON消息: {message}")
                logger.error(f"JSON解析错误: {e}")
            except Exception as e:
                logger.error(f"❌ 处理消息错误: {e}")
                logger.error(f"详细错误堆栈: {traceback.format_exc()}")
                logger.error(f"原始消息: {message}")
                logger.error(f"客户端ID: {client_id}")

    except websockets.exceptions.ConnectionClosedError as e:
        # WebSocket连接异常关闭，检查是否是"no close frame"错误
        error_msg = str(e).lower()
        if "no close frame" in error_msg:
            logger.warning(f"⚠️ 客户端 {client_id or 'unknown'} WebSocket状态异常但可能仍连接: {e}")
            logger.warning(f"⚠️ 不立即清理，等待心跳机制确认连接状态")
            # 不立即清理，让心跳机制来判断连接是否真的断开
            should_cleanup = False

            # 确保客户端在心跳跟踪中，如果没有心跳记录则添加当前时间
            if client_id and client_id not in last_heartbeat:
                last_heartbeat[client_id] = int(time.time())
                logger.info(f"💓 为客户端 {client_id} 添加心跳跟踪记录")

            return
        else:
            logger.info(f"🔌 客户端连接异常关闭: {client_id or 'unknown'} | 原因: {e}")
            logger.debug(f"WebSocket状态: {websocket.state if websocket else 'None'}")
    except websockets.exceptions.ConnectionClosedOK as e:
        # WebSocket正常关闭
        logger.info(f"🔌 客户端正常断开: {client_id or 'unknown'}")
    except Exception as e:
        logger.error(f"❌ 处理客户端消息错误: {e}")
        logger.error(f"详细错误堆栈: {traceback.format_exc()}")
        logger.error(f"客户端ID: {client_id}")
        logger.error(f"WebSocket状态: {websocket.state if websocket else 'None'}")
    
    finally:
        # 安全的连接清理
        if client_id and should_cleanup:
            logger.info(f"🧹 开始清理客户端: {client_id}")

            # 从连接列表中移除
            if client_id in connected_clients:
                del connected_clients[client_id]
                logger.info(f"🔌 客户端已从连接列表移除: {client_id}")

            # 清理相关的连接状态
            keys_to_remove = []
            for key in list(connection_states.keys()):  # 使用list()避免运行时修改字典
                if client_id in key:
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                if key in connection_states:
                    del connection_states[key]
                if key in active_connections:
                    del active_connections[key]
                    logger.info(f"🔗 清理连接状态: {key}")

            # 清理视频源
            if client_id in stream_sources:
                del stream_sources[client_id]
                logger.info(f"📹 视频源已移除: {client_id}")

                # 通知管理控制台设备离线
                if client_id.startswith('gamev-'):
                    asyncio.create_task(notify_device_offline(client_id, 'sender'))

            # 清理管理控制台
            if client_id in admin_consoles:
                del admin_consoles[client_id]
                logger.info(f"🎛️ 管理控制台已移除: {client_id}")

        # 清理开机监控订阅者（无论是否有client_id）
        if websocket in boot_monitor_subscribers:
            boot_monitor_subscribers.pop(websocket, None)
            logger.info(f"📱 开机监控订阅者已移除: {getattr(websocket, 'remote_address', 'unknown')}")

            # 标记设备离线（如果是发送端）
            if client_id and (client_id in stream_sources or client_id.startswith('gamev-')):
                try:
                    # 异步标记设备离线
                    import asyncio
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，创建任务
                        asyncio.create_task(mark_device_offline(client_id))
                    else:
                        # 如果事件循环未运行，直接运行
                        loop.run_until_complete(mark_device_offline(client_id))
                    logger.info(f"📱 设备已标记为离线: {client_id}")
                except Exception as e:
                    logger.error(f"❌ 标记设备离线失败: {client_id} | 错误: {e}")

            # 打印当前活跃连接状态
            log_active_connections()

            # 不再自动广播客户端离开信息
            # 但需要通知相关的发送端清理连接
            if client_id and client_id not in stream_sources:
                # 检查是否是真正的接收端（以receiver-开头）
                if client_id.startswith('receiver-'):
                    # 这是一个接收端离开，通知所有视频源清理该连接
                    logger.info(f"接收端 {client_id} 已离开，通知视频源清理连接")
                    for source_id in stream_sources.keys():
                        if source_id in connected_clients:
                            try:
                                await connected_clients[source_id].send(json.dumps({
                                    'type': 'peer_disconnected',
                                    'peer_id': client_id
                                }))
                                logger.info(f"📤 通知视频源 {source_id} 清理连接: {client_id}")
                            except Exception as e:
                                logger.warning(f"⚠️ 通知视频源 {source_id} 失败: {e}")
                else:
                    # 这是一个发送端离开，但不在stream_sources中（可能是异常断开）
                    logger.info(f"发送端 {client_id} 异常离开（不在stream_sources中），不通知其他发送端")

            # 更准确的客户端类型判断
            if client_id in stream_sources:
                client_type = "视频源"
            elif client_id and client_id.startswith('receiver-'):
                client_type = "接收端"
            elif client_id and client_id.startswith('gamev-'):
                client_type = "发送端（异常断开）"
            else:
                client_type = "未知类型"

            logger.info(f"客户端 {client_id} 已离开，类型: {client_type}")

            # 清理无效连接
            logger.info(f"🔍 开始检查无效连接，当前连接数: {len(connected_clients)}")
            disconnected_clients = []
            for cid, ws in list(connected_clients.items()):
                try:
                    # 检查连接是否有效
                    is_closed = False
                    if hasattr(ws, 'closed') and ws.closed:
                        is_closed = True
                        logger.debug(f"🔍 客户端 {cid} 连接已关闭 (closed属性)")
                    elif hasattr(ws, 'close_code') and ws.close_code is not None:
                        is_closed = True
                        logger.debug(f"🔍 客户端 {cid} 连接已关闭 (close_code: {ws.close_code})")

                    if is_closed:
                        disconnected_clients.append(cid)
                        logger.info(f"🔍 发现无效连接: {cid}")
                except Exception as e:
                    logger.warning(f"⚠️ 检查客户端 {cid} 连接状态失败: {e}")
                    disconnected_clients.append(cid)

            if disconnected_clients:
                logger.info(f"🧹 准备清理 {len(disconnected_clients)} 个无效连接: {disconnected_clients}")
                for cid in disconnected_clients:
                    if cid in connected_clients:
                        del connected_clients[cid]
                        logger.info(f"🧹 已清理无效连接: {cid}")
                logger.info(f"🧹 清理完成，剩余连接数: {len(connected_clients)}")
            else:
                logger.info(f"✅ 无需清理，所有连接都有效")

        logger.info(f"✅ 客户端清理完成: {client_id or 'unknown'}")


class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, directory=None, **kwargs):
        self.directory = directory
        logger.info(f"Web文件目录1: {self.directory}")
        super().__init__(*args, **kwargs)

    def log_message(self, format, *args):
        logger.info(format % args)

    def do_GET(self):
        # 处理开机设备列表请求
        if self.path == '/api/boot_devices':
            self._handle_boot_devices_list()
            return

        # API接口处理
        if self.path.startswith('/api/'):
            self._handle_api_request()
            return

        if self.path == '/':
            self.path = '/index.html'

        try:
            # 使用传入的目录或默认目录
            if hasattr(self, 'web_directory') and self.web_directory:
                self.directory = self.web_directory
            else:
                # 优先查找本地web目录，然后是默认目录
                local_web_dir = os.path.join(os.path.dirname(__file__), "web")
                default_web_dir = "/www/wwwroot/sj/web"

                if os.path.exists(local_web_dir):
                    self.directory = local_web_dir
                else:
                    self.directory = default_web_dir

            file_path = os.path.normpath(os.path.join(self.directory, self.path.lstrip('/')))
            logger.info(f"请求静态文件: {file_path}")
            if os.path.exists(file_path) and os.path.isfile(file_path):
                self.send_response(HTTPStatus.OK)
                self.send_header("Content-type", self._get_content_type(file_path))
                self.send_header("Content-Length", str(os.path.getsize(file_path)))
                self.end_headers()

                with open(file_path, 'rb') as file:
                    self.wfile.write(file.read())
            else:
                self.send_error(HTTPStatus.NOT_FOUND, "File not found")
        except Exception as e:
            logger.error(f"处理HTTP请求错误: {e}")
            self.send_error(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    def do_POST(self):
        # 处理开机信息上报
        if self.path == '/api/boot_report':
            self._handle_boot_report()
            return

        # 其他API接口处理
        if self.path.startswith('/api/'):
            self._handle_api_request()
            return

        self.send_error(HTTPStatus.METHOD_NOT_ALLOWED, "POST not allowed for static files")

    def _handle_api_request(self):
        """处理API请求"""
        try:
            # 解析路径
            path_parts = self.path.split('/')
            if len(path_parts) < 3:
                self._send_json_response({'error': 'Invalid API path'}, 400)
                return

            api_version = path_parts[2]  # api/v1/...
            if api_version != 'v1':
                self._send_json_response({'error': 'Unsupported API version'}, 400)
                return

            if len(path_parts) < 4:
                self._send_json_response({'error': 'Missing API endpoint'}, 400)
                return

            endpoint = path_parts[3]

            # 处理不同的API端点
            if endpoint == 'receivers':
                self._handle_receivers_api(path_parts[4:])
            elif endpoint == 'senders':
                self._handle_senders_api(path_parts[4:])
            elif endpoint == 'config':
                self._handle_config_api(path_parts[4:])
            elif endpoint == 'commands':
                self._handle_commands_api(path_parts[4:])
            elif endpoint == 'boot':
                self._handle_boot_api(path_parts[4:])
            else:
                self._send_json_response({'error': 'Unknown API endpoint'}, 404)

        except Exception as e:
            logger.error(f"API请求处理错误: {e}")
            self._send_json_response({'error': str(e)}, 500)

    def _handle_boot_report(self):
        """处理开机信息上报"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self._send_json_response({'error': '缺少请求数据'}, 400)
                return

            post_data = self.rfile.read(content_length)
            boot_data = json.loads(post_data.decode('utf-8'))

            # 验证必要字段
            cpu_unique_id = boot_data.get('cpu_unique_id')
            if not cpu_unique_id:
                self._send_json_response({'error': '缺少cpu_unique_id'}, 400)
                return

            # 存储开机信息
            boot_time = boot_data.get('boot_time', time.time() * 1000)
            current_time = time.time() * 1000

            boot_devices[cpu_unique_id] = {
                'cpu_unique_id': cpu_unique_id,
                'boot_time': boot_time,
                'report_time': current_time,
                'device_info': {
                    'brand': boot_data.get('device_brand', 'Unknown'),
                    'model': boot_data.get('device_model', 'Unknown'),
                    'android_version': boot_data.get('android_version', 'Unknown'),
                    'app_version': boot_data.get('app_version', 'Unknown'),
                    'game_package': boot_data.get('game_package', ''),
                    'local_ip': boot_data.get('local_ip', 'Unknown'),
                    'public_ip': boot_data.get('public_ip', 'Unknown')
                },
                'last_update': current_time
            }

            logger.info(f"📱 收到开机信息: CPU_ID={cpu_unique_id[:8]}..., 游戏={boot_data.get('game_package', 'None')}")

            # 异步通知所有监控订阅者
            import asyncio
            asyncio.create_task(send_boot_devices_update())

            self._send_json_response({
                'success': True,
                'message': '开机信息已记录',
                'cpu_unique_id': cpu_unique_id
            })

        except json.JSONDecodeError:
            self._send_json_response({'error': 'JSON格式错误'}, 400)
        except Exception as e:
            logger.error(f"❌ 处理开机信息失败: {e}")
            self._send_json_response({'error': str(e)}, 500)

    def _handle_boot_devices_list(self):
        """处理获取开机设备列表请求"""
        try:
            current_time = time.time() * 1000
            devices_list = []

            # 按开机时间排序（最新的在前面）
            sorted_devices = sorted(boot_devices.items(),
                                  key=lambda x: x[1]['boot_time'],
                                  reverse=True)

            for cpu_id, device_info in sorted_devices:
                boot_time = device_info['boot_time']
                uptime_seconds = int((current_time - boot_time) / 1000)

                device_data = {
                    'cpu_unique_id': cpu_id,
                    'sender_id': device_info.get('sender_id', 'unknown'),
                    'boot_time': boot_time,
                    'uptime_seconds': uptime_seconds,
                    'uptime_display': format_uptime(uptime_seconds),
                    'auto_start_game_package': device_info['device_info'].get('auto_start_game_package', ''),
                    'device_brand': device_info['device_info'].get('brand', 'Unknown'),
                    'device_model': device_info['device_info'].get('model', 'Unknown'),
                    'android_version': device_info['device_info'].get('android_version', 'Unknown'),
                    'app_version': device_info['device_info'].get('app_version', 'Unknown'),
                    'local_ip': device_info['device_info'].get('local_ip', 'Unknown'),
                    'public_ip': device_info['device_info'].get('public_ip', 'Unknown'),
                    'report_time': device_info['report_time'],
                    'last_update': device_info['last_update']
                }

                devices_list.append(device_data)

            self._send_json_response({
                'success': True,
                'devices': devices_list,
                'total': len(devices_list),
                'timestamp': current_time
            })

        except Exception as e:
            logger.error(f"❌ 获取开机设备列表失败: {e}")
            self._send_json_response({'error': str(e)}, 500)

    def _handle_receivers_api(self, sub_paths):
        """处理接收端相关API"""
        if self.command == 'GET':
            if not sub_paths:
                # 获取所有接收端状态
                receivers_info = {}
                for client_id, status in receiver_status.items():
                    receivers_info[client_id] = {
                        **status,
                        'online': client_id in connected_clients
                    }
                self._send_json_response({'receivers': receivers_info})
            else:
                client_id = sub_paths[0]
                if client_id in receiver_status:
                    status = receiver_status[client_id].copy()
                    status['online'] = client_id in connected_clients
                    self._send_json_response({'receiver': status})
                else:
                    self._send_json_response({'error': 'Receiver not found'}, 404)
        else:
            self._send_json_response({'error': 'Method not allowed'}, 405)

    def _handle_senders_api(self, sub_paths):
        """处理发送端相关API"""
        if self.command == 'GET':
            if not sub_paths:
                # 获取所有发送端状态（优先从数据库获取详细信息）
                senders_info = {}

                # 从数据库获取设备信息
                connection = get_db_connection()
                if connection:
                    cursor = None
                    try:
                        cursor = connection.cursor(pymysql.cursors.DictCursor)
                        sql = """
                            SELECT sender_id, cpu_unique_id, wechat_sn, motherboard_model,
                                   android_version, system_version, available_storage, total_storage,
                                   total_memory, available_memory, cpu_temperature,
                                   public_ip, public_ipv6, local_ip, network_type, mac_address,
                                   city, region, country, location, isp_org, postal_code, timezone,
                                   screen_resolution, screen_orientation, system_time,
                                   app_signaling_url, app_sender_id, app_video_source, app_video_resolution,
                                   app_video_bitrate, app_video_codec, app_video_framerate, app_camera_id,
                                   app_screen_capture_quality, app_audio_source, app_auto_start_game,
                                   app_auto_start_game_package, app_log_display_enabled, app_version,
                                   app_version_code, app_build_type, app_has_manual_operation, app_first_install,
                                   app_hdmi_device_path, app_usb_capture_device_path,
                                   room_server_domain, room_id, room_name, room_category_id, room_sort_order,
                                   is_online, first_online_time, last_online_time, last_offline_time,
                                   last_update_time, heartbeat_count
                            FROM fa_sender_device_info
                            ORDER BY room_server_domain, room_category_id, room_sort_order, last_online_time DESC
                            """
                        cursor.execute(sql)
                        db_senders = cursor.fetchall()

                        for sender in db_senders:
                            sender_id = sender['sender_id']
                            # 检查是否在线（结合内存状态）
                            is_online = sender_id in connected_clients or sender_id in stream_sources

                            # 格式化时间字段
                            for time_field in ['first_online_time', 'last_online_time', 'last_offline_time', 'last_update_time', 'system_time']:
                                if sender[time_field]:
                                    sender[time_field] = sender[time_field].isoformat()

                            senders_info[sender_id] = {
                                'id': sender_id,
                                'name': f"Device {sender_id}",
                                'description': f"{sender['motherboard_model'] or 'Unknown'} - {sender['android_version'] or 'Unknown'}",
                                'online': is_online,
                                'last_heartbeat': last_heartbeat.get(sender_id, 0),
                                'connection_time': connection_states.get(sender_id, {}).get('connected_at', 0),
                                'device_info': {
                                    'cpu_unique_id': sender['cpu_unique_id'],
                                    'wechat_sn': sender['wechat_sn'],
                                    'motherboard_model': sender['motherboard_model'],
                                    'android_version': sender['android_version'],
                                    'system_version': sender['system_version'],
                                    'available_storage': sender['available_storage'],
                                    'total_storage': sender['total_storage'],
                                    'total_memory': sender['total_memory'],
                                    'available_memory': sender['available_memory'],
                                    'cpu_temperature': sender['cpu_temperature'],
                                    'public_ip': sender['public_ip'],
                                    'public_ipv6': sender['public_ipv6'],
                                    'local_ip': sender['local_ip'],
                                    'network_type': sender['network_type'],
                                    'mac_address': sender['mac_address'],
                                    'city': sender['city'],
                                    'region': sender['region'],
                                    'country': sender['country'],
                                    'location': sender['location'],
                                    'isp_org': sender['isp_org'],
                                    'postal_code': sender['postal_code'],
                                    'timezone': sender['timezone'],
                                    'screen_resolution': sender['screen_resolution'],
                                    'screen_orientation': sender['screen_orientation'],
                                    'system_time': sender['system_time'],
                                    'room_server_domain': sender['room_server_domain'],
                                    'room_id': sender['room_id'],
                                    'room_name': sender['room_name'],
                                    'room_category_id': sender['room_category_id'],
                                    'room_sort_order': sender['room_sort_order']
                                },
                                'app_settings': {
                                    'signaling_url': sender['app_signaling_url'],
                                    'sender_id': sender['app_sender_id'],
                                    'video_source': sender['app_video_source'],
                                    'video_resolution': sender['app_video_resolution'],
                                    'video_bitrate': sender['app_video_bitrate'],
                                    'video_codec': sender['app_video_codec'],
                                    'video_framerate': sender['app_video_framerate'],
                                    'camera_id': sender['app_camera_id'],
                                    'screen_capture_quality': sender['app_screen_capture_quality'],
                                    'audio_source': sender['app_audio_source'],
                                    'auto_start_game': bool(sender['app_auto_start_game']),
                                    'auto_start_game_package': sender['app_auto_start_game_package'],
                                    'log_display_enabled': bool(sender['app_log_display_enabled']),
                                    'version': sender['app_version'],
                                    'version_code': sender['app_version_code'],
                                    'build_type': sender['app_build_type'],
                                    'has_manual_operation': bool(sender['app_has_manual_operation']),
                                    'first_install': bool(sender['app_first_install']),
                                    'hdmi_device_path': sender['app_hdmi_device_path'],
                                    'usb_capture_device_path': sender['app_usb_capture_device_path']
                                },
                                'status': {
                                    'first_online_time': sender['first_online_time'],
                                    'last_online_time': sender['last_online_time'],
                                    'last_offline_time': sender['last_offline_time'],
                                    'last_update_time': sender['last_update_time'],
                                    'heartbeat_count': sender['heartbeat_count']
                                }
                            }

                    except Exception as e:
                        logger.error(f"查询发送端信息失败: {e}")
                    finally:
                        if cursor:
                            cursor.close()
                        connection.close()

                # 添加仅在内存中的发送端（没有数据库记录的）
                for client_id, source_info in stream_sources.items():
                    if client_id not in senders_info:
                        senders_info[client_id] = {
                            **source_info,
                            'online': client_id in connected_clients,
                            'last_heartbeat': last_heartbeat.get(client_id, 0),
                            'connection_time': connection_states.get(client_id, {}).get('connected_at', 0),
                            'device_info': {},
                            'status': {'source': 'heartbeat_only'}
                        }

                # 也包括其他连接的客户端（可能是发送端但没有注册为视频源）
                for client_id in connected_clients:
                    if client_id not in senders_info and client_id not in receiver_connections:
                        senders_info[client_id] = {
                            'id': client_id,
                            'name': client_id,
                            'description': '未知发送端',
                            'online': True,
                            'last_heartbeat': last_heartbeat.get(client_id, 0),
                            'connection_time': connection_states.get(client_id, {}).get('connected_at', 0),
                            'device_info': {},
                            'status': {'source': 'heartbeat_only'}
                        }

                self._send_json_response({'senders': senders_info})
            else:
                client_id = sub_paths[0]
                if client_id in stream_sources:
                    sender_info = stream_sources[client_id].copy()
                    sender_info['online'] = client_id in connected_clients
                    sender_info['last_heartbeat'] = last_heartbeat.get(client_id, 0)
                    sender_info['connection_time'] = connection_states.get(client_id, {}).get('connected_at', 0)
                    self._send_json_response({'sender': sender_info})
                elif client_id in connected_clients:
                    # 连接的客户端但不是注册的视频源
                    sender_info = {
                        'id': client_id,
                        'name': client_id,
                        'description': '未知发送端',
                        'online': True,
                        'last_heartbeat': last_heartbeat.get(client_id, 0),
                        'connection_time': connection_states.get(client_id, {}).get('connected_at', 0)
                    }
                    self._send_json_response({'sender': sender_info})
                else:
                    self._send_json_response({'error': 'Sender not found'}, 404)
        else:
            self._send_json_response({'error': 'Method not allowed'}, 405)

    def _handle_config_api(self, sub_paths):
        """处理配置相关API"""
        if self.command == 'GET':
            self._send_json_response({'config': server_config})
        elif self.command == 'POST':
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                try:
                    data = json.loads(post_data.decode('utf-8'))

                    # 管理台的配置操作不需要验证签名
                    # 信令服务器会为发送给接收端的配置添加签名

                    if sub_paths and sub_paths[0] == 'broadcast':
                        # 广播配置到所有接收端
                        add_async_task('broadcast_config', {})
                        self._send_json_response({
                            'message': 'Config broadcast request received',
                            'note': 'Broadcast will be processed asynchronously'
                        })
                        logger.info("📡 收到配置广播请求，已加入异步队列")

                    elif sub_paths and sub_paths[0] == 'send':
                        # 向指定接收端发送配置
                        receiver_ids = data.get('receiver_ids', [])
                        if receiver_ids:
                            add_async_task('send_config', {'receiver_ids': receiver_ids})
                            self._send_json_response({
                                'message': f'Config send request received for {len(receiver_ids)} receivers',
                                'receiver_ids': receiver_ids
                            })
                            logger.info(f"📡 收到向指定接收端发送配置请求: {receiver_ids}")
                        else:
                            self._send_json_response({'error': 'No receiver_ids provided'}, 400)
                    else:
                        # 更新配置
                        new_config = data.get('config', {})
                        auto_broadcast = data.get('auto_broadcast', False)

                        # 更新配置
                        server_config.update(new_config)

                        response = {'message': 'Config updated successfully'}

                        if auto_broadcast:
                            # 如果设置了自动广播，添加到异步队列
                            add_async_task('broadcast_config', {})
                            response['broadcast'] = 'Config will be broadcasted to all receivers'
                            logger.info("📡 配置更新后将自动广播到所有接收端")

                        self._send_json_response(response)
                        logger.info("服务器配置已更新")

                except json.JSONDecodeError:
                    self._send_json_response({'error': 'Invalid JSON'}, 400)
            else:
                # 处理无数据的广播请求
                if sub_paths and sub_paths[0] == 'broadcast':
                    add_async_task('broadcast_config', {})
                    self._send_json_response({
                        'message': 'Config broadcast request received',
                        'note': 'Broadcast will be processed asynchronously'
                    })
                    logger.info("📡 收到配置广播请求，已加入异步队列")
                else:
                    self._send_json_response({'error': 'No data provided'}, 400)
        else:
            self._send_json_response({'error': 'Method not allowed'}, 405)

    def _handle_commands_api(self, sub_paths):
        """处理命令相关API"""
        if self.command == 'POST' and sub_paths:
            client_id = sub_paths[0]
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                try:
                    command_data = json.loads(post_data.decode('utf-8'))
                    command = command_data.get('command')
                    params = command_data.get('params', {})
                    device_id = command_data.get('device_id')

                    # 管理台发送的命令不需要验证签名
                    # 信令服务器会为命令添加签名后发送给接收端

                    # 智能检查客户端是否在线
                    if not is_client_really_online(client_id):
                        # 获取详细的离线原因
                        offline_reason = "unknown"
                        if client_id not in connected_clients:
                            offline_reason = "not_in_connection_list"
                        elif client_id in last_heartbeat:
                            current_time = int(time.time())
                            time_since_heartbeat = current_time - last_heartbeat[client_id]
                            if time_since_heartbeat > HEARTBEAT_TIMEOUT:
                                offline_reason = f"heartbeat_timeout_{time_since_heartbeat}s"
                            else:
                                offline_reason = "websocket_disconnected"
                        else:
                            offline_reason = "no_heartbeat_record"

                        logger.warning(f"❌ 客户端 {client_id} 离线，原因: {offline_reason}")
                        self._send_json_response({
                            'error': f'Client {client_id} is not online',
                            'reason': offline_reason,
                            'success': False
                        }, 404)
                        return

                    # 创建命令消息
                    if command == 'upgrade':
                        # 升级命令
                        message = {
                            'type': 'upgrade_command',
                            'apk_url': params.get('apk_url'),
                            'version': params.get('version'),
                            'force': params.get('force', False),
                            'timestamp': int(time.time())
                        }
                    else:
                        # 普通控制命令
                        message = {
                            'type': 'control_command',
                            'command': command,
                            'params': params,
                            'timestamp': int(time.time())
                        }

                    # 信令服务器总是为发送给接收端的命令添加签名
                    # 使用目标客户端ID作为设备ID进行签名
                    signed_message = create_signed_command(message, client_id)
                    if signed_message:
                        message = signed_message
                        logger.info(f"🔐 已为命令添加签名: {client_id}")
                    else:
                        logger.warning(f"⚠️ 为命令添加签名失败: {client_id}")
                        # 即使签名失败也继续发送，让接收端决定是否接受

                    # 发送命令到WebSocket客户端
                    try:
                        # 这里需要在事件循环中执行
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        async def send_command():
                            try:
                                await connected_clients[client_id].send(json.dumps(message))
                                return True
                            except Exception as e:
                                logger.error(f"发送命令失败: {client_id} | 错误: {e}")
                                return False

                        success = loop.run_until_complete(send_command())
                        loop.close()

                        if success:
                            logger.info(f"📤 已发送命令给 {client_id}: {command}")
                            self._send_json_response({
                                'message': f'Command {command} sent to {client_id}',
                                'success': True
                            })
                        else:
                            self._send_json_response({
                                'error': f'Failed to send command to {client_id}',
                                'success': False
                            }, 500)

                    except Exception as e:
                        logger.error(f"发送命令异常: {client_id} | 错误: {e}")
                        self._send_json_response({
                            'error': f'Exception sending command: {str(e)}',
                            'success': False
                        }, 500)

                except json.JSONDecodeError:
                    self._send_json_response({'error': 'Invalid JSON'}, 400)
            else:
                self._send_json_response({'error': 'No command data provided'}, 400)
        else:
            self._send_json_response({'error': 'Method not allowed'}, 405)

    def _send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response.encode('utf-8'))))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))

    def _get_content_type(self, path):
        return mimetypes.guess_type(path)[0] or 'application/octet-stream'


def start_http_server(host, port, directory):
    class CustomHandler(CustomHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            self.web_directory = directory
            super().__init__(*args, **kwargs)

    httpd = HTTPServer((host, port), CustomHandler)
    logger.info(f"HTTP服务器启动在 http://{host}:{port}")
    if directory:
        logger.info(f"Web文件目录: {directory}")
    httpd.serve_forever()

def setup_logging(log_dir=None, log_level="INFO", console_output=True):
    """设置日志配置"""
    import logging.handlers
    from datetime import datetime

    # 创建日志格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'

    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 控制台输出
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(log_format, date_format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

    # 文件输出
    if log_dir:
        try:
            # 创建日志目录
            os.makedirs(log_dir, exist_ok=True)

            # 生成日志文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"signaling_server_{timestamp}.log"
            log_filepath = os.path.join(log_dir, log_filename)

            # 创建文件处理器（支持轮转）
            file_handler = logging.handlers.RotatingFileHandler(
                log_filepath,
                maxBytes=50*1024*1024,  # 50MB
                backupCount=10,         # 保留10个备份文件
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            file_formatter = logging.Formatter(log_format, date_format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)

            print(f"📝 日志文件已创建: {log_filepath}")
            print(f"📁 日志目录: {log_dir}")

        except Exception as e:
            print(f"❌ 创建日志文件失败: {e}")
            print("⚠️ 将只输出到控制台")

async def main():
    parser = argparse.ArgumentParser(description="增强版WebRTC信令服务器")
    parser.add_argument("--ws-host", default="0.0.0.0", help="WebSocket服务器主机")
    parser.add_argument("--ws-port", type=int, default=28765, help="WebSocket服务器端口")
    parser.add_argument("--http-host", default="0.0.0.0", help="HTTP服务器主机")
    parser.add_argument("--http-port", type=int, default=28080, help="HTTP服务器端口")
    parser.add_argument("--web-dir", default="/www/wwwroot/sj/web", help="Web文件目录")
    parser.add_argument("--use-ssl", action="store_true", help="使用SSL")
    parser.add_argument("--ssl-cert", default="", help="SSL证书文件")
    parser.add_argument("--ssl-key", default="", help="SSL密钥文件")

    # 日志相关参数
    parser.add_argument("--log-dir", default="./logs", help="日志文件保存目录")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别")
    parser.add_argument("--no-console", action="store_true", help="禁用控制台输出（仅输出到文件）")

    args = parser.parse_args()

    # 设置日志配置
    setup_logging(
        log_dir=args.log_dir if args.log_dir else None,
        log_level=args.log_level,
        console_output=not args.no_console
    )

    # 初始化数据库连接池
    logger.info("🗄️ 初始化数据库连接...")
    if not init_database():
        logger.error("❌ 数据库初始化失败，服务器将继续运行但设备信息功能不可用")
    else:
        logger.info("✅ 数据库连接池初始化成功")
    
    
    os.makedirs(args.web_dir, exist_ok=True)
    logger.info(f"Web文件目录: {args.web_dir}")
    
    http_thread = Thread(target=start_http_server, 
                         args=(args.http_host, args.http_port, args.web_dir),
                         daemon=True)
    http_thread.start()
    
    
    local_ip = get_local_ip()
    
    
    if args.use_ssl and args.ssl_cert and args.ssl_key:
        
        ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        ssl_context.load_cert_chain(args.ssl_cert, args.ssl_key)
        
        
        server = await websockets.serve(
            handle_client, 
            args.ws_host, 
            args.ws_port,
            ssl=ssl_context,
            ping_interval=30,  
            ping_timeout=60,   
            close_timeout=10   
        )
        logger.info(f"信令服务器启动在 wss://{args.ws_host}:{args.ws_port}")
        logger.info(f"可通过 https://{local_ip}:{args.http_port} 访问Web界面")
    else:
        
        server = await websockets.serve(
            handle_client, 
            args.ws_host, 
            args.ws_port,
            ping_interval=30,  
            ping_timeout=60,   
            close_timeout=10   
        )
        logger.info(f"信令服务器启动在 ws://{args.ws_host}:{args.ws_port}")
        logger.info(f"可通过 http://{local_ip}:{args.http_port} 访问Web界面")
    
    logger.info("当前可用的STUN/TURN服务器:")
    logger.info("  - stun:stun.l.google.com:19302")
    logger.info("  - stun:stun1.l.google.com:19302")
    logger.info("  - stun:stun2.l.google.com:19302")
    logger.info("  - turn:numb.viagenie.ca (<EMAIL>:muazkh)")

    # 启动心跳超时检查任务
    async def heartbeat_checker():
        while True:
            await asyncio.sleep(30)  # 每30秒检查一次
            await check_heartbeat_timeouts()

    # 启动接收端状态监控任务
    async def receiver_status_monitor():
        while True:
            await asyncio.sleep(STATUS_REPORT_INTERVAL)  # 每60秒检查一次
            current_time = int(time.time())

            # 检查接收端状态报告超时
            for client_id, conn_info in list(receiver_connections.items()):
                last_seen = conn_info.get('last_seen', 0)
                if current_time - last_seen > STATUS_REPORT_INTERVAL * 2:  # 超过2个周期没有状态报告
                    logger.warning(f"⏰ 接收端 {client_id} 状态报告超时")

                    # 尝试发送保活消息
                    if client_id in connected_clients:
                        try:
                            await connected_clients[client_id].send(json.dumps({
                                'type': 'ping',
                                'timestamp': int(time.time())
                            }))
                        except:
                            logger.warning(f"发送保活消息失败: {client_id}")



    # 启动异步任务处理器
    async def async_task_processor():
        logger.info("⚙️ 异步任务处理器开始运行")
        while True:
            await asyncio.sleep(1)  # 每秒检查一次待处理任务
            if pending_tasks:
                logger.info(f"📋 发现 {len(pending_tasks)} 个待处理任务")
                await process_pending_tasks()
            # 每30秒输出一次状态
            if int(time.time()) % 30 == 0:
                logger.debug(f"⚙️ 异步任务处理器运行中，待处理任务: {len(pending_tasks)}")

    # 设备离线检查任务
    async def device_offline_checker():
        logger.info("📱 设备离线检查任务开始运行")
        while True:
            await asyncio.sleep(30)  # 每30秒检查一次
            await check_device_offline()

    # 房间信息初始化任务（仅启动时执行一次）
    async def room_info_initializer():
        if ROOM_INFO_UPDATE_ON_STARTUP:
            logger.info("🏠 开始初始化房间信息...")
            await update_all_server_rooms()
            logger.info("🏠 房间信息初始化完成")
        else:
            logger.info("🏠 跳过房间信息初始化")

    async def update_all_server_rooms():
        """更新所有服务器群的房间信息"""
        logger.info("🏠 开始更新所有服务器群的房间信息")

        for server_config in SERVER_GROUPS:
            try:
                # 兼容旧的字符串格式和新的配置格式
                if isinstance(server_config, str):
                    domain = server_config
                    name = domain
                else:
                    domain = server_config.get('domain', '')
                    name = server_config.get('name', domain)

                rooms_data = await fetch_room_info_from_server(server_config)
                if rooms_data:
                    await update_room_info_to_database(domain, rooms_data)

                    # 通知管理员更新
                    await notify_admin_room_update(domain, len(rooms_data))

            except Exception as e:
                logger.error(f"更新服务器 {name} ({domain}) 房间信息失败: {e}")

        logger.info("🏠 所有服务器群房间信息更新完成")

    async def notify_admin_room_update(domain, room_count):
        """通知管理员房间信息更新"""
        if admin_consoles:
            message = {
                'type': 'room_info_updated',
                'domain': domain,
                'room_count': room_count,
                'timestamp': int(time.time())
            }

            for admin_id, admin_ws in list(admin_consoles.items()):
                try:
                    await admin_ws.send(json.dumps(message))
                    logger.debug(f"已通知管理员 {admin_id} 房间信息更新")
                except Exception as e:
                    logger.warning(f"通知管理员 {admin_id} 失败: {e}")

# 函数已移到全局作用域

    # 添加房间信息字段到设备信息表
    await add_room_info_fields()

    # 初始化房间信息（仅启动时执行一次）
    await room_info_initializer()

    # 启动所有后台任务
    heartbeat_task = asyncio.create_task(heartbeat_checker())
    status_monitor_task = asyncio.create_task(receiver_status_monitor())
    task_processor_task = asyncio.create_task(async_task_processor())
    device_offline_task = asyncio.create_task(device_offline_checker())
    heartbeat_timeout_task = asyncio.create_task(check_heartbeat_timeout())

    logger.info("💓 心跳超时检查任务已启动")
    logger.info("📊 接收端状态监控任务已启动")
    logger.info("⚙️ 异步任务处理器已启动")
    logger.info("📱 设备离线检查任务已启动")
    logger.info("⏰ 心跳超时监控任务已启动")

    try:
        await server.wait_closed()
    finally:
        # 取消所有后台任务
        heartbeat_task.cancel()
        status_monitor_task.cancel()
        task_processor_task.cancel()
        device_offline_task.cancel()
        heartbeat_timeout_task.cancel()
        logger.info("所有后台任务已取消")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("服务器已停止")
        sys.exit(0)



