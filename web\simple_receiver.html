<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WebRTC视频</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
        }
        video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <video id="video" autoplay playsinline disablePictureInPicture controlsList="nodownload nofullscreen noremoteplayback"></video>

    <script>
        // 配置
        const SIGNALING_URL = 'wss://sling.91jdcd.com/ws/';
        const SENDER_ID = 'android-d651577e'; // 固定的发送端ID
        const ICE_SERVERS = [
            { urls: ['stun:stun.l.google.com:19302', 'stun:stun1.l.google.com:19302', 'stun:stun2.l.google.com:19302', 'stun:stun3.l.google.com:19302', 'stun:stun4.l.google.com:19302'] },
            {
                urls: 'turn:numb.viagenie.ca',
                username: '<EMAIL>',
                credential: 'muazkh'
            }
        ];

        // 调试信息
        console.log('页面加载完成，WebRTC简易接收端初始化');

        // 元素
        const videoEl = document.getElementById('video');

        // 变量
        let pc = null;
        let ws = null;
        let dataChannel = null;
        let receiverId = `receiver-${Math.random().toString(36).substring(2, 10)}`;
        let pendingIceCandidates = [];
        let connectionTimeout = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;

        // 连接到信令服务器
        function connectToSignalingServer() {
            console.log('正在连接到信令服务器...');

            try {
                ws = new WebSocket(SIGNALING_URL);

                ws.onopen = () => {
                    console.log('已连接到信令服务器');
                    reconnectAttempts = 0;

                    // 注册接收端
                    ws.send(JSON.stringify({
                        type: 'register',
                        id: receiverId,
                        role: 'viewer',
                        name: '全屏接收端'
                    }));
                };

                ws.onmessage = async (event) => {
                    const data = JSON.parse(event.data);
                    console.log('收到消息:', data.type);

                    if (data.type === 'registered') {
                        console.log('已注册，准备连接到发送端');
                        connectToPeer();
                    }
                    else if (data.type === 'offer' && data.from === SENDER_ID) {
                        try {
                            if (!pc) {
                                setupPeerConnection();
                            }

                            console.log('收到offer SDP:', data.sdp);
                            const offer = new RTCSessionDescription({
                                type: 'offer',
                                sdp: data.sdp
                            });

                            try {
                                await pc.setRemoteDescription(offer);
                                console.log('已设置远程描述(offer)');
                            } catch (e) {
                                console.error('设置远程描述失败:', e);
                                // 尝试重新创建PeerConnection
                                if (pc) {
                                    pc.close();
                                    pc = null;
                                }
                                setupPeerConnection();
                                await pc.setRemoteDescription(offer);
                                console.log('第二次尝试设置远程描述成功');
                            }

                            // 添加之前缓存的ICE候选
                            if (pendingIceCandidates.length > 0) {
                                console.log(`添加 ${pendingIceCandidates.length} 个缓存的ICE候选`);
                                for (const candidate of pendingIceCandidates) {
                                    try {
                                        await pc.addIceCandidate(candidate);
                                        console.log('成功添加缓存的ICE候选');
                                    } catch (e) {
                                        console.error('添加缓存的ICE候选错误:', e, candidate);
                                    }
                                }
                                pendingIceCandidates = [];
                            }

                            const answer = await pc.createAnswer();
                            await pc.setLocalDescription(answer);

                            const formattedSdp = pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n');

                            ws.send(JSON.stringify({
                                type: 'answer',
                                target: SENDER_ID,
                                sdp: formattedSdp
                            }));

                            console.log('已发送answer');

                            if (connectionTimeout) {
                                clearTimeout(connectionTimeout);
                            }

                            connectionTimeout = setTimeout(() => {
                                if (pc && (pc.iceConnectionState !== 'connected' && pc.iceConnectionState !== 'completed')) {
                                    console.log('连接超时，尝试重新连接');
                                    reconnect();
                                }
                            }, 15000);
                        } catch (e) {
                            console.error('处理offer错误:', e);
                        }
                    }
                    else if (data.type === 'candidate' && data.from === SENDER_ID) {
                        try {
                            const candidate = data.candidate;
                            if (candidate) {
                                console.log('收到ICE候选:', candidate);

                                if (!pc) {
                                    console.log('PeerConnection不存在，创建新的连接');
                                    setupPeerConnection();
                                }

                                if (!pc.remoteDescription || pc.remoteDescription.type === '') {
                                    pendingIceCandidates.push(candidate);
                                    console.log('缓存ICE候选，等待远程描述设置，当前缓存数量:', pendingIceCandidates.length);
                                } else {
                                    try {
                                        await pc.addIceCandidate(new RTCIceCandidate(candidate));
                                        console.log('已添加ICE候选');
                                    } catch (e) {
                                        console.error('添加ICE候选失败:', e);
                                        // 尝试再次添加
                                        pendingIceCandidates.push(candidate);
                                    }
                                }
                            }
                        } catch (e) {
                            console.error('处理ICE候选错误:', e);
                        }
                    }
                    else if (data.type === 'client_joined' && data.id === SENDER_ID) {
                        console.log('发送端已加入，尝试连接');
                        connectToPeer();
                    }
                    else if (data.type === 'client_left' && data.id === SENDER_ID) {
                        console.log('发送端已离开，等待重新连接');
                    }
                };

                ws.onclose = () => {
                    console.log('信令服务器连接已关闭');

                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        console.log(`尝试重新连接 (${reconnectAttempts}/${maxReconnectAttempts})...`);
                        setTimeout(connectToSignalingServer, 3000);
                    }
                };

                ws.onerror = (error) => {
                    console.error('信令服务器错误:', error);
                };
            } catch (e) {
                console.error('连接信令服务器错误:', e);
                setTimeout(connectToSignalingServer, 5000);
            }
        }

        // 设置对等连接
        function setupPeerConnection() {
            pc = new RTCPeerConnection({
                iceServers: ICE_SERVERS,
                iceTransportPolicy: 'all',
                bundlePolicy: 'max-bundle',
                rtcpMuxPolicy: 'require',
                sdpSemantics: 'unified-plan'
            });

            // 创建数据通道
            dataChannel = pc.createDataChannel('control');

            dataChannel.onopen = () => {
                console.log('数据通道已打开');
                dataChannel.send(JSON.stringify({
                    type: 'hello',
                    from: receiverId
                }));
            };

            dataChannel.onmessage = (event) => {
                console.log('收到数据通道消息:', event.data);
            };

            pc.onicecandidate = (event) => {
                if (event.candidate) {
                    const candidate = {
                        candidate: event.candidate.candidate,
                        sdpMid: event.candidate.sdpMid,
                        sdpMLineIndex: event.candidate.sdpMLineIndex
                    };

                    ws.send(JSON.stringify({
                        type: 'candidate',
                        target: SENDER_ID,
                        candidate: candidate
                    }));
                }
            };

            pc.oniceconnectionstatechange = () => {
                console.log('ICE连接状态:', pc.iceConnectionState);
                if (pc.iceConnectionState === 'connected' || pc.iceConnectionState === 'completed') {
                    console.log('已连接到发送端');
                    if (connectionTimeout) {
                        clearTimeout(connectionTimeout);
                        connectionTimeout = null;
                    }
                } else if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
                    console.log('连接失败或断开，尝试重新连接');
                    reconnect();
                }
            };

            pc.onconnectionstatechange = () => {
                console.log('连接状态:', pc.connectionState);
            };

            pc.onsignalingstatechange = () => {
                console.log('信令状态:', pc.signalingState);
            };

            pc.ontrack = (event) => {
                console.log('收到轨道:', event.track.kind, '轨道ID:', event.track.id);
                console.log('轨道状态:', event.track.readyState);
                console.log('轨道设置:', event.track.getSettings());

                if (event.track.kind === 'video') {
                    // 检查流是否有效
                    if (event.streams && event.streams.length > 0) {
                        console.log('收到视频流, 轨道数量:', event.streams[0].getTracks().length);

                        // 移除之前的视频源
                        if (videoEl.srcObject) {
                            const tracks = videoEl.srcObject.getTracks();
                            tracks.forEach(track => track.stop());
                        }

                        // 设置新的视频源
                        videoEl.srcObject = event.streams[0];
                        videoEl.muted = false; // 确保不是静音的

                        // 监听轨道结束事件
                        event.track.onended = () => {
                            console.log('视频轨道已结束');
                        };

                        event.track.onmute = () => {
                            console.log('视频轨道已静音');
                        };

                        event.track.onunmute = () => {
                            console.log('视频轨道已取消静音');
                        };

                        // 确保视频自动播放
                        videoEl.play().then(() => {
                            console.log('视频播放成功');
                        }).catch(e => {
                            console.error('自动播放失败:', e);

                            // 添加用户交互事件处理器来启动播放
                            document.body.addEventListener('click', () => {
                                videoEl.play().then(() => {
                                    console.log('用户交互后视频播放成功');
                                }).catch(e => console.error('播放失败:', e));
                            }, { once: true });

                            // 显示点击播放提示
                            alert('请点击屏幕开始播放视频');
                        });
                    } else {
                        console.error('收到视频轨道但没有关联的媒体流');
                    }
                }
            };

            pc.ondatachannel = (event) => {
                const channel = event.channel;
                console.log('收到数据通道:', channel.label);

                dataChannel = channel;

                channel.onopen = () => {
                    console.log('数据通道已打开');
                    channel.send(JSON.stringify({
                        type: 'hello',
                        from: receiverId
                    }));
                };

                channel.onmessage = (event) => {
                    console.log('收到数据通道消息:', event.data);
                };
            };
        }

        // 连接到对等端
        function connectToPeer() {
            console.log('正在连接到发送端...');

            if (pc) {
                pc.close();
                pc = null;
            }

            setupPeerConnection();

            // 创建并发送offer
            pc.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true,
                iceRestart: true
            }).then(offer => {
                return pc.setLocalDescription(offer);
            }).then(() => {
                const formattedSdp = pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n');

                ws.send(JSON.stringify({
                    type: 'offer',
                    target: SENDER_ID,
                    sdp: formattedSdp
                }));

                console.log('已发送offer');

                connectionTimeout = setTimeout(() => {
                    if (pc && (pc.iceConnectionState !== 'connected' && pc.iceConnectionState !== 'completed')) {
                        console.log('连接超时，尝试重新连接');
                        reconnect();
                    }
                }, 15000);
            }).catch(e => {
                console.error('创建offer错误:', e);
                setTimeout(connectToPeer, 3000);
            });
        }

        // 重新连接
        function reconnect() {
            if (connectionTimeout) {
                clearTimeout(connectionTimeout);
                connectionTimeout = null;
            }

            if (pc) {
                pc.close();
                pc = null;
            }

            setTimeout(connectToPeer, 2000);
        }

        // 防止视频暂停
        videoEl.addEventListener('pause', () => {
            videoEl.play().catch(e => console.error('重新播放失败:', e));
        });

        // 处理页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                if (!pc || pc.iceConnectionState === 'disconnected' || pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'closed') {
                    console.log('页面可见，检测到连接已断开，尝试重新连接');
                    reconnect();
                }

                // 确保视频在页面可见时播放
                if (videoEl.paused && videoEl.srcObject) {
                    videoEl.play().catch(e => console.error('重新播放失败:', e));
                }
            }
        });

        // 网络状态变化处理
        window.addEventListener('online', () => {
            console.log('网络已恢复');
            if (!pc || pc.iceConnectionState !== 'connected') {
                reconnect();
            }
        });

        // 自动进入全屏模式
        function requestFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(e => {
                    console.error('全屏请求被拒绝:', e);
                });
            }
        }

        // 用户交互后尝试进入全屏
        document.addEventListener('click', requestFullscreen, { once: true });

        // 启动连接
        connectToSignalingServer();

        // 防止视频控制
        videoEl.addEventListener('contextmenu', e => e.preventDefault());
    </script>
</body>
</html>
