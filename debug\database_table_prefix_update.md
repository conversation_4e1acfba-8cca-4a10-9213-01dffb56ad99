# 数据库表前缀更新

## 🎯 更新内容

为数据库表添加 `fa_` 前缀，以符合项目的命名规范。

## 📊 表名变更

| 原表名 | 新表名 |
|--------|--------|
| `sender_device_info` | `fa_sender_device_info` |
| `sender_status_history` | `fa_sender_status_history` |

## 🔧 修改的文件

### 1. `database/sender_device_info_simple.sql`
```sql
-- 修改前
CREATE TABLE IF NOT EXISTS sender_device_info (
CREATE TABLE IF NOT EXISTS sender_status_history (
FOREIGN KEY (sender_id) REFERENCES sender_device_info(sender_id)
INSERT INTO sender_device_info (

-- 修改后
CREATE TABLE IF NOT EXISTS fa_sender_device_info (
CREATE TABLE IF NOT EXISTS fa_sender_status_history (
FOREIGN KEY (sender_id) REFERENCES fa_sender_device_info(sender_id)
INSERT INTO fa_sender_device_info (
```

### 2. `enhanced_signaling_server.py`
```python
# 修改前
INSERT INTO sender_device_info (
UPDATE sender_device_info 
FROM sender_device_info

# 修改后
INSERT INTO fa_sender_device_info (
UPDATE fa_sender_device_info 
FROM fa_sender_device_info
```

## 🚀 部署步骤

### 1. 如果已有旧表，需要重命名
```sql
-- 重命名现有表（如果存在）
RENAME TABLE sender_device_info TO fa_sender_device_info;
RENAME TABLE sender_status_history TO fa_sender_status_history;
```

### 2. 或者删除旧表重新创建
```sql
-- 删除旧表
DROP TABLE IF EXISTS sender_status_history;
DROP TABLE IF EXISTS sender_device_info;

-- 执行新的SQL文件
SOURCE database/sender_device_info_simple.sql;
```

### 3. 重启信令服务器
```bash
# 停止服务
pkill -f enhanced_signaling_server.py

# 启动服务
python enhanced_signaling_server.py --ws-port 8765 --http-port 28080
```

## 🔍 验证更新

### 1. 检查表是否创建成功
```sql
SHOW TABLES LIKE 'fa_%';
```

应该显示：
```
+---------------------------+
| Tables_in_db (fa_%)       |
+---------------------------+
| fa_sender_device_info     |
| fa_sender_status_history  |
+---------------------------+
```

### 2. 检查表结构
```sql
DESCRIBE fa_sender_device_info;
```

### 3. 测试数据插入
```sql
SELECT * FROM fa_sender_device_info LIMIT 5;
```

## 📝 注意事项

1. **外键约束**: 已更新外键引用的表名
2. **示例数据**: 示例数据会插入到新的表中
3. **API兼容**: 信令服务器的API查询已更新表名
4. **备份**: 建议在执行前备份现有数据

## 🎯 影响范围

- ✅ 数据库表结构
- ✅ 信令服务器SQL查询
- ✅ 外键约束
- ✅ 示例数据插入
- ❌ Android端代码（无需修改）
- ❌ Web管理界面（无需修改）

## 🔄 回滚方案

如果需要回滚到原表名：
```sql
RENAME TABLE fa_sender_device_info TO sender_device_info;
RENAME TABLE fa_sender_status_history TO sender_status_history;
```

然后在信令服务器代码中将表名改回原来的名称。

现在数据库表已使用 `fa_` 前缀，符合项目命名规范！
