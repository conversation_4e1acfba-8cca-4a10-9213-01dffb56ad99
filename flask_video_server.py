#!/usr/bin/env python3
# flask_video_server.py - 使用Flask提供视频流
import cv2
import time
import threading
import argparse
import logging
import requests
from flask import Flask, Response, render_template, request

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('flask-video-server')

# 解析命令行参数
parser = argparse.ArgumentParser(description="Flask视频流服务器")
parser.add_argument("--video", help="视频源URL (例如 http://*************:80/0.mp4 或 0 表示摄像头)", required=True)
parser.add_argument("--port", help="服务器端口", type=int, default=5000)
parser.add_argument("--host", help="服务器主机", default="0.0.0.0")
args = parser.parse_args()

# 创建Flask应用
app = Flask(__name__)

# 全局变量
video_source = None
frame_buffer = None
last_frame_time = 0
is_running = False
lock = threading.Lock()

def get_video_source():
    """创建视频源"""
    global video_source
    try:
        # 检查是否是摄像头索引
        if args.video.isdigit():
            source = int(args.video)
        else:
            source = args.video
        
        # 打开视频源
        video_source = cv2.VideoCapture(source)
        
        if not video_source.isOpened():
            logger.error(f"无法打开视频源: {args.video}")
            return False
        
        width = int(video_source.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(video_source.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = video_source.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 30
        
        logger.info(f"视频源: {args.video}, 分辨率: {width}x{height}, FPS: {fps}")
        return True
    except Exception as e:
        logger.error(f"创建视频源失败: {e}")
        return False

def video_capture_thread():
    """视频捕获线程"""
    global frame_buffer, last_frame_time, is_running
    
    logger.info("启动视频捕获线程")
    is_running = True
    
    while is_running:
        try:
            # 读取视频帧
            ret, frame = video_source.read()
            
            if not ret:
                # 如果是视频文件，循环播放
                if not args.video.isdigit():
                    video_source.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    ret, frame = video_source.read()
                    if not ret:
                        logger.error("无法读取视频帧")
                        time.sleep(0.1)
                        continue
                else:
                    logger.error("无法读取视频帧")
                    time.sleep(0.1)
                    continue
            
            # 更新帧缓冲
            with lock:
                frame_buffer = frame
                last_frame_time = time.time()
            
            # 控制帧率
            time.sleep(0.03)  # 约30FPS
            
        except Exception as e:
            logger.error(f"视频捕获错误: {e}")
            time.sleep(0.1)
    
    logger.info("视频捕获线程已停止")

def generate_frames():
    """生成MJPEG流的帧"""
    global frame_buffer, last_frame_time
    
    while True:
        # 获取当前帧
        with lock:
            if frame_buffer is None:
                time.sleep(0.1)
                continue
            
            frame = frame_buffer.copy()
        
        # 转换为JPEG
        ret, jpeg = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
        
        if not ret:
            continue
        
        # 返回MJPEG流的一帧
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + jpeg.tobytes() + b'\r\n')
        
        # 控制帧率
        time.sleep(0.03)  # 约30FPS

@app.route('/')
def index():
    """主页"""
    # 获取本机IP
    local_ip = request.host.split(':')[0]
    if local_ip == '0.0.0.0':
        local_ip = '127.0.0.1'
    
    # 获取公网IP
    public_ip = None
    try:
        response = requests.get('https://api.ipify.org', timeout=5)
        public_ip = response.text
    except:
        pass
    
    # 渲染模板
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>视频流服务器</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
            .container {{ max-width: 800px; margin: 0 auto; }}
            .video-container {{ margin: 20px 0; }}
            img {{ width: 100%; border: 1px solid #ddd; }}
            .info {{ background: #f0f0f0; padding: 15px; border-radius: 5px; }}
            .url {{ font-family: monospace; background: #ddd; padding: 5px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>视频流服务器</h1>
            
            <div class="video-container">
                <h2>实时视频</h2>
                <img src="/video_feed" alt="视频流">
            </div>
            
            <div class="info">
                <h2>访问信息</h2>
                <p>视频流URL:</p>
                <p class="url">http://{local_ip}:{args.port}/video_feed</p>
                
                <h3>本地网络访问</h3>
                <p>在同一网络中的设备可以通过以下URL访问:</p>
                <p class="url">http://{local_ip}:{args.port}/video_feed</p>
                
                {f'''
                <h3>公网访问</h3>
                <p>您的公网IP是: {public_ip}</p>
                <p>如果您已经设置了端口转发，可以通过以下URL从外部访问:</p>
                <p class="url">http://{public_ip}:{args.port}/video_feed</p>
                ''' if public_ip else ''}
                
                <h3>使用说明</h3>
                <p>1. 在浏览器中直接访问上述URL可以查看视频流</p>
                <p>2. 在其他应用中使用上述URL作为视频源</p>
                <p>3. 如需从外部网络访问，请在路由器中设置端口转发</p>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/video_feed')
def video_feed():
    """视频流路由"""
    return Response(generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/status')
def status():
    """服务器状态"""
    global last_frame_time
    
    # 检查视频源是否正常
    if video_source is None or not video_source.isOpened():
        status = "错误"
        message = "视频源未打开"
    elif time.time() - last_frame_time > 5:
        status = "警告"
        message = "视频流可能已冻结"
    else:
        status = "正常"
        message = "视频流正常"
    
    return {
        "status": status,
        "message": message,
        "video_source": args.video,
        "last_frame_time": last_frame_time
    }

def main():
    """主函数"""
    # 创建视频源
    if not get_video_source():
        return
    
    # 启动视频捕获线程
    capture_thread = threading.Thread(target=video_capture_thread)
    capture_thread.daemon = True
    capture_thread.start()
    
    try:
        # 启动Flask服务器
        logger.info(f"启动Flask服务器在 http://{args.host}:{args.port}")
        app.run(host=args.host, port=args.port, threaded=True)
    except KeyboardInterrupt:
        logger.info("程序已终止")
    finally:
        # 停止视频捕获线程
        global is_running
        is_running = False
        capture_thread.join(timeout=1.0)
        
        # 释放视频源
        if video_source is not None:
            video_source.release()

if __name__ == "__main__":
    main()
