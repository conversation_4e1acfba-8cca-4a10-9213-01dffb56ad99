# 在线设备监控功能更新

## 问题描述

原来的 `boot_monitor.html` 页面只显示通过 `boot_report` 上报的开机设备，不会显示其他已经在线但没有重新开机的设备。这导致监控页面信息不完整。

## 解决方案

### 1. 功能扩展
- **原功能**: 只显示开机上报的设备
- **新功能**: 显示所有在线设备（开机设备 + 数据库中的在线设备）

### 2. 数据来源
1. **开机上报设备** (`boot_report`)
   - 通过WebSocket接收的开机信息
   - 包含详细的设备硬件信息
   - 实时的开机时间和运行状态

2. **数据库在线设备**
   - 从 `fa_sender_device_info` 表查询 `is_online = 1` 的设备
   - 使用 `first_online_time` 作为"开机"时间
   - 补充显示其他在线但未重新开机的设备

### 3. 界面改进

#### 设备卡片显示
```html
发送端: gamev-xxx | 游戏: 水浒传 | 📱 开机上报
🏠 房间: 水浒传_DF14 | 🌐 服务器: http://testva2.91jdcd.com
```

#### 数据来源标识
- **📱 开机上报** (绿色) - 来自 `boot_report` 的实时开机信息
- **💾 数据库** (灰色) - 来自数据库的在线设备信息

#### 页面标题更新
- 原标题: "设备开机监控"
- 新标题: "设备在线监控"
- 副标题: "实时监控设备在线状态和运行信息"

### 4. 技术实现

#### 新增函数
```python
async def get_all_devices_info(device_room_info, current_time):
    """获取所有设备信息（开机设备 + 在线设备）"""
    # 1. 添加开机设备
    # 2. 查询数据库中的在线设备
    # 3. 合并并排序
```

#### 修改函数
```python
async def send_boot_devices_update(websocket=None):
    """发送设备更新到订阅者（包括开机设备和所有在线设备）"""
    # 获取所有设备信息而不仅仅是开机设备
```

#### 权限控制保持不变
- 管理员密码: `tb###` - 查看所有设备
- 域名密码: 只能查看对应服务器域名的设备

### 5. 数据库查询

#### 查询在线设备
```sql
SELECT sender_id, cpu_unique_id, wechat_sn, motherboard_model,
       android_version, system_version, app_version,
       local_ip, public_ip, app_auto_start_game_package,
       last_online_time, first_online_time
FROM fa_sender_device_info 
WHERE is_online = 1 AND sender_id != ''
```

#### 设备信息映射
- `first_online_time` → `boot_time` (首次上线时间作为开机时间)
- `motherboard_model` → `device_model`
- `app_auto_start_game_package` → `auto_start_game_package`

### 6. 显示逻辑

#### 设备排序
1. 按 `last_update` 时间排序（最新的在前）
2. 开机设备优先显示（因为信息更准确）
3. 数据库设备补充显示

#### 在线状态检查
1. **开机设备**: 检查心跳超时（60秒）
2. **数据库设备**: 查询数据库中的 `is_online` 字段

### 7. 日志信息

#### 调试日志
```
📊 获取到 15 个设备信息（开机设备: 3, 总设备: 15）
📊 添加在线设备到监控列表: gamev-xxx
📤 构建设备数据: CPU_ID=12345678..., 房间=水浒传_DF14, 在线=true, 来源=数据库
```

#### 信息日志
```
📱 收到开机信息: gamev-xxx | CPU_ID=12345678... | 游戏=com.example.game
```

### 8. 版本信息

- **当前版本**: 1.0.9
- **更新内容**: 
  - 扩展监控页面显示所有在线设备
  - 新增数据来源标识
  - 优化设备信息获取逻辑
  - 更新页面标题和提示信息
- **更新时间**: 2025-08-28

### 9. 使用效果

#### 之前
- 只显示最近开机的设备
- 其他在线设备不可见
- 信息不完整

#### 现在
- 显示所有在线设备
- 区分数据来源（开机上报 vs 数据库）
- 完整的设备监控视图
- 保持权限控制功能

### 10. 注意事项

1. **性能考虑**: 每次更新都会查询数据库，大量设备时可能影响性能
2. **数据一致性**: 开机设备和数据库设备可能有重复，已做去重处理
3. **权限过滤**: 所有设备都会经过权限检查，确保安全性
4. **实时性**: 开机设备信息更实时，数据库设备信息可能有延迟

现在监控页面可以显示完整的在线设备列表，不再局限于最近开机的设备！
