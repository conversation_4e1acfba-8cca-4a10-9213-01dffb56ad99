# 实时开机监控功能

## 功能说明

实现了纯粹的实时开机监控，只接收和转发开机上报信息，不查询数据库中的其他设备。

## 数据流程

```
发送端开机 → WebSocket → 信令服务器 → 缓存房间信息 → WebSocket → 监控页面
```

### 详细流程

1. **发送端开机**: 设备启动后发送 `boot_report` 消息
2. **信令服务器接收**: 处理开机信息并存储到内存
3. **获取房间信息**: 从缓存中获取设备的房间信息
4. **实时转发**: 立即通过WebSocket转发给所有监控页面
5. **监控页面显示**: 实时显示开机设备信息

## 核心特性

### 1. 纯开机上报
- **只处理开机设备**: 不查询数据库中的其他在线设备
- **实时转发**: 收到开机信息立即转发给监控页面
- **内存存储**: 开机设备信息存储在 `boot_devices` 内存中

### 2. 房间信息缓存
- **缓存机制**: 房间信息缓存30秒，避免频繁数据库查询
- **按需获取**: 只在需要时查询房间信息
- **性能优化**: 大幅减少数据库负载

### 3. 权限控制
- **管理员密码**: `tb###` - 查看所有开机设备
- **域名密码**: 只能查看对应服务器域名的开机设备
- **实时过滤**: 根据权限实时过滤显示内容

## 技术实现

### 1. 开机信息处理
```python
elif data.get('type') == 'boot_report':
    # 存储开机信息到内存
    boot_devices[cpu_unique_id] = {
        'cpu_unique_id': cpu_unique_id,
        'sender_id': sender_id,
        'boot_time': boot_time,
        'device_info': {...}
    }
    
    # 实时转发给监控页面
    await send_boot_devices_update()
```

### 2. 实时转发逻辑
```python
async def send_boot_devices_update(websocket=None):
    # 获取缓存的房间信息
    device_room_info = await get_devices_room_info_cached()
    
    # 只处理开机设备
    sorted_devices = sorted(boot_devices.items(), ...)
    
    # 转发给所有监控订阅者
    for ws, subscriber_info in boot_monitor_subscribers.items():
        await send_filtered_devices_to_subscriber(...)
```

### 3. 权限过滤
```python
# 权限检查
if '*' not in allowed_domains:
    if not room_server_domain or room_server_domain not in allowed_domains:
        continue  # 跳过无权限访问的设备
```

## 数据结构

### 开机设备信息
```python
boot_devices = {
    'cpu_unique_id': {
        'cpu_unique_id': 'xxx',
        'sender_id': 'gamev-xxx',
        'boot_time': 1234567890000,
        'report_time': 1234567890000,
        'device_info': {
            'brand': 'Samsung',
            'model': 'Galaxy Tab',
            'android_version': '11',
            'app_version': '1.0.0',
            'auto_start_game_package': 'com.example.game',
            'local_ip': '*************',
            'public_ip': '*******'
        },
        'last_update': 1234567890000
    }
}
```

### 监控订阅者
```python
boot_monitor_subscribers = {
    websocket: {
        'allowed_domains': ['*'] or ['http://domain1.com'],
        'password': 'tb###',
        'remote_address': '************'
    }
}
```

## 前端显示

### 设备卡片
```html
CPU ID: 1234567890abcdef 🟢 在线
发送端: gamev-b246c42d | 游戏: 水浒传 | 📱 开机上报
🏠 房间: 水浒传_DF14 | 🌐 服务器: http://testva2.91jdcd.com
```

### 数据来源标识
- **📱 开机上报** (绿色) - 所有设备都是开机上报来源
- 移除了数据库来源标识，因为不再查询数据库

## 性能优势

### 1. 响应速度
- **实时转发**: 开机信息立即转发，无延迟
- **内存操作**: 所有开机设备信息存储在内存中
- **缓存优化**: 房间信息缓存减少数据库查询

### 2. 数据库负载
- **最小查询**: 只查询房间信息，不查询设备列表
- **缓存机制**: 30秒缓存有效期
- **按需获取**: 只在需要时查询

### 3. 网络效率
- **精确数据**: 只传输开机设备信息
- **权限过滤**: 服务器端过滤，减少网络传输
- **WebSocket**: 实时双向通信

## 日志信息

### 开机信息接收
```
📱 收到开机信息: gamev-b246c42d | CPU_ID=12345678... | 游戏=com.example.game
📱 实时转发开机信息到监控页面: gamev-b246c42d
```

### 缓存使用
```
📋 使用缓存的设备房间信息
📋 刷新设备房间信息缓存
```

### 设备数据构建
```
📤 构建开机设备数据: CPU_ID=12345678..., 房间=水浒传_DF14, 在线=true
```

## 版本信息

- **当前版本**: 1.1.1
- **更新内容**: 
  - 纯开机上报监控，移除数据库设备查询
  - 实时转发开机信息
  - 优化缓存机制
  - 简化数据流程
- **更新时间**: 2025-08-28

## 使用场景

### 1. 设备开机监控
- 实时监控设备开机状态
- 查看开机设备的详细信息
- 按房间和服务器分组显示

### 2. 权限管理
- 不同用户查看不同服务器的设备
- 管理员查看所有设备
- 实时权限过滤

### 3. 故障排除
- 快速识别开机设备
- 查看设备硬件信息
- 监控游戏启动状态

## 注意事项

1. **只显示开机设备**: 不会显示其他在线但未重新开机的设备
2. **内存存储**: 服务器重启后开机设备列表会清空
3. **实时性**: 开机信息实时转发，延迟极低
4. **权限控制**: 确保设置正确的监控密码

现在监控页面完全基于实时开机上报，数据流程简洁高效！
