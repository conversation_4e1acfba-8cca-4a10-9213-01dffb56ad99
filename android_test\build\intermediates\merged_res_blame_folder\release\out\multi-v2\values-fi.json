{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-fi\\values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7335", "endColumns": "100", "endOffsets": "7431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,381,481,590,676,781,899,985,1064,1155,1248,1343,1437,1531,1624,1720,1819,1910,2004,2084,2191,2292,2389,2495,2595,2693,2843,7254", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "376,476,585,671,776,894,980,1059,1150,1243,1338,1432,1526,1619,1715,1814,1905,1999,2079,2186,2287,2384,2490,2590,2688,2838,2938,7330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,306,402,510,594,659,752,827,892,980,1046,1104,1175,1241,1295,1405,1465,1529,1583,1656,1772,1856,1937,2040,2125,2210,2300,2367,2433,2510,2592,2676,2750,2829,2906,2978,3067,3140,3231,3326,3400,3473,3567,3621,3693,3779,3865,3927,3991,4054,4155,4257,4352,4455", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,95,107,83,64,92,74,64,87,65,57,70,65,53,109,59,63,53,72,115,83,80,102,84,84,89,66,65,76,81,83,73,78,76,71,88,72,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,78", "endOffsets": "218,301,397,505,589,654,747,822,887,975,1041,1099,1170,1236,1290,1400,1460,1524,1578,1651,1767,1851,1932,2035,2120,2205,2295,2362,2428,2505,2587,2671,2745,2824,2901,2973,3062,3135,3226,3321,3395,3468,3562,3616,3688,3774,3860,3922,3986,4049,4150,4252,4347,4450,4529"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2943,3026,3122,3230,3314,3379,3472,3547,3612,3700,3766,3824,3895,3961,4015,4125,4185,4249,4303,4376,4492,4576,4657,4760,4845,4930,5020,5087,5153,5230,5312,5396,5470,5549,5626,5698,5787,5860,5951,6046,6120,6193,6287,6341,6413,6499,6585,6647,6711,6774,6875,6977,7072,7175", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,82,95,107,83,64,92,74,64,87,65,57,70,65,53,109,59,63,53,72,115,83,80,102,84,84,89,66,65,76,81,83,73,78,76,71,88,72,90,94,73,72,93,53,71,85,85,61,63,62,100,101,94,102,78", "endOffsets": "268,3021,3117,3225,3309,3374,3467,3542,3607,3695,3761,3819,3890,3956,4010,4120,4180,4244,4298,4371,4487,4571,4652,4755,4840,4925,5015,5082,5148,5225,5307,5391,5465,5544,5621,5693,5782,5855,5946,6041,6115,6188,6282,6336,6408,6494,6580,6642,6706,6769,6870,6972,7067,7170,7249"}}]}]}