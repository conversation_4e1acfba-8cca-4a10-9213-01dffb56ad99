{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ldltr-v21\\values-ldltr-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}]}