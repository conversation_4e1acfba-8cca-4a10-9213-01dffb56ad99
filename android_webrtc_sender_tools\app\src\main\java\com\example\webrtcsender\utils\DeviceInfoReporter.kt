package com.example.webrtcsender.utils

import android.content.Context
import android.util.Log
import com.example.webrtcsender.signaling.SignalingClient
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * 设备信息上报器
 * 负责收集设备信息并发送给信令服务器
 */
class DeviceInfoReporter(
    private val context: Context,
    private val signalingClient: SignalingClient
) {
    
    companion object {
        private const val TAG = "DeviceInfoReporter"
        private const val REPORT_INTERVAL = 60 * 60 * 1000L // 60分钟上报一次
        private const val PREF_NAME = "device_info_reporter"
        private const val KEY_NEED_REPORT_BOOT = "need_report_boot" // 简单标记：是否需要上报开机
    }
    
    private val deviceInfoCollector = DeviceInfoCollector(context)
    private var reportJob: Job? = null
    private var isReporting = false

    init {
        // 应用启动时设置需要上报开机的标记
        setNeedReportBoot()
    }

    /**
     * 设置需要上报开机的标记（应用启动时调用）
     */
    private fun setNeedReportBoot() {
        try {
            val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_NEED_REPORT_BOOT, true).apply()
            Log.d(TAG, "🏁 应用启动，设置开机上报标记")
        } catch (e: Exception) {
            Log.e(TAG, "设置开机上报标记失败", e)
        }
    }
    
    /**
     * 立即上报设备信息
     */
    suspend fun reportDeviceInfo(senderId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.i(TAG, "开始收集设备信息: $senderId")
                
                // 收集设备信息
                val deviceInfo = deviceInfoCollector.collectDeviceInfo()
                
                // 构建上报消息
                val messageMap = mapOf(
                    "type" to "device_info",
                    "sender_id" to senderId,
                    "device_info" to deviceInfo.toString(),
                    "timestamp" to (System.currentTimeMillis() / 1000)
                )

                Log.i(TAG, "发送设备信息到信令服务器: $senderId")
                Log.d(TAG, "设备信息内容: $messageMap")

                // 发送到信令服务器
                signalingClient.sendMessage(messageMap)
                
                Log.i(TAG, "设备信息上报成功: $senderId")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "设备信息上报失败: $senderId", e)
                false
            }
        }
    }
    
    /**
     * 开始定期上报设备信息
     */
    fun startPeriodicReporting(senderId: String) {
        if (isReporting) {
            Log.w(TAG, "定期上报已在运行中")
            return
        }

        Log.i(TAG, "开始定期上报设备信息，间隔: ${REPORT_INTERVAL / 1000}秒")

        isReporting = true
        reportJob = CoroutineScope(Dispatchers.IO).launch {
            // 首次延迟，避免与连接时的立即上报重复
            delay(REPORT_INTERVAL)

            while (isReporting && isActive) {
                try {
                    // 检查信令客户端是否连接
                    if (signalingClient.isConnected()) {
                        reportDeviceInfo(senderId)
                    } else {
                        Log.w(TAG, "信令客户端未连接，跳过本次上报")
                    }

                    // 等待下次上报
                    delay(REPORT_INTERVAL)

                } catch (e: Exception) {
                    Log.e(TAG, "定期上报过程中发生错误", e)
                    // 发生错误时等待较短时间后重试
                    delay(30000) // 30秒后重试
                }
            }
        }
    }
    
    /**
     * 停止定期上报
     */
    fun stopPeriodicReporting() {
        Log.i(TAG, "停止定期上报设备信息")
        isReporting = false
        reportJob?.cancel()
        reportJob = null
    }
    
    /**
     * 在连接成功时上报设备信息
     */
    suspend fun onConnectionEstablished(senderId: String) {
        Log.i(TAG, "连接建立，立即上报设备信息: $senderId")

        // 延迟一小段时间确保连接稳定
        delay(1000)

        // 检查是否需要上报开机信息
        checkAndReportBootIfNeeded(senderId)

        // 立即上报一次设备信息
        val success = reportDeviceInfo(senderId)

        if (success) {
            Log.i(TAG, "连接建立后设备信息上报成功")
        } else {
            Log.e(TAG, "连接建立后设备信息上报失败")
        }
    }
    
    /**
     * 在重连成功时上报设备信息
     */
    suspend fun onReconnectionEstablished(senderId: String) {
        Log.i(TAG, "重连成功，立即上报设备信息: $senderId")
        
        // 延迟一小段时间确保重连稳定
        delay(2000)
        
        // 立即上报一次设备信息
        val success = reportDeviceInfo(senderId)
        
        if (success) {
            Log.i(TAG, "重连后设备信息上报成功")
        } else {
            Log.e(TAG, "重连后设备信息上报失败")
        }
    }
    
    /**
     * 获取设备信息摘要（用于日志）
     */
    suspend fun getDeviceInfoSummary(): String {
        return try {
            val deviceInfo = deviceInfoCollector.collectDeviceInfo()
            val summary = StringBuilder()
            
            summary.append("设备信息摘要:\n")
            summary.append("- 主板: ${deviceInfo.optString("motherboard_model", "Unknown")}\n")
            summary.append("- 系统: ${deviceInfo.optString("android_version", "Unknown")}\n")
            summary.append("- 分辨率: ${deviceInfo.optString("screen_resolution", "Unknown")}\n")
            summary.append("- 网络: ${deviceInfo.optString("network_type", "Unknown")}\n")
            summary.append("- 本地IP: ${deviceInfo.optString("local_ip", "Unknown")}\n")
            
            val appSettings = deviceInfo.optJSONObject("app_settings")
            if (appSettings != null) {
                summary.append("- 视频设置: ${appSettings.optString("video_resolution", "Unknown")} @ ${appSettings.optInt("video_fps", 0)}fps\n")
                summary.append("- 视频码率: ${appSettings.optInt("video_bitrate", 0)}kbps\n")
            }
            
            summary.toString()
        } catch (e: Exception) {
            Log.e(TAG, "获取设备信息摘要失败", e)
            "设备信息摘要获取失败: ${e.message}"
        }
    }
    
    /**
     * 检查是否需要上报开机信息（简单标记方式）
     */
    private suspend fun checkAndReportBootIfNeeded(senderId: String) {
        try {
            val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            val needReportBoot = prefs.getBoolean(KEY_NEED_REPORT_BOOT, false)

            Log.d(TAG, "🔍 检查开机上报标记: $needReportBoot")

            if (needReportBoot) {
                Log.i(TAG, "🚀 需要上报开机信息")

                val success = reportBootInfo(senderId)

                if (success) {
                    // 上报成功后，清除标记
                    prefs.edit().putBoolean(KEY_NEED_REPORT_BOOT, false).apply()
                    Log.i(TAG, "✅ 开机信息上报成功，已清除标记")
                } else {
                    Log.w(TAG, "❌ 开机信息上报失败，保留标记")
                }
            } else {
                Log.d(TAG, "📱 无需上报开机信息")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 开机检查异常", e)
        }
    }

    /**
     * 获取系统启动时间
     */
    private fun getSystemBootTime(): Long {
        return try {
            // 获取系统启动时间戳
            System.currentTimeMillis() - android.os.SystemClock.elapsedRealtime()
        } catch (e: Exception) {
            Log.e(TAG, "获取系统启动时间失败", e)
            0L
        }
    }

    /**
     * 上报开机信息
     */
    private suspend fun reportBootInfo(senderId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.i(TAG, "📤 开始上报开机信息: $senderId")

                // 收集开机信息
                val bootInfo = collectBootInfo(senderId)

                // 构建开机上报消息
                val messageMap = mapOf(
                    "type" to "boot_report",
                    "sender_id" to senderId,
                    "boot_info" to bootInfo.toString(),
                    "timestamp" to (System.currentTimeMillis() / 1000)
                )

                Log.i(TAG, "📤 发送开机信息到信令服务器: $senderId")
                Log.d(TAG, "开机信息内容: CPU_ID=${bootInfo.optString("cpu_unique_id", "").take(8)}..., 游戏=${bootInfo.optString("game_package", "None")}")

                // 发送到信令服务器
                signalingClient.sendMessage(messageMap)

                Log.i(TAG, "✅ 开机信息上报成功: $senderId")
                true

            } catch (e: Exception) {
                Log.e(TAG, "❌ 开机信息上报失败: $senderId", e)
                false
            }
        }
    }

    /**
     * 收集开机信息
     */
    private suspend fun collectBootInfo(senderId: String): JSONObject {
        val bootInfo = JSONObject()

        try {
            // 基本信息
            bootInfo.put("type", "boot_report")
            bootInfo.put("timestamp", System.currentTimeMillis())
            bootInfo.put("boot_time", getSystemBootTime())
            bootInfo.put("sender_id", senderId)

            // 收集设备信息
            val deviceInfo = deviceInfoCollector.collectDeviceInfo()

            // 提取关键信息
            bootInfo.put("cpu_unique_id", deviceInfo.optString("cpu_unique_id", ""))
            bootInfo.put("device_brand", android.os.Build.BRAND)
            bootInfo.put("device_model", android.os.Build.MODEL)
            bootInfo.put("device_name", android.os.Build.DEVICE)
            bootInfo.put("android_version", android.os.Build.VERSION.RELEASE)
            bootInfo.put("sdk_version", android.os.Build.VERSION.SDK_INT)

            // 应用信息
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            bootInfo.put("app_version", packageInfo.versionName)
            bootInfo.put("app_version_code", packageInfo.versionCode)

            // 网络信息
            bootInfo.put("local_ip", deviceInfo.optString("local_ip", "unknown"))
            bootInfo.put("public_ip", deviceInfo.optString("public_ip", "unknown"))

            // 游戏信息
            val gamePackage = getConfiguredGamePackage()
            bootInfo.put("auto_start_game_package", gamePackage)
            bootInfo.put("has_game_configured", gamePackage.isNotEmpty())

            Log.d(TAG, "📋 开机信息收集完成: CPU_ID=${bootInfo.optString("cpu_unique_id", "").take(8)}..., 游戏=${gamePackage}")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 收集开机信息失败", e)
            bootInfo.put("error", "收集信息失败: ${e.message}")
        }

        return bootInfo
    }

    /**
     * 获取配置的游戏包名
     */
    private fun getConfiguredGamePackage(): String {
        return try {
            // 使用正确的SharedPreferences名称和键名
            val prefs = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
            val gamePackage = prefs.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""

            Log.d(TAG, "获取游戏包名: $gamePackage (从${Constants.PREF_NAME}/${Constants.PREF_AUTO_START_GAME_PACKAGE})")
            gamePackage
        } catch (e: Exception) {
            Log.e(TAG, "获取游戏包名失败", e)
            ""
        }
    }

    /**
     * 检查是否正在上报
     */
    fun isReporting(): Boolean = isReporting
}
