#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试命令发送
"""

import requests
import json
import time

def test_direct_command():
    """直接发送命令到服务器"""
    print("🧪 直接测试命令发送")
    print("=" * 50)
    
    # 服务器地址
    server_url = "http://localhost:8765"
    
    # 目标设备ID
    device_id = "gamev-81216f2a"  # 从日志中获取的设备ID
    
    # 命令数据
    command_data = {
        "command": "reboot_device",
        "params": {}
    }
    
    print(f"目标设备: {device_id}")
    print(f"命令数据: {command_data}")
    
    # 发送命令
    url = f"{server_url}/api/commands/{device_id}"
    
    try:
        print(f"发送请求到: {url}")
        response = requests.post(url, json=command_data, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 命令发送成功: {result}")
        else:
            print(f"❌ 命令发送失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_direct_command()
