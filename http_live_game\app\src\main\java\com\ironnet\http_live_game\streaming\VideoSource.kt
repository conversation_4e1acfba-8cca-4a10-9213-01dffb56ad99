package com.ironnet.http_live_game.streaming

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.ImageFormat
import android.graphics.Rect
import android.graphics.SurfaceTexture
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.view.Surface
import android.graphics.PixelFormat
import androidx.camera.core.ImageProxy
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.nio.ByteBuffer
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

class VideoSource(
    private val context: Context,
    private val config: StreamConfig,
    private val mediaProjection: MediaProjection?,
    private val httpServer: HttpServer?
) {
    private val TAG = "VideoSource"

    private val sourceJob = SupervisorJob()
    private val sourceScope = CoroutineScope(Dispatchers.IO + sourceJob)

    // 编码器相关
    private var encoder: MediaCodec? = null
    private var surface: Surface? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var cameraProvider: ProcessCameraProvider? = null
    private var surfaceTexture: SurfaceTexture? = null

    // MJPEG相关
    private var imageReader: ImageReader? = null
    private var imageReaderSurface: Surface? = null
    private var imageReaderThread: HandlerThread? = null
    private var imageReaderHandler: Handler? = null

    // 状态标志
    private var isRunning = false
    private var isHeaderSent = false
    private var mjpegEnabled = true

    fun start() {
        if (isRunning) {
            Log.d(TAG, "VideoSource already running, ignoring start request")
            return
        }

        Log.d(TAG, "Starting VideoSource with config: useCamera=${config.useCamera}, resolution=${config.width}x${config.height}, format=${config.videoFormat}")

        try {
            // 设置MJPEG图像读取器
            if (mjpegEnabled) {
                Log.d(TAG, "Setting up MJPEG image reader")
                setupImageReader()
            }

            // 设置视频编码器
            Log.d(TAG, "Setting up video encoder")
            setupEncoder()

            // 设置视频源
            if (config.useCamera) {
                Log.d(TAG, "Setting up camera source")
                setupCamera()
            } else {
                if (mediaProjection == null) {
                    Log.e(TAG, "MediaProjection is null, cannot start screen capture")
                    throw IllegalStateException("MediaProjection is required for screen capture")
                }
                Log.d(TAG, "Setting up screen capture")
                setupScreenCapture()
            }

            // 启动编码线程
            Log.d(TAG, "Starting encoder thread")
            startEncoderThread()
            isRunning = true
            Log.d(TAG, "VideoSource started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting video source: ${e.message}", e)
            stop()
        }
    }

    fun stop() {
        Log.d(TAG, "Stopping VideoSource")

        if (!isRunning) {
            Log.d(TAG, "VideoSource already stopped")
            return
        }

        isRunning = false
        isHeaderSent = false

        try {
            // 释放屏幕捕获资源
            Log.d(TAG, "Releasing virtual display")
            virtualDisplay?.release()
            virtualDisplay = null

            // 释放相机资源
            Log.d(TAG, "Unbinding camera")
            cameraProvider?.unbindAll()
            cameraProvider = null

            // 释放纹理
            Log.d(TAG, "Releasing surface texture")
            surfaceTexture?.release()
            surfaceTexture = null

            // 释放编码器表面
            Log.d(TAG, "Releasing encoder surface")
            surface?.release()
            surface = null

            // 释放编码器
            Log.d(TAG, "Stopping and releasing encoder")
            encoder?.stop()
            encoder?.release()
            encoder = null

            // 释放MJPEG资源
            Log.d(TAG, "Releasing MJPEG resources")
            imageReaderSurface?.release()
            imageReaderSurface = null

            imageReader?.close()
            imageReader = null

            imageReaderThread?.quitSafely()
            imageReaderThread = null
            imageReaderHandler = null

            // 取消协程
            Log.d(TAG, "Cancelling source job")
            sourceJob.cancel()

            Log.d(TAG, "VideoSource stopped successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping video source: ${e.message}", e)
        }
    }

    private fun setupEncoder() {
        val format = MediaFormat.createVideoFormat(config.videoFormat, config.width, config.height)

        // Configure video encoder
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
        format.setInteger(MediaFormat.KEY_BIT_RATE, config.bitRate)
        format.setInteger(MediaFormat.KEY_FRAME_RATE, config.frameRate)
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, config.iFrameInterval)

        // Create encoder
        encoder = MediaCodec.createEncoderByType(config.videoFormat)
        encoder?.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)

        // Get input surface
        surface = encoder?.createInputSurface()

        // Start encoder
        encoder?.start()
    }

    private fun setupImageReader() {
        // 创建图像读取线程
        imageReaderThread = HandlerThread("ImageReaderThread").apply { start() }
        imageReaderHandler = Handler(imageReaderThread!!.looper)

        // 创建图像读取器 - 使用RGBA_8888格式
        imageReader = ImageReader.newInstance(
            config.width,
            config.height,
            PixelFormat.RGBA_8888, // 使用RGBA格式，这是屏幕捕获的默认格式
            2
        ).apply {
            setOnImageAvailableListener({ reader ->
                try {
                    val image = reader.acquireLatestImage()
                    if (image != null) {
                        try {
                            // 直接从RGBA图像创建Bitmap
                            val bitmap = rgbaToBitmap(image)
                            if (bitmap != null) {
                                // 发送位图副本，原始位图由sendMjpegFrame内部处理
                                httpServer?.sendMjpegFrame(bitmap)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing image in ImageReader: ${e.message}", e)
                        }
                        image.close()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing image: ${e.message}", e)
                }
            }, imageReaderHandler)
        }

        // 获取Surface
        imageReaderSurface = imageReader?.surface
    }

    private fun setupCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()

                // 设置相机选择器
                val cameraSelector = when (config.cameraId) {
                    "1" -> CameraSelector.DEFAULT_FRONT_CAMERA
                    else -> CameraSelector.DEFAULT_BACK_CAMERA
                }

                // 创建预览纹理
                surfaceTexture = SurfaceTexture(0).apply {
                    setDefaultBufferSize(config.width, config.height)
                }

                val previewSurface = Surface(surfaceTexture)

                // 创建预览用例
                val preview = Preview.Builder()
                    .setTargetResolution(android.util.Size(config.width, config.height))
                    .build()
                    .also {
                        it.setSurfaceProvider { _ ->
                            // 将相机预览重定向到编码器表面
                            surface?.let { encoderSurface ->
                                previewSurface
                            }
                        }
                    }

                // 创建图像分析用例（用于MJPEG）
                val imageAnalysis = if (mjpegEnabled && imageReaderSurface != null) {
                    ImageAnalysis.Builder()
                        .setTargetResolution(android.util.Size(config.width, config.height))
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()
                        .also {
                            it.setAnalyzer(Executors.newSingleThreadExecutor()) { image ->
                                try {
                                    try {
                                        // 将图像转换为Bitmap并发送
                                        val bitmap = imageProxyToBitmap(image)
                                        if (bitmap != null) {
                                            // 发送位图副本，原始位图由sendMjpegFrame内部处理
                                            httpServer?.sendMjpegFrame(bitmap)
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "Error processing image in ImageAnalysis: ${e.message}", e)
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error processing camera image: ${e.message}", e)
                                } finally {
                                    image.close()
                                }
                            }
                        }
                } else null

                // 解绑之前的用例
                cameraProvider?.unbindAll()

                // 将相机绑定到生命周期
                if (context is LifecycleOwner) {
                    if (imageAnalysis != null) {
                        cameraProvider?.bindToLifecycle(
                            context,
                            cameraSelector,
                            preview,
                            imageAnalysis
                        )
                    } else {
                        cameraProvider?.bindToLifecycle(
                            context,
                            cameraSelector,
                            preview
                        )
                    }
                } else {
                    Log.e(TAG, "Context is not a LifecycleOwner, cannot bind camera")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up camera: ${e.message}", e)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    private fun setupScreenCapture() {
        val density = context.resources.displayMetrics.densityDpi

        // 创建用于编码的虚拟显示
        virtualDisplay = mediaProjection?.createVirtualDisplay(
            "ScreenCapture",
            config.width,
            config.height,
            density,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            surface,
            null,
            null
        )

        // 如果启用了MJPEG，创建另一个虚拟显示用于MJPEG流
        if (mjpegEnabled && imageReaderSurface != null) {
            mediaProjection?.createVirtualDisplay(
                "MjpegCapture",
                config.width,
                config.height,
                density,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReaderSurface,
                null,
                null
            )
        }
    }

    // 将RGBA_8888格式的Image转换为Bitmap
    private fun rgbaToBitmap(image: Image): Bitmap? {
        try {
            val width = image.width
            val height = image.height

            // 获取RGBA平面
            val plane = image.planes[0]
            val buffer = plane.buffer
            val pixelStride = plane.pixelStride
            val rowStride = plane.rowStride
            val rowPadding = rowStride - pixelStride * width

            // 创建Bitmap
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)

            // 创建临时Bitmap来处理可能的行填充
            val tempBitmap = Bitmap.createBitmap(
                width + rowPadding / pixelStride,
                height,
                Bitmap.Config.ARGB_8888
            )

            // 从缓冲区复制像素
            tempBitmap.copyPixelsFromBuffer(buffer)

            // 如果有行填充，裁剪到正确的尺寸
            if (rowPadding > 0) {
                // 创建裁剪后的Bitmap
                val canvas = Canvas(bitmap)
                canvas.drawBitmap(
                    tempBitmap,
                    Rect(0, 0, width, height),
                    Rect(0, 0, width, height),
                    null
                )
                tempBitmap.recycle()
                return bitmap
            } else {
                // 如果没有行填充，直接返回
                return tempBitmap
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error converting RGBA to bitmap: ${e.message}", e)
        }
        return null
    }

    // 将ImageProxy转换为Bitmap
    private fun imageProxyToBitmap(imageProxy: ImageProxy): Bitmap? {
        val image = imageProxy.image ?: return null
        return rgbaToBitmap(image)
    }

    private fun startEncoderThread() {
        sourceScope.launch {
            val bufferInfo = MediaCodec.BufferInfo()

            try {
                while (isRunning) {
                    val encoderStatus = encoder?.dequeueOutputBuffer(bufferInfo, 10000)

                    when (encoderStatus) {
                        MediaCodec.INFO_TRY_AGAIN_LATER -> {
                            // No output available yet
                        }
                        MediaCodec.INFO_OUTPUT_FORMAT_CHANGED -> {
                            // 格式改变，获取CSD（编解码器特定数据）并发送
                            val outputFormat = encoder?.outputFormat
                            Log.d(TAG, "Encoder output format changed: $outputFormat")

                            // 从输出格式中获取CSD块（SPS和PPS）
                            try {
                                val csd0 = outputFormat?.getByteBuffer("csd-0")
                                val csd1 = outputFormat?.getByteBuffer("csd-1")

                                if (csd0 != null && csd1 != null) {
                                    // 组合CSD数据
                                    val csd0Size = csd0.remaining()
                                    val csd1Size = csd1.remaining()
                                    val headerData = ByteArray(csd0Size + csd1Size)

                                    csd0.get(headerData, 0, csd0Size)
                                    csd1.get(headerData, csd0Size, csd1Size)

                                    // 发送组合的CSD数据作为头信息
                                    Log.d(TAG, "Sending CSD data as header (${headerData.size} bytes)")
                                    httpServer?.sendVideoData(headerData, isHeader = true, videoFormat = config.videoFormat)
                                    isHeaderSent = true
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error extracting CSD data: ${e.message}", e)
                            }
                        }
                        MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED -> {
                            // Ignore, we're using getOutputBuffer
                        }
                        else -> {
                            // We have a valid buffer index
                            encoderStatus?.let { index ->
                                if (index >= 0) {
                                    val encodedData = encoder?.getOutputBuffer(index)

                                    encodedData?.let { buffer ->
                                        val data = ByteArray(bufferInfo.size)
                                        buffer.position(bufferInfo.offset)
                                        buffer.limit(bufferInfo.offset + bufferInfo.size)
                                        buffer.get(data)

                                        // Check if this is a config frame (SPS, PPS)
                                        val isConfigFrame = bufferInfo.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG != 0

                                        // Check if this is a key frame
                                        val isKeyFrame = bufferInfo.flags and MediaCodec.BUFFER_FLAG_KEY_FRAME != 0

                                        // Send data to HTTP server
                                        if (isConfigFrame || isKeyFrame) {
                                            httpServer?.sendVideoData(data, isHeader = true, videoFormat = config.videoFormat)
                                            isHeaderSent = true
                                        } else if (isHeaderSent) {
                                            httpServer?.sendVideoData(data, videoFormat = config.videoFormat)
                                        }
                                    }

                                    encoder?.releaseOutputBuffer(index, false)

                                    // Check if we're at the end of the stream
                                    if (bufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
                                        Log.d(TAG, "End of stream reached")
                                        isRunning = false // Use flag instead of break to exit the loop
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in encoder thread: ${e.message}", e)
            }
        }
    }
}
