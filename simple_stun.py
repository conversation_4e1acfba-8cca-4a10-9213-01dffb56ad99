#!/usr/bin/env python3
# simple_stun.py - 使用pystun3库的简单STUN客户端
import socket
import sys

def get_ip_info():
    """获取本地IP和公网IP信息"""
    # 获取本地IP
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # 不需要真正连接
        s.connect(('**************', 1))
        local_ip = s.getsockname()[0]
    except Exception:
        local_ip = '127.0.0.1'
    finally:
        s.close()
    
    print(f"本地IP地址: {local_ip}")
    
    # 尝试使用socket直接连接STUN服务器
    stun_host = 'stun.l.google.com'
    stun_port = 19302
    
    print(f"\n尝试连接STUN服务器 {stun_host}:{stun_port}...")
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.settimeout(5)
    
    try:
        sock.connect((stun_host, stun_port))
        print(f"成功连接到STUN服务器")
        print(f"本地端口: {sock.getsockname()[1]}")
        
        # 发送一些数据
        print("发送测试数据...")
        sock.send(b"STUN test")
        
        # 尝试接收响应
        try:
            data = sock.recv(1024)
            print(f"收到响应: {len(data)} 字节")
        except socket.timeout:
            print("接收响应超时")
    except Exception as e:
        print(f"连接STUN服务器失败: {e}")
    finally:
        sock.close()
    
    # 尝试使用HTTP服务获取公网IP
    print("\n尝试通过HTTP服务获取公网IP...")
    try:
        import urllib.request
        response = urllib.request.urlopen('https://api.ipify.org')
        public_ip = response.read().decode('utf-8')
        print(f"公网IP地址: {public_ip}")
        
        # 检查是否是CGNAT地址
        if public_ip.startswith('100.64.'):
            print("\n注意: 您的公网IP属于CGNAT地址范围 (**********/10)")
            print("这表明您的ISP使用了运营商级NAT，您没有真正的公网IP")
            print("在这种情况下，外部互联网无法直接访问您的服务")
    except Exception as e:
        print(f"获取公网IP失败: {e}")

if __name__ == "__main__":
    print("STUN客户端 - 网络信息检测工具\n")
    get_ip_info()
