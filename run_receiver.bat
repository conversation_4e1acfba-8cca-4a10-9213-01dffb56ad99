@echo off
echo ===================================
echo    WebRTC Receiver
echo ===================================
echo.

set /p SENDER_ID="Enter sender ID (e.g. sender-test-16760): "

echo.
echo Starting receiver...
echo.
echo Connection info:
echo - Signaling server: wss://sling.91jdcd.com/ws/
echo - Target sender: %SENDER_ID%
echo.
echo Press Ctrl+C to stop
echo.

python simple_receiver.py --sender "%SENDER_ID%" --signaling wss://sling.91jdcd.com/ws/

pause
