# 简化开机上报逻辑

## 设计思路

按照用户建议，采用最简单的标记方式：
1. **应用启动时做标记** - 设置 `need_report_boot = true`
2. **发送开机后抹掉标记** - 设置 `need_report_boot = false`  
3. **下次不再发送** - 检查标记为 `false`，跳过上报

## 实现方案

### 1. 简化的常量定义
```kotlin
companion object {
    private const val KEY_NEED_REPORT_BOOT = "need_report_boot" // 简单标记：是否需要上报开机
}
```

移除了复杂的时间比较相关常量：
- ~~`KEY_BOOT_TIME`~~
- ~~`KEY_BOOT_REPORTED`~~  
- ~~`KEY_APP_START_TIME`~~
- ~~`BOOT_TIME_THRESHOLD`~~

### 2. 应用启动时设置标记
```kotlin
init {
    // 应用启动时设置需要上报开机的标记
    setNeedReportBoot()
}

private fun setNeedReportBoot() {
    val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    prefs.edit().putBoolean(KEY_NEED_REPORT_BOOT, true).apply()
    Log.d(TAG, "🏁 应用启动，设置开机上报标记")
}
```

### 3. 简化的开机检测逻辑
```kotlin
private suspend fun checkAndReportBootIfNeeded(senderId: String) {
    val prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    val needReportBoot = prefs.getBoolean(KEY_NEED_REPORT_BOOT, false)

    if (needReportBoot) {
        Log.i(TAG, "🚀 需要上报开机信息")
        
        val success = reportBootInfo(senderId)
        
        if (success) {
            // 上报成功后，清除标记
            prefs.edit().putBoolean(KEY_NEED_REPORT_BOOT, false).apply()
            Log.i(TAG, "✅ 开机信息上报成功，已清除标记")
        } else {
            Log.w(TAG, "❌ 开机信息上报失败，保留标记")
        }
    } else {
        Log.d(TAG, "📱 无需上报开机信息")
    }
}
```

### 4. 移除复杂逻辑
删除了以下复杂的检测逻辑：
- ~~时间差异计算~~
- ~~启动时间比较~~
- ~~应用启动时间跟踪~~
- ~~多重条件验证~~

## 工作流程

### 1. 应用启动
```
应用启动 → init() → setNeedReportBoot() → need_report_boot = true
```

### 2. 连接建立
```
连接建立 → checkAndReportBootIfNeeded() → 检查标记
```

### 3. 开机上报
```
标记为true → 上报开机信息 → 上报成功 → need_report_boot = false
```

### 4. 后续连接
```
重连/重启 → checkAndReportBootIfNeeded() → 标记为false → 跳过上报
```

## 日志输出

### 应用启动
```
🏁 应用启动，设置开机上报标记
```

### 检查标记
```
🔍 检查开机上报标记: true
🚀 需要上报开机信息
✅ 开机信息上报成功，已清除标记
```

### 后续检查
```
🔍 检查开机上报标记: false
📱 无需上报开机信息
```

## 优势

### 1. 极简设计
- **只有一个布尔标记** - 不需要复杂的时间计算
- **逻辑清晰** - 应用启动设置，上报后清除
- **易于理解** - 代码简洁，维护方便

### 2. 可靠性高
- **不依赖时间** - 避免时间同步问题
- **不依赖系统状态** - 只依赖简单的布尔值
- **容错性好** - 上报失败时保留标记，下次重试

### 3. 性能优化
- **无复杂计算** - 只需要读取一个布尔值
- **无数据库查询** - 只使用SharedPreferences
- **响应快速** - 检查逻辑极简

## 边界情况处理

### 1. 上报失败
- **保留标记** - 上报失败时不清除标记
- **下次重试** - 下次连接时会重新尝试上报
- **日志记录** - 记录失败原因

### 2. 数据丢失
- **默认值** - 标记默认为 `false`，不会误报
- **重新设置** - 应用重新安装时会重新设置标记

### 3. 多次连接
- **首次上报** - 只有第一次连接时上报
- **后续跳过** - 标记清除后不再上报
- **重启重置** - 应用重启时重新设置标记

## 版本信息

### Android应用
- **版本**: 1.0.3 → 1.0.4
- **版本代码**: 103 → 104
- **更新内容**: 简化开机上报逻辑为标记方式

### 主要改进
1. **移除复杂时间比较逻辑**
2. **采用简单布尔标记方式**
3. **应用启动时设置标记**
4. **上报成功后清除标记**
5. **大幅简化代码复杂度**

## 测试验证

### 1. 正常流程
- 应用启动 → 设置标记 → 连接成功 → 上报开机 → 清除标记
- 应用重连 → 检查标记 → 标记为false → 跳过上报

### 2. 异常流程
- 上报失败 → 保留标记 → 下次重试
- 应用重启 → 重新设置标记 → 重新上报

### 3. 预期效果
- **减少虚假上报**: 每次应用启动只上报一次
- **提高准确性**: 避免重连时的误报
- **简化维护**: 代码逻辑清晰易懂

现在开机上报逻辑变得非常简单：应用启动设标记，上报后清标记，就这么简单！
