# 摄像头策略错误自动恢复机制

## 🎯 问题描述

从日志可以看到摄像头126被系统策略禁用的问题：
```
Camera "126" disabled by policy
CAMERA_DISABLED (1): connectHelper:2306: Camera "126" disabled by policy
```

这种错误通常是间歇性的，由系统动态控制，会导致推流中断。

## 🔧 自动恢复机制

### 1. 错误检测
```kotlin
override fun onCameraError(errorDescription: String?) {
    Logger.e(TAG, "🎥 [摄像头] 摄像头错误: $errorDescription")
    
    // 检查是否是策略错误
    if (errorDescription?.contains("disabled by policy") == true || 
        errorDescription?.contains("CAMERA_DISABLED") == true) {
        Logger.w(TAG, "🎥 [摄像头] 检测到摄像头策略错误，启动自动恢复")
        handleCameraPolicyError(cameraId)
    }
}
```

### 2. 智能重试机制
```kotlin
private fun handleCameraPolicyError(cameraId: String) {
    // 检查重试次数限制
    if (cameraPolicyErrorRetryCount >= Constants.CAMERA_POLICY_ERROR_MAX_RETRIES) {
        Logger.e(TAG, "🎥 [策略错误] 已达到最大重试次数，停止自动恢复")
        return
    }
    
    // 指数退避算法
    val retryDelay = (Constants.CAMERA_POLICY_ERROR_RETRY_DELAY * 
                     Math.pow(Constants.CAMERA_POLICY_ERROR_BACKOFF_MULTIPLIER.toDouble(), 
                             (cameraPolicyErrorRetryCount - 1).toDouble())).toLong()
}
```

### 3. 安全恢复流程
```kotlin
// 延迟重试处理
cameraPolicyErrorHandler?.postDelayed({
    try {
        Logger.i(TAG, "🎥 [策略错误] 尝试恢复摄像头$cameraId")
        
        // 1. 停止当前视频源
        stopVideoSource()
        
        // 2. 等待系统释放资源
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                // 3. 重新启动摄像头
                createCameraVideoSource(cameraId)
                Logger.i(TAG, "🎥 [策略错误] ✅ 摄像头恢复成功")
                cameraPolicyErrorRetryCount = 0 // 重置重试计数
            } catch (e: Exception) {
                Logger.e(TAG, "🎥 [策略错误] 摄像头恢复失败: ${e.message}")
                // 继续下一次重试
                handleCameraPolicyError(cameraId)
            }
        }, 2000) // 等待2秒让系统释放资源
        
    } catch (e: Exception) {
        Logger.e(TAG, "🎥 [策略错误] 恢复过程出错: ${e.message}")
        // 继续下一次重试
        handleCameraPolicyError(cameraId)
    }
}, retryDelay)
```

## 📊 配置参数

### 1. 重试配置
```kotlin
// 在Constants.kt中添加
const val CAMERA_POLICY_ERROR_RETRY_DELAY = 30000L // 30秒后重试
const val CAMERA_POLICY_ERROR_MAX_RETRIES = 10 // 最大重试次数
const val CAMERA_POLICY_ERROR_BACKOFF_MULTIPLIER = 1.5f // 退避倍数
```

### 2. 重试时间表
| 重试次数 | 延迟时间 | 累计时间 |
|---------|----------|----------|
| 1 | 30秒 | 30秒 |
| 2 | 45秒 | 1分15秒 |
| 3 | 67秒 | 2分22秒 |
| 4 | 101秒 | 4分3秒 |
| 5 | 151秒 | 6分34秒 |
| 6 | 227秒 | 10分21秒 |
| 7 | 340秒 | 16分1秒 |
| 8 | 510秒 | 24分31秒 |
| 9 | 765秒 | 37分16秒 |
| 10 | 1147秒 | 56分23秒 |

## 🛡️ 安全措施

### 1. 资源清理
```kotlin
// 确保在重试前清理资源
stopVideoSource() // 停止当前视频源
Handler.postDelayed({ /* 重新启动 */ }, 2000) // 等待资源释放
```

### 2. 重试限制
- 最大重试次数：10次
- 总重试时间：约56分钟
- 指数退避：避免频繁重试

### 3. 内存管理
```kotlin
// 在cleanup方法中清理Handler
cameraPolicyErrorHandler?.removeCallbacksAndMessages(null)
cameraPolicyErrorHandler = null
```

## 🎯 触发条件

### 1. 系统策略限制
- 设备温度过高
- 电池电量过低
- 系统资源不足
- 安全策略变更

### 2. 硬件状态
- 摄像头硬件故障
- 驱动程序问题
- 权限变更

### 3. 应用状态
- 其他应用占用摄像头
- 系统优先级调整
- 后台限制

## 📝 日志示例

### 正常恢复流程
```
🎥 [摄像头] 摄像头错误: Failed to open camera: CAMERA_DISABLED (1): Camera "126" disabled by policy
🎥 [摄像头] 检测到摄像头策略错误，启动自动恢复
🎥 [策略错误] 摄像头126被策略禁用，启动恢复机制
🎥 [策略错误] 第1次重试，30秒后尝试恢复
🎥 [策略错误] 尝试恢复摄像头126
🎥 [策略错误] ✅ 摄像头恢复成功
```

### 重试失败流程
```
🎥 [策略错误] 第2次重试，45秒后尝试恢复
🎥 [策略错误] 摄像头恢复失败: Camera still disabled by policy
🎥 [策略错误] 第3次重试，67秒后尝试恢复
```

### 达到重试限制
```
🎥 [策略错误] 第10次重试失败
🎥 [策略错误] 已达到最大重试次数，停止自动恢复
```

## 🚀 预期效果

### 1. 自动恢复
- 检测到策略错误时自动启动恢复
- 智能重试，避免频繁尝试
- 成功恢复后继续正常推流

### 2. 用户体验
- 推流中断时间最小化
- 无需手动干预
- 自动恢复到正常状态

### 3. 系统稳定性
- 避免无限重试导致系统负载
- 合理的退避算法
- 资源安全清理

## ⚠️ 注意事项

### 1. 不是万能解决方案
- 如果摄像头硬件故障，无法恢复
- 如果系统永久禁用，重试会失败
- 需要配合其他故障排除方法

### 2. 资源消耗
- 重试过程会消耗一定系统资源
- 长时间重试可能影响电池续航
- 建议在关键场景使用

### 3. 用户通知
- 可以考虑在UI中显示恢复状态
- 提供手动停止重试的选项
- 记录详细日志供故障分析

## 🔧 使用建议

### 1. 监控日志
- 关注策略错误的发生频率
- 分析触发条件和恢复成功率
- 根据实际情况调整重试参数

### 2. 优化配置
- 根据设备性能调整重试间隔
- 考虑不同场景的重试策略
- 平衡恢复速度和系统负载

### 3. 备用方案
- 考虑切换到其他可用摄像头
- 提供屏幕录制作为备选
- 实现降级服务模式

现在摄像头策略错误会自动检测并尝试恢复，大大提高了推流的稳定性！
