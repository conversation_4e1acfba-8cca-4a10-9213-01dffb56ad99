#!/usr/bin/env python3
"""
版本更新脚本 - 自动更新服务器版本号
"""

import re
import os
from datetime import datetime

def update_version_in_file(file_path, version_pattern, new_version):
    """更新文件中的版本号"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换版本号
        updated_content = re.sub(version_pattern, new_version, content)
        
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"✅ 已更新 {file_path}")
            return True
        else:
            print(f"⚠️ 未找到版本号模式: {file_path}")
            return False
    
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def get_current_version():
    """获取当前版本号"""
    try:
        with open('enhanced_signaling_server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找版本号
        match = re.search(r'VERSION\s*=\s*["\']([^"\']+)["\']', content)
        if match:
            return match.group(1)
        
        # 如果没有找到，返回默认版本
        return "1.0.0"
    
    except Exception as e:
        print(f"获取当前版本失败: {e}")
        return "1.0.0"

def increment_version(version_str):
    """递增版本号 (增加0.0.1)"""
    try:
        parts = version_str.split('.')
        if len(parts) >= 3:
            # 递增最后一位
            parts[-1] = str(int(parts[-1]) + 1)
            return '.'.join(parts)
        else:
            return version_str + ".1"
    except:
        return version_str + ".1"

def main():
    """主函数"""
    print("🚀 开始更新服务器版本...")
    
    # 获取当前版本
    current_version = get_current_version()
    new_version = increment_version(current_version)
    
    print(f"当前版本: {current_version}")
    print(f"新版本: {new_version}")
    
    # 更新服务器端版本
    server_files = [
        {
            'file': 'enhanced_signaling_server.py',
            'pattern': r'VERSION\s*=\s*["\'][^"\']+["\']',
            'replacement': f'VERSION = "{new_version}"'
        }
    ]
    
    # 更新Android端版本 (如果存在)
    android_files = [
        {
            'file': 'android_webrtc_sender_tools/app/build.gradle',
            'pattern': r'versionName\s+"[^"]+"',
            'replacement': f'versionName "{new_version}"'
        },
        {
            'file': 'android_webrtc_sender_tools/app/build.gradle',
            'pattern': r'versionCode\s+\d+',
            'replacement': f'versionCode {int(new_version.replace(".", ""))}'
        }
    ]
    
    # 更新Web端版本
    web_files = [
        {
            'file': 'web/admin.html',
            'pattern': r'<title>[^<]*</title>',
            'replacement': f'<title>信令服务器管理控制台 v{new_version}</title>'
        }
    ]
    
    success_count = 0
    total_count = 0
    
    # 更新所有文件
    all_files = server_files + android_files + web_files
    
    for file_info in all_files:
        total_count += 1
        if update_version_in_file(
            file_info['file'], 
            file_info['pattern'], 
            file_info['replacement']
        ):
            success_count += 1
    
    # 添加版本常量到服务器文件（如果不存在）
    server_file = 'enhanced_signaling_server.py'
    if os.path.exists(server_file):
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'VERSION =' not in content:
            # 在文件开头添加版本常量
            lines = content.split('\n')
            # 找到合适的位置插入（在import之后）
            insert_pos = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    insert_pos = i + 1
            
            lines.insert(insert_pos, f'\n# 版本信息\nVERSION = "{new_version}"\n')
            
            with open(server_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            print(f"✅ 已添加版本常量到 {server_file}")
    
    print(f"\n📊 更新完成: {success_count}/{total_count} 个文件")
    print(f"🎉 服务器版本已更新到: {new_version}")
    
    # 创建版本更新日志
    log_entry = f"""
# 版本 {new_version} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 新增功能
- 服务器群房间信息自动更新
- WebSocket实时通信支持
- 按域名和游戏分类的设备显示
- 命令执行状态实时转发
- 房间信息关联到设备信息

## 改进
- 优化设备列表显示界面
- 增强WebSocket连接稳定性
- 改进错误处理和日志记录

## 修复
- 修复WebSocket连接对象类型异常问题
- 修复接收端检测逻辑
- 优化数据库连接管理

---
"""
    
    # 写入更新日志
    with open('CHANGELOG.md', 'a', encoding='utf-8') as f:
        f.write(log_entry)
    
    print("📝 版本更新日志已记录到 CHANGELOG.md")

if __name__ == "__main__":
    main()
