<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单位置修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: visible;
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 18px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            position: relative;
        }
        
        .control-dropdown {
            position: relative;
            z-index: 1000;
        }
        
        .dropdown-toggle {
            position: relative;
            z-index: 100000;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .dropdown-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .dropdown-toggle.active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .dropdown-menu {
            position: fixed;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw;
            z-index: 999999;
            display: none;
            padding: 15px;
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
            visibility: hidden;
        }
        
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            visibility: visible !important;
        }
        
        .dropdown-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .dropdown-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .dropdown-btn {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
        }
        
        .dropdown-btn:hover {
            background-color: #f8f9fa;
        }
        
        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 0.9em;
        }
        
        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .device-info-value {
            color: #495057;
            font-weight: 600;
        }
        
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .position-indicator {
            position: fixed;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000000;
            display: none;
        }
    </style>
</head>
<body>
    <div class="position-indicator" id="positionIndicator"></div>
    
    <div class="container">
        <h1>🔧 下拉菜单位置修复测试</h1>
        
        <div class="test-info">
            <h3>修复内容</h3>
            <ul>
                <li>✅ 修复位置计算逻辑，确保在按钮正下方显示</li>
                <li>✅ 提高z-index到999999，确保在按钮之上</li>
                <li>✅ 减少动画时间，避免闪烁</li>
                <li>✅ 先计算位置再显示，避免位置跳跃</li>
                <li>✅ 使用visibility控制显示，减少重排</li>
            </ul>
        </div>
        
        <h2>📊 位置测试 - 不同位置的按钮</h2>
        
        <div class="test-grid">
            <!-- 左上角 -->
            <div class="test-card">
                <h3>🎮 左上角设备</h3>
                <div class="device-info-item">
                    <span class="device-info-label">位置:</span>
                    <span class="device-info-value">左上角</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">预期:</span>
                    <span class="device-info-value">按钮下方，左对齐</span>
                </div>
                
                <div class="control-dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown('topleft')">
                        ⚙️ 功能菜单
                    </button>
                    <div class="dropdown-menu" id="dropdown-topleft">
                        <div class="dropdown-columns">
                            <div class="dropdown-section">
                                <h4>服务控制</h4>
                                <button class="dropdown-btn">🚀 启动服务</button>
                                <button class="dropdown-btn">⏹️ 停止服务</button>
                                <button class="dropdown-btn">🔄 重启服务</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>视频控制</h4>
                                <button class="dropdown-btn">🎥 视频参数设置</button>
                                <button class="dropdown-btn">📸 视频流截屏</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>网络配置</h4>
                                <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                <button class="dropdown-btn">🔄 重启设备</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 中间 -->
            <div class="test-card">
                <h3>🎮 中间设备</h3>
                <div class="device-info-item">
                    <span class="device-info-label">位置:</span>
                    <span class="device-info-value">中间</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">预期:</span>
                    <span class="device-info-value">按钮下方，居中</span>
                </div>
                
                <div class="control-dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown('center')">
                        ⚙️ 功能菜单
                    </button>
                    <div class="dropdown-menu" id="dropdown-center">
                        <div class="dropdown-columns">
                            <div class="dropdown-section">
                                <h4>服务控制</h4>
                                <button class="dropdown-btn">🚀 启动服务</button>
                                <button class="dropdown-btn">⏹️ 停止服务</button>
                                <button class="dropdown-btn">🔄 重启服务</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>视频控制</h4>
                                <button class="dropdown-btn">🎥 视频参数设置</button>
                                <button class="dropdown-btn">📸 视频流截屏</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>网络配置</h4>
                                <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                <button class="dropdown-btn">🔄 重启设备</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右上角 -->
            <div class="test-card">
                <h3>🎮 右上角设备</h3>
                <div class="device-info-item">
                    <span class="device-info-label">位置:</span>
                    <span class="device-info-value">右上角</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">预期:</span>
                    <span class="device-info-value">按钮下方，右对齐</span>
                </div>
                
                <div class="control-dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown('topright')">
                        ⚙️ 功能菜单
                    </button>
                    <div class="dropdown-menu" id="dropdown-topright">
                        <div class="dropdown-columns">
                            <div class="dropdown-section">
                                <h4>服务控制</h4>
                                <button class="dropdown-btn">🚀 启动服务</button>
                                <button class="dropdown-btn">⏹️ 停止服务</button>
                                <button class="dropdown-btn">🔄 重启服务</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>视频控制</h4>
                                <button class="dropdown-btn">🎥 视频参数设置</button>
                                <button class="dropdown-btn">📸 视频流截屏</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>网络配置</h4>
                                <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                <button class="dropdown-btn">🔄 重启设备</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部测试 -->
        <div style="margin-top: 500px;">
            <h2>📊 底部位置测试</h2>
            <div class="test-card" style="margin-bottom: 50px;">
                <h3>🎮 底部设备</h3>
                <div class="device-info-item">
                    <span class="device-info-label">位置:</span>
                    <span class="device-info-value">页面底部</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">预期:</span>
                    <span class="device-info-value">按钮上方显示</span>
                </div>
                
                <div class="control-dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown('bottom')">
                        ⚙️ 功能菜单
                    </button>
                    <div class="dropdown-menu" id="dropdown-bottom">
                        <div class="dropdown-columns">
                            <div class="dropdown-section">
                                <h4>服务控制</h4>
                                <button class="dropdown-btn">🚀 启动服务</button>
                                <button class="dropdown-btn">⏹️ 停止服务</button>
                                <button class="dropdown-btn">🔄 重启服务</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>视频控制</h4>
                                <button class="dropdown-btn">🎥 视频参数设置</button>
                                <button class="dropdown-btn">📸 视频流截屏</button>
                            </div>
                            <div class="dropdown-section">
                                <h4>网络配置</h4>
                                <button class="dropdown-btn">🌐 STUN/TURN配置</button>
                                <button class="dropdown-btn">🔄 重启设备</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(deviceId) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu.id !== `dropdown-${deviceId}`) {
                    menu.classList.remove('show');
                    menu.style.left = '';
                    menu.style.top = '';
                    menu.style.visibility = '';
                }
            });
            document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                if (!btn.onclick.toString().includes(deviceId)) {
                    btn.classList.remove('active');
                }
            });

            const dropdown = document.getElementById(`dropdown-${deviceId}`);
            const button = document.querySelector(`[onclick="toggleDropdown('${deviceId}')"]`);
            const isShowing = dropdown.classList.contains('show');

            if (isShowing) {
                dropdown.classList.remove('show');
                button.classList.remove('active');
                dropdown.style.left = '';
                dropdown.style.top = '';
                dropdown.style.visibility = '';
                hidePositionIndicator();
            } else {
                // 先计算位置，再显示
                adjustDropdownPosition(dropdown, button);
                dropdown.classList.add('show');
                button.classList.add('active');
            }
        }

        function adjustDropdownPosition(dropdown, button) {
            // 先显示下拉菜单以获取实际尺寸
            dropdown.style.visibility = 'hidden';
            dropdown.style.display = 'block';
            
            const buttonRect = button.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            const dropdownWidth = dropdownRect.width || 720;
            const dropdownHeight = dropdownRect.height || 400;

            // 计算最佳位置 - 默认在按钮正下方
            let left = buttonRect.left;
            let top = buttonRect.bottom + 8; // 按钮下方，留8px间距

            // 检查右边界
            if (left + dropdownWidth > windowWidth - 20) {
                // 右对齐到按钮右边
                left = buttonRect.right - dropdownWidth;
            }

            // 再次检查左边界
            if (left < 20) {
                left = 20; // 最小左边距
            }

            // 检查下边界
            if (top + dropdownHeight > windowHeight - 20) {
                // 显示在按钮上方
                top = buttonRect.top - dropdownHeight - 8;
            }

            // 确保不超出上边界
            if (top < 20) {
                top = 20; // 最小上边距
            }

            // 应用位置
            dropdown.style.left = `${left}px`;
            dropdown.style.top = `${top}px`;
            dropdown.style.visibility = 'visible';
            
            // 显示位置指示器
            showPositionIndicator(left, top, dropdownWidth, dropdownHeight, buttonRect);
        }
        
        function showPositionIndicator(left, top, width, height, buttonRect) {
            const indicator = document.getElementById('positionIndicator');
            indicator.style.left = `${left + width + 10}px`;
            indicator.style.top = `${top}px`;
            indicator.style.display = 'block';
            indicator.innerHTML = `
                位置: ${Math.round(left)}, ${Math.round(top)}<br>
                尺寸: ${Math.round(width)} × ${Math.round(height)}<br>
                按钮: ${Math.round(buttonRect.left)}, ${Math.round(buttonRect.bottom)}
            `;
        }
        
        function hidePositionIndicator() {
            document.getElementById('positionIndicator').style.display = 'none';
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                    menu.style.left = '';
                    menu.style.top = '';
                    menu.style.visibility = '';
                });
                document.querySelectorAll('.dropdown-toggle').forEach(btn => {
                    btn.classList.remove('active');
                });
                hidePositionIndicator();
            }
        });
    </script>
</body>
</html>
