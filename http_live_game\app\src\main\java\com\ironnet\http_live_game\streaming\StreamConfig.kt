package com.ironnet.http_live_game.streaming

import android.media.MediaFormat

data class StreamConfig(
    // Source configuration
    val useCamera: Boolean = true,
    val cameraId: String = "0", // 0 for back camera, 1 for front camera

    // Video configuration
    val width: Int = 1280,
    val height: Int = 720,
    val frameRate: Int = 30,
    val bitRate: Int = 2_000_000, // 2 Mbps
    val iFrameInterval: Int = 1, // Keyframe interval in seconds
    val videoFormat: String = MediaFormat.MIMETYPE_VIDEO_AVC, // H.264/AVC

    // Server configuration
    val port: Int = 8080
) {
    companion object {
        val SUPPORTED_RESOLUTIONS = listOf(
            Pair(640, 480),   // VGA
            Pair(1280, 720),  // 720p
            Pair(1920, 1080), // 1080p
            Pair(3840, 2160)  // 4K
        )

        val SUPPORTED_FRAME_RATES = listOf(15, 24, 30, 60)

        val SUPPORTED_BIT_RATES = listOf(
            500_000,    // 500 Kbps
            1_000_000,  // 1 Mbps
            2_000_000,  // 2 Mbps
            4_000_000,  // 4 Mbps
            8_000_000   // 8 Mbps
        )

        val SUPPORTED_FORMATS = listOf(
            MediaFormat.MIMETYPE_VIDEO_AVC,  // H.264/AVC
            MediaFormat.MIMETYPE_VIDEO_HEVC, // H.265/HEVC
            MediaFormat.MIMETYPE_VIDEO_VP8,  // VP8
            MediaFormat.MIMETYPE_VIDEO_VP9   // VP9
        )

        // 端口范围，如果默认端口被占用，将尝试这些端口
        val PORT_RANGE = 8080..8200

        fun getFormatName(format: String): String {
            return when (format) {
                MediaFormat.MIMETYPE_VIDEO_AVC -> "H.264/AVC"
                MediaFormat.MIMETYPE_VIDEO_HEVC -> "H.265/HEVC"
                MediaFormat.MIMETYPE_VIDEO_VP8 -> "VP8"
                MediaFormat.MIMETYPE_VIDEO_VP9 -> "VP9"
                else -> format
            }
        }
    }
}
