<dependencies>
  <compile
      roots="com.google.android.material:material:1.4.0@aar,androidx.appcompat:appcompat:1.3.1@aar,androidx.constraintlayout:constraintlayout:2.1.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.2.4@aar,androidx.appcompat:appcompat-resources:1.3.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.5.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar,androidx.savedstate:savedstate:1.1.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation:1.2.0@jar,androidx.annotation:annotation-experimental:1.0.0@aar">
    <dependency
        name="com.google.android.material:material:1.4.0@aar"/>
    <dependency
        name="androidx.appcompat:appcompat:1.3.1@aar"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.0@aar"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"/>
    <dependency
        name="androidx.activity:activity:1.2.4@aar"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.3.1@aar"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"/>
    <dependency
        name="androidx.core:core:1.5.0@aar"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"/>
    <dependency
        name="androidx.savedstate:savedstate:1.1.0@aar"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"/>
    <dependency
        name="androidx.annotation:annotation:1.2.0@jar"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.0.0@aar"/>
  </compile>
  <package
      roots="com.google.android.material:material:1.4.0@aar,androidx.constraintlayout:constraintlayout:2.1.0@aar,androidx.appcompat:appcompat:1.3.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.2.4@aar,androidx.appcompat:appcompat-resources:1.3.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.5.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar,androidx.savedstate:savedstate:1.1.0@aar,androidx.lifecycle:lifecycle-runtime:2.3.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar,androidx.lifecycle:lifecycle-common:2.3.1@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation:1.2.0@jar,androidx.annotation:annotation-experimental:1.0.0@aar,androidx.constraintlayout:constraintlayout-core:1.0.0@jar">
    <dependency
        name="com.google.android.material:material:1.4.0@aar"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.0@aar"/>
    <dependency
        name="androidx.appcompat:appcompat:1.3.1@aar"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"/>
    <dependency
        name="androidx.activity:activity:1.2.4@aar"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.3.1@aar"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"/>
    <dependency
        name="androidx.core:core:1.5.0@aar"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar"/>
    <dependency
        name="androidx.savedstate:savedstate:1.1.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.3.1@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.3.1@jar"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"/>
    <dependency
        name="androidx.annotation:annotation:1.2.0@jar"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.0.0@aar"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.0@jar"/>
  </package>
</dependencies>
