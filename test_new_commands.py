#!/usr/bin/env python3
"""
测试新增命令的脚本
用于验证信令服务器对新命令的支持
"""

import asyncio
import websockets
import json
import time
import requests

# 配置
SIGNALING_SERVER = "ws://localhost:8765"
HTTP_API_BASE = "http://localhost:8080/api/v1"
TEST_DEVICE_ID = "test-device-001"

async def test_websocket_commands():
    """测试WebSocket命令发送"""
    print("🔗 连接到信令服务器...")
    
    try:
        async with websockets.connect(SIGNALING_SERVER) as websocket:
            # 注册为测试设备
            register_msg = {
                "type": "register",
                "id": TEST_DEVICE_ID,
                "role": "sender",
                "name": "测试设备",
                "description": "用于测试新命令的虚拟设备"
            }
            
            await websocket.send(json.dumps(register_msg))
            print(f"📝 已注册设备: {TEST_DEVICE_ID}")
            
            # 等待注册确认
            response = await websocket.recv()
            data = json.loads(response)
            print(f"📨 收到响应: {data.get('type')}")
            
            # 发送心跳保持连接
            heartbeat_msg = {
                "type": "heartbeat",
                "from": TEST_DEVICE_ID,
                "timestamp": int(time.time())
            }
            await websocket.send(json.dumps(heartbeat_msg))
            print("💓 已发送心跳")
            
            # 监听消息
            print("👂 开始监听命令...")
            
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(message)
                    message_type = data.get('type')
                    
                    print(f"📨 收到消息: {message_type}")
                    
                    if message_type == "control_command":
                        command = data.get('command')
                        params = data.get('params', {})
                        print(f"🎮 收到控制命令: {command}")
                        print(f"📋 参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
                        
                        # 模拟命令执行并发送响应
                        response_msg = {
                            "type": "command_response",
                            "command": command,
                            "success": True,
                            "message": f"命令 {command} 执行成功",
                            "timestamp": int(time.time())
                        }
                        await websocket.send(json.dumps(response_msg))
                        print(f"✅ 已发送命令响应")
                        
                        # 如果是截屏命令，发送截屏结果
                        if command == "take_screenshot":
                            request_id = params.get('request_id', 'unknown')
                            screenshot_result = {
                                "type": "screenshot_result",
                                "request_id": request_id,
                                "success": True,
                                "full_url": f"https://example.com/screenshots/{request_id}.jpg",
                                "message": "截屏成功",
                                "timestamp": int(time.time())
                            }
                            await websocket.send(json.dumps(screenshot_result))
                            print(f"📸 已发送截屏结果")
                    
                    elif message_type == "heartbeat_ack":
                        print("💓 收到心跳确认")
                        
            except asyncio.TimeoutError:
                print("⏰ 等待消息超时，发送心跳...")
                await websocket.send(json.dumps(heartbeat_msg))
                
    except Exception as e:
        print(f"❌ WebSocket连接错误: {e}")

def test_http_api():
    """测试HTTP API命令发送"""
    print("\n🌐 测试HTTP API...")
    
    # 测试命令列表
    test_commands = [
        {
            "name": "设置自动启动游戏",
            "command": "set_auto_start_game",
            "params": {
                "enabled": True,
                "package_name": "com.example.testgame"
            }
        },
        {
            "name": "开启日志显示",
            "command": "toggle_log_display",
            "params": {
                "enabled": True
            }
        },
        {
            "name": "下载日志",
            "command": "download_logs",
            "params": {
                "method": "ftp"
            }
        },
        {
            "name": "截屏",
            "command": "take_screenshot",
            "params": {
                "request_id": f"test_{int(time.time())}"
            }
        }
    ]
    
    for test_cmd in test_commands:
        print(f"\n📤 发送命令: {test_cmd['name']}")
        
        try:
            response = requests.post(
                f"{HTTP_API_BASE}/commands/{TEST_DEVICE_ID}",
                json={
                    "command": test_cmd["command"],
                    "params": test_cmd["params"]
                },
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 命令发送成功: {result.get('message')}")
            else:
                print(f"❌ 命令发送失败: {response.status_code} - {response.text}")
                
        except requests.RequestException as e:
            print(f"❌ HTTP请求错误: {e}")
        
        # 等待一下再发送下一个命令
        time.sleep(2)

def test_server_status():
    """测试服务器状态"""
    print("\n📊 检查服务器状态...")
    
    try:
        # 检查发送端列表
        response = requests.get(f"{HTTP_API_BASE}/senders", timeout=5)
        if response.status_code == 200:
            senders = response.json().get('senders', {})
            print(f"📱 在线发送端数量: {len(senders)}")
            for sender_id, info in senders.items():
                print(f"  - {sender_id}: {'在线' if info.get('online') else '离线'}")
        else:
            print(f"❌ 获取发送端列表失败: {response.status_code}")
            
    except requests.RequestException as e:
        print(f"❌ 检查服务器状态失败: {e}")

async def main():
    """主函数"""
    print("🚀 开始测试新增命令功能")
    print("=" * 50)
    
    # 测试服务器状态
    test_server_status()
    
    # 启动WebSocket监听（在后台运行）
    websocket_task = asyncio.create_task(test_websocket_commands())
    
    # 等待一下让WebSocket连接建立
    await asyncio.sleep(3)
    
    # 测试HTTP API
    test_http_api()
    
    # 继续监听一段时间
    print("\n👂 继续监听命令 (按Ctrl+C退出)...")
    try:
        await websocket_task
    except KeyboardInterrupt:
        print("\n👋 测试结束")
        websocket_task.cancel()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
