<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信令服务器控制台演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .api-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🚀 信令服务器控制台演示</h1>
        <p>这是一个演示页面，展示如何使用信令服务器的各种API功能。</p>

        <div class="demo-section">
            <h3>📡 配置管理演示</h3>
            <p>演示如何更新和广播服务器配置：</p>
            
            <button class="btn" onclick="demoUpdateConfig()">更新配置</button>
            <button class="btn" onclick="demoBroadcastConfig()">广播配置</button>
            <button class="btn" onclick="demoGetConfig()">获取配置</button>
            
            <div class="api-example">
                <strong>API示例:</strong><br>
                POST /api/v1/config<br>
                {<br>
                &nbsp;&nbsp;"config": {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"stun_servers": ["stun:stun.l.google.com:19302"],<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"turn_servers": [...]<br>
                &nbsp;&nbsp;},<br>
                &nbsp;&nbsp;"auto_broadcast": true<br>
                }
            </div>
        </div>

        <div class="demo-section">
            <h3>🎮 控制命令演示</h3>
            <p>演示如何向接收端发送控制命令：</p>
            
            <button class="btn" onclick="demoStartService()">开始服务</button>
            <button class="btn" onclick="demoStopService()">停止服务</button>
            <button class="btn" onclick="demoChangeResolution()">改变分辨率</button>
            
            <div class="api-example">
                <strong>API示例:</strong><br>
                POST /api/v1/commands/receiver-001<br>
                {<br>
                &nbsp;&nbsp;"command": "start_service",<br>
                &nbsp;&nbsp;"params": {"resolution": "1920x1080"},<br>
                &nbsp;&nbsp;"device_id": "device_md5_hash"<br>
                }
            </div>
        </div>

        <div class="demo-section">
            <h3>📦 升级管理演示</h3>
            <p>演示如何发送升级命令：</p>
            
            <button class="btn" onclick="demoUpgrade()">发送升级命令</button>
            
            <div class="api-example">
                <strong>API示例:</strong><br>
                POST /api/v1/commands/receiver-001<br>
                {<br>
                &nbsp;&nbsp;"command": "upgrade",<br>
                &nbsp;&nbsp;"params": {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"apk_url": "https://example.com/app-v2.0.apk",<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"version": "2.0.0",<br>
                &nbsp;&nbsp;&nbsp;&nbsp;"force": true<br>
                &nbsp;&nbsp;}<br>
                }
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 状态监控演示</h3>
            <p>演示如何获取接收端状态：</p>
            
            <button class="btn" onclick="demoGetReceivers()">获取所有接收端</button>
            <button class="btn" onclick="demoGetReceiver()">获取单个接收端</button>
            
            <div class="api-example">
                <strong>API示例:</strong><br>
                GET /api/v1/receivers<br>
                GET /api/v1/receivers/receiver-001
            </div>
        </div>

        <div id="demoResults"></div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        
        function showResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('demoResults');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${isSuccess ? 'success' : 'error'}`;
            statusDiv.textContent = message;
            resultsDiv.appendChild(statusDiv);
            
            // 自动清理旧结果
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }

        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(API_BASE + endpoint, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || `HTTP ${response.status}`);
                }

                return result;
            } catch (error) {
                showResult(`API调用失败: ${error.message}`, false);
                throw error;
            }
        }

        async function demoUpdateConfig() {
            try {
                const result = await apiCall('/config', 'POST', {
                    config: {
                        stun_servers: ['stun:demo.example.com:19302'],
                        turn_servers: [{
                            urls: 'turn:demo.example.com:3478',
                            username: 'demo',
                            credential: 'demo123'
                        }]
                    },
                    auto_broadcast: true
                });
                showResult('配置更新成功并已广播');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoBroadcastConfig() {
            try {
                const result = await apiCall('/config/broadcast', 'POST');
                showResult('配置广播成功');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoGetConfig() {
            try {
                const result = await apiCall('/config');
                showResult(`当前配置: STUN服务器${result.config.stun_servers.length}个, TURN服务器${result.config.turn_servers.length}个`);
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoStartService() {
            try {
                const result = await apiCall('/commands/demo-receiver-001', 'POST', {
                    command: 'start_service',
                    params: {
                        resolution: '1920x1080',
                        bitrate: 3000
                    }
                });
                showResult('开始服务命令已发送');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoStopService() {
            try {
                const result = await apiCall('/commands/demo-receiver-001', 'POST', {
                    command: 'stop_service'
                });
                showResult('停止服务命令已发送');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoChangeResolution() {
            try {
                const result = await apiCall('/commands/demo-receiver-001', 'POST', {
                    command: 'change_resolution',
                    params: {
                        resolution: '2560x1440'
                    }
                });
                showResult('改变分辨率命令已发送');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoUpgrade() {
            try {
                const result = await apiCall('/commands/demo-receiver-001', 'POST', {
                    command: 'upgrade',
                    params: {
                        apk_url: 'https://example.com/demo-app-v2.0.apk',
                        version: '2.0.0',
                        force: false
                    }
                });
                showResult('升级命令已发送');
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoGetReceivers() {
            try {
                const result = await apiCall('/receivers');
                const count = Object.keys(result.receivers).length;
                showResult(`获取到 ${count} 个接收端状态`);
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }

        async function demoGetReceiver() {
            try {
                const result = await apiCall('/receivers/demo-receiver-001');
                showResult(`接收端状态: ${result.receiver.online ? '在线' : '离线'}`);
            } catch (error) {
                // 错误已在apiCall中处理
            }
        }
    </script>
</body>
</html>
