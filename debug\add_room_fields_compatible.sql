-- 兼容版：适用于不支持 IF NOT EXISTS 的MySQL版本
-- 如果字段已存在会报错，但不影响其他字段的添加

-- 添加房间信息字段（逐个添加，避免一个失败影响全部）

-- 添加服务器域名字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_server_domain VARCHAR(255) DEFAULT '' COMMENT '关联的服务器域名';

-- 添加房间ID字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_id INT DEFAULT 0 COMMENT '关联的房间ID';

-- 添加房间名称字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_name VARCHAR(100) DEFAULT '' COMMENT '关联的房间名称';

-- 添加房间分类ID字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_category_id INT DEFAULT 0 COMMENT '房间分类ID';

-- 添加房间排序字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_sort_order INT DEFAULT 0 COMMENT '房间排序';

-- 添加房间信息最后更新时间字段
ALTER TABLE fa_sender_device_info 
ADD COLUMN room_last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '房间信息最后更新时间';

-- 添加索引（逐个添加）

-- 服务器域名索引
ALTER TABLE fa_sender_device_info 
ADD INDEX idx_room_server_domain (room_server_domain);

-- 房间分类索引
ALTER TABLE fa_sender_device_info 
ADD INDEX idx_room_category_id (room_category_id);

-- 房间排序索引
ALTER TABLE fa_sender_device_info 
ADD INDEX idx_room_sort_order (room_sort_order);

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE table_schema = DATABASE() 
AND table_name = 'fa_sender_device_info'
AND COLUMN_NAME LIKE 'room_%'
ORDER BY ORDINAL_POSITION;
