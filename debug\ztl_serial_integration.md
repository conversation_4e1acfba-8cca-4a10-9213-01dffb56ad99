# ZtlManager 序列号集成

## 概述

已将 `DeviceInfoCollector.kt` 中的 `getWechatSN()` 方法修改为使用 ZtlManager 的 `getBuildSerial()` 接口获取设备序列号。

## 修改内容

### 原始代码
```kotlin
/**
 * 获取微信SN（需要微信SDK支持）
 */
private fun getWechatSN(): String {
    // TODO: 如果集成了微信SDK，在这里获取微信SN
    // 目前返回模拟数据
    return "WX_SN_${Build.SERIAL.take(8)}"
}
```

### 修改后代码
```kotlin
/**
 * 获取设备序列号（使用ZtlManager接口）
 */
private fun getWechatSN(): String {
    return try {
        // 使用ZtlManager获取生成的序列号
        val ztlManager = ZtlManager.GetInstance()
        val buildSerial = ztlManager.getBuildSerial()
        
        if (buildSerial.isNotEmpty()) {
            "ZTL_SN_$buildSerial"
        } else {
            // 如果ZtlManager返回空值，使用备用方案
            "FALLBACK_SN_${Build.SERIAL.take(8)}"
        }
    } catch (e: Exception) {
        Logger.w(TAG, "获取ZtlManager序列号失败: ${e.message}")
        // 异常情况下使用备用方案
        "ERROR_SN_${Build.SERIAL.take(8)}"
    }
}
```

### 添加的导入
```kotlin
import ZtlApi.ZtlManager
```

## 功能特性

### 1. 主要功能
- **使用 ZtlManager.getBuildSerial()** 获取设备序列号
- **前缀标识** - 返回值带有 `ZTL_SN_` 前缀，便于识别来源
- **异常处理** - 完善的错误处理机制
- **备用方案** - 多层次的备用序列号生成

### 2. 返回值格式
| 情况 | 返回格式 | 示例 |
|------|----------|------|
| 正常获取 | `ZTL_SN_[序列号]` | `ZTL_SN_ABC123DEF456` |
| 空值备用 | `FALLBACK_SN_[Build.SERIAL前8位]` | `FALLBACK_SN_12345678` |
| 异常备用 | `ERROR_SN_[Build.SERIAL前8位]` | `ERROR_SN_87654321` |

### 3. 错误处理机制
1. **ZtlManager 调用异常** - 捕获并记录日志，使用备用方案
2. **返回值为空** - 检测空值，使用备用方案
3. **系统序列号备用** - 使用 Android Build.SERIAL 作为最后备用

## 依赖关系

### ZtlApi.jar
- **位置**: `android_webrtc_sender_tools/app/libs/ZtlApi.jar`
- **配置**: 已在 `build.gradle` 中配置
```gradle
implementation files("libs\\ZtlApi.jar")
```

### ZtlManager 接口
- **类名**: `ZtlApi.ZtlManager`
- **方法**: `getBuildSerial()`
- **返回**: `String` - 设备生成的序列号

## 使用场景

### 设备信息收集
该序列号会被包含在设备信息中，用于：
1. **设备识别** - 在服务器端识别不同设备
2. **状态上报** - 作为设备唯一标识上报状态
3. **日志追踪** - 在日志中标识设备来源

### 数据上报格式
```json
{
    "device_info": {
        "wechat_sn": "ZTL_SN_ABC123DEF456",
        "device_id": "...",
        "brand": "...",
        "model": "..."
    }
}
```

## 测试建议

### 1. 正常情况测试
```kotlin
// 验证 ZtlManager 正常工作
val deviceInfo = DeviceInfoCollector.collectDeviceInfo(context)
val wechatSn = deviceInfo["wechat_sn"] as String
assert(wechatSn.startsWith("ZTL_SN_"))
```

### 2. 异常情况测试
- **ZtlManager 不可用** - 验证备用方案是否生效
- **返回空值** - 验证空值检测和备用方案
- **权限问题** - 验证权限不足时的处理

### 3. 日志验证
查看日志中是否有相关记录：
```
[DeviceInfoCollector] WARN: 获取ZtlManager序列号失败: [异常信息]
```

## 注意事项

### 1. 序列号唯一性
- **ZtlManager 序列号不保证唯一**（根据接口文档）
- **如需唯一设备识别码，建议使用 `getDeviceID()` 接口**
- **当前实现主要用于设备标识，非严格唯一性要求**

### 2. 兼容性
- **向后兼容** - 方法名保持 `getWechatSN()` 不变
- **返回格式** - 保持字符串格式，添加前缀标识
- **异常安全** - 确保在任何情况下都能返回有效值

### 3. 性能考虑
- **单例模式** - ZtlManager 使用单例，性能较好
- **异常处理** - 异常情况下的备用方案执行快速
- **缓存机制** - 可考虑缓存序列号避免重复调用

## 版本更新

当前版本：v1.20 → v1.21

主要改进：
- 集成 ZtlManager.getBuildSerial() 接口
- 完善设备序列号获取机制
- 增强异常处理和备用方案
- 改进设备信息收集准确性

## 相关文件

- `DeviceInfoCollector.kt` - 设备信息收集器（主要修改）
- `ZtlInstallHelper.java` - ZtlManager 使用示例
- `build.gradle` - ZtlApi.jar 依赖配置
- `libs/ZtlApi.jar` - ZtlManager API 库
