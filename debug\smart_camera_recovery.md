# 智能摄像头恢复机制

## 🎯 问题分析

从日志可以看到摄像头125被系统策略持续禁用：
```
Camera 125: Access for "com.example.webrtcsender" has been restricted
CAMERA_DISABLED (1): Camera "125" disabled by policy
```

这是一个系统级的策略限制，可能由以下原因导致：
- 设备管理策略
- 硬件保护机制
- 温度保护
- 电源管理
- 安全策略

## 🔧 智能恢复策略

### 1. 三阶段恢复机制

```kotlin
private fun handleCameraPolicyError(cameraId: String) {
    // 阶段1: 直接恢复（前3次重试）
    if (cameraPolicyErrorRetryCount < 3) {
        attemptDirectRecovery(cameraId)
    } 
    // 阶段2: 备用摄像头切换
    else if (!fallbackCameraAttempted) {
        attemptFallbackCamera(cameraId)
    } 
    // 阶段3: 继续重试原摄像头
    else {
        attemptDirectRecovery(cameraId)
    }
}
```

### 2. 备用摄像头优先级

```kotlin
// 摄像头优先级排序：0 > 1 > 其他
val prioritizedCameras = fallbackCameras.sortedWith { a, b ->
    when {
        a == "0" -> -1  // 摄像头0优先级最高
        b == "0" -> 1
        a == "1" -> -1  // 摄像头1次优先级
        b == "1" -> 1
        else -> a.compareTo(b)  // 其他按数字排序
    }
}
```

### 3. 配置自动更新

```kotlin
// 成功切换后更新配置
WebRTCManager.setCameraId(fallbackCameraId)
Logger.i(TAG, "📝 已更新配置摄像头ID: $originalCameraId -> $fallbackCameraId")
```

## 📊 恢复流程图

```mermaid
graph TD
    A[摄像头125策略错误] --> B{重试次数 < 3?}
    B -->|是| C[直接恢复摄像头125]
    B -->|否| D{已尝试备用摄像头?}
    D -->|否| E[获取可用摄像头列表]
    D -->|是| C
    
    E --> F[排除问题摄像头125]
    F --> G[按优先级排序: 0>1>其他]
    G --> H[尝试备用摄像头]
    
    H --> I{备用摄像头成功?}
    I -->|是| J[✅ 更新配置并继续推流]
    I -->|否| K{还有其他备用摄像头?}
    
    K -->|是| L[尝试下一个备用摄像头]
    K -->|否| M[回到原摄像头重试]
    
    C --> N{恢复成功?}
    N -->|是| J
    N -->|否| O[继续重试]
    
    L --> H
    M --> C
    O --> A
```

## 🎯 预期效果

### 场景1: 摄像头125临时被禁用
```
🎥 [策略错误] 摄像头125被策略禁用，启动智能恢复机制
🎥 [策略错误] 第1次重试，30秒后尝试恢复摄像头125
🎥 [策略错误] ✅ 摄像头恢复成功
```

### 场景2: 摄像头125持续被禁用，切换到备用摄像头
```
🎥 [策略错误] 摄像头125被策略禁用，启动智能恢复机制
🎥 [策略错误] 第3次重试失败
🎥 [策略错误] 摄像头125 持续被禁用，尝试切换到备用摄像头
🎥 [策略错误] 可用摄像头列表: 125, 0, 1
🎥 [策略错误] 🔄 尝试切换到备用摄像头: 0
🎥 [策略错误] ✅ 成功切换到备用摄像头: 0
🎥 [策略错误] 📝 已更新配置摄像头ID: 125 -> 0
```

### 场景3: 所有摄像头都有问题
```
🎥 [策略错误] 备用摄像头0 也失败
🎥 [策略错误] 🔄 尝试下一个备用摄像头...
🎥 [策略错误] 备用摄像头1 也失败
🎥 [策略错误] ⚠️ 所有备用摄像头都失败，回到原摄像头重试
```

## 🛡️ 安全措施

### 1. 状态跟踪
```kotlin
private var originalCameraId: String? = null // 记录原始摄像头ID
private var fallbackCameraAttempted = false // 是否已尝试备用摄像头
```

### 2. 资源管理
```kotlin
// 停止当前视频源
WebRTCManager.stopVideoSource(context)

// 等待资源释放
Handler.postDelayed({ /* 重新启动 */ }, 3000)
```

### 3. 错误处理
```kotlin
try {
    createCameraVideoSource(fallbackCameraId)
} catch (e: Exception) {
    Logger.e(TAG, "备用摄像头也失败: ${e.message}")
    // 继续尝试其他备用摄像头
}
```

## 📝 详细日志

### 1. 策略错误检测
```
🎥 [摄像头] 摄像头错误: Failed to open camera: CAMERA_DISABLED (1)
🎥 [摄像头] 检测到摄像头策略错误，启动自动恢复
```

### 2. 恢复过程跟踪
```
🎥 [策略错误] 记录原始摄像头ID: 125
🎥 [策略错误] 第1次重试，30秒后尝试恢复摄像头125
🎥 [策略错误] 尝试恢复摄像头125
```

### 3. 备用摄像头切换
```
🎥 [策略错误] 可用摄像头列表: 125, 0, 1
🎥 [策略错误] 🔄 尝试切换到备用摄像头: 0
🎥 [策略错误] ✅ 成功切换到备用摄像头: 0
```

## 🚀 优势特点

### 1. 智能决策
- 前3次重试直接恢复
- 失败后自动切换备用摄像头
- 按优先级选择最佳备用摄像头

### 2. 配置同步
- 成功切换后自动更新配置
- 用户下次启动时使用工作的摄像头
- 避免重复遇到同样问题

### 3. 全面覆盖
- 处理临时策略限制
- 处理持续策略限制
- 处理硬件故障

### 4. 用户友好
- 自动处理，无需用户干预
- 保持推流连续性
- 详细日志便于故障排除

## ⚠️ 注意事项

### 1. 摄像头差异
- 不同摄像头可能有不同的分辨率支持
- 画质和性能可能有差异
- 需要重新协商编码参数

### 2. 配置持久化
- 切换后的摄像头ID会保存到配置
- 用户可以在设置中手动调整
- 原摄像头恢复后可以手动切换回去

### 3. 系统兼容性
- 不同设备的摄像头命名可能不同
- 某些设备可能只有一个摄像头
- 需要处理各种边界情况

## 🔧 测试验证

### 1. 编译安装
```bash
./gradlew clean assembleDebug
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 2. 观察日志
```bash
adb logcat | grep "策略错误"
```

### 3. 验证切换
- 观察是否成功切换到备用摄像头
- 检查配置是否更新
- 确认推流是否恢复正常

现在当摄像头125被持续禁用时，应用会智能地切换到可用的备用摄像头（如摄像头0或1），确保推流服务的连续性！
