#!/usr/bin/env python3
# simple_receiver.py - 简化版WebRTC视频流接收端
import asyncio
import json
import logging
import argparse
import cv2
import numpy as np
import time
import uuid
import websockets
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCConfiguration, RTCIceCandidate, MediaStreamTrack, VideoStreamTrack, RTCIceServer
from aiortc.contrib.media import MediaRecorder, MediaBlackhole
from av import VideoFrame

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('simple-receiver')

# 解析命令行参数
parser = argparse.ArgumentParser(description="简化版WebRTC视频流接收端")
parser.add_argument("--sender", help="发送端ID", required=True)
parser.add_argument("--signaling", help="信令服务器URL", default="wss://sling.91jdcd.com/ws/")
args = parser.parse_args()

# STUN服务器配置
ICE_SERVERS = [
    RTCIceServer(urls="stun:stun.l.google.com:19302")
]

async def run():
    # 连接到信令服务器
    logger.info(f"正在连接到信令服务器: {args.signaling}")

    try:
        async with websockets.connect(args.signaling) as websocket:
            # 生成随机ID
            receiver_id = f"simple-receiver-{uuid.uuid4().hex[:8]}"

            # 注册客户端
            await websocket.send(json.dumps({
                "type": "register",
                "id": receiver_id
            }))
            logger.info(f"已注册接收端ID: {receiver_id}")

            # 等待注册确认
            response = await websocket.recv()
            data = json.loads(response)
            if data["type"] == "registered":
                logger.info("注册成功")
            else:
                logger.error(f"注册失败: {data}")
                return

            # 创建对等连接
            pc = RTCPeerConnection(RTCConfiguration(iceServers=ICE_SERVERS))

            # 创建一个虚拟视频轨道
            class DummyVideoTrack(VideoStreamTrack):
                """
                一个虚拟视频轨道，用于创建offer
                """
                def __init__(self):
                    super().__init__()
                    self.counter = 0
                    self.width = 640
                    self.height = 480
                    logger.info("创建虚拟视频轨道")

                async def recv(self):
                    pts, time_base = await self.next_timestamp()

                    # 创建一个黑色帧
                    frame = np.zeros((self.height, self.width, 3), np.uint8)

                    # 添加文本
                    cv2.putText(frame, "等待视频流...", (50, self.height // 2),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                    # 转换为VideoFrame
                    video_frame = VideoFrame.from_ndarray(frame, format="bgr24")
                    video_frame.pts = pts
                    video_frame.time_base = time_base

                    return video_frame

            # 添加虚拟视频轨道，并明确设置方向为 recvonly
            dummy_track = DummyVideoTrack()
            sender = pc.addTrack(dummy_track)
            for transceiver in pc.getTransceivers():
                if transceiver.sender == sender:
                    transceiver.direction = "recvonly"

            # 创建数据通道
            channel = pc.createDataChannel("control")

            @channel.on("open")
            def on_open():
                logger.info("数据通道已打开")
                channel.send("Hello from receiver")

            # 处理视频轨道
            @pc.on("track")
            async def on_track(track):
                logger.info(f"收到轨道: {track.kind}")

                if track.kind == "video":
                    # 创建视频窗口
                    cv2.namedWindow("视频", cv2.WINDOW_NORMAL)
                    cv2.resizeWindow("视频", 800, 600)

                    while True:
                        try:
                            frame = await track.recv()
                            # 转换为OpenCV格式
                            img = frame.to_ndarray(format="bgr24")

                            # 显示帧
                            cv2.imshow("视频", img)
                            cv2.waitKey(1)
                        except Exception as e:
                            logger.error(f"处理视频帧错误: {e}")
                            break

            # 处理数据通道
            @pc.on("datachannel")
            def on_datachannel(channel):
                logger.info(f"收到数据通道: {channel.label}")

                @channel.on("message")
                def on_message(message):
                    logger.info(f"收到消息: {message}")

            # 处理连接状态变化
            @pc.on("connectionstatechange")
            async def on_connectionstatechange():
                logger.info(f"连接状态: {pc.connectionState}")

                if pc.connectionState == "failed":
                    logger.error("连接失败")
                    await pc.close()

            # 创建offer
            offer = await pc.createOffer()
            await pc.setLocalDescription(offer)

            # 发送offer到发送端
            await websocket.send(json.dumps({
                "type": "offer",
                "target": args.sender,
                "sdp": pc.localDescription.sdp
            }))
            logger.info(f"已发送offer到发送端: {args.sender}")

            # 等待answer
            while True:
                try:
                    message = await websocket.recv()
                    data = json.loads(message)
                    logger.info(f"收到消息类型: {data['type']}")

                    if data["type"] == "answer" and data.get("from") == args.sender:
                        # 设置远程描述
                        answer = RTCSessionDescription(sdp=data["sdp"], type="answer")
                        await pc.setRemoteDescription(answer)
                        logger.info("已设置远程描述")

                    elif data["type"] == "candidate" and data.get("from") == args.sender:
                        # 处理ICE候选
                        candidate = data["candidate"]
                        if candidate and "candidate" in candidate:
                            await pc.addIceCandidate(RTCIceCandidate(
                                sdpMid=candidate.get("sdpMid", ""),
                                sdpMLineIndex=candidate.get("sdpMLineIndex", 0),
                                candidate=candidate["candidate"]
                            ))
                            logger.info("已添加ICE候选")

                    elif data["type"] == "client_left" and data.get("id") == args.sender:
                        logger.info(f"发送端已离开: {args.sender}")
                        break

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"处理消息错误: {e}")

            # 关闭连接
            await pc.close()
            logger.info("已关闭连接")

    except Exception as e:
        logger.error(f"连接错误: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        logger.info("程序已终止")
