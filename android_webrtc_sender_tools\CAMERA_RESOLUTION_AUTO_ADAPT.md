# 摄像头分辨率自动适配功能

## 问题描述

用户设置的分辨率（如 1024x576）与实际编码器输出的分辨率（如 1024x768）不一致。这是因为：

1. **摄像头硬件限制**：不是所有摄像头都支持所有理论分辨率
2. **编码器自动调整**：当请求不支持的分辨率时，编码器会自动选择最接近的支持分辨率
3. **缺乏分辨率检查**：应用没有检查摄像头实际支持的分辨率

## 解决方案

### 1. 分辨率检查和自动适配

#### 实现原理
```kotlin
// 检查摄像头支持的分辨率并选择最佳匹配
val actualResolution = findBestSupportedResolution(cameraEnumerator, cameraId, requestedResolution)

if (actualResolution != requestedResolution) {
    Logger.w(TAG, "🎥 [摄像头] 摄像头不支持期望分辨率 ${requestedResolution.first}x${requestedResolution.second}")
    Logger.i(TAG, "🎥 [摄像头] 使用最佳匹配分辨率: ${actualResolution.first}x${actualResolution.second}")
}
```

#### 匹配算法（优先保证宽高比，避免画面变形）

**核心原则：宽高比优先，避免画面变形**

```kotlin
private fun findClosestResolution(
    supportedResolutions: List<Pair<Int, Int>>,
    requestedResolution: Pair<Int, Int>
): Pair<Int, Int> {
    val requestedAspectRatio = requestedWidth.toFloat() / requestedHeight.toFloat()

    // 首先筛选出宽高比相近的分辨率（容差0.1）
    val aspectRatioTolerance = 0.1f
    val sameAspectRatioResolutions = supportedResolutions.filter { resolution ->
        val aspectRatio = resolution.first.toFloat() / resolution.second.toFloat()
        val aspectRatioDiff = kotlin.math.abs(aspectRatio - requestedAspectRatio)
        aspectRatioDiff <= aspectRatioTolerance
    }

    // 如果有相同宽高比的分辨率，优先从中选择
    val candidateResolutions = if (sameAspectRatioResolutions.isNotEmpty()) {
        sameAspectRatioResolutions  // 优先选择相同宽高比，避免变形
    } else {
        supportedResolutions        // 没有相同宽高比时才考虑所有分辨率
    }

    // 评分算法
    for (resolution in candidateResolutions) {
        val score = if (sameAspectRatioResolutions.contains(resolution)) {
            // 相同宽高比：90%权重给分辨率差异，10%给宽高比微调
            resolutionDiff * 0.9f + aspectRatioDiff * 100000 * 0.1f
        } else {
            // 不同宽高比：50%权重给分辨率差异，50%给宽高比差异
            resolutionDiff * 0.5f + aspectRatioDiff * 1000000 * 0.5f
        }
    }

    return bestMatch
}
```

**示例：1024x576 (16:9) 的匹配过程**
- 1280x720 (16:9) - 宽高比完全匹配 ✅
- 1024x768 (4:3) - 宽高比不匹配，会变形 ❌

### 2. 支持分辨率查询

#### WebRTCManager 新增方法
```kotlin
fun getCameraSupportedResolutions(context: Context, cameraId: String): List<Pair<Int, Int>> {
    val supportedResolutions = mutableListOf<Pair<Int, Int>>()
    
    try {
        val camera2Enumerator = Camera2Enumerator(context)
        val supportedFormats = camera2Enumerator.getSupportedFormats(cameraId)
        
        if (supportedFormats != null) {
            for (format in supportedFormats) {
                supportedResolutions.add(Pair(format.width, format.height))
            }
            
            // 按分辨率大小排序
            supportedResolutions.sortByDescending { it.first * it.second }
        }
    } catch (e: Exception) {
        Logger.e(TAG, "查询摄像头分辨率失败", e)
    }
    
    return supportedResolutions
}
```

### 3. 详细日志输出

#### 分辨率检查日志
```
🎥 [分辨率检查] 检查摄像头 0 支持的分辨率
🎥 [分辨率检查] 摄像头支持 15 种格式
🎥 [分辨率检查] 支持分辨率: 1920x1080
🎥 [分辨率检查] 支持分辨率: 1280x720
🎥 [分辨率检查] 支持分辨率: 1024x768
🎥 [分辨率检查] 支持分辨率: 640x480
🎥 [分辨率检查] 🔄 选择最接近的分辨率: 1024x768
```

#### 匹配过程日志
```
🎥 [分辨率匹配] 期望分辨率: 1024x576, 宽高比: 1.78
🎥 [分辨率匹配] 找到 2 个相同宽高比的分辨率
🎥 [分辨率匹配] ✅ 优先选择相同宽高比的分辨率，避免画面变形
🎥 [分辨率匹配] 1920x1080 (1.78): 分辨率差异=1024000.0, 宽高比差异=0.0, 评分=921600.0
🎥 [分辨率匹配] 1280x720 (1.78): 分辨率率差异=332800.0, 宽高比差异=0.0, 评分=299520.0
🎥 [分辨率匹配] ✅ 最佳匹配: 1280x720 (宽高比匹配，无变形)
```

#### 支持分辨率列表
```
🎥 [支持分辨率] 摄像头 0 支持的所有分辨率:
🎥 [支持分辨率] - 1920x1080
🎥 [支持分辨率] - 1280x720
🎥 [支持分辨率] - 1024x768
🎥 [支持分辨率] - 800x600
🎥 [支持分辨率] - 640x480
🎥 [支持分辨率] - 320x240
```

## 使用效果

### 修改前
```
用户设置: 1024x576 (16:9)
实际输出: 1024x768 (4:3) - 画面变形！
日志显示: 配置参数: 分辨率=1024x576 (误导性信息)
问题: 用户不知道画面已经变形
```

### 修改后
```
用户设置: 1024x576 (16:9)
检查结果: 摄像头不支持 1024x576
智能选择: 1280x720 (16:9) - 保持宽高比，无变形！
实际输出: 1280x720 (与预期宽高比一致)
日志显示:
  - 摄像头不支持期望分辨率 1024x576
  - 找到 2 个相同宽高比的分辨率
  - ✅ 优先选择相同宽高比的分辨率，避免画面变形
  - ✅ 最佳匹配: 1280x720 (宽高比匹配，无变形)
  - 摄像头捕获已启动: 1280x720@60fps
```

## 优势

1. **画面质量**：优先保证宽高比，避免画面变形
2. **智能匹配**：自动选择相同宽高比的最佳分辨率
3. **透明度**：用户清楚知道实际使用的分辨率和原因
4. **准确性**：避免设置与实际不符的情况
5. **调试友好**：详细的日志帮助问题诊断
6. **兼容性**：对不支持分辨率查询的设备有回退机制

### 核心改进：宽高比优先
- **1024x576 (16:9)** → **1280x720 (16:9)** ✅ 无变形
- **1024x576 (16:9)** → **1024x768 (4:3)** ❌ 画面变形

## 日志标识

- `🎥 [分辨率检查]` - 分辨率检查过程
- `🎥 [分辨率匹配]` - 分辨率匹配算法
- `🎥 [支持分辨率]` - 摄像头支持的分辨率列表
- `🔄` - 自动调整标识
- `✅` - 直接支持标识
- `⚠️` - 不支持警告

现在摄像头会自动检查支持的分辨率，并选择最接近用户设置的分辨率，避免设置与实际输出不一致的问题！
