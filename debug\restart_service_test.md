# 重启服务功能测试文档

## 🎯 修复的问题

### 问题描述
- ❌ **原问题**: 设备在线时，主按钮显示"重启服务"，但实际发送的是`start_service`命令
- ✅ **修复方案**: 根据设备状态动态发送正确的命令
  - 设备在线时: 发送`restart_service`命令
  - 设备离线时: 发送`start_service`命令

## 🔧 修复详情

### 1. Web端修复
```javascript
// 修复前
<button class="control-main-btn" onclick="admin.sendQuickCommand('${id}', 'start_service')">
    ${status.online ? '重启服务' : '启动服务'}
</button>

// 修复后
<button class="control-main-btn" onclick="admin.sendQuickCommand('${id}', '${status.online ? 'restart_service' : 'start_service'}')">
    ${status.online ? '重启服务' : '启动服务'}
</button>
```

### 2. Android端实现验证
Android端的`ServiceCommandHandler.kt`已经正确实现了重启逻辑：

```kotlin
private fun restartService(params: JSONObject): Bo<PERSON>an {
    return try {
        Logger.i(TAG, "🔄 重启服务")
        statusCallback("service", "restarting", 25, "正在重启推流服务...")
        
        // 先停止再启动
        val stopSuccess = stopService(params)
        if (stopSuccess) {
            statusCallback("service", "restarting", 75, "服务已停止，正在重新启动...")
            // 等待一秒
            Thread.sleep(1000)
            val startSuccess = startService(params)
            if (startSuccess) {
                statusCallback("service", "restarted", 100, "推流服务已重启")
            }
            return startSuccess
        }
        false
    } catch (e: Exception) {
        Logger.e(TAG, "❌ 重启服务失败: ${e.message}")
        statusCallback("service", "restart_failed", 0, "重启服务异常: ${e.message}")
        false
    }
}
```

## 📋 重启服务流程

### 完整的重启流程
1. **接收命令**: Web端发送`restart_service`命令
2. **命令分发**: `CommandDispatcher`将命令路由到`ServiceCommandHandler`
3. **停止推流**: 调用`webrtcManager.stopVideoSource(context)`
4. **状态回调**: 发送"服务已停止，正在重新启动..."状态
5. **等待间隔**: 等待1秒确保资源完全释放
6. **启动推流**: 调用`webrtcManager.startVideoSource(context)`
7. **完成回调**: 发送"推流服务已重启"状态

### 状态回调序列
```
25% - "正在重启推流服务..."
50% - "正在停止推流服务..." (stopService内部)
75% - "服务已停止，正在重新启动..."
50% - "正在启动推流服务..." (startService内部)
100% - "推流服务已重启"
```

## 🧪 测试场景

### 1. 主按钮测试
| 设备状态 | 按钮文本 | 发送命令 | 预期行为 |
|----------|----------|----------|----------|
| 在线 | 重启服务 | `restart_service` | 先停止后启动推流 |
| 离线 | 启动服务 | `start_service` | 直接启动推流 |

### 2. 下拉菜单测试
下拉菜单中的服务控制按钮：
- 🚀 启动服务 → `start_service`
- ⏹️ 停止服务 → `stop_service`  
- 🔄 重启服务 → `restart_service`

### 3. 命令处理测试
```json
// 重启服务命令格式
{
  "type": "command",
  "target": "device_id",
  "command": "restart_service",
  "params": {}
}
```

## 🔍 验证步骤

### 步骤1: Web端验证
1. 打开管理控制台
2. 找到一个在线设备
3. 确认主按钮显示"重启服务"
4. 点击按钮，检查浏览器开发者工具Network标签
5. 确认发送的命令是`restart_service`

### 步骤2: Android端验证
1. 在Android设备上查看日志
2. 点击重启服务按钮
3. 确认日志显示以下序列：
   ```
   🔄 重启服务
   🛑 停止服务
   ✅ 服务停止成功
   🚀 开始服务
   ✅ 服务启动成功
   ```

### 步骤3: 推流状态验证
1. 在重启前确认设备正在推流
2. 点击重启服务
3. 观察推流是否短暂中断后恢复
4. 确认重启后推流正常工作

## 📊 测试用例

### 用例1: 正常重启
- **前置条件**: 设备在线且正在推流
- **操作**: 点击"重启服务"按钮
- **预期结果**: 
  - 推流短暂停止（约1-2秒）
  - 推流恢复正常
  - 状态显示"推流服务已重启"

### 用例2: 离线设备启动
- **前置条件**: 设备离线
- **操作**: 点击"启动服务"按钮
- **预期结果**:
  - 直接启动推流服务
  - 状态显示"推流服务已启动"

### 用例3: 重启失败处理
- **前置条件**: 设备在线但推流异常
- **操作**: 点击"重启服务"按钮
- **预期结果**:
  - 显示具体的错误信息
  - 状态显示"重启服务异常: [错误详情]"

## 🚨 注意事项

### 1. 推流中断
- 重启服务会导致短暂的推流中断（约1-2秒）
- 观看端可能会短暂断开连接
- 这是正常现象，符合"先停后启"的设计

### 2. 资源释放
- 1秒的等待时间确保WebRTC资源完全释放
- 避免资源冲突导致的启动失败

### 3. 错误处理
- 如果停止失败，不会尝试启动
- 如果启动失败，会返回相应的错误状态
- 所有异常都会记录详细日志

## ✅ 验证清单

- [x] Web端主按钮命令修复
- [x] Android端重启逻辑验证
- [x] 命令分发路由确认
- [x] 状态回调序列检查
- [x] 错误处理机制验证
- [x] 测试用例设计完成

**修复状态**: 🎉 **完成** - 重启服务功能已正确实现先停后启的逻辑
