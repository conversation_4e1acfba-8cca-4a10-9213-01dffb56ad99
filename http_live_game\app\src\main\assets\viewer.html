<!DOCTYPE html>
<html>
<head>
    <title>MJPEG流查看器</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        h1 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .stream-container { margin-top: 20px; }
        img { max-width: 100%; border: 1px solid #ddd; }
        .status { margin-top: 10px; padding: 10px; background: #f5f5f5; border: 1px solid #ddd; }
        .button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; margin-top: 10px; }
        .input-group { margin-top: 20px; }
        input[type="text"] { padding: 8px; width: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MJPEG流查看器</h1>
        
        <div class="input-group">
            <label for="streamUrl">流URL:</label>
            <input type="text" id="streamUrl" placeholder="http://192.168.x.x:8080/stream.mjpeg">
            <button onclick="connectStream()" class="button">连接</button>
        </div>
        
        <div class="stream-container">
            <img id="mjpegPlayer" src="" alt="MJPEG Stream" style="display: none;">
        </div>
        
        <div class="status" id="statusText">请输入流URL并点击连接</div>
        
        <div class="input-group">
            <button onclick="refreshStream()" class="button">刷新流</button>
            <button onclick="detectIp()" class="button">检测IP</button>
        </div>
    </div>
    
    <script>
        const mjpegPlayer = document.getElementById('mjpegPlayer');
        const streamUrlInput = document.getElementById('streamUrl');
        const statusText = document.getElementById('statusText');
        
        // 尝试检测本地IP地址
        function detectIp() {
            statusText.textContent = "正在检测本地IP地址...";
            
            // 尝试常见的IP地址和端口
            const commonIps = [
                "192.168.1", "192.168.0", "192.168.2", "192.168.3", "192.168.4",
                "10.0.0", "10.0.1", "10.0.2", "172.16.0", "172.17.0"
            ];
            
            const ports = ["8080", "8081", "8082", "8083", "8084", "8085", "8086", "8087", "8088", "8089", "8090"];
            
            let suggestions = "";
            
            for (const ipBase of commonIps) {
                for (let i = 1; i <= 10; i++) {
                    const ip = `${ipBase}.${i}`;
                    for (const port of ports) {
                        suggestions += `http://${ip}:${port}/stream.mjpeg\n`;
                    }
                }
            }
            
            statusText.innerHTML = "可能的URL列表:<br><small>请尝试以下URL（点击连接前复制到上面的输入框）:</small><br>" +
                                  "<textarea style='width:100%; height:200px;'>" + suggestions + "</textarea>";
        }
        
        // 连接到流
        function connectStream() {
            const url = streamUrlInput.value.trim();
            
            if (!url) {
                statusText.textContent = "请输入有效的URL";
                return;
            }
            
            statusText.textContent = `正在连接到 ${url}...`;
            mjpegPlayer.style.display = "none";
            
            // 添加时间戳防止缓存
            mjpegPlayer.src = url + (url.includes('?') ? '&' : '?') + 't=' + new Date().getTime();
            mjpegPlayer.style.display = "block";
            
            // 设置事件监听器
            mjpegPlayer.onload = function() {
                statusText.textContent = `已连接到 ${url}`;
            };
            
            mjpegPlayer.onerror = function() {
                statusText.textContent = `连接失败: ${url}`;
                mjpegPlayer.style.display = "none";
            };
        }
        
        // 刷新流
        function refreshStream() {
            if (mjpegPlayer.src) {
                const url = mjpegPlayer.src.split('?')[0]; // 移除查询参数
                mjpegPlayer.src = url + '?t=' + new Date().getTime();
                statusText.textContent = `正在刷新流...`;
            } else {
                statusText.textContent = "请先连接到流";
            }
        }
        
        // 初始化
        window.onload = function() {
            // 尝试从URL参数获取流地址
            const urlParams = new URLSearchParams(window.location.search);
            const streamParam = urlParams.get('stream');
            
            if (streamParam) {
                streamUrlInput.value = streamParam;
                connectStream();
            }
        };
    </script>
</body>
</html>
