{"logs": [{"outputFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\miniupnpc\\android_test\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-v21\\values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25012fcc08d4c5cf5a2844c1a21a6fbe\\transformed\\material-1.4.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,25,28,31,34,37,40,41,44,49,60,66,72,78,84,90,91,92,93,97,100,103,106,109,113,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,290,378,466,554,641,728,815,902,995,1102,1207,1326,1539,1798,2069,2287,2519,2755,3005,3236,3352,3522,3843,4872,5329,5671,6015,6365,6715,6853,6997,7153,7546,7764,7986,8212,8428,8669,8928", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,18,21,24,27,30,33,36,39,40,43,48,59,65,71,77,83,89,90,91,92,96,99,102,105,108,112,116,119", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "118,197,285,373,461,549,636,723,810,897,990,1097,1202,1321,1534,1793,2064,2282,2514,2750,3000,3231,3347,3517,3838,4867,5324,5666,6010,6360,6710,6848,6992,7148,7541,7759,7981,8207,8423,8664,8923,9100"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,81,84,87,182,185,188,191,197,264,265,268,273,284,332,338,344,350,356,357,358,359,363,366,369,372,383,387,391", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,241,320,408,496,584,672,759,846,933,6582,6675,6782,6887,7109,7322,7581,14194,14412,14644,14880,15329,20116,20232,20402,20723,21752,25054,25396,25740,26090,26440,26578,26722,26878,27271,27489,27711,27937,28677,28918,29177", "endLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,83,86,89,184,187,190,193,199,264,267,272,283,289,337,343,349,355,356,357,358,362,365,368,371,374,386,390,393", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "236,315,403,491,579,667,754,841,928,1015,6670,6777,6882,7001,7317,7576,7847,14407,14639,14875,15125,15555,20227,20397,20718,21747,22204,25391,25735,26085,26435,26573,26717,26873,27266,27484,27706,27932,28148,28913,29172,29349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\255d63c0f9f8f5d12ab3191ffb377750\\transformed\\core-1.5.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,13,14,15,328,329,330,331,375,378", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1020,1084,1151,24558,24674,24800,24926,28153,28325", "endLines": "2,13,14,15,328,329,330,331,377,382", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1079,1146,1210,24669,24795,24921,25049,28320,28672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ecd700c773cf5624732e3577602f1ea\\transformed\\appcompat-1.3.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8499,8684,11670,11867,12066,12189,12312,12425,12608,12863,13064,13153,13264,13497,13598,13693,13816,13945,14062,14239,14338,14473,14616,14751,14870,15071,15190,15283,15394,15450,15557,15752,15863,15996,16091,16182,16273,16366,16483,16622,16693,16776,17456,17513,17571,18265", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8494,8679,11665,11862,12061,12184,12307,12420,12603,12858,13059,13148,13259,13492,13593,13688,13811,13940,14057,14234,14333,14468,14611,14746,14865,15066,15185,15278,15389,15445,15552,15747,15858,15991,16086,16177,16268,16361,16478,16617,16688,16771,17451,17508,17566,18260,18966"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,33,34,35,36,38,40,41,42,43,44,46,48,50,52,54,56,57,62,64,66,67,68,70,72,73,74,75,80,90,133,136,179,194,200,202,204,206,209,213,216,217,218,221,222,223,224,225,226,229,230,232,234,236,238,242,244,245,246,247,249,253,255,257,258,259,260,261,262,290,291,292,302,303,304,316", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1215,1306,1409,1512,1617,1724,1833,1942,2051,2160,2269,2376,2479,2598,2753,2908,3013,3134,3235,3382,3523,3626,3745,3852,3955,4110,4281,4430,4595,4752,4903,5022,5373,5522,5671,5783,5930,6083,6230,6305,6394,6481,7006,7852,10826,11011,13997,15130,15560,15683,15806,15919,16102,16357,16558,16647,16758,16991,17092,17187,17310,17439,17556,17733,17832,17967,18110,18245,18364,18565,18684,18777,18888,18944,19051,19246,19357,19490,19585,19676,19767,19860,19977,22209,22280,22363,23043,23100,23158,23852", "endLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,30,32,33,34,35,37,39,40,41,42,43,45,47,49,51,53,55,56,61,63,65,66,67,69,71,72,73,74,75,80,132,135,178,181,196,201,203,205,208,212,215,216,217,220,221,222,223,224,225,228,229,231,233,235,237,241,243,244,245,246,248,252,254,256,257,258,259,260,261,263,290,291,301,302,303,315,327", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1301,1404,1507,1612,1719,1828,1937,2046,2155,2264,2371,2474,2593,2748,2903,3008,3129,3230,3377,3518,3621,3740,3847,3950,4105,4276,4425,4590,4747,4898,5017,5368,5517,5666,5778,5925,6078,6225,6300,6389,6476,6577,7104,10821,11006,13992,14189,15324,15678,15801,15914,16097,16352,16553,16642,16753,16986,17087,17182,17305,17434,17551,17728,17827,17962,18105,18240,18359,18560,18679,18772,18883,18939,19046,19241,19352,19485,19580,19671,19762,19855,19972,20111,22275,22358,23038,23095,23153,23847,24553"}}]}]}