package com.example.webrtcsender.service

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.example.webrtcsender.utils.Logger

/**
 * 升级监控服务
 * 独立运行，监听应用安装完成事件并自动启动应用
 */
class UpgradeWatcherService : Service() {
    
    companion object {
        private const val TAG = "UpgradeWatcherService"
        private const val EXTRA_TARGET_VERSION = "target_version"
        private const val EXTRA_PACKAGE_NAME = "package_name"
        
        /**
         * 启动升级监控服务
         */
        fun startWatching(context: Context, targetVersion: String, packageName: String) {
            val intent = Intent(context, UpgradeWatcherService::class.java).apply {
                putExtra(EXTRA_TARGET_VERSION, targetVersion)
                putExtra(EXTRA_PACKAGE_NAME, packageName)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            
            Logger.i(TAG, "🔍 启动升级监控服务，目标版本: $targetVersion")
        }
        
        /**
         * 停止升级监控服务
         */
        fun stopWatching(context: Context) {
            val intent = Intent(context, UpgradeWatcherService::class.java)
            context.stopService(intent)
            Logger.i(TAG, "🛑 停止升级监控服务")
        }
    }
    
    private var installReceiver: BroadcastReceiver? = null
    private var targetVersion: String? = null
    private var targetPackageName: String? = null
    private var versionCheckHandler: Handler? = null
    private var versionCheckRunnable: Runnable? = null
    private var checkCount = 0
    private val maxChecks = 24 // 2分钟，每5秒检查一次
    
    override fun onCreate() {
        super.onCreate()
        Logger.i(TAG, "🔍 升级监控服务创建")
        versionCheckHandler = Handler(Looper.getMainLooper())
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        targetVersion = intent?.getStringExtra(EXTRA_TARGET_VERSION)
        targetPackageName = intent?.getStringExtra(EXTRA_PACKAGE_NAME) ?: packageName
        
        Logger.i(TAG, "🔍 开始监控升级，目标版本: $targetVersion, 包名: $targetPackageName")
        
        // 注册安装完成广播接收器
        registerInstallReceiver()
        
        // 启动版本检查轮询
        startVersionCheckPolling()
        
        // 设置超时停止（5分钟后自动停止服务）
        versionCheckHandler?.postDelayed({
            Logger.w(TAG, "⏰ 升级监控超时，停止服务")
            stopSelf()
        }, 300000) // 5分钟
        
        return START_NOT_STICKY // 不需要系统重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Logger.i(TAG, "🔍 升级监控服务销毁")
        
        // 清理资源
        unregisterInstallReceiver()
        stopVersionCheckPolling()
    }
    
    /**
     * 注册安装完成广播接收器
     */
    private fun registerInstallReceiver() {
        try {
            unregisterInstallReceiver()
            
            installReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    try {
                        val action = intent?.action
                        val packageName = intent?.data?.schemeSpecificPart
                        
                        Logger.d(TAG, "📡 收到广播: action=$action, packageName=$packageName, target=$targetPackageName")
                        
                        when (action) {
                            Intent.ACTION_PACKAGE_REPLACED,
                            Intent.ACTION_PACKAGE_ADDED -> {
                                if (packageName == targetPackageName) {
                                    Logger.i(TAG, "✅ 目标应用安装完成，准备自动启动")
                                    
                                    // 延迟启动应用
                                    versionCheckHandler?.postDelayed({
                                        autoStartApplication()
                                    }, 2000)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "❌ 处理安装广播失败", e)
                    }
                }
            }
            
            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_PACKAGE_ADDED)
                addAction(Intent.ACTION_PACKAGE_REPLACED)
                addDataScheme("package")
            }
            
            registerReceiver(installReceiver, filter)
            Logger.d(TAG, "📡 已注册安装完成广播接收器")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 注册安装广播接收器失败", e)
        }
    }
    
    /**
     * 注销安装完成广播接收器
     */
    private fun unregisterInstallReceiver() {
        try {
            installReceiver?.let {
                unregisterReceiver(it)
                installReceiver = null
                Logger.d(TAG, "📡 已注销安装完成广播接收器")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 注销安装广播接收器失败", e)
        }
    }
    
    /**
     * 启动版本检查轮询
     */
    private fun startVersionCheckPolling() {
        val currentVersion = getCurrentVersion()
        
        Logger.d(TAG, "🔄 启动版本检查轮询，当前版本: $currentVersion, 目标版本: $targetVersion")
        
        versionCheckRunnable = object : Runnable {
            override fun run() {
                checkCount++
                val newVersion = getCurrentVersion()
                
                Logger.d(TAG, "🔄 版本检查 ($checkCount/$maxChecks): 当前版本=$newVersion, 目标版本=$targetVersion")
                
                if (newVersion != currentVersion && newVersion == targetVersion) {
                    // 版本已更新，说明安装完成
                    Logger.i(TAG, "✅ 检测到版本更新，应用安装完成")
                    
                    // 延迟启动应用
                    versionCheckHandler?.postDelayed({
                        autoStartApplication()
                    }, 2000)
                    
                } else if (checkCount < maxChecks) {
                    // 继续检查
                    versionCheckHandler?.postDelayed(this, 5000)
                } else {
                    // 检查超时
                    Logger.w(TAG, "⏰ 版本检查超时，停止监控")
                    stopSelf()
                }
            }
        }
        
        // 延迟10秒开始检查（给安装一些时间）
        versionCheckHandler?.postDelayed(versionCheckRunnable!!, 10000)
    }
    
    /**
     * 停止版本检查轮询
     */
    private fun stopVersionCheckPolling() {
        versionCheckRunnable?.let {
            versionCheckHandler?.removeCallbacks(it)
            versionCheckRunnable = null
        }
    }
    
    /**
     * 获取当前应用版本
     */
    private fun getCurrentVersion(): String {
        return try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.getPackageInfo(targetPackageName!!, PackageManager.PackageInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(targetPackageName!!, 0)
            }
            packageInfo.versionName ?: "unknown"
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 获取版本失败", e)
            "unknown"
        }
    }
    
    /**
     * 自动启动应用
     */
    private fun autoStartApplication() {
        try {
            Logger.i(TAG, "🚀 自动启动应用: $targetPackageName")
            
            val packageManager = packageManager
            val launchIntent = packageManager.getLaunchIntentForPackage(targetPackageName!!)
            
            if (launchIntent != null) {
                launchIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                startActivity(launchIntent)
                
                Logger.i(TAG, "✅ 应用启动成功")
                
                // 启动成功后停止服务
                versionCheckHandler?.postDelayed({
                    stopSelf()
                }, 3000)
                
            } else {
                Logger.e(TAG, "❌ 无法获取应用启动Intent")
                stopSelf()
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 自动启动应用失败", e)
            stopSelf()
        }
    }
}
