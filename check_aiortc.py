#!/usr/bin/env python3
# check_aiortc.py - 检查aiortc库是否正确安装

try:
    import aiortc
    print(f"aiortc库已安装，版本: {aiortc.__version__}")
    
    # 检查关键类
    from aiortc import RTCPeerConnection, RTCSessionDescription, RTCConfiguration
    print("成功导入关键类")
    
    # 创建配置
    config = RTCConfiguration()
    config.iceServers = [{"urls": "stun:stun.l.google.com:19302"}]
    print("成功创建配置")
    
    # 创建对等连接
    pc = RTCPeerConnection(configuration=config)
    print("成功创建对等连接")
    
    # 创建数据通道
    dc = pc.createDataChannel("test")
    print("成功创建数据通道")
    
    print("\naiortc库测试成功!")
except ImportError:
    print("aiortc库未安装，请运行: pip install aiortc")
except Exception as e:
    print(f"测试aiortc库时出错: {e}")
