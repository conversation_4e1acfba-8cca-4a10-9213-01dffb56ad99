package com.example.webrtcsender

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.FirstInstallConfigManager
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.utils.DeviceLogManager
import com.example.webrtcsender.webrtc.WebRTCManager
import org.webrtc.PeerConnectionFactory

class WebRTCSenderApp : Application() {

    companion object {
        private const val TAG = "WebRTCSenderApp"
        lateinit var instance: WebRTCSenderApp
            private set
    }

    // 共享偏好设置
    lateinit var preferences: SharedPreferences

    override fun onCreate() {
        super.onCreate()
        instance = this

        // 设置日志过滤，减少MediaCodec相关的调试日志
        try {
            System.setProperty("log.tag.CCodec", "WARN")
            System.setProperty("log.tag.CCodecConfig", "WARN")
            System.setProperty("log.tag.MediaCodec", "WARN")
        } catch (e: Exception) {
            // 忽略设置失败
        }

        // 初始化共享偏好设置
        preferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)

        // 检查是否首次安装并设置默认配置
        FirstInstallConfigManager.setupFirstInstallDefaults(this, preferences)

        // 确保日志显示设置在应用启动时正确加载
        Logger.loadDisplaySettingFromPreferences(this)

        // 检查每日重启计数（会自动重置新的一天）
        val rebootCount = DeviceLogManager.getInstance().checkDailyRebootCount(this)
        Logger.i(TAG, "📊 今日重启计数: $rebootCount")

        // 初始化WebRTC
        initWebRTC()

        Log.d(TAG, "应用已初始化")
    }



    private fun initWebRTC() {
        Log.d(TAG, "初始化WebRTC")

        try {
            // 初始化日志
            val logDir = getExternalFilesDir("logs")
            if (logDir != null) {
                Logger.init(logDir, true)
            }

            // 初始化PeerConnectionFactory
            PeerConnectionFactory.initialize(
                PeerConnectionFactory.InitializationOptions.builder(this)
                    .setEnableInternalTracer(true)
                    .createInitializationOptions()
            )

            Log.d(TAG, "PeerConnectionFactory已初始化")
        } catch (e: Exception) {
            Log.e(TAG, "初始化WebRTC失败", e)
        }
    }
}
