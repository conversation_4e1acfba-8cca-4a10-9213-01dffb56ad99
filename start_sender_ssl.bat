@echo off
chcp 65001 > nul
echo ===================================
echo    WebRTC视频流发送端启动脚本 (SSL)
echo ===================================
echo.

REM 信令服务器URL
set SIGNALING_URL=wss://sling.91jdcd.com/ws/

REM 检查视频源
echo 正在检查视频源...
set VIDEO_SOURCE=http://192.168.3.168:80/0.mp4
curl -s -I %VIDEO_SOURCE% > nul
if %errorlevel% neq 0 (
    echo 警告: 无法访问视频源 %VIDEO_SOURCE%
    echo 使用测试视频源...
    set VIDEO_SOURCE=test
) else (
    echo 视频源可访问: %VIDEO_SOURCE%
)

REM 启动发送端
echo.
echo 正在启动WebRTC视频流发送端...
echo.
echo 发送端信息:
echo - 信令服务器: %SIGNALING_URL%
echo - 视频源: %VIDEO_SOURCE%
echo.
echo 按Ctrl+C停止发送端
echo.

python web_sender_adapter.py --video %VIDEO_SOURCE% --id video-stream --name "视频流" --description "WebRTC视频流" --signaling %SIGNALING_URL%
