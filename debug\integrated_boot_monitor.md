# 集成开机监控系统

## 功能概述

已将开机信息上报功能集成到现有的 `DeviceInfoReporter` 中，通过 WebSocket 发送，简化了实现。

## 主要改进

### 1. 简化架构

**原来**：独立的 `BootReporter.kt` + HTTP API
```
BootReporter.kt → HTTP POST /api/boot_report → 服务器存储
```

**现在**：集成到 `DeviceInfoReporter.kt` + WebSocket
```
DeviceInfoReporter.kt → WebSocket boot_report → 服务器存储 → 通知监控订阅者
```

### 2. 统一通信方式

**原来**：
- 设备信息：WebSocket
- 开机信息：HTTP API

**现在**：
- 设备信息：WebSocket
- 开机信息：WebSocket（统一）

## 技术实现

### 1. Android端集成

**文件**: `DeviceInfoReporter.kt`

**新增功能**：
```kotlin
// 在连接成功时自动检查开机状态
suspend fun onConnectionEstablished(senderId: String) {
    delay(1000)
    
    // 检查并上报开机信息（第一次开机时）
    checkAndReportBoot(senderId)
    
    // 上报设备信息
    reportDeviceInfo(senderId)
}

// 开机检测逻辑
private suspend fun checkAndReportBoot(senderId: String) {
    val currentBootTime = getSystemBootTime()
    val lastReportedBootTime = prefs.getLong(KEY_BOOT_TIME, 0L)
    
    if (currentBootTime != lastReportedBootTime) {
        // 新开机，上报信息
        reportBootInfo(senderId, currentBootTime)
        // 记录已上报
        prefs.edit().putLong(KEY_BOOT_TIME, currentBootTime).apply()
    }
}
```

**WebSocket消息格式**：
```kotlin
val messageMap = mapOf(
    "type" to "boot_report",
    "sender_id" to senderId,
    "boot_info" to bootInfo.toString(),
    "timestamp" to (System.currentTimeMillis() / 1000)
)
```

### 2. 服务器端处理

**文件**: `enhanced_signaling_server.py`

**新增处理**：
```python
elif data.get('type') == 'boot_report':
    # 处理开机信息上报
    sender_id = data.get('sender_id') or client_id
    boot_info = json.loads(data.get('boot_info', '{}'))
    
    # 存储开机信息
    cpu_unique_id = boot_info.get('cpu_unique_id', '')
    if cpu_unique_id:
        boot_devices[cpu_unique_id] = {
            'cpu_unique_id': cpu_unique_id,
            'sender_id': sender_id,
            'boot_time': boot_info.get('boot_time', 0),
            'device_info': {...},
            'last_update': current_time
        }
        
        # 通知所有监控订阅者
        await send_boot_devices_update()
```

### 3. 监控页面实时更新

**WebSocket通信**：
- 设备开机时自动通过WebSocket上报
- 服务器立即通知所有监控订阅者
- H5页面实时显示新开机设备

## 工作流程

### 1. 设备开机流程

```
设备开机 → 应用启动 → WebRTC连接 → DeviceInfoReporter.onConnectionEstablished()
    ↓
检查开机状态 → 新开机？ → 收集开机信息 → WebSocket发送boot_report
    ↓
服务器接收 → 存储设备信息 → 通知监控订阅者 → H5页面实时显示
```

### 2. 监控页面更新

```
H5页面 → WebSocket连接 → 密码认证 → 订阅开机监控
    ↓
设备开机上报 → 服务器处理 → 推送更新 → 页面实时显示
```

## 数据格式

### 1. 开机上报消息

**WebSocket消息**：
```json
{
    "type": "boot_report",
    "sender_id": "gamev-001",
    "boot_info": "{...}",
    "timestamp": 1692960000
}
```

**开机信息内容**：
```json
{
    "type": "boot_report",
    "timestamp": 1692960000000,
    "boot_time": 1692959000000,
    "sender_id": "gamev-001",
    "cpu_unique_id": "CPU_12345678",
    "device_brand": "Samsung",
    "device_model": "SM-G973F",
    "android_version": "11",
    "app_version": "1.25",
    "game_package": "com.example.game",
    "local_ip": "*************",
    "public_ip": "***********"
}
```

### 2. 服务器确认响应

```json
{
    "type": "boot_report_ack",
    "success": true,
    "timestamp": 1692960000,
    "message": "开机信息已记录",
    "cpu_unique_id": "CPU_12345678"
}
```

### 3. 监控页面更新

```json
{
    "type": "boot_devices_update",
    "success": true,
    "devices": [...],
    "total": 1,
    "timestamp": 1692963600000
}
```

## 优势对比

### 1. 架构简化

| 方面 | 原来 | 现在 |
|------|------|------|
| 文件数量 | 2个独立文件 | 1个集成文件 |
| 通信方式 | WebSocket + HTTP | 纯WebSocket |
| 代码维护 | 分散管理 | 统一管理 |

### 2. 性能提升

| 方面 | 原来 | 现在 |
|------|------|------|
| 连接数 | WebSocket + HTTP | 仅WebSocket |
| 实时性 | HTTP轮询延迟 | WebSocket实时 |
| 资源消耗 | 双重连接 | 单一连接 |

### 3. 功能完整性

- ✅ **第一次开机检测** - 基于系统启动时间
- ✅ **信息完整收集** - CPU ID、设备信息、游戏配置
- ✅ **WebSocket统一通信** - 与设备信息上报共用连接
- ✅ **实时监控更新** - 开机信息立即推送到监控页面
- ✅ **状态持久化** - SharedPreferences记录上报状态

## 使用方法

### 1. 启动服务器
```bash
python3 enhanced_signaling_server.py --log-dir ./logs
```

### 2. 访问监控页面
```
http://your-server:28080/boot_monitor.html
```
密码：`tb###`

### 3. 设备开机
- Android设备开机后应用自动启动
- 连接到信令服务器时自动检测开机状态
- 第一次开机会自动上报信息
- 监控页面实时显示新开机设备

## 日志示例

### Android端日志
```
[DeviceInfoReporter] INFO: 连接建立，立即上报设备信息: gamev-001
[DeviceInfoReporter] DEBUG: 🔍 检查开机状态: 当前启动时间=1692959000000, 上次上报时间=0
[DeviceInfoReporter] INFO: 🚀 检测到新开机，准备上报开机信息
[DeviceInfoReporter] INFO: 📤 开始上报开机信息: gamev-001
[DeviceInfoReporter] INFO: ✅ 开机信息上报成功: gamev-001
```

### 服务器端日志
```
📱 收到开机信息: gamev-001 | CPU_ID=CPU_1234... | 游戏=com.example.game
📤 开机信息确认已发送: gamev-001
📱 开机监控更新已推送给 2 个订阅者
```

## 版本更新

当前版本：v1.25 → v1.26

主要改进：
- 将开机上报集成到DeviceInfoReporter
- 统一使用WebSocket通信
- 简化架构和代码维护
- 提升实时性和性能
- 完善错误处理和日志记录
