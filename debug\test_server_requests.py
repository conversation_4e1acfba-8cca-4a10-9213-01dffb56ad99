#!/usr/bin/env python3
"""
测试不同服务器的请求方式
"""

import asyncio
import aiohttp
import json

# 服务器配置
SERVER_GROUPS = [
    {
        'domain': 'http://testva2.91jdcd.com',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '测试服务器V2'
    },
    {
        'domain': 'http://bsth5.yinmengkj.cn',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '银梦科技服务器'
    },
    {
        'domain': 'http://yx.yhdyc.com',
        'method': 'POST',  # 所有接口都使用POST方式
        'name': '游戏服务器'
    }
]

async def test_server_request(server_config):
    """测试单个服务器的请求"""
    domain = server_config.get('domain', '')
    method = server_config.get('method', 'GET').upper()
    name = server_config.get('name', domain)
    
    api_url = f"{domain}/api/game/list_dcl_ast4"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json'
    }
    
    print(f"\n🔍 测试服务器: {name}")
    print(f"   域名: {domain}")
    print(f"   方法: {method}")
    print(f"   URL: {api_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            if method == 'POST':
                # POST请求
                post_data = {}
                async with session.post(api_url, headers=headers, json=post_data, timeout=10) as response:
                    print(f"   状态码: {response.status}")
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if data.get('code') == 1 and 'data' in data:
                                print(f"   ✅ 成功获取房间信息，共 {len(data['data'])} 个房间")
                                
                                # 显示前3个房间信息
                                for i, room in enumerate(data['data'][:3]):
                                    print(f"      {i+1}. {room.get('name', '')} (ID: {room.get('id', 0)}, 发送端: {room.get('heiqiplayer_sender_id', '无')})")
                                
                                if len(data['data']) > 3:
                                    print(f"      ... 还有 {len(data['data']) - 3} 个房间")
                                
                                return True
                            else:
                                print(f"   ❌ 服务器返回错误: {data.get('msg', '未知错误')}")
                                return False
                        except json.JSONDecodeError:
                            text = await response.text()
                            print(f"   ❌ 响应不是有效的JSON: {text[:100]}...")
                            return False
                    else:
                        text = await response.text()
                        print(f"   ❌ HTTP错误: {text[:100]}...")
                        return False
            else:
                # GET请求
                async with session.get(api_url, headers=headers, timeout=10) as response:
                    print(f"   状态码: {response.status}")
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if data.get('code') == 1 and 'data' in data:
                                print(f"   ✅ 成功获取房间信息，共 {len(data['data'])} 个房间")
                                
                                # 显示前3个房间信息
                                for i, room in enumerate(data['data'][:3]):
                                    print(f"      {i+1}. {room.get('name', '')} (ID: {room.get('id', 0)}, 发送端: {room.get('heiqiplayer_sender_id', '无')})")
                                
                                if len(data['data']) > 3:
                                    print(f"      ... 还有 {len(data['data']) - 3} 个房间")
                                
                                return True
                            else:
                                print(f"   ❌ 服务器返回错误: {data.get('msg', '未知错误')}")
                                return False
                        except json.JSONDecodeError:
                            text = await response.text()
                            print(f"   ❌ 响应不是有效的JSON: {text[:100]}...")
                            return False
                    else:
                        text = await response.text()
                        print(f"   ❌ HTTP错误: {text[:100]}...")
                        return False
                        
    except asyncio.TimeoutError:
        print(f"   ❌ 请求超时")
        return False
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False

async def test_all_servers():
    """测试所有服务器"""
    print("🚀 开始测试所有服务器的房间信息接口...")
    
    success_count = 0
    total_count = len(SERVER_GROUPS)
    
    for server_config in SERVER_GROUPS:
        success = await test_server_request(server_config)
        if success:
            success_count += 1
    
    print(f"\n📊 测试完成:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("   🎉 所有服务器都正常!")
    elif success_count > 0:
        print("   ⚠️ 部分服务器有问题，请检查配置")
    else:
        print("   ❌ 所有服务器都无法访问")

async def test_both_methods(domain):
    """测试同一个服务器的GET和POST方法"""
    print(f"\n🔄 测试服务器 {domain} 的不同请求方法:")
    
    api_url = f"{domain}/api/game/list_dcl_ast4"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json'
    }
    
    async with aiohttp.ClientSession() as session:
        # 测试GET
        print("   测试GET请求...")
        try:
            async with session.get(api_url, headers=headers, timeout=10) as response:
                print(f"   GET状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == 1:
                        print(f"   GET成功: {len(data.get('data', []))} 个房间")
                    else:
                        print(f"   GET错误: {data.get('msg', '未知错误')}")
                else:
                    print(f"   GET失败: HTTP {response.status}")
        except Exception as e:
            print(f"   GET异常: {e}")
        
        # 测试POST
        print("   测试POST请求...")
        try:
            async with session.post(api_url, headers=headers, json={}, timeout=10) as response:
                print(f"   POST状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    if data.get('code') == 1:
                        print(f"   POST成功: {len(data.get('data', []))} 个房间")
                    else:
                        print(f"   POST错误: {data.get('msg', '未知错误')}")
                else:
                    print(f"   POST失败: HTTP {response.status}")
        except Exception as e:
            print(f"   POST异常: {e}")

if __name__ == "__main__":
    # 测试所有配置的服务器
    asyncio.run(test_all_servers())
    
    # 单独测试有问题的服务器
    print("\n" + "="*50)
    asyncio.run(test_both_methods("http://yx.yhdyc.com"))
