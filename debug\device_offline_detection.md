# 设备断线检测和游戏名称优化

## 功能概述

添加了实时设备断线检测功能，并优化了游戏名称显示，支持包含匹配的内置游戏。

## 主要改进

### 1. 游戏名称包含匹配

**修改前**：精确匹配完整包名
```javascript
const gameNames = {
    'com.tencent.tmgp.pubgmhd': '和平精英',
    'com.tencent.tmgp.sgame': '王者荣耀',
    ...
};
return gameNames[packageName] || packageName;
```

**修改后**：包含匹配关键字
```javascript
const gameKeywords = {
    'BirdKing': '鸟王',
    'ocean3': '海王',
    '.example.demo': '一筒天下',
    'WaterMargin': '水浒传'
};

// 检查包名是否包含关键字
for (const [keyword, displayName] of Object.entries(gameKeywords)) {
    if (packageName.toLowerCase().includes(keyword.toLowerCase())) {
        return displayName;
    }
}
```

### 2. 设备在线状态显示

**新增状态指示器**：
```html
<span class="status-badge ${device.is_online === false ? 'offline' : 'online'}">
    ${device.is_online === false ? '🔴 离线' : '🟢 在线'}
</span>
```

**离线设备样式**：
```css
.device-item.offline {
    background: #f8f9fa;
    border: 2px solid #dc3545;
    opacity: 0.7;
}

.status-badge.offline {
    background: #f8d7da;
    color: #721c24;
}
```

### 3. 服务器端断线检测

**心跳状态管理**：
```python
# 设备在线状态管理
device_online_status = {}  # 格式: {sender_id: {cpu_unique_id, last_heartbeat, is_online}}

# 心跳处理中更新状态
if from_id.startswith('gamev-'):
    device_online_status[from_id] = {
        'cpu_unique_id': cpu_id,
        'last_heartbeat': server_time,
        'is_online': True
    }
```

**离线检测逻辑**：
```python
async def check_device_offline():
    current_time = time.time()
    offline_devices = []
    
    for sender_id, status in device_online_status.items():
        if status['is_online'] and current_time - status['last_heartbeat'] > 60:
            # 设备离线（超过60秒没有心跳）
            status['is_online'] = False
            offline_devices.append(sender_id)
    
    # 通知监控订阅者
    if offline_devices and boot_monitor_subscribers:
        await send_boot_devices_update()
```

## 支持的内置游戏

| 关键字 | 中文名称 | 匹配示例 |
|--------|----------|----------|
| BirdKing | 鸟王 | com.example.BirdKing |
| ocean3 | 海王 | com.game.ocean3.main |
| .example.demo | 一筒天下 | com.company.example.demo |
| WaterMargin | 水浒传 | com.game.WaterMargin.android |

## 显示效果

### 在线设备
```
┌─────────────────────────────────────┐
│ CPU ID: CPU_12345678901234567890 🟢 在线 │ 已开机3分9秒
│ 发送端: gamev-b246c42d | 游戏: 鸟王      │
├─────────────────────────────────────┤
│ 📱 设备: Allwinner A527 PRO        │
│ 🤖 系统: Android 13                │
│ ...                                 │
└─────────────────────────────────────┘
```

### 离线设备
```
┌─────────────────────────────────────┐ (灰色背景，红色边框)
│ CPU ID: CPU_12345678901234567890 🔴 离线 │ 已开机3分9秒
│ 发送端: gamev-b246c42d | 游戏: 海王      │
├─────────────────────────────────────┤
│ 📱 设备: Allwinner A527 PRO        │
│ 🤖 系统: Android 13                │
│ ...                                 │
└─────────────────────────────────────┘
```

## 技术实现

### 1. 前端状态显示

**设备卡片样式**：
```javascript
<div class="device-item ${device.is_online === false ? 'offline' : ''}">
    <div class="device-id">
        <div>
            CPU ID: ${device.cpu_unique_id}
            <span class="status-badge ${device.is_online === false ? 'offline' : 'online'}">
                ${device.is_online === false ? '🔴 离线' : '🟢 在线'}
            </span>
        </div>
    </div>
</div>
```

### 2. 服务器端状态管理

**数据结构**：
```python
device_online_status = {
    "gamev-b246c42d": {
        "cpu_unique_id": "CPU_12345678901234567890",
        "last_heartbeat": 1692960000,
        "is_online": True
    }
}
```

**状态检查**：
```python
# 在send_boot_devices_update中检查状态
is_online = True
if sender_id in device_online_status:
    last_heartbeat = device_online_status[sender_id]['last_heartbeat']
    if current_time / 1000 - last_heartbeat > 60:
        is_online = False
        device_online_status[sender_id]['is_online'] = False

device_data['is_online'] = is_online
```

### 3. 定时检查任务

**后台任务**：
```python
async def device_offline_checker():
    while True:
        await asyncio.sleep(30)  # 每30秒检查一次
        await check_device_offline()

# 启动任务
device_offline_task = asyncio.create_task(device_offline_checker())
```

## 断线检测机制

### 1. 心跳超时判断
- **心跳间隔**：Android端每30秒发送一次心跳
- **超时阈值**：60秒没有心跳认为离线
- **检查频率**：服务器每30秒检查一次

### 2. 状态更新流程
```
设备发送心跳 → 服务器更新last_heartbeat → 定时检查超时 → 标记离线 → 通知监控页面
```

### 3. 实时通知
- 设备离线时立即通知所有监控订阅者
- 监控页面实时更新设备状态
- 离线设备显示特殊样式

## 游戏匹配规则

### 1. 包含匹配逻辑
```javascript
function getGameDisplayName(packageName) {
    if (!packageName) return '未配置';
    
    // 不区分大小写的包含匹配
    for (const [keyword, displayName] of Object.entries(gameKeywords)) {
        if (packageName.toLowerCase().includes(keyword.toLowerCase())) {
            return displayName;
        }
    }
    
    return packageName;  // 未匹配时返回原包名
}
```

### 2. 匹配示例
- `com.example.BirdKing.v1.0` → **鸟王**
- `cn.game.ocean3.mobile` → **海王**
- `com.company.example.demo.test` → **一筒天下**
- `org.game.WaterMargin.hd` → **水浒传**

## 扩展性

### 1. 添加新游戏
```javascript
const gameKeywords = {
    // 现有游戏...
    'newgame': '新游戏名称',
    'anothergame': '另一个游戏',
};
```

### 2. 调整超时时间
```python
# 修改超时阈值（秒）
DEVICE_OFFLINE_TIMEOUT = 60

# 修改检查频率（秒）
OFFLINE_CHECK_INTERVAL = 30
```

### 3. 自定义状态样式
```css
.device-item.offline {
    /* 自定义离线设备样式 */
}

.status-badge.offline {
    /* 自定义离线状态徽章样式 */
}
```

## 故障排除

### 1. 设备显示离线但实际在线
- 检查心跳发送是否正常
- 确认网络连接稳定
- 查看服务器日志中的心跳记录

### 2. 游戏名称显示不正确
- 确认包名包含正确的关键字
- 检查大小写匹配
- 查看浏览器控制台错误

### 3. 状态更新不及时
- 检查WebSocket连接状态
- 确认定时任务正常运行
- 查看服务器后台任务日志

## 版本更新

当前版本：v1.30 → v1.31

主要改进：
- 添加实时设备断线检测
- 游戏名称支持包含匹配
- 优化设备状态显示
- 完善后台监控任务
- 提升用户体验和可靠性
