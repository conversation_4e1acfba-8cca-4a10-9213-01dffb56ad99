<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WebRTC 移动端视频流接收端</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        h1 {
            font-size: 1.5rem;
            text-align: center;
            margin: 10px 0;
        }
        .video-container {
            position: relative;
            width: 100%;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }
        .input-group {
            display: flex;
            flex-direction: row;
            gap: 5px;
        }
        input, button, select {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            -webkit-appearance: none;
        }
        input {
            flex: 1;
            min-width: 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
            min-width: 80px;
        }
        button:hover, button:active {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
            font-size: 14px;
        }
        .error {
            border-left-color: #f44336;
            color: #f44336;
        }
        .log-container {
            max-height: 150px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-time {
            color: #666;
            margin-right: 5px;
        }
        .log-info {
            color: #2196F3;
        }
        .log-error {
            color: #f44336;
        }
        .log-success {
            color: #4CAF50;
        }
        .sender-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 10px 0;
        }
        .sender-item {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
            flex: 1;
            min-width: 120px;
            text-align: center;
        }
        .sender-item:hover, .sender-item:active {
            background-color: #e0e0e0;
        }
        .sender-item.active {
            background-color: #4CAF50;
            color: white;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .fullscreen-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
        }
        .fullscreen-btn svg {
            width: 24px;
            height: 24px;
        }
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 20px;
            display: none;
        }
        .loading-indicator.active {
            display: block;
        }

        /* 视频控制样式 */
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .video-container:hover .video-controls,
        .video-controls:hover,
        .video-controls.active {
            opacity: 1;
        }

        .control-btn {
            background: transparent;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            cursor: pointer;
            padding: 8px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .control-btn svg {
            width: 24px;
            height: 24px;
        }

        .seek-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .seek-slider {
            width: 100%;
            height: 4px;
            -webkit-appearance: none;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
            border-radius: 2px;
            margin-bottom: 5px;
        }

        .seek-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
        }

        .time-display {
            color: white;
            font-size: 12px;
            text-align: right;
        }

        /* 针对移动设备的优化 */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
            }
            h1 {
                font-size: 1.2rem;
                margin: 5px 0;
            }
            .controls {
                gap: 5px;
            }
            input, button, select {
                padding: 10px;
                font-size: 14px;
            }
            .status {
                padding: 8px;
                font-size: 12px;
            }
            .log-container {
                max-height: 100px;
                font-size: 10px;
            }
            .sender-item {
                padding: 8px;
                font-size: 12px;
                min-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC 移动端视频流接收端</h1>

        <div class="video-container">
            <video id="video" autoplay playsinline></video>
            <button class="fullscreen-btn" id="fullscreen-btn">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                </svg>
            </button>
            <div class="loading-indicator" id="loading-indicator">连接中...</div>

            <!-- 视频控制面板 -->
            <div class="video-controls" id="video-controls" style="display: none;">
                <button id="play-pause-btn" class="control-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="play-icon">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="pause-icon" style="display:none;">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                </button>
                <div class="seek-container">
                    <input type="range" id="seek-slider" min="0" max="100" value="0" step="1" class="seek-slider">
                    <div class="time-display">
                        <span id="current-time">00:00</span> / <span id="duration">00:00</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="input-group">
                <input type="text" id="signaling-url" value="wss://sling.91jdcd.com/ws/" placeholder="信令服务器URL">
            </div>
            <div class="input-group">
                <input type="text" id="sender-id" value="android-5799e3cb">
            </div>
            <div class="button-group">
                <button id="connect-btn">连接</button>
                <button id="disconnect-btn" disabled>断开</button>
            </div>
        </div>

        <div class="status" id="status">
            未连接
        </div>

        <div class="sender-list" id="sender-list">
            <!-- 发送端列表将在这里动态生成 -->
        </div>

        <div class="log-container" id="log">
            <!-- 日志将在这里动态生成 -->
        </div>
    </div>

    <script>
        
        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' },
                {
                    urls: 'turn:numb.viagenie.ca',
                    username: '<EMAIL>',
                    credential: 'muazkh'
                }
            ],
            
            iceTransportPolicy: 'all',
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require',
            sdpSemantics: 'unified-plan'
        };

        
        const signalingUrlInput = document.getElementById('signaling-url');
        const senderIdInput = document.getElementById('sender-id');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const statusEl = document.getElementById('status');
        const videoEl = document.getElementById('video');
        const logEl = document.getElementById('log');
        const senderListEl = document.getElementById('sender-list');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const loadingIndicator = document.getElementById('loading-indicator');

        
        const videoControls = document.getElementById('video-controls');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const playIcon = playPauseBtn.querySelector('.play-icon');
        const pauseIcon = playPauseBtn.querySelector('.pause-icon');
        const seekSlider = document.getElementById('seek-slider');
        const currentTimeEl = document.getElementById('current-time');
        const durationEl = document.getElementById('duration');

        
        let pc = null;
        let dataChannel = null;
        let ws = null;
        let receiverId = `receiver-${Math.random().toString(36).substring(2, 10)}`;
        let activeSenderId = null;
        let senders = [];
        let connectionTimeout = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 3;

        
        let isPlaying = true;
        let videoDuration = 0;
        let currentPosition = 0;
        let isLocalFile = false;
        let seekUpdateInterval = null;
        let controlsTimeout = null;

        
        function log(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';

            const timeSpan = document.createElement('span');
            timeSpan.className = 'log-time';
            timeSpan.textContent = timeStr;

            const messageSpan = document.createElement('span');
            messageSpan.className = `log-${type}`;
            messageSpan.textContent = message;

            logEntry.appendChild(timeSpan);
            logEntry.appendChild(messageSpan);

            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type}] ${message}`);
        }

        
        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            if (isError) {
                statusEl.classList.add('error');
            } else {
                statusEl.classList.remove('error');
            }
        }

        
        function setLoading(isLoading) {
            if (isLoading) {
                loadingIndicator.classList.add('active');
            } else {
                loadingIndicator.classList.remove('active');
            }
        }

        
        async function connectToSignalingServer() {
            const url = signalingUrlInput.value;

            try {
                ws = new WebSocket(url);

                ws.onopen = () => {
                    log('已连接到信令服务器');
                    reconnectAttempts = 0;

                    
                    ws.send(JSON.stringify({
                        type: 'register',
                        id: receiverId,
                        role: 'viewer',
                        name: '移动端接收端',
                        description: '移动设备WebRTC接收端'
                    }));
                };

                ws.onmessage = async (event) => {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${data.type}`);

                    if (data.type === 'registered') {
                        updateStatus(`已注册，ID: ${receiverId}`);
                        
                        ws.send(JSON.stringify({
                            type: 'list_clients'
                        }));
                    }
                    else if (data.type === 'client_list') {
                        handleClientList(data.clients || []);
                    }
                    else if (data.type === 'client_joined') {
                        
                        if (data.role === 'source' || data.id.startsWith('sender-')) {
                            addSender(data);
                        }
                    }
                    else if (data.type === 'client_left') {
                        
                        removeSender(data.id);
                    }
                    else if (data.type === 'offer' && data.from) {
                        
                        try {
                            
                            if (activeSenderId && data.from !== activeSenderId) {
                                log(`忽略来自 ${data.from} 的offer，当前连接到 ${activeSenderId}`);
                                return;
                            }

                            
                            if (!activeSenderId) {
                                activeSenderId = data.from;
                                log(`设置活跃发送端为 ${activeSenderId}`);

                                
                                if (!pc) {
                                    pc = new RTCPeerConnection(config);
                                    setupPeerConnection(pc, activeSenderId);
                                    log(`为 ${activeSenderId} 创建了新的PeerConnection`);

                                    


                                }
                            }

                            
                            const offer = new RTCSessionDescription({
                                type: 'offer',
                                sdp: data.sdp
                            });

                            await pc.setRemoteDescription(offer);
                            log('已设置远程描述(offer)');

                            
                            if (window.pendingIceCandidates && window.pendingIceCandidates.length > 0) {
                                log(`添加 ${window.pendingIceCandidates.length} 个缓存的ICE候选`);
                                for (const candidate of window.pendingIceCandidates) {
                                    try {
                                        await pc.addIceCandidate(candidate);
                                        log('已添加缓存的ICE候选');
                                    } catch (e) {
                                        log(`添加缓存的ICE候选错误: ${e.message}`, 'error');
                                    }
                                }
                                window.pendingIceCandidates = [];
                            }

                            
                            const answer = await pc.createAnswer();
                            await pc.setLocalDescription(answer);
                            log('已设置本地描述(answer)');

                            
                            
                            const formattedSdp = pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n');

                            ws.send(JSON.stringify({
                                type: 'answer',
                                target: activeSenderId,
                                sdp: formattedSdp
                            }));
                            log('已发送answer');

                            updateStatus(`正在连接到发送端 ${activeSenderId}...`);
                            setLoading(true);
                        } catch (e) {
                            log(`处理offer错误: ${e.message}`, 'error');
                            updateStatus(`处理offer错误: ${e.message}`, true);
                        }
                    }
                    else if (data.type === 'answer' && data.from === activeSenderId) {
                        
                        try {
                            const answer = new RTCSessionDescription({
                                type: 'answer',
                                sdp: data.sdp
                            });
                            await pc.setRemoteDescription(answer);
                            log('已设置远程描述(answer)');

                            
                            if (window.pendingIceCandidates && window.pendingIceCandidates.length > 0) {
                                log(`添加 ${window.pendingIceCandidates.length} 个缓存的ICE候选`);
                                for (const candidate of window.pendingIceCandidates) {
                                    try {
                                        await pc.addIceCandidate(candidate);
                                        log('已添加缓存的ICE候选');
                                    } catch (e) {
                                        log(`添加缓存的ICE候选错误: ${e.message}`, 'error');
                                    }
                                }
                                window.pendingIceCandidates = [];
                            }

                            
                            setInterval(() => {
                                        pc.getStats(null).then(stats => {
                                            stats.forEach(report => {
                                                if (report.type === 'inbound-rtp' && report.framesPerSecond!=undefined) { // 对于接收流
                                                    console.log(`Frames per second: ${report.framesPerSecond}`);
                                                }
                                                // 也可以检查其他类型的报告，比如outbound-rtp用于发送流
                                            });
                                        });
                                    }, 1000);
                                    
                            if (connectionTimeout) {
                                clearTimeout(connectionTimeout);
                                connectionTimeout = null;
                            }
                        } catch (e) {
                            log(`设置远程描述错误: ${e.message}`, 'error');
                            updateStatus(`设置远程描述错误: ${e.message}`, true);
                        }
                    }
                    else if (data.type === 'candidate' && data.from === activeSenderId) {
                        
                        try {
                            const candidate = data.candidate;
                            if (candidate) {
                                
                                if (!pc || !pc.remoteDescription) {
                                    if (!window.pendingIceCandidates) {
                                        window.pendingIceCandidates = [];
                                    }
                                    window.pendingIceCandidates.push(candidate);
                                    log('缓存ICE候选，等待远程描述设置');
                                } else {
                                    await pc.addIceCandidate(candidate);
                                    log('已添加ICE候选');
                                }
                            }
                        } catch (e) {
                            log(`添加ICE候选错误: ${e.message}`, 'error');
                        }
                    }
                };

                ws.onclose = () => {
                    log('信令服务器连接已关闭', 'error');
                    updateStatus('信令服务器连接已关闭', true);

                    
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`尝试重新连接 (${reconnectAttempts}/${maxReconnectAttempts})...`);
                        setTimeout(connectToSignalingServer, 2000);
                    } else {
                        disconnectFromPeer();
                        connectBtn.disabled = false;
                        disconnectBtn.disabled = true;
                    }
                };

                ws.onerror = (error) => {
                    log(`信令服务器错误: ${error.message}`, 'error');
                    updateStatus(`信令服务器错误: ${error.message}`, true);
                };

            } catch (e) {
                log(`连接信令服务器错误: ${e.message}`, 'error');
                updateStatus(`连接信令服务器错误: ${e.message}`, true);
            }
        }

        
        function handleClientList(clients) {
            senders = clients.filter(client =>
                client.role === 'source' || client.id.startsWith('sender-')
            );

            updateSenderList();
        }

        
        function updateSenderList() {
            senderListEl.innerHTML = '';

            if (senders.length === 0) {
                const noSenders = document.createElement('div');
                noSenders.textContent = '没有可用的发送端';
                senderListEl.appendChild(noSenders);
                return;
            }

            senders.forEach(sender => {
                const senderItem = document.createElement('div');
                senderItem.className = 'sender-item';
                if (sender.id === activeSenderId) {
                    senderItem.classList.add('active');
                }

                const displayName = sender.name || sender.id;
                senderItem.textContent = displayName.length > 15 ? displayName.substring(0, 12) + '...' : displayName;
                senderItem.title = `${sender.name || sender.id} ${sender.description ? `(${sender.description})` : ''}`;
                senderItem.dataset.id = sender.id;

                senderItem.addEventListener('click', () => {
                    senderIdInput.value = sender.id;
                });

                senderListEl.appendChild(senderItem);
            });
        }

        
        function addSender(sender) {
            const existingIndex = senders.findIndex(s => s.id === sender.id);
            if (existingIndex >= 0) {
                senders[existingIndex] = sender;
            } else {
                senders.push(sender);
            }
            updateSenderList();
        }

        
        function removeSender(senderId) {
            senders = senders.filter(s => s.id !== senderId);
            updateSenderList();

            if (senderId === activeSenderId) {
                disconnectFromPeer();
                updateStatus(`发送端 ${senderId} 已离开`, true);
            }
        }

        
        async function connectToPeer() {
            const senderId = senderIdInput.value.trim();

            if (!senderId) {
                updateStatus('请输入发送端ID', true);
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                await connectToSignalingServer();
            }

            
            if (pc) {
                await disconnectFromPeer();
            }

            setLoading(true);

            try {
                
                pc = new RTCPeerConnection(config);

                
                setupPeerConnection(pc, senderId);

                
                dataChannel = pc.createDataChannel('control');

                dataChannel.onopen = () => {
                    log('数据通道已打开');
                    dataChannel.send('Hello from web receiver!');
                };

                dataChannel.onmessage = (event) => {
                    log(`收到数据通道消息: ${event.data}`);

                    
                    try {
                        if (event.data.startsWith('{') && event.data.endsWith('}')) {
                            const data = JSON.parse(event.data);

                            
                            if (data.type === 'source_info') {
                                log(`收到源信息: ${data.source_id}`, 'success');
                                
                            }

                            
                            else if (data.type === 'command_result') {
                                log(`命令结果: ${data.command} - ${data.success ? '成功' : '失败'}`, data.success ? 'success' : 'error');

                                
                                if (data.command === 'pause' || data.command === 'resume' || data.command === 'toggle_pause') {
                                    updatePlayPauseState(data.state === 'playing');
                                }

                                
                                if (data.command === 'seek' && data.success) {
                                    
                                }
                            }

                            
                            else if (data.type === 'status') {
                                log(`收到状态信息: ${data.state}`, 'info');

                                
                                updatePlayPauseState(data.state === 'playing');

                                
                                if (data.duration > 0) {
                                    videoDuration = data.duration;
                                    currentPosition = data.position;
                                    isLocalFile = data.is_local_file;

                                    
                                    updateSeekBar();

                                    
                                    if (isLocalFile) {
                                        videoControls.classList.add('active');
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        log(`解析消息错误: ${e.message}`, 'error');
                    }
                };

                dataChannel.onclose = () => {
                    log('数据通道已关闭');
                };

                
                connectionTimeout = setTimeout(() => {
                    log('连接超时，尝试重新连接');
                    updateStatus('连接超时，尝试重新连接', true);

                    
                    if (pc) {
                        const iceState = pc.iceConnectionState;
                        const connState = pc.connectionState;
                        log(`连接状态: ICE=${iceState}, Conn=${connState}`);

                        if (iceState !== 'connected' && iceState !== 'completed' && connState !== 'connected') {
                            
                            try {
                                log('尝试重新创建offer');

                                
                                pc.createOffer({
                                    offerToReceiveAudio: true,
                                    offerToReceiveVideo: true
                                }).then(offer => {
                                    return pc.setLocalDescription(offer);
                                }).then(() => {
                                    
                                    ws.send(JSON.stringify({
                                        type: 'offer',
                                        target: senderId,
                                        sdp: pc.localDescription.sdp
                                    }));
                                    log('已重新发送offer');

                                    
                                    connectionTimeout = setTimeout(() => {
                                        log('重新连接超时，断开连接');
                                        updateStatus('重新连接超时，断开连接', true);
                                        disconnectFromPeer();
                                    }, 15000);
                                }).catch(e => {
                                    log(`重新创建offer失败: ${e.message}`, 'error');
                                    disconnectFromPeer();
                                });
                            } catch (e) {
                                log(`重新连接失败: ${e.message}`, 'error');
                                disconnectFromPeer();
                            }
                        } else {
                            
                            log('连接已建立，但可能没有收到媒体流');

                            
                            if (!videoEl.srcObject || !videoEl.srcObject.getVideoTracks().length) {
                                log('没有视频轨道，尝试重新连接');
                                disconnectFromPeer();
                                setTimeout(() => {
                                    connectToPeer();
                                }, 1000);
                            }
                        }
                    } else {
                        disconnectFromPeer();
                    }
                }, 30000); 

                
                const offer = await pc.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true,
                    iceRestart: true
                });

                
                let modifiedSdp = offer.sdp;

                
                if (!modifiedSdp.includes('VP8') || !modifiedSdp.includes('H264')) {
                    log('修改SDP以添加VP8和H264支持');
                    
                }

                
                await pc.setLocalDescription(new RTCSessionDescription({
                    type: offer.type,
                    sdp: modifiedSdp
                }));
                log('已设置本地描述');

                
                
                const formattedSdp = modifiedSdp.replace(/\r\n|\n|\r/g, '\\n');

                ws.send(JSON.stringify({
                    type: 'offer',
                    target: senderId,
                    sdp: formattedSdp
                }));
                log('已发送offer');

                activeSenderId = senderId;
                updateStatus(`正在连接到发送端 ${senderId}...`);
                updateSenderList();

                connectBtn.disabled = true;
                disconnectBtn.disabled = false; 

            } catch (e) {
                log(`连接错误: ${e.message}`, 'error');
                updateStatus(`连接错误: ${e.message}`, true);
                setLoading(false);
                disconnectFromPeer();
            }
        }

        
        function setupPeerConnection(peerConnection, senderId) {
            
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    const candidate = {
                        candidate: event.candidate.candidate,
                        sdpMid: event.candidate.sdpMid,
                        sdpMLineIndex: event.candidate.sdpMLineIndex
                    };

                    ws.send(JSON.stringify({
                        type: 'candidate',
                        target: senderId,
                        candidate: candidate
                    }));

                    log('已发送ICE候选');
                }
            };

            
            peerConnection.oniceconnectionstatechange = () => {
                log(`ICE连接状态: ${peerConnection.iceConnectionState}`);

                if (peerConnection.iceConnectionState === 'failed' || peerConnection.iceConnectionState === 'disconnected' || peerConnection.iceConnectionState === 'closed') {
                    updateStatus(`ICE连接状态: ${peerConnection.iceConnectionState}`, true);
                    setLoading(false);
                } else if (peerConnection.iceConnectionState === 'connected' || peerConnection.iceConnectionState === 'completed') {
                    updateStatus(`已连接到发送端 ${senderId}`);
                    setLoading(false);
                }
            };

            
            peerConnection.onconnectionstatechange = () => {
                log(`连接状态: ${peerConnection.connectionState}`);

                if (peerConnection.connectionState === 'failed' || peerConnection.connectionState === 'disconnected' || peerConnection.connectionState === 'closed') {
                    updateStatus(`连接状态: ${peerConnection.connectionState}`, true);
                    setLoading(false);
                } else if (peerConnection.connectionState === 'connected') {
                    updateStatus(`已连接到发送端 ${senderId}`);
                    setLoading(false);
                }
            };

            
            peerConnection.onsignalingstatechange = () => {
                log(`信令状态: ${peerConnection.signalingState}`);
            };

            
            peerConnection.ontrack = (event) => {
                log(`收到轨道: ${event.track.kind}`);

                if (event.track.kind === 'video') {
                    videoEl.srcObject = event.streams[0];
                    videoEl.muted = false;
                    log('已设置视频源');

                    
                    videoEl.play().catch(e => {
                        log(`自动播放失败: ${e.message}`, 'error');
                        
                        updateStatus('点击视频区域开始播放', true);

                        
                        videoEl.addEventListener('click', () => {
                            videoEl.play().catch(e => {
                                log(`播放失败: ${e.message}`, 'error');
                            });
                        }, { once: true });
                    });
                }
            };

            
            peerConnection.ondatachannel = (event) => {
                const channel = event.channel;
                log(`收到数据通道: ${channel.label}`);

                
                dataChannel = channel;

                channel.onopen = () => {
                    log(`数据通道 ${channel.label} 已打开`);
                    channel.send('Hello from web receiver!');
                };

                channel.onmessage = (event) => {
                    log(`收到数据通道消息: ${event.data}`);

                    
                    try {
                        if (event.data.startsWith('{') && event.data.endsWith('}')) {
                            const data = JSON.parse(event.data);

                            
                            if (data.type === 'source_info') {
                                log(`收到源信息: ${data.source_id}`, 'success');
                                
                            }

                            
                            else if (data.type === 'command_result') {
                                log(`命令结果: ${data.command} - ${data.success ? '成功' : '失败'}`, data.success ? 'success' : 'error');

                                
                                if (data.command === 'pause' || data.command === 'resume' || data.command === 'toggle_pause') {
                                    updatePlayPauseState(data.state === 'playing');
                                }
                            }
                        }
                    } catch (e) {
                        log(`解析消息错误: ${e.message}`, 'error');
                    }
                };

                channel.onclose = () => {
                    log('数据通道已关闭');
                };
            };
        }

        
        async function disconnectFromPeer() {
            setLoading(false);

            if (connectionTimeout) {
                clearTimeout(connectionTimeout);
                connectionTimeout = null;
            }

            if (pc) {
                pc.oniceconnectionstatechange = null;
                pc.onconnectionstatechange = null;
                pc.onsignalingstatechange = null;
                pc.ontrack = null;
                pc.ondatachannel = null;
                pc.onicecandidate = null;

                pc.getSenders().forEach(sender => {
                    if (sender.track) {
                        sender.track.stop();
                    }
                });

                pc.close();
                pc = null;
            }

            if (dataChannel) {
                dataChannel.close();
                dataChannel = null;
            }

            if (videoEl.srcObject) {
                const tracks = videoEl.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                videoEl.srcObject = null;
            }

            activeSenderId = null;
            updateSenderList();

            connectBtn.disabled = false;
            disconnectBtn.disabled = true;

            updateStatus('已断开连接');
            log('已断开连接');
        }

        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                if (videoEl.requestFullscreen) {
                    videoEl.requestFullscreen();
                } else if (videoEl.webkitRequestFullscreen) {
                    videoEl.webkitRequestFullscreen();
                } else if (videoEl.mozRequestFullScreen) {
                    videoEl.mozRequestFullScreen();
                } else if (videoEl.msRequestFullscreen) {
                    videoEl.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        }

        
        function updatePlayPauseState(playing) {
            isPlaying = playing;

            if (playing) {
                playIcon.style.display = 'none';
                pauseIcon.style.display = 'block';
            } else {
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
            }
        }

        
        function updateSeekBar() {
            if (videoDuration > 0) {
                const percent = (currentPosition / videoDuration) * 100;
                seekSlider.value = percent;

                
                currentTimeEl.textContent = formatTime(currentPosition / 30); 
                durationEl.textContent = formatTime(videoDuration / 30); 
            }
        }

        
        function formatTime(seconds) {
            seconds = Math.floor(seconds);
            const minutes = Math.floor(seconds / 60);
            seconds = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        
        function sendCommand(command, params = {}) {
            if (dataChannel && dataChannel.readyState === 'open') {
                const message = {
                    command: command,
                    ...params
                };

                dataChannel.send(JSON.stringify(message));
                log(`发送命令: ${command}`);
                return true;
            } else {
                log('数据通道未就绪，无法发送命令', 'error');
                return false;
            }
        }

        
        function requestVideoStatus() {
            return sendCommand('get_status');
        }

        
        function togglePlayPause() {
            return sendCommand('toggle_pause');
        }

        
        function seekTo(position) {
            return sendCommand('seek', { position: Math.floor(position) });
        }

        
        function startProgressUpdates() {
            
            if (seekUpdateInterval) {
                clearInterval(seekUpdateInterval);
            }

            
            seekUpdateInterval = setInterval(() => {
                if (isPlaying && videoDuration > 0) {
                    currentPosition++;
                    if (currentPosition > videoDuration) {
                        currentPosition = 0;
                    }
                    updateSeekBar();
                }
            }, 1000 / 30); 

            
            setInterval(() => {
                if (dataChannel && dataChannel.readyState === 'open') {
                    requestVideoStatus();
                }
            }, 10000);
        }

        
        function showControls() {
            videoControls.classList.add('active');

            
            if (controlsTimeout) {
                clearTimeout(controlsTimeout);
            }

            
            controlsTimeout = setTimeout(() => {
                videoControls.classList.remove('active');
            }, 3000);
        }

        
        connectBtn.addEventListener('click', connectToPeer);
        disconnectBtn.addEventListener('click', disconnectFromPeer);
        fullscreenBtn.addEventListener('click', toggleFullscreen);

        
        playPauseBtn.addEventListener('click', togglePlayPause);

        
        seekSlider.addEventListener('input', () => {
            if (videoDuration > 0) {
                const position = Math.floor((seekSlider.value / 100) * videoDuration);
                currentTimeEl.textContent = formatTime(position / 30); 
            }
        });

        seekSlider.addEventListener('change', () => {
            if (videoDuration > 0) {
                const position = Math.floor((seekSlider.value / 100) * videoDuration);
                seekTo(position);
            }
        });

        
        videoEl.addEventListener('click', (e) => {
            
            if (e.target === videoEl) {
                showControls();
            }
        });

        
        videoEl.addEventListener('playing', () => {
            if (dataChannel && dataChannel.readyState === 'open') {
                
                requestVideoStatus();
                
                //startProgressUpdates();
            }
        });

        
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                
                if (pc && (pc.iceConnectionState === 'disconnected' || pc.iceConnectionState === 'failed')) {
                    log('检测到连接已断开，尝试重新连接');
                    connectToPeer();
                }
            }
        });

        
        window.addEventListener('online', () => {
            log('网络已恢复');
            if (activeSenderId && (!pc || pc.iceConnectionState !== 'connected')) {
                log('尝试重新连接');
                connectToPeer();
            }
        });

        window.addEventListener('offline', () => {
            log('网络已断开', 'error');
            updateStatus('网络已断开', true);
        });

        // 页面卸载时清理连接
        window.addEventListener('beforeunload', () => {
            log('页面即将卸载，清理连接');

            // 断开WebRTC连接
            if (pc) {
                pc.close();
                pc = null;
            }

            // 断开信令服务器连接
            if (ws && ws.readyState === WebSocket.OPEN) {
                // 发送退出消息
                try {
                    ws.send(JSON.stringify({
                        type: 'unregister',
                        id: receiverId
                    }));
                } catch (e) {
                    console.log('发送退出消息失败:', e);
                }

                // 关闭WebSocket连接
                ws.close();
                ws = null;
            }

            // 清理定时器
            if (connectionTimeout) {
                clearTimeout(connectionTimeout);
                connectionTimeout = null;
            }

            if (seekUpdateInterval) {
                clearInterval(seekUpdateInterval);
                seekUpdateInterval = null;
            }
        });

        // 页面隐藏时也进行清理（移动端切换应用）
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                log('页面已隐藏，清理连接');

                // 断开WebRTC连接
                if (pc) {
                    pc.close();
                    pc = null;
                }

                // 断开信令服务器连接
                if (ws && ws.readyState === WebSocket.OPEN) {
                    try {
                        ws.send(JSON.stringify({
                            type: 'unregister',
                            id: receiverId
                        }));
                    } catch (e) {
                        console.log('发送退出消息失败:', e);
                    }

                    ws.close();
                    ws = null;
                }

                updateStatus('已断开连接（页面隐藏）');
            } else if (document.visibilityState === 'visible') {
                // 页面重新可见时，检查是否需要重新连接
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    log('页面重新可见，重新连接');
                    setTimeout(() => {
                        connectToSignalingServer();
                    }, 1000);
                }
            }
        });


        connectToSignalingServer();
    </script>
</body>
</html>
