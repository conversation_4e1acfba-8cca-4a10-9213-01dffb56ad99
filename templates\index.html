<!DOCTYPE html>
<html>
<head>
    <title>视频流查看器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        .video-stream {
            width: 100%;
            max-width: 800px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .info {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .url {
            font-family: monospace;
            background-color: #ddd;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频流查看器</h1>
        
        <div class="video-container">
            <img id="video-stream" class="video-stream" src="/video_feed" alt="视频流">
        </div>
        
        <div class="controls">
            <button id="refresh-btn">刷新视频</button>
            <button id="fullscreen-btn">全屏显示</button>
        </div>
        
        <div class="info">
            <h2>访问信息</h2>
            <p>视频流URL:</p>
            <p class="url" id="stream-url"></p>
            
            <h3>使用说明</h3>
            <p>1. 在浏览器中直接访问上述URL可以查看视频流</p>
            <p>2. 在其他应用中使用上述URL作为视频源</p>
            <p>3. 如需从外部网络访问，请在路由器中设置端口转发</p>
        </div>
    </div>
    
    <script>
        // 获取元素
        const videoStream = document.getElementById('video-stream');
        const refreshBtn = document.getElementById('refresh-btn');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const streamUrl = document.getElementById('stream-url');
        
        // 设置视频流URL
        const videoFeedUrl = window.location.origin + '/video_feed';
        streamUrl.textContent = videoFeedUrl;
        
        // 刷新视频
        refreshBtn.addEventListener('click', () => {
            videoStream.src = videoFeedUrl + '?t=' + new Date().getTime();
        });
        
        // 全屏显示
        fullscreenBtn.addEventListener('click', () => {
            if (videoStream.requestFullscreen) {
                videoStream.requestFullscreen();
            } else if (videoStream.webkitRequestFullscreen) {
                videoStream.webkitRequestFullscreen();
            } else if (videoStream.msRequestFullscreen) {
                videoStream.msRequestFullscreen();
            }
        });
        
        // 检查视频流状态
        function checkStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    if (data.status !== 'normal') {
                        console.warn('视频流状态:', data.status, data.message);
                        // 如果视频流冻结，尝试刷新
                        if (data.status === 'warning' && data.message.includes('冻结')) {
                            videoStream.src = videoFeedUrl + '?t=' + new Date().getTime();
                        }
                    }
                })
                .catch(error => console.error('检查状态错误:', error));
        }
        
        // 定期检查状态
        setInterval(checkStatus, 10000);
    </script>
</body>
</html>
