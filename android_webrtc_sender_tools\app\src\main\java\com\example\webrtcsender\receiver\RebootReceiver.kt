package com.example.webrtcsender.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import com.example.webrtcsender.utils.Logger

/**
 * 系统重启广播接收器
 * 接收AlarmManager的延迟重启信号并执行系统重启
 */
class RebootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "RebootReceiver"
        const val ACTION_DELAYED_REBOOT = "com.example.webrtcsender.DELAYED_REBOOT"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            val action = intent.action
            Logger.i(TAG, "🔄 收到重启广播: $action")
            
            when (action) {
                ACTION_DELAYED_REBOOT -> {
                    Logger.i(TAG, "⏰ 执行延迟系统重启")
                    performSystemReboot(context)
                }
                else -> {
                    Logger.d(TAG, "📡 未处理的广播: $action")
                }
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 处理重启广播失败", e)
        }
    }
    
    /**
     * 执行系统重启
     */
    private fun performSystemReboot(context: Context) {
        try {
            Logger.i(TAG, "🔄 开始执行系统重启")
            
            // 方法1: 使用PowerManager (需要系统权限)
            try {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    powerManager.reboot("upgrade")
                } else {
                    @Suppress("DEPRECATION")
                    powerManager.reboot("upgrade")
                }
                Logger.i(TAG, "✅ PowerManager重启命令已发送")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ PowerManager重启失败，尝试其他方法: ${e.message}")
            }
            
            // 方法2: 使用Runtime执行shell命令
            try {
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", "reboot"))
                process.waitFor()
                Logger.i(TAG, "✅ Shell重启命令已发送")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ Shell重启失败，尝试其他方法: ${e.message}")
            }
            
            // 方法3: 使用系统广播
            try {
                val rebootIntent = Intent("android.intent.action.REBOOT")
                rebootIntent.putExtra("nowait", 1)
                rebootIntent.putExtra("interval", 1)
                rebootIntent.putExtra("window", 0)
                context.sendBroadcast(rebootIntent)
                Logger.i(TAG, "✅ 重启广播已发送")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ 重启广播失败: ${e.message}")
            }
            
            // 方法4: 直接调用系统命令
            try {
                Runtime.getRuntime().exec("reboot")
                Logger.i(TAG, "✅ 直接重启命令已发送")
                return
            } catch (e: Exception) {
                Logger.w(TAG, "⚠️ 直接重启失败: ${e.message}")
            }
            
            // 如果所有方法都失败
            Logger.e(TAG, "❌ 所有重启方法都失败")
            
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 执行系统重启失败", e)
        }
    }
}
