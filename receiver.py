#!/usr/bin/env python3
# receiver.py - 视频流接收端
import asyncio
import websockets
import json
import cv2
import numpy as np
import argparse
import logging
import uuid
import traceback
import time
import os
from aiortc import RTCPeerConnection, RTCSessionDescription, MediaStreamTrack, RTCConfiguration, VideoStreamTrack
from aiortc.contrib.media import MediaRecorder
from av import VideoFrame

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('receiver')

# 设置aiortc日志级别
aiortc_logger = logging.getLogger('aiortc')
aiortc_logger.setLevel(logging.INFO)

# 设置aioice日志级别
aioice_logger = logging.getLogger('aioice')
aioice_logger.setLevel(logging.INFO)

# 解析命令行参数
parser = argparse.ArgumentParser(description="WebRTC视频流接收端")
parser.add_argument("--target", help="目标发送端ID", required=True)
parser.add_argument("--id", help="接收端ID", default=f"receiver-{uuid.uuid4().hex[:8]}")
parser.add_argument("--signaling", help="信令服务器URL", default="wss://sling.91jdcd.com/ws/")
parser.add_argument("--record", help="录制视频文件", default=None)
parser.add_argument("--no-display", help="不显示视频窗口", action="store_true")
parser.add_argument("--debug", help="启用调试模式", action="store_true")
parser.add_argument("--save-frames", help="保存视频帧到指定目录", default="")
args = parser.parse_args()

# 启用更详细的调试信息
if args.debug:
    logger.setLevel(logging.DEBUG)
    aiortc_logger.setLevel(logging.DEBUG)
    aioice_logger.setLevel(logging.DEBUG)
    logging.getLogger().setLevel(logging.DEBUG)
    print("已启用调试模式")

# STUN和TURN服务器配置
from aiortc.rtcicetransport import RTCIceServer

ICE_SERVERS = [
    RTCIceServer(urls=["stun:stun.l.google.com:19302"]),
    RTCIceServer(urls=["stun:stun1.l.google.com:19302"]),
    RTCIceServer(urls=["stun:stun2.l.google.com:19302"]),
    RTCIceServer(
        urls=["turn:numb.viagenie.ca"],
        username="<EMAIL>",
        credential="muazkh"
    )
]

# 虚拟视频轨道类 - 确保offer包含视频媒体行
class DummyVideoTrack(VideoStreamTrack):
    """虚拟视频轨道，只用于创建offer"""
    def __init__(self):
        super().__init__()
        self.counter = 0
        self.width = 640
        self.height = 480
        logger.info("创建虚拟视频轨道")

    async def recv(self):
        pts, time_base = await self.next_timestamp()

        # 创建黑色帧
        frame = np.zeros((self.height, self.width, 3), np.uint8)
        self.counter += 1

        # 添加文本
        cv2.putText(frame, "Waiting for video...", (50, self.height//2),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, f"Frame: {self.counter}", (50, self.height//2 + 50),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 转换为VideoFrame
        video_frame = VideoFrame.from_ndarray(frame, format="bgr24")
        video_frame.pts = pts
        video_frame.time_base = time_base

        return video_frame

# 创建对等连接
config = RTCConfiguration()
config.iceServers = ICE_SERVERS
pc = RTCPeerConnection(configuration=config)

# 创建数据通道 - 这是必要的，否则无法创建offer
data_channel = pc.createDataChannel("control")

# 添加虚拟视频轨道 - 确保offer包含视频媒体行
dummy_track = DummyVideoTrack()
pc.addTrack(dummy_track)

@data_channel.on("open")
def on_open():
    logger.info("数据通道已打开")

@data_channel.on("message")
def on_message(message):
    logger.info(f"收到消息: {message}")

# 视频处理
class VideoReceiver:
    def __init__(self):
        self.recorder = None
        self.frame_count = 0
        self.start_time = None
        self.save_dir = args.save_frames

        if args.record:
            self.recorder = MediaRecorder(args.record)
            logger.info(f"视频将录制到: {args.record}")

        if not args.no_display:
            # 创建视频窗口
            cv2.namedWindow("视频流", cv2.WINDOW_NORMAL)
            cv2.resizeWindow("视频流", 800, 600)

        # 如果需要保存帧
        if self.save_dir:
            import os
            if not os.path.exists(self.save_dir):
                os.makedirs(self.save_dir)
            logger.info(f"视频帧将保存到: {self.save_dir}")

    async def start(self):
        self.start_time = time.time()
        if self.recorder:
            await self.recorder.start()

    async def stop(self):
        if self.recorder:
            await self.recorder.stop()

        if not args.no_display:
            cv2.destroyAllWindows()

        # 打印统计信息
        if self.start_time:
            elapsed = time.time() - self.start_time
            fps = self.frame_count / elapsed if elapsed > 0 else 0
            logger.info(f"视频统计: 总帧数={self.frame_count}, 总时长={elapsed:.2f}秒, 平均FPS={fps:.2f}")

    def process_frame(self, frame):
        self.frame_count += 1

        # 计算FPS
        if self.start_time:
            elapsed = time.time() - self.start_time
            fps = self.frame_count / elapsed if elapsed > 0 else 0

            # 每30帧记录一次FPS
            if self.frame_count % 30 == 0:
                logger.info(f"视频统计: 帧数={self.frame_count}, FPS={fps:.2f}")

        # 添加帧信息
        height, width = frame.shape[:2]
        cv2.putText(frame, f"Frame: {self.frame_count}", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        if self.start_time:
            elapsed = time.time() - self.start_time
            fps = self.frame_count / elapsed if elapsed > 0 else 0
            cv2.putText(frame, f"FPS: {fps:.2f}", (10, 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 添加时间戳
        cv2.putText(frame, time.strftime("%Y-%m-%d %H:%M:%S"), (width - 250, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 保存帧
        if self.save_dir and self.frame_count % 10 == 0:  # 每10帧保存一次
            import os
            frame_path = os.path.join(self.save_dir, f"frame_{self.frame_count:06d}.jpg")
            cv2.imwrite(frame_path, frame)
            if self.frame_count % 100 == 0:  # 每100帧记录一次
                logger.info(f"已保存帧: {frame_path}")

        if not args.no_display:
            # 显示帧
            cv2.imshow("视频流", frame)
            cv2.waitKey(1)

video_receiver = VideoReceiver()

# 处理接收到的视频轨道
@pc.on("track")
async def on_track(track):
    logger.info(f"接收到媒体轨道: {track.kind}")

    if track.kind == "video":
        if video_receiver.recorder:
            video_receiver.recorder.addTrack(track)

        await video_receiver.start()

        while True:
            try:
                frame = await track.recv()
                # 转换为OpenCV格式
                img = frame.to_ndarray(format="bgr24")
                video_receiver.process_frame(img)
            except Exception as e:
                logger.error(f"处理视频帧错误: {e}")
                break

async def handle_signaling():
    # 连接到信令服务器
    uri = args.signaling
    async with websockets.connect(uri) as websocket:
        # 注册客户端
        await websocket.send(json.dumps({
            "type": "register",
            "id": args.id
        }))
        logger.info(f"已连接到信令服务器，ID: {args.id}")

        # 处理ICE候选
        @pc.on("icecandidate")
        async def on_icecandidate(event):
            if event.candidate:
                try:
                    # 确保所有必要的字段都存在
                    candidate_dict = {
                        "candidate": event.candidate.candidate,
                        "sdpMid": event.candidate.sdpMid or "",
                        "sdpMLineIndex": event.candidate.sdpMLineIndex or 0
                    }

                    await websocket.send(json.dumps({
                        "type": "candidate",
                        "target": args.target,
                        "candidate": candidate_dict
                    }))
                    logger.info(f"发送ICE候选到 {args.target}")
                except Exception as e:
                    logger.error(f"发送ICE候选错误: {e}")

        # 处理连接状态变化
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"连接状态: {pc.connectionState}")
            if pc.connectionState == "failed":
                await pc.close()

        # 处理信令消息
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"收到消息: {data['type']}")

                if data["type"] == "registered":
                    logger.info("注册成功")

                    try:
                        # 创建offer
                        logger.info("创建offer...")
                        offer = await pc.createOffer()
                        logger.info("创建offer成功")

                        # 检查offer是否包含视频轨道
                        sdp = offer.sdp
                        has_video = "m=video" in sdp
                        has_application = "m=application" in sdp
                        logger.info(f"Offer SDP 包含视频轨道: {has_video}, 包含数据通道: {has_application}")

                        # 设置本地描述
                        logger.info("设置本地描述...")
                        await pc.setLocalDescription(offer)
                        logger.info("设置本地描述成功")

                        # 发送offer
                        await websocket.send(json.dumps({
                            "type": "offer",
                            "target": args.target,
                            "sdp": pc.localDescription.sdp
                        }))
                        logger.info(f"已发送offer到 {args.target}")
                    except Exception as e:
                        logger.error(f"创建和发送offer错误: {e}\n{traceback.format_exc()}")

                elif data["type"] == "answer" and data["from"] == args.target:
                    # 设置远程描述
                    answer = RTCSessionDescription(sdp=data["sdp"], type="answer")
                    await pc.setRemoteDescription(answer)
                    logger.info("已设置远程描述")

                elif data["type"] == "candidate" and data["from"] == args.target:
                    # 添加ICE候选
                    try:
                        candidate = data["candidate"]
                        # 创建RTCIceCandidate对象
                        from aiortc import RTCIceCandidate

                        # 确保candidate字段不为None
                        if candidate.get("candidate") is None:
                            logger.warning(f"ICE候选缺少candidate字段: {candidate}")
                            return

                        # 确保必要的字段存在
                        if "sdpMid" not in candidate:
                            candidate["sdpMid"] = ""
                        if "sdpMLineIndex" not in candidate:
                            candidate["sdpMLineIndex"] = 0

                        ice_candidate = RTCIceCandidate(
                            candidate=candidate.get("candidate"),
                            sdpMid=candidate.get("sdpMid", ""),
                            sdpMLineIndex=candidate.get("sdpMLineIndex", 0)
                        )

                        await pc.addIceCandidate(ice_candidate)
                        logger.info("添加ICE候选")
                    except Exception as e:
                        logger.error(f"处理ICE候选错误: {e}")

                elif data["type"] == "client_list":
                    clients = data["clients"]
                    logger.info(f"可用客户端: {', '.join(clients)}")

                elif data["type"] == "client_left" and data["id"] == args.target:
                    logger.info(f"目标 {args.target} 已断开连接")
                    return

            except Exception as e:
                logger.error(f"处理消息错误: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(handle_signaling())
    except KeyboardInterrupt:
        logger.info("程序已终止")
    finally:
        # 关闭连接和资源
        loop = asyncio.new_event_loop()
        loop.run_until_complete(video_receiver.stop())
        loop.run_until_complete(pc.close())
        loop.close()
