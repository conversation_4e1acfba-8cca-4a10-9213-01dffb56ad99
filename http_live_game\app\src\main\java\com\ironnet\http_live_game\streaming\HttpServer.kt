package com.ironnet.http_live_game.streaming

import android.graphics.Bitmap
import android.media.MediaFormat
import android.util.Log
import fi.iki.elonen.NanoHTTPD
import fi.iki.elonen.NanoHTTPD.Response.Status
import java.io.ByteArrayInputStream
import java.io.IOException
import java.io.PipedInputStream
import java.io.PipedOutputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicReference

class HttpServer(private var port: Int) : NanoHTTPD(port) {
    private val TAG = "HttpServer"

    // Map to store active stream connections
    private val activeStreams = ConcurrentHashMap<String, StreamConnection>()

    // Map to store active MJPEG connections
    private val mjpegStreams = ConcurrentHashMap<String, MjpegStreamer>()

    // Latest video data for new connections
    private var videoHeader: ByteArray? = null

    // Current video format
    private var currentVideoFormat: String = MediaFormat.MIMETYPE_VIDEO_AVC

    // Latest frame for MJPEG streaming (using AtomicReference to ensure thread safety)
    private val latestFrame = AtomicReference<Bitmap?>(null)

    init {
        // 不在init中启动服务器，而是在外部显式调用start方法
    }

    // 启动服务器，尝试多个端口
    fun startServer(): Boolean {
        // 先尝试指定的端口
        if (tryStart()) {
            Log.i(TAG, "Server started on port $port")
            return true
        }

        // 如果失败，尝试更广泛的端口范围
        for (newPort in 8100..8200) {
            port = newPort
            if (tryStart()) {
                Log.i(TAG, "Server started on port $port")
                return true
            }
        }

        // 如果仍然失败，尝试随机端口
        for (i in 1..10) {
            port = 10000 + (Math.random() * 20000).toInt()
            if (tryStart()) {
                Log.i(TAG, "Server started on port $port")
                return true
            }
        }

        Log.e(TAG, "Failed to start server on any port")
        return false
    }

    // 尝试启动服务器
    private fun tryStart(): Boolean {
        return try {
            start()
            true
        } catch (e: IOException) {
            Log.e(TAG, "Failed to start server on port $port: ${e.message}")
            false
        }
    }

    // 获取当前端口
    fun getPort(): Int {
        return port
    }

    override fun serve(session: IHTTPSession): Response {
        val uri = session.uri
        Log.d(TAG, "Received request: $uri")

        return when {
            uri.endsWith("/stream.mp4") || uri.endsWith("/stream.webm") -> serveVideoStream(session)
            uri.endsWith("/stream.mjpeg") -> serveMjpegStream(session)
            uri.endsWith("/direct-mjpeg") -> serveDirectMjpeg(session)
            uri.endsWith("/viewer") || uri.endsWith("/viewer.html") -> serveViewer(session)
            uri.endsWith("/info") -> serveInfo(session)
            else -> serveIndex(session)
        }
    }

    private fun serveVideoStream(session: IHTTPSession): Response {
        val streamId = session.remoteHostName + ":" + session.remoteIpAddress
        Log.d(TAG, "Starting stream for client: $streamId")

        try {
            // 检查是否已经有相同客户端的连接，如果有则关闭旧连接
            if (activeStreams.containsKey(streamId)) {
                Log.d(TAG, "Closing existing connection for client: $streamId")
                closeConnection(streamId)
            }

            // 创建新的流连接
            val streamConnection = StreamConnection()
            activeStreams[streamId] = streamConnection

            // 发送视频头信息（如果可用）
            videoHeader?.let { header ->
                try {
                    Log.d(TAG, "Sending header data to client: $streamId (${header.size} bytes)")
                    streamConnection.outputStream.write(header)
                    streamConnection.outputStream.flush()
                } catch (e: IOException) {
                    Log.e(TAG, "Error writing header to stream: ${e.message}", e)
                }
            } ?: Log.w(TAG, "No video header available yet")

            // 创建响应
            val mimeType = getMimeTypeForFormat(currentVideoFormat)
            Log.d(TAG, "Using MIME type: $mimeType for format: $currentVideoFormat")

            val response = newChunkedResponse(
                Response.Status.OK,
                mimeType,
                streamConnection.inputStream
            )

            // 设置流媒体所需的HTTP头
            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
            response.addHeader("Pragma", "no-cache")
            response.addHeader("Expires", "0")
            response.addHeader("Access-Control-Allow-Origin", "*")
            response.addHeader("Connection", "keep-alive")
            response.addHeader("Content-Type", mimeType)

            // 设置流选项
            response.setKeepAlive(true)
            response.setChunkedTransfer(true)

            return response
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up video stream: ${e.message}", e)
            return newFixedLengthResponse(
                Response.Status.INTERNAL_ERROR,
                MIME_PLAINTEXT,
                "Error setting up video stream: ${e.message}"
            )
        }
    }

    private fun serveDirectMjpeg(session: IHTTPSession): Response {
        // 这个方法直接返回MJPEG流，不包含任何HTML包装
        // 这对于某些不支持在HTML中嵌入MJPEG流的浏览器很有用
        return serveMjpegStream(session)
    }

    private fun serveViewer(session: IHTTPSession): Response {
        // 创建一个简单的HTML查看器
        val ipAddress = session.headers["host"]?.split(":")?.get(0) ?: "localhost"
        val port = session.headers["host"]?.split(":")?.get(1) ?: port.toString()

        val mjpegUrl = "http://$ipAddress:$port/stream.mjpeg"

        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>MJPEG流查看器</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    h1 { color: #333; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .stream-container { margin-top: 20px; }
                    img { max-width: 100%; border: 1px solid #ddd; }
                    .status { margin-top: 10px; padding: 10px; background: #f5f5f5; border: 1px solid #ddd; }
                    .button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; margin-top: 10px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>MJPEG流查看器</h1>

                    <div class="stream-container">
                        <img id="mjpegPlayer" src="$mjpegUrl" alt="MJPEG Stream">
                    </div>

                    <div class="status" id="statusText">正在连接到 $mjpegUrl</div>

                    <button onclick="refreshStream()" class="button">刷新流</button>
                </div>

                <script>
                    const mjpegPlayer = document.getElementById('mjpegPlayer');
                    const statusText = document.getElementById('statusText');

                    // 设置事件监听器
                    mjpegPlayer.onload = function() {
                        statusText.textContent = "已连接到流";
                    };

                    mjpegPlayer.onerror = function() {
                        statusText.textContent = "连接失败，请刷新页面重试";
                    };

                    // 刷新流
                    function refreshStream() {
                        mjpegPlayer.src = "$mjpegUrl?t=" + new Date().getTime();
                        statusText.textContent = "正在刷新流...";
                    }
                </script>
            </body>
            </html>
        """.trimIndent()

        return newFixedLengthResponse(
            Response.Status.OK,
            "text/html",
            html
        )
    }

    private fun serveInfo(session: IHTTPSession): Response {
        val formatName = when (currentVideoFormat) {
            MediaFormat.MIMETYPE_VIDEO_VP8 -> "vp8"
            MediaFormat.MIMETYPE_VIDEO_VP9 -> "vp9"
            MediaFormat.MIMETYPE_VIDEO_HEVC -> "hevc"
            else -> "h264"
        }

        val info = """
            {
                "status": "active",
                "clients": ${activeStreams.size + mjpegStreams.size},
                "mjpeg_clients": ${mjpegStreams.size},
                "video_clients": ${activeStreams.size},
                "uptime": ${System.currentTimeMillis()},
                "format": "$formatName",
                "port": $port,
                "has_frame": ${latestFrame.get() != null},
                "server_time": "${java.util.Date()}"
            }
        """.trimIndent()

        return newFixedLengthResponse(
            Response.Status.OK,
            "application/json",
            info
        )
    }

    private fun serveIndex(session: IHTTPSession): Response {
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>HTTP Video Stream</title>
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    video { width: 100%; max-width: 800px; background-color: #000; }
                    img { width: 100%; max-width: 800px; background-color: #000; }
                    h1 { color: #333; }
                    .controls { margin-top: 20px; }
                    button { padding: 8px 16px; margin-right: 10px; }
                    .status { margin-top: 10px; color: #666; }
                    .stream-container { margin-top: 20px; }
                    .tab-buttons { margin-bottom: 10px; }
                    .tab-button { padding: 8px 16px; margin-right: 5px; cursor: pointer; }
                    .tab-button.active { background-color: #4CAF50; color: white; }
                    .tab-content { display: none; }
                    .tab-content.active { display: block; }
                </style>
            </head>
            <body>
                <h1>HTTP Video Stream</h1>

                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="mjpeg">MJPEG流 (推荐)</button>
                    <button class="tab-button" data-tab="video">视频流</button>
                    <button class="tab-button" data-tab="direct">直接MJPEG</button>
                    <button class="tab-button" data-tab="debug">调试信息</button>
                </div>

                <div class="tab-content active" id="mjpeg-tab">
                    <h2>MJPEG流 (推荐大多数浏览器)</h2>
                    <div class="stream-container">
                        <img id="mjpegPlayer" src="/stream.mjpeg" alt="MJPEG Stream">
                    </div>
                    <div class="controls">
                        <button id="refreshMjpegBtn">刷新MJPEG</button>
                    </div>
                    <div class="status" id="mjpegStatusText">正在连接MJPEG流...</div>
                </div>

                <div class="tab-content" id="video-tab">
                    <h2>视频流 (H.264/VP8/VP9)</h2>
                    <div class="stream-container">
                        <video id="videoPlayer" controls autoplay muted>
                            <!-- 视频源将由JavaScript动态设置 -->
                        </video>
                    </div>
                    <div class="controls">
                        <button id="refreshBtn">刷新视频</button>
                        <button id="toggleMuteBtn">开关声音</button>
                    </div>
                    <div class="status" id="statusText">正在连接视频流...</div>
                </div>

                <div class="tab-content" id="direct-tab">
                    <h2>直接MJPEG流 (适用于某些浏览器)</h2>
                    <p>如果上面的MJPEG流不工作，请尝试这个直接链接：</p>
                    <div class="stream-container">
                        <iframe src="/direct-mjpeg" width="100%" height="480" frameborder="0"></iframe>
                    </div>
                    <p>如果上面的iframe不显示，<a href="/direct-mjpeg" target="_blank">点击这里在新标签页中打开</a></p>
                </div>

                <div class="tab-content" id="debug-tab">
                    <h2>调试信息</h2>
                    <div class="debug-info">
                        <p><strong>服务器信息:</strong></p>
                        <p>服务器端口: <span id="serverPort">${port}</span></p>
                        <p>活跃连接数: <span id="activeClients">${activeStreams.size + mjpegStreams.size}</span></p>
                        <p>MJPEG连接数: <span id="mjpegClients">${mjpegStreams.size}</span></p>
                        <p>视频连接数: <span id="videoClients">${activeStreams.size}</span></p>
                        <p>有可用帧: <span id="hasFrame">${latestFrame.get() != null}</span></p>
                        <p>服务器时间: <span id="serverTime">${java.util.Date()}</span></p>
                        <button id="refreshInfoBtn" class="button">刷新信息</button>
                    </div>
                    <div class="debug-info">
                        <p><strong>直接链接:</strong></p>
                        <p>MJPEG流: <a href="/stream.mjpeg" target="_blank">/stream.mjpeg</a></p>
                        <p>视频流: <a href="/stream.mp4" target="_blank">/stream.mp4</a></p>
                        <p>直接MJPEG: <a href="/direct-mjpeg" target="_blank">/direct-mjpeg</a></p>
                        <p>信息API: <a href="/info" target="_blank">/info</a></p>
                    </div>
                </div>

                <script>
                    // 标签页切换
                    const tabButtons = document.querySelectorAll('.tab-button');
                    const tabContents = document.querySelectorAll('.tab-content');

                    tabButtons.forEach(button => {
                        button.addEventListener('click', () => {
                            const tabId = button.getAttribute('data-tab');

                            // 更新按钮状态
                            tabButtons.forEach(btn => btn.classList.remove('active'));
                            button.classList.add('active');

                            // 更新内容显示
                            tabContents.forEach(content => content.classList.remove('active'));
                            document.getElementById(tabId + '-tab').classList.add('active');
                        });
                    });

                    // MJPEG流处理
                    const mjpegPlayer = document.getElementById('mjpegPlayer');
                    const refreshMjpegBtn = document.getElementById('refreshMjpegBtn');
                    const mjpegStatusText = document.getElementById('mjpegStatusText');

                    function refreshMjpeg() {
                        mjpegStatusText.textContent = '正在重新连接MJPEG流...';
                        mjpegPlayer.src = '/stream.mjpeg?t=' + new Date().getTime();
                    }

                    refreshMjpegBtn.addEventListener('click', refreshMjpeg);

                    mjpegPlayer.addEventListener('load', function() {
                        mjpegStatusText.textContent = 'MJPEG流已连接';
                    });

                    mjpegPlayer.addEventListener('error', function() {
                        mjpegStatusText.textContent = 'MJPEG流连接失败，5秒后自动重试...';
                        setTimeout(refreshMjpeg, 5000);
                    });

                    // 视频流处理
                    const videoPlayer = document.getElementById('videoPlayer');
                    const refreshBtn = document.getElementById('refreshBtn');
                    const toggleMuteBtn = document.getElementById('toggleMuteBtn');
                    const statusText = document.getElementById('statusText');

                    // 视频格式和URL
                    let currentFormat = 'h264';
                    let videoUrl = '/stream.mp4';
                    let mimeType = 'video/mp4';

                    // 更新视频源
                    function updateVideoSource(format) {
                        currentFormat = format;

                        if (format === 'vp8' || format === 'vp9') {
                            videoUrl = '/stream.webm';
                            mimeType = 'video/webm';
                        } else {
                            videoUrl = '/stream.mp4';
                            mimeType = 'video/mp4';
                        }

                        loadVideo();
                    }

                    // 加载视频
                    function loadVideo() {
                        // 移除旧的视频源
                        while (videoPlayer.firstChild) {
                            videoPlayer.removeChild(videoPlayer.firstChild);
                        }

                        // 创建新的视频源
                        const source = document.createElement('source');
                        source.src = videoUrl + '?t=' + new Date().getTime(); // 添加时间戳防止缓存
                        source.type = mimeType;
                        videoPlayer.appendChild(source);

                        // 加载视频
                        videoPlayer.load();
                        statusText.textContent = '正在加载视频流 (' + currentFormat + ')...';

                        // 自动播放
                        videoPlayer.play().catch(e => {
                            console.error('自动播放失败:', e);
                            statusText.textContent = '自动播放失败，请点击视频播放';
                        });
                    }

                    // 从服务器获取格式信息
                    function fetchFormatInfo() {
                        fetch('/info')
                            .then(response => response.json())
                            .then(data => {
                                if (data.format && data.format !== currentFormat) {
                                    statusText.textContent = '检测到视频格式: ' + data.format;
                                    updateVideoSource(data.format);
                                }
                            })
                            .catch(error => {
                                console.error('获取格式信息失败:', error);
                                statusText.textContent = '获取视频格式失败，使用默认格式';
                            });
                    }

                    // 视频事件处理
                    videoPlayer.addEventListener('playing', function() {
                        statusText.textContent = '视频播放中 (' + currentFormat + ')';
                    });

                    videoPlayer.addEventListener('error', function(e) {
                        console.error('视频错误:', e);
                        statusText.textContent = '视频加载失败，5秒后自动重试...';
                        setTimeout(loadVideo, 5000);
                    });

                    videoPlayer.addEventListener('stalled', function() {
                        statusText.textContent = '视频加载停滞，请等待或刷新...';
                    });

                    // 按钮事件
                    refreshBtn.addEventListener('click', function() {
                        fetchFormatInfo();
                        loadVideo();
                    });

                    toggleMuteBtn.addEventListener('click', function() {
                        videoPlayer.muted = !videoPlayer.muted;
                        toggleMuteBtn.textContent = videoPlayer.muted ? '打开声音' : '关闭声音';
                    });

                    // 调试信息刷新
                    const refreshInfoBtn = document.getElementById('refreshInfoBtn');

                    function updateDebugInfo() {
                        fetch('/info')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('serverPort').textContent = data.port;
                                document.getElementById('activeClients').textContent = data.clients;
                                document.getElementById('mjpegClients').textContent = data.mjpeg_clients;
                                document.getElementById('videoClients').textContent = data.video_clients;
                                document.getElementById('hasFrame').textContent = data.has_frame;
                                document.getElementById('serverTime').textContent = data.server_time;
                                console.log('Debug info updated:', data);
                            })
                            .catch(error => {
                                console.error('Error fetching debug info:', error);
                            });
                    }

                    refreshInfoBtn?.addEventListener('click', updateDebugInfo);

                    // 初始化
                    fetchFormatInfo();

                    // 每5秒自动刷新一次调试信息
                    setInterval(function() {
                        if (document.getElementById('debug-tab').classList.contains('active')) {
                            updateDebugInfo();
                        }
                    }, 5000);
                </script>
            </body>
            </html>
        """.trimIndent()

        return newFixedLengthResponse(html)
    }

    fun sendVideoData(data: ByteArray, isHeader: Boolean = false, videoFormat: String = MediaFormat.MIMETYPE_VIDEO_AVC) {
        if (isHeader) {
            videoHeader = data.copyOf()
            currentVideoFormat = videoFormat
        }

        // Send to all active connections
        val closedStreams = mutableListOf<String>()

        activeStreams.forEach { (clientId, connection) ->
            try {
                connection.outputStream.write(data)
                connection.outputStream.flush()
            } catch (e: IOException) {
                Log.e(TAG, "Error sending data to client $clientId: ${e.message}")
                closedStreams.add(clientId)
            }
        }

        // Clean up closed connections
        closedStreams.forEach { clientId ->
            closeConnection(clientId)
        }
    }

    fun sendMjpegFrame(bitmap: Bitmap) {
        try {
            if (bitmap.isRecycled) {
                Log.e(TAG, "Cannot send a recycled bitmap")
                return
            }

            // 创建一个副本，以便原始位图可以被回收
            val frameCopy = bitmap.copy(bitmap.config, false)

            // 保存最新的帧
            val oldFrame = latestFrame.getAndSet(frameCopy)

            // 回收旧帧（如果有）
            if (oldFrame != null && !oldFrame.isRecycled) {
                oldFrame.recycle()
            }

            // 检查是否有活跃的MJPEG连接
            if (mjpegStreams.isEmpty()) {
                Log.d(TAG, "No active MJPEG streams, keeping frame for future connections")
                // 回收原始位图，因为我们已经创建了副本
                bitmap.recycle()
                return
            }

            Log.d(TAG, "Sending MJPEG frame to ${mjpegStreams.size} active streams")

            // 发送到所有MJPEG连接
            val closedStreams = mutableListOf<String>()

            mjpegStreams.forEach { (clientId, streamer) ->
                try {
                    if (streamer.isRunning()) {
                        // 为每个客户端创建一个新的副本
                        val clientCopy = frameCopy.copy(frameCopy.config, false)
                        streamer.sendFrame(clientCopy)
                        // 在MjpegStreamer中处理回收
                    } else {
                        Log.d(TAG, "Stream for client $clientId is not running, marking for cleanup")
                        closedStreams.add(clientId)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending MJPEG frame to client $clientId: ${e.message}")
                    closedStreams.add(clientId)
                }
            }

            // 清理关闭的连接
            if (closedStreams.isNotEmpty()) {
                Log.d(TAG, "Cleaning up ${closedStreams.size} closed streams")
                closedStreams.forEach { clientId ->
                    closeMjpegConnection(clientId)
                }
            }

            // 回收原始位图，因为我们已经创建了副本
            bitmap.recycle()
        } catch (e: Exception) {
            Log.e(TAG, "Error in sendMjpegFrame: ${e.message}", e)
            // 确保原始位图被回收
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
    }

    private fun serveMjpegStream(session: IHTTPSession): Response {
        val streamId = session.remoteHostName + ":" + session.remoteIpAddress
        Log.d(TAG, "Starting MJPEG stream for client: $streamId")

        // 创建一个管道流，用于传输MJPEG数据
        val pipedOutputStream = PipedOutputStream()
        val pipedInputStream = PipedInputStream(pipedOutputStream, 64 * 1024) // 64KB buffer

        // 创建MJPEG流
        val mjpegStreamer = MjpegStreamer(pipedOutputStream)

        // 在后台线程中启动MJPEG流
        Thread {
            try {
                // 启动MJPEG流
                mjpegStreamer.start()

                // 将流添加到活跃流列表中
                mjpegStreams[streamId] = mjpegStreamer

                // 检查是否有最新的帧，如果有则发送
                val currentFrame = latestFrame.get()
                if (currentFrame != null && !currentFrame.isRecycled) {
                    // 创建一个副本发送
                    val copy = currentFrame.copy(currentFrame.config, false)
                    mjpegStreamer.sendFrame(copy)
                }

                // 保持连接，直到客户端断开
                while (mjpegStreamer.isRunning()) {
                    Thread.sleep(100)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in MJPEG stream thread: ${e.message}", e)
            } finally {
                closeMjpegConnection(streamId)
            }
        }.start()

        // 创建响应
        val response = newChunkedResponse(
            Response.Status.OK,
            "multipart/x-mixed-replace; boundary=mjpegstream",
            pipedInputStream
        )

        // 设置响应选项
        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
        response.addHeader("Pragma", "no-cache")
        response.addHeader("Expires", "0")
        response.addHeader("Access-Control-Allow-Origin", "*")
        response.setKeepAlive(true)

        return response
    }

    private fun closeMjpegConnection(clientId: String) {
        try {
            val streamer = mjpegStreams[clientId]
            if (streamer != null) {
                Log.d(TAG, "Stopping MJPEG streamer for client: $clientId")
                streamer.stop()
                mjpegStreams.remove(clientId)
                Log.d(TAG, "Closed MJPEG connection for client: $clientId")
            } else {
                Log.d(TAG, "No MJPEG streamer found for client: $clientId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error closing MJPEG connection for client $clientId: ${e.message}", e)
        }
    }

    private fun getMimeTypeForFormat(format: String): String {
        return when (format) {
            MediaFormat.MIMETYPE_VIDEO_VP8, MediaFormat.MIMETYPE_VIDEO_VP9 -> "video/webm"
            else -> "video/mp4"
        }
    }

    private fun closeConnection(clientId: String) {
        try {
            activeStreams[clientId]?.close()
            activeStreams.remove(clientId)
            Log.d(TAG, "Closed connection for client: $clientId")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing connection for client $clientId: ${e.message}")
        }
    }

    override fun stop() {
        // Close all active connections
        activeStreams.keys.toList().forEach { clientId ->
            closeConnection(clientId)
        }

        // Close all MJPEG connections
        mjpegStreams.keys.toList().forEach { clientId ->
            closeMjpegConnection(clientId)
        }

        super.stop()
    }

    inner class StreamConnection {
        val outputStream = PipedOutputStream()
        val inputStream = PipedInputStream(outputStream, 64 * 1024) // 64KB buffer

        fun close() {
            try {
                outputStream.close()
                inputStream.close()
            } catch (e: IOException) {
                Log.e(TAG, "Error closing stream connection: ${e.message}")
            }
        }
    }
}
