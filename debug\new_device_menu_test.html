<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新设备菜单系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-devices {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .device-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .device-info {
            margin-bottom: 15px;
        }
        
        .device-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .device-info-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .device-info-value {
            color: #495057;
            font-weight: 600;
        }
        
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        
        .device-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .control-main-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
        }
        
        /* ===== 新的设备菜单系统 ===== */
        .device-menu-container {
            position: relative;
            display: inline-block;
        }

        .device-menu-trigger {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            position: relative;
            z-index: 10;
        }

        .device-menu-trigger:hover:not(.menu-active) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .device-menu-trigger.menu-active {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .device-menu-panel {
            position: fixed;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            min-width: 720px;
            max-width: 90vw;
            z-index: 999999;
            display: none;
            padding: 15px;
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.15s ease, transform 0.15s ease;
        }

        .device-menu-panel.menu-visible {
            display: block;
            opacity: 1;
            transform: scale(1);
        }

        .device-menu-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .device-menu-section {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f1f3f4;
        }

        .device-menu-section:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .device-menu-section h4 {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .device-menu-item {
            display: block;
            width: 100%;
            background: none;
            border: none;
            padding: 8px 12px;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 3px;
            transition: background-color 0.2s;
            color: #495057;
        }

        .device-menu-item:hover {
            background-color: #f8f9fa;
        }

        .device-menu-item.item-success {
            color: #28a745;
        }

        .device-menu-item.item-warning {
            color: #ffc107;
        }

        .device-menu-item.item-danger {
            color: #dc3545;
        }

        .device-menu-item.item-info {
            color: #17a2b8;
        }

        .device-menu-item.item-primary {
            color: #007bff;
        }

        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000000;
            max-width: 300px;
            font-family: monospace;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .device-menu-panel {
                min-width: 280px;
                max-width: 95vw;
            }
            
            .device-menu-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debugPanel">
        <strong>🎛️ 新设备菜单测试</strong><br>
        <div id="debugInfo">等待操作...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🎛️ 新设备菜单系统测试</h1>
            <p>完全重新设计的菜单系统，避免CSS/JS冲突</p>
        </div>

        <div class="test-devices">
            <!-- 测试设备1 -->
            <div class="device-card">
                <div class="device-info">
                    <div class="device-info-item">
                        <span class="device-info-label">设备ID:</span>
                        <span class="device-info-value">test-device-001</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">ocean3</span>
                    </div>
                </div>
                
                <div class="device-controls">
                    <button class="control-main-btn" onclick="testAction('重启服务')">
                        重启服务
                    </button>
                    <div class="device-menu-container">
                        <button class="device-menu-trigger" onclick="showDeviceMenu('test-device-001', this)">
                            ⚙️
                        </button>
                    </div>
                </div>
            </div>

            <!-- 测试设备2 -->
            <div class="device-card">
                <div class="device-info">
                    <div class="device-info-item">
                        <span class="device-info-label">设备ID:</span>
                        <span class="device-info-value">test-device-002</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">mygame</span>
                    </div>
                </div>
                
                <div class="device-controls">
                    <button class="control-main-btn" onclick="testAction('重启服务')">
                        重启服务
                    </button>
                    <div class="device-menu-container">
                        <button class="device-menu-trigger" onclick="showDeviceMenu('test-device-002', this)">
                            ⚙️
                        </button>
                    </div>
                </div>
            </div>

            <!-- 测试设备3 -->
            <div class="device-card">
                <div class="device-info">
                    <div class="device-info-item">
                        <span class="device-info-label">设备ID:</span>
                        <span class="device-info-value">test-device-003</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">状态:</span>
                        <span class="device-info-value status-online">● 在线</span>
                    </div>
                    <div class="device-info-item">
                        <span class="device-info-label">游戏:</span>
                        <span class="device-info-value">testgame</span>
                    </div>
                </div>
                
                <div class="device-controls">
                    <button class="control-main-btn" onclick="testAction('重启服务')">
                        重启服务
                    </button>
                    <div class="device-menu-container">
                        <button class="device-menu-trigger" onclick="showDeviceMenu('test-device-003', this)">
                            ⚙️
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 菜单容器 -->
    <div id="device-menu-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 999998;"></div>

    <script>
        let activeMenuId = null;
        let activeTrigger = null;
        let menuOverlay = document.getElementById('device-menu-overlay');

        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <strong>最新操作:</strong> ${message}<br>
                <strong>活动菜单:</strong> ${activeMenuId || '无'}<br>
                <small>时间: ${new Date().toLocaleTimeString()}</small>
            `;
        }

        function showDeviceMenu(deviceId, triggerButton) {
            console.log('🎛️ 显示设备菜单:', deviceId);
            updateDebugInfo(`显示菜单: ${deviceId}`);
            
            // 隐藏所有其他菜单
            hideAllDeviceMenus();
            
            // 创建菜单面板
            const menuPanel = createMenuPanel(deviceId);
            
            // 计算位置
            const position = calculateMenuPosition(triggerButton, menuPanel);
            
            // 设置位置
            menuPanel.style.left = `${position.left}px`;
            menuPanel.style.top = `${position.top}px`;
            
            // 添加到容器
            menuOverlay.appendChild(menuPanel);
            menuOverlay.style.pointerEvents = 'auto';
            
            // 激活按钮状态
            triggerButton.classList.add('menu-active');
            
            // 显示菜单（带动画）
            requestAnimationFrame(() => {
                menuPanel.classList.add('menu-visible');
            });
            
            // 记录当前活动菜单
            activeMenuId = deviceId;
            activeTrigger = triggerButton;
            
            console.log('✅ 设备菜单显示完成:', deviceId);
        }

        function createMenuPanel(deviceId) {
            const panel = document.createElement('div');
            panel.className = 'device-menu-panel';
            panel.id = `device-menu-${deviceId}`;
            
            panel.innerHTML = `
                <div class="device-menu-grid">
                    <div class="device-menu-section">
                        <h4>服务控制</h4>
                        <button class="device-menu-item item-success" onclick="executeMenuAction('${deviceId}', 'start_service')">
                            🚀 启动服务
                        </button>
                        <button class="device-menu-item item-danger" onclick="executeMenuAction('${deviceId}', 'stop_service')">
                            ⏹️ 停止服务
                        </button>
                        <button class="device-menu-item item-warning" onclick="executeMenuAction('${deviceId}', 'restart_service')">
                            🔄 重启服务
                        </button>
                        <h4>视频控制</h4>
                        <button class="device-menu-item item-info" onclick="executeMenuAction('${deviceId}', 'video_settings')">
                            🎥 视频参数设置
                        </button>
                        <button class="device-menu-item item-primary" onclick="executeMenuAction('${deviceId}', 'take_screenshot')">
                            📸 视频流截屏
                        </button>
                    </div>
                    <div class="device-menu-section">
                        <h4>游戏控制</h4>
                        <button class="device-menu-item item-info" onclick="executeMenuAction('${deviceId}', 'game_settings')">
                            🎮 游戏设置
                        </button>
                        <h4>日志管理</h4>
                        <button class="device-menu-item item-success" onclick="executeMenuAction('${deviceId}', 'enable_log')">
                            📝 开启日志显示
                        </button>
                        <button class="device-menu-item item-warning" onclick="executeMenuAction('${deviceId}', 'disable_log')">
                            🚫 关闭日志显示
                        </button>
                        <button class="device-menu-item item-primary" onclick="executeMenuAction('${deviceId}', 'download_logs')">
                            📥 下载日志(FTP)
                        </button>
                    </div>
                    <div class="device-menu-section">
                        <h4>网络配置</h4>
                        <button class="device-menu-item item-info" onclick="executeMenuAction('${deviceId}', 'stun_turn_config')">
                            🌐 STUN/TURN配置
                        </button>
                        <button class="device-menu-item item-success" onclick="executeMenuAction('${deviceId}', 'send_network_config')">
                            📡 发送网络配置
                        </button>
                        <h4>系统控制</h4>
                        <button class="device-menu-item item-danger" onclick="executeMenuAction('${deviceId}', 'reboot_device')">
                            🔄 重启设备
                        </button>
                        <button class="device-menu-item item-warning" onclick="executeMenuAction('${deviceId}', 'upgrade_app')">
                            📦 升级应用
                        </button>
                    </div>
                </div>
            `;
            
            return panel;
        }

        function calculateMenuPosition(triggerButton, menuPanel) {
            const buttonRect = triggerButton.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            
            // 临时添加到DOM以获取尺寸
            menuPanel.style.visibility = 'hidden';
            menuPanel.style.display = 'block';
            menuOverlay.appendChild(menuPanel);
            
            const menuRect = menuPanel.getBoundingClientRect();
            const menuWidth = menuRect.width;
            const menuHeight = menuRect.height;
            
            // 移除临时添加的元素
            menuOverlay.removeChild(menuPanel);
            menuPanel.style.visibility = '';
            menuPanel.style.display = '';
            
            // 默认位置：按钮下方左对齐
            let left = buttonRect.left;
            let top = buttonRect.bottom + 10;
            
            // 右边界检查
            if (left + menuWidth > windowWidth - 20) {
                left = buttonRect.right - menuWidth;
            }
            
            // 左边界检查
            if (left < 20) {
                left = 20;
            }
            
            // 下边界检查
            if (top + menuHeight > windowHeight - 20) {
                top = buttonRect.top - menuHeight - 10;
            }
            
            // 上边界检查
            if (top < 20) {
                top = 20;
            }
            
            console.log('📍 菜单位置计算:', { left, top, menuWidth, menuHeight });
            
            return { left, top };
        }

        function hideAllDeviceMenus() {
            if (menuOverlay) {
                // 移除所有菜单面板
                menuOverlay.innerHTML = '';
                menuOverlay.style.pointerEvents = 'none';
            }
            
            // 移除所有按钮的激活状态
            document.querySelectorAll('.device-menu-trigger.menu-active').forEach(button => {
                button.classList.remove('menu-active');
            });
            
            // 清除活动菜单记录
            activeMenuId = null;
            activeTrigger = null;
            
            console.log('❌ 所有设备菜单已隐藏');
            updateDebugInfo('所有菜单已隐藏');
        }

        function executeMenuAction(deviceId, action) {
            console.log('⚡ 执行菜单动作:', { deviceId, action });
            updateDebugInfo(`执行动作: ${action} -> ${deviceId}`);
            
            // 隐藏菜单
            hideAllDeviceMenus();
            
            // 显示执行结果
            alert(`执行动作: ${action}\n设备: ${deviceId}`);
        }

        function testAction(action) {
            updateDebugInfo(`主按钮: ${action}`);
            alert(`主按钮动作: ${action}`);
        }

        // 点击外部关闭菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.device-menu-container') && 
                !event.target.closest('.device-menu-panel')) {
                hideAllDeviceMenus();
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                hideAllDeviceMenus();
            }
        });

        updateDebugInfo('页面加载完成');
        console.log('🎛️ 新设备菜单系统测试页面已加载');
    </script>
</body>
</html>
