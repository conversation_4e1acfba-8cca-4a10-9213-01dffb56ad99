#!/usr/bin/env python3
"""
FTP升级功能测试脚本
用于测试管理控制台的FTP升级功能
"""

import json
import asyncio
import websockets
import argparse
import sys

class AdminTestClient:
    def __init__(self, server_url="ws://localhost:8765"):
        self.server_url = server_url
        self.websocket = None
        
    async def connect(self):
        """连接到信令服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            print(f"✅ 已连接到信令服务器: {self.server_url}")
            
            # 注册为管理控制台
            register_msg = {
                "type": "register",
                "id": "test-admin-console",
                "role": "admin",
                "name": "测试管理控制台"
            }
            await self.websocket.send(json.dumps(register_msg))
            print("📝 已注册为管理控制台")
            
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def send_upgrade_command(self, target_sender, apk_url, version, force=False):
        """发送升级命令"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return False
            
        try:
            upgrade_command = {
                "type": "command",
                "target": target_sender,
                "command": "upgrade",
                "params": {
                    "apk_url": apk_url,
                    "version": version,
                    "force": force
                }
            }
            
            await self.websocket.send(json.dumps(upgrade_command))
            print(f"📦 已发送升级命令到 {target_sender}")
            print(f"   APK地址: {apk_url}")
            print(f"   版本: {version}")
            print(f"   强制升级: {force}")
            
            return True
        except Exception as e:
            print(f"❌ 发送升级命令失败: {e}")
            return False
    
    async def listen_for_responses(self, timeout=300):
        """监听服务器响应"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return
            
        print(f"👂 开始监听响应消息 (超时: {timeout}秒)...")
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    print(f"⚠️ 收到非JSON消息: {message}")
                except Exception as e:
                    print(f"❌ 处理消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 连接已关闭")
        except asyncio.TimeoutError:
            print("⏰ 监听超时")
        except Exception as e:
            print(f"❌ 监听失败: {e}")
    
    async def handle_message(self, data):
        """处理收到的消息"""
        message_type = data.get("type")
        
        if message_type == "status_update":
            # 处理状态更新消息
            status_type = data.get("status_type")
            step = data.get("step")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            device_id = data.get("from", "未知设备")
            
            if status_type == "upgrade":
                print(f"📊 [{device_id}] 升级进度: {step} ({progress}%) - {message}")
            else:
                print(f"📊 [{device_id}] 状态更新: {status_type} - {step} - {message}")
                
        elif message_type == "command_response":
            # 处理命令响应
            command = data.get("command")
            success = data.get("success")
            message = data.get("message", "")
            device_id = data.get("from", "未知设备")
            
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} [{device_id}] 命令响应: {command} - {message}")
            
        elif message_type == "registered":
            # 处理注册响应
            success = data.get("success", False)
            message = data.get("message", "")
            
            if success:
                print(f"✅ 注册成功: {message}")
            else:
                print(f"❌ 注册失败: {message}")
                
        else:
            print(f"📨 收到消息: {message_type} - {data}")
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 已断开连接")

async def test_http_upgrade():
    """测试HTTP升级"""
    print("\n🧪 测试HTTP升级功能")
    client = AdminTestClient()
    
    if await client.connect():
        await client.send_upgrade_command(
            target_sender="gamev-test001",
            apk_url="https://example.com/app-v2.0.apk",
            version="2.0.0",
            force=False
        )
        
        await client.listen_for_responses(timeout=60)
        await client.disconnect()

async def test_ftp_upgrade():
    """测试FTP升级"""
    print("\n🧪 测试FTP升级功能")
    client = AdminTestClient()
    
    if await client.connect():
        await client.send_upgrade_command(
            target_sender="gamev-test001",
            apk_url="ftp://39.96.165.173/webrtc_sender_builds/webrtc_sender.apk",
            version="2.1.0",
            force=True
        )
        
        await client.listen_for_responses(timeout=300)  # FTP下载可能需要更长时间
        await client.disconnect()

async def test_custom_upgrade(apk_url, version, target, force):
    """测试自定义升级参数"""
    print(f"\n🧪 测试自定义升级: {apk_url}")
    client = AdminTestClient()
    
    if await client.connect():
        await client.send_upgrade_command(
            target_sender=target,
            apk_url=apk_url,
            version=version,
            force=force
        )
        
        await client.listen_for_responses(timeout=300)
        await client.disconnect()

def main():
    parser = argparse.ArgumentParser(description="FTP升级功能测试工具")
    parser.add_argument("--server", default="ws://localhost:8765", help="信令服务器地址")
    parser.add_argument("--test", choices=["http", "ftp", "custom"], default="ftp", help="测试类型")
    parser.add_argument("--url", help="自定义APK下载地址")
    parser.add_argument("--version", default="1.0.0", help="版本号")
    parser.add_argument("--target", default="gamev-test001", help="目标设备ID")
    parser.add_argument("--force", action="store_true", help="强制升级")
    
    args = parser.parse_args()
    
    print("🚀 FTP升级功能测试工具")
    print("=" * 50)
    
    try:
        if args.test == "http":
            asyncio.run(test_http_upgrade())
        elif args.test == "ftp":
            asyncio.run(test_ftp_upgrade())
        elif args.test == "custom":
            if not args.url:
                print("❌ 自定义测试需要提供 --url 参数")
                sys.exit(1)
            asyncio.run(test_custom_upgrade(args.url, args.version, args.target, args.force))
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
