# WebRTC视频流系统部署指南

本指南将帮助您将WebRTC视频流系统部署到域名 `sling.91jdcd.com` 并启用SSL。

## 1. 服务器准备

### 系统要求

- 操作系统: Ubuntu 20.04/22.04 LTS 或 CentOS 8 (推荐Ubuntu)
- Python 3.7+
- Nginx
- 开放端口: 80, 443

### 安装基本软件

```bash
# Ubuntu
sudo apt update
sudo apt install -y python3 python3-pip nginx certbot python3-certbot-nginx git

# CentOS
sudo yum update
sudo yum install -y python3 python3-pip nginx certbot git
```

### 安装Python依赖

```bash
pip3 install websockets aiortc opencv-python numpy
```

## 2. 获取SSL证书

使用Let's Encrypt获取免费的SSL证书:

```bash
# 确保域名 sling.91jdcd.com 已经解析到服务器IP
sudo certbot certonly --nginx -d sling.91jdcd.com
```

证书将保存在以下位置:
- 证书文件: `/etc/letsencrypt/live/sling.91jdcd.com/fullchain.pem`
- 私钥文件: `/etc/letsencrypt/live/sling.91jdcd.com/privkey.pem`

## 3. 配置Nginx

创建Nginx配置文件:

```bash
sudo nano /etc/nginx/sites-available/sling.91jdcd.com
```

添加以下内容:

```nginx
server {
    listen 80;
    server_name sling.91jdcd.com;
    
    # 将HTTP请求重定向到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name sling.91jdcd.com;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/sling.91jdcd.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sling.91jdcd.com/privkey.pem;
    
    # SSL参数优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # HSTS (可选，但推荐)
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 静态文件服务
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://localhost:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;  # 24小时
    }
}
```

启用配置并重启Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/sling.91jdcd.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 4. 部署WebRTC视频流系统

### 创建部署目录

```bash
sudo mkdir -p /opt/webrtc-signaling
sudo chown $USER:$USER /opt/webrtc-signaling
cd /opt/webrtc-signaling
```

### 下载代码

将所有文件上传到服务器，或者使用git克隆代码库。

### 设置系统服务

```bash
sudo cp webrtc-signaling.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable webrtc-signaling
sudo systemctl start webrtc-signaling
sudo systemctl status webrtc-signaling
```

## 5. 测试部署

1. 在浏览器中访问 `https://sling.91jdcd.com`
2. 您应该能看到WebRTC视频流系统的Web界面
3. 启动视频发送端:

```bash
cd /opt/webrtc-signaling
python3 web_sender_adapter.py --video test --id video-stream --name "视频流" --description "WebRTC视频流" --signaling wss://sling.91jdcd.com/ws/
```

4. 在Web界面中，您应该能看到视频源并连接观看

## 6. 故障排除

### 检查服务状态

```bash
sudo systemctl status webrtc-signaling
sudo systemctl status nginx
```

### 查看日志

```bash
sudo journalctl -u webrtc-signaling -f
sudo tail -f /var/log/nginx/error.log
```

### 检查防火墙

确保端口80和443已开放:

```bash
# Ubuntu
sudo ufw status
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS
sudo firewall-cmd --list-all
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 检查SSL证书

```bash
sudo certbot certificates
```

## 7. 自动续期SSL证书

Let's Encrypt证书有效期为90天，需要定期续期。Certbot会自动添加一个cron任务来处理续期:

```bash
sudo systemctl status certbot.timer
```

如果需要手动续期:

```bash
sudo certbot renew
```

## 8. 安全建议

1. 设置防火墙，只开放必要的端口
2. 定期更新系统和软件包
3. 使用强密码保护服务器
4. 考虑添加访问控制，例如HTTP基本认证

## 9. 性能优化

1. 调整Nginx工作进程数:

```bash
sudo nano /etc/nginx/nginx.conf
```

修改 `worker_processes` 为服务器CPU核心数。

2. 启用Nginx缓存:

```nginx
# 在http块中添加
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=webrtc_cache:10m max_size=1g inactive=60m;

# 在location块中添加
proxy_cache webrtc_cache;
proxy_cache_valid 200 10m;
```

3. 优化系统参数:

```bash
sudo nano /etc/sysctl.conf
```

添加以下内容:

```
net.core.somaxconn = 4096
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.ip_local_port_range = 1024 65535
net.ipv4.tcp_tw_reuse = 1
```

应用更改:

```bash
sudo sysctl -p
```
