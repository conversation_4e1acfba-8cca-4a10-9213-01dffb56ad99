# 游戏包名获取问题修复

## 问题分析

### 1. 日志显示的问题

**Android端日志**：
```
🔍 检查开机状态: 当前启动时间=1756106101603, 上次上报时间=1756104508868
🚀 检测到新开机，准备上报开机信息
📋 开机信息收集完成: CPU_ID=50864464..., 游戏=
📤 发送开机信息到信令服务器: gamev-b246c42d
开机信息内容: CPU_ID=50864464..., 游戏=None
```

**网页显示问题**：
```
CPU ID: undefined
⚠️ 未配置游戏
```

### 2. 根本原因

1. **SharedPreferences名称错误**：
   - 错误使用：`"webrtc_preferences"`
   - 正确应该：`"webrtc_sender_prefs"`（Constants.PREF_NAME）

2. **键名不匹配**：
   - 应用实际使用：`Constants.PREF_AUTO_START_GAME_PACKAGE`
   - 错误尝试：`"auto_start_game_package"`, `"game_package"` 等

3. **CPU ID 传输问题**：
   - 服务器端数据结构正确
   - 可能是前端接收问题

## 修复方案

### 1. 修复游戏包名获取

**修复前**：
```kotlin
private fun getConfiguredGamePackage(): String {
    val prefs = context.getSharedPreferences("webrtc_preferences", Context.MODE_PRIVATE)
    val gamePackage = prefs.getString("auto_start_game_package", "") 
        ?: prefs.getString("game_package", "")
        ?: prefs.getString("selected_game_package", "")
        ?: ""
    return gamePackage
}
```

**修复后**：
```kotlin
private fun getConfiguredGamePackage(): String {
    // 使用正确的SharedPreferences名称和键名
    val prefs = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
    val gamePackage = prefs.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""
    
    Log.d(TAG, "获取游戏包名: $gamePackage (从${Constants.PREF_NAME}/${Constants.PREF_AUTO_START_GAME_PACKAGE})")
    return gamePackage
}
```

### 2. 正确的配置路径

**SharedPreferences配置**：
```kotlin
// 文件名
Constants.PREF_NAME = "webrtc_sender_prefs"

// 游戏包名键
Constants.PREF_AUTO_START_GAME_PACKAGE = "auto_start_game_package"

// 默认值
Constants.DEFAULT_AUTO_START_GAME_PACKAGE = ""
```

**应用中的使用**：
```kotlin
// GameLauncher.kt
fun getAutoStartGamePackage(context: Context): String {
    val preferences = context.getSharedPreferences("webrtc_sender_prefs", Context.MODE_PRIVATE)
    return preferences.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""
}

// WebRTCManager.kt
fun getGamePackage(): String {
    return WebRTCSenderApp.instance.preferences.getString(
        Constants.PREF_AUTO_START_GAME_PACKAGE,
        ""
    ) ?: ""
}

// SettingsActivity.kt
selectedGamePackage = preferences.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, Constants.DEFAULT_AUTO_START_GAME_PACKAGE) ?: ""
```

### 3. 数据流验证

**完整的数据流**：
```
用户在设置页面选择游戏 → 保存到SharedPreferences
    ↓
DeviceInfoReporter.getConfiguredGamePackage() → 读取SharedPreferences
    ↓
bootInfo.put("auto_start_game_package", gamePackage) → 添加到开机信息
    ↓
WebSocket发送boot_report → 服务器接收
    ↓
服务器存储device_info.auto_start_game_package → 发送给监控页面
    ↓
H5页面显示device.auto_start_game_package → 用户看到游戏信息
```

## 调试方法

### 1. Android端调试

**检查SharedPreferences内容**：
```kotlin
val prefs = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
val allPrefs = prefs.all
Log.d(TAG, "所有SharedPreferences内容: $allPrefs")

val gamePackage = prefs.getString(Constants.PREF_AUTO_START_GAME_PACKAGE, "NOT_FOUND")
Log.d(TAG, "游戏包名: $gamePackage")
```

**检查常量值**：
```kotlin
Log.d(TAG, "PREF_NAME: ${Constants.PREF_NAME}")
Log.d(TAG, "PREF_AUTO_START_GAME_PACKAGE: ${Constants.PREF_AUTO_START_GAME_PACKAGE}")
Log.d(TAG, "DEFAULT_AUTO_START_GAME_PACKAGE: ${Constants.DEFAULT_AUTO_START_GAME_PACKAGE}")
```

### 2. 服务器端调试

**检查接收到的数据**：
```python
logger.info(f"📱 收到开机信息: {sender_id}")
logger.debug(f"完整开机信息: {boot_info}")
logger.debug(f"游戏包名: {boot_info.get('auto_start_game_package', 'NOT_FOUND')}")
```

**检查存储的数据**：
```python
logger.debug(f"存储的设备信息: {boot_devices[cpu_unique_id]}")
```

### 3. H5页面调试

**检查接收到的数据**：
```javascript
case 'boot_devices_update':
    console.log('接收到设备更新:', data);
    console.log('设备列表:', data.devices);
    data.devices.forEach(device => {
        console.log(`设备 ${device.cpu_unique_id}: 游戏=${device.auto_start_game_package}`);
    });
    break;
```

## 测试验证

### 1. 设置游戏

1. 打开应用设置页面
2. 启用"自动启动游戏"
3. 选择一个游戏
4. 保存设置

### 2. 验证存储

检查日志中是否显示：
```
获取游戏包名: com.example.game (从webrtc_sender_prefs/auto_start_game_package)
```

### 3. 验证上报

检查开机上报日志：
```
📋 开机信息收集完成: CPU_ID=50864464..., 游戏=com.example.game
开机信息内容: CPU_ID=50864464..., 游戏=com.example.game
```

### 4. 验证显示

监控页面应该显示：
```
🎮 启动游戏: com.example.game
```

## 常见问题

### 1. 游戏包名仍然为空

**可能原因**：
- 用户没有在设置中选择游戏
- SharedPreferences文件损坏
- 应用权限问题

**解决方法**：
```kotlin
// 检查是否有游戏配置
val hasGame = prefs.getBoolean(Constants.PREF_AUTO_START_GAME, false)
Log.d(TAG, "是否启用自动启动游戏: $hasGame")

if (!hasGame) {
    Log.w(TAG, "用户未启用自动启动游戏功能")
}
```

### 2. CPU ID 显示 undefined

**可能原因**：
- 服务器端数据结构问题
- 前端JavaScript错误
- WebSocket消息格式问题

**解决方法**：
- 检查服务器端日志
- 检查浏览器控制台
- 验证WebSocket消息内容

### 3. 数据不一致

**可能原因**：
- 缓存问题
- 多个SharedPreferences文件
- 数据同步问题

**解决方法**：
- 清除应用数据重新测试
- 检查所有相关的SharedPreferences文件
- 确保数据流的一致性

## 版本更新

当前版本：v1.28 → v1.29

主要修复：
- 修复游戏包名获取的SharedPreferences名称错误
- 使用正确的常量定义获取配置
- 添加详细的调试日志
- 完善错误处理机制
