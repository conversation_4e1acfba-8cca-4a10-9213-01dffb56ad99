# 公网IP地址获取修复

## 问题描述

### 1. IPv6 获取问题
- `public_ipv6` 字段获取到HTML内容而不是IPv6地址
- 原因：`https://ifconfig.me` 返回的是网页内容

### 2. IPv4 地址问题
- `public_ip` 有时返回IPv6地址，但需要的是IPv4地址
- 原因：某些IP服务可能返回IPv6地址

## 修复方案

### 1. 改进IP获取逻辑

**原始代码问题**：
```kotlin
// 尝试获取IPv6
val ipv6 = getSimpleIp("https://ifconfig.me")  // 返回HTML内容
if (ipv6.isNotEmpty() && ipv6.contains(":")) {
    result.put("ipv6", ipv6)
}
```

**修复后**：
```kotlin
// 使用专门的IPv4服务
val ipv4Services = listOf(
    "https://ipv4.icanhazip.com",
    "https://api.ipify.org",
    "https://ipv4.jsonip.com"
)

// 使用专门的IPv6服务
val ipv6Services = listOf(
    "https://ipv6.icanhazip.com",
    "https://api6.ipify.org"
)
```

### 2. 添加IP地址验证

**IPv4验证**：
```kotlin
private fun isValidIPv4(ip: String): Boolean {
    if (ip.isBlank()) return false
    
    return try {
        val parts = ip.split(".")
        if (parts.size != 4) return false
        
        parts.all { part ->
            val num = part.toIntOrNull()
            num != null && num in 0..255
        }
    } catch (e: Exception) {
        false
    }
}
```

**IPv6验证**：
```kotlin
private fun isValidIPv6(ip: String): Boolean {
    if (ip.isBlank()) return false
    
    return try {
        ip.contains(":") && 
        ip.split(":").size >= 3 && 
        ip.split(":").size <= 8 &&
        !ip.contains("<") && // 排除HTML内容
        !ip.contains(">") &&
        !ip.contains("html", ignoreCase = true)
    } catch (e: Exception) {
        false
    }
}
```

## 修复后的服务列表

### IPv4专用服务
| 服务 | URL | 返回格式 |
|------|-----|----------|
| icanhazip | `https://ipv4.icanhazip.com` | 纯文本 |
| ipify | `https://api.ipify.org` | 纯文本 |
| jsonip | `https://ipv4.jsonip.com` | JSON |

### IPv6专用服务
| 服务 | URL | 返回格式 |
|------|-----|----------|
| icanhazip | `https://ipv6.icanhazip.com` | 纯文本 |
| ipify | `https://api6.ipify.org` | 纯文本 |

### 备用服务（带地理信息）
| 服务 | URL | 返回格式 |
|------|-----|----------|
| ipinfo.io | `https://ipinfo.io/json` | JSON（完整信息） |
| httpbin | `https://httpbin.org/ip` | JSON |

## 修复效果

### 修复前
```json
{
    "public_ip": "2001:db8::1",  // 可能是IPv6
    "public_ipv6": "<!DOCTYPE html><html>..."  // HTML内容
}
```

### 修复后
```json
{
    "public_ip": "***********",  // 确保是IPv4
    "public_ipv6": "2001:db8::1"  // 确保是IPv6或null
}
```

## 错误处理机制

### 1. 服务降级
1. **首选**：ipinfo.io（提供完整地理信息）
2. **备选1**：httpbin.org
3. **备选2**：专用IPv4服务列表
4. **最后**：返回"unknown"

### 2. 格式验证
- **IPv4**：严格的点分十进制验证
- **IPv6**：基本格式检查 + HTML内容过滤
- **异常处理**：所有网络请求都有超时和异常处理

### 3. 日志记录
```kotlin
Log.d(TAG, "从 $service 获取IPv4失败: ${e.message}")
Log.d(TAG, "从 $service 获取IPv6失败: ${e.message}")
```

## 测试建议

### 1. 功能测试
```kotlin
// 测试IPv4获取
val deviceInfo = DeviceInfoCollector.collectDeviceInfo(context)
val publicIp = deviceInfo.getString("public_ip")
assert(isValidIPv4(publicIp) || publicIp == "unknown")

// 测试IPv6获取
val publicIpv6 = deviceInfo.optString("public_ipv6", null)
assert(publicIpv6 == null || isValidIPv6(publicIpv6))
```

### 2. 网络异常测试
- 断网情况下的处理
- 服务超时的处理
- 服务返回错误的处理

### 3. 格式验证测试
```kotlin
// IPv4验证测试
assert(isValidIPv4("***********") == true)
assert(isValidIPv4("256.1.1.1") == false)
assert(isValidIPv4("<!DOCTYPE html>") == false)

// IPv6验证测试
assert(isValidIPv6("2001:db8::1") == true)
assert(isValidIPv6("***********") == false)
assert(isValidIPv6("<!DOCTYPE html>") == false)
```

## 性能优化

### 1. 超时设置
- **连接超时**：5秒
- **读取超时**：5秒
- **总体策略**：快速失败，立即尝试下一个服务

### 2. 服务选择
- **优先级**：地理信息服务 > 专用服务 > 通用服务
- **并发**：目前是串行，可考虑并发获取提高速度

### 3. 缓存机制
- **建议**：可考虑缓存IP地址一段时间
- **实现**：SharedPreferences + 时间戳

## 相关文件

- `DeviceInfoCollector.kt` - 主要修改文件
- `debug/ip_address_fix.md` - 本修复说明文档

## 版本更新

当前版本：v1.21 → v1.22

主要改进：
- 修复IPv6获取返回HTML的问题
- 确保IPv4地址的准确性
- 添加IP地址格式验证
- 改进错误处理和服务降级机制
