#!/bin/bash

# WebRTC 信令服务器启动脚本
# 使用方法: ./start_signaling_server.sh [dev|prod|background]

# 默认配置
DEFAULT_WS_HOST="0.0.0.0"
DEFAULT_WS_PORT=28765
DEFAULT_HTTP_HOST="0.0.0.0"
DEFAULT_HTTP_PORT=28080
DEFAULT_WEB_DIR="/www/wwwroot/sj/web"
DEFAULT_LOG_DIR="./logs"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_SCRIPT="$SCRIPT_DIR/enhanced_signaling_server.py"

# 检查 Python 脚本是否存在
if [ ! -f "$SERVER_SCRIPT" ]; then
    echo "❌ 错误: 找不到服务器脚本 $SERVER_SCRIPT"
    exit 1
fi

# 创建日志目录
mkdir -p "$DEFAULT_LOG_DIR"

# 获取运行模式
MODE=${1:-dev}

case $MODE in
    "dev"|"development")
        echo "🚀 启动开发模式..."
        python3 "$SERVER_SCRIPT" \
            --ws-host "$DEFAULT_WS_HOST" \
            --ws-port "$DEFAULT_WS_PORT" \
            --http-host "$DEFAULT_HTTP_HOST" \
            --http-port "$DEFAULT_HTTP_PORT" \
            --web-dir "$DEFAULT_WEB_DIR" \
            --log-dir "$DEFAULT_LOG_DIR" \
            --log-level DEBUG
        ;;
    
    "prod"|"production")
        echo "🏭 启动生产模式..."
        python3 "$SERVER_SCRIPT" \
            --ws-host "$DEFAULT_WS_HOST" \
            --ws-port "$DEFAULT_WS_PORT" \
            --http-host "$DEFAULT_HTTP_HOST" \
            --http-port "$DEFAULT_HTTP_PORT" \
            --web-dir "$DEFAULT_WEB_DIR" \
            --log-dir "$DEFAULT_LOG_DIR" \
            --log-level INFO
        ;;
    
    "background"|"bg")
        echo "🌙 启动后台模式..."
        nohup python3 "$SERVER_SCRIPT" \
            --ws-host "$DEFAULT_WS_HOST" \
            --ws-port "$DEFAULT_WS_PORT" \
            --http-host "$DEFAULT_HTTP_HOST" \
            --http-port "$DEFAULT_HTTP_PORT" \
            --web-dir "$DEFAULT_WEB_DIR" \
            --log-dir "$DEFAULT_LOG_DIR" \
            --log-level INFO \
            --no-console > /dev/null 2>&1 &
        
        echo "✅ 服务器已在后台启动"
        echo "📝 日志文件: $DEFAULT_LOG_DIR/signaling_server_*.log"
        echo "🔍 查看日志: tail -f $DEFAULT_LOG_DIR/signaling_server_*.log"
        echo "⏹️  停止服务: pkill -f enhanced_signaling_server.py"
        ;;
    
    "stop")
        echo "⏹️  停止信令服务器..."
        pkill -f enhanced_signaling_server.py
        if [ $? -eq 0 ]; then
            echo "✅ 服务器已停止"
        else
            echo "⚠️  没有找到运行中的服务器进程"
        fi
        ;;
    
    "status")
        echo "🔍 检查服务器状态..."
        if pgrep -f enhanced_signaling_server.py > /dev/null; then
            echo "✅ 服务器正在运行"
            echo "📊 进程信息:"
            ps aux | grep enhanced_signaling_server.py | grep -v grep
        else
            echo "❌ 服务器未运行"
        fi
        ;;
    
    "logs")
        echo "📝 查看最新日志..."
        if [ -d "$DEFAULT_LOG_DIR" ]; then
            LATEST_LOG=$(ls -t "$DEFAULT_LOG_DIR"/signaling_server_*.log 2>/dev/null | head -1)
            if [ -n "$LATEST_LOG" ]; then
                echo "📄 日志文件: $LATEST_LOG"
                tail -f "$LATEST_LOG"
            else
                echo "❌ 没有找到日志文件"
            fi
        else
            echo "❌ 日志目录不存在: $DEFAULT_LOG_DIR"
        fi
        ;;
    
    "help"|"-h"|"--help")
        echo "WebRTC 信令服务器启动脚本"
        echo ""
        echo "使用方法:"
        echo "  $0 [模式]"
        echo ""
        echo "模式:"
        echo "  dev, development  - 开发模式 (DEBUG日志级别)"
        echo "  prod, production  - 生产模式 (INFO日志级别)"
        echo "  background, bg    - 后台模式 (后台运行，仅文件日志)"
        echo "  stop              - 停止服务器"
        echo "  status            - 查看服务器状态"
        echo "  logs              - 查看最新日志"
        echo "  help              - 显示此帮助信息"
        echo ""
        echo "配置:"
        echo "  WebSocket端口: $DEFAULT_WS_PORT"
        echo "  HTTP端口: $DEFAULT_HTTP_PORT"
        echo "  Web目录: $DEFAULT_WEB_DIR"
        echo "  日志目录: $DEFAULT_LOG_DIR"
        echo ""
        echo "示例:"
        echo "  $0 dev              # 开发模式"
        echo "  $0 prod             # 生产模式"
        echo "  $0 background       # 后台运行"
        echo "  $0 stop             # 停止服务"
        echo "  $0 logs             # 查看日志"
        ;;
    
    *)
        echo "❌ 未知模式: $MODE"
        echo "💡 使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac
